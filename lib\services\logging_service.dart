/// ✅ API重定向文件
///
/// 此文件已被重构，统一使用 AppLogger
/// 为保持向后兼容性，提供重定向导出

// 重定向到新的统一日志系统
export 'package:loadguard/utils/app_logger.dart';

import 'package:loadguard/utils/app_logger.dart';

/// ⚠️ 已废弃：请使用 AppLogger 替代
///
/// 此类已被 AppLogger 替代，提供更统一的日志功能
@Deprecated('使用 AppLogger 替代')
class LoggingService {
  static const String _defaultTag = 'LoadGuard';

  /// ⚠️ 已废弃：请使用 AppLogger.info() 替代
  @Deprecated('使用 AppLogger.info() 替代')
  static void info(String message, {String? tag, Object? error}) {
    AppLogger.info(message, error: error);
  }

  /// ⚠️ 已废弃：请使用 AppLogger.warning() 替代
  @Deprecated('使用 AppLogger.warning() 替代')
  static void warning(String message, {String? tag, Object? error}) {
    AppLogger.warning(message, error: error);
  }

  /// ⚠️ 已废弃：请使用 AppLogger.error() 替代
  @Deprecated('使用 AppLogger.error() 替代')
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    AppLogger.error(message, error: error, stackTrace: stackTrace);
  }

  /// ⚠️ 已废弃：请使用 AppLogger.debug() 替代
  @Deprecated('使用 AppLogger.debug() 替代')
  static void debug(String message, {String? tag, Object? error}) {
    AppLogger.debug(message, error: error);
  }

  /// ⚠️ 已废弃：请使用 AppLogger.info() 记录性能信息
  @Deprecated('使用 AppLogger.info() 记录性能信息')
  static void perf(String operation, Duration duration, {String? tag}) {
    AppLogger.info('$operation took ${duration.inMilliseconds}ms');
  }
}

/// ⚠️ 已废弃：Log类别名，请使用 AppLogger 替代
@Deprecated('使用 AppLogger 替代')
class Log {
  @Deprecated('使用 AppLogger.info() 替代')
  static void i(String message, {String? tag}) {
    AppLogger.info(message);
  }

  @Deprecated('使用 AppLogger.warning() 替代')
  static void w(String message, {String? tag}) {
    AppLogger.warning(message);
  }

  @Deprecated('使用 AppLogger.error() 替代')
  static void e(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    AppLogger.error(message, error: error, stackTrace: stackTrace);
  }

  @Deprecated('使用 AppLogger.debug() 替代')
  static void d(String message, {String? tag}) {
    AppLogger.debug(message);
  }

  static void perf(String operation, Duration duration, {String? tag}) {
    LoggingService.perf(operation, duration, tag: tag);
  }
}
