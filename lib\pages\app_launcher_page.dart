import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/app_security_service.dart';
import '../services/strict_security_service.dart';
import '../services/enterprise_license_service.dart';
import '../services/logging_service.dart';
import '../widgets/industrial_logo.dart';
import 'package:go_router/go_router.dart';
import '../utils/theme_colors.dart';
import '../utils/simple_navigation_helper.dart';
import '../utils/onboarding_helper.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

/// 应用启动检查页面
/// 检查激活状态并决定跳转到主页还是激活页面
class AppLauncherPage extends ConsumerStatefulWidget {
  const AppLauncherPage({Key? key}) : super(key: key);

  @override
  ConsumerState<AppLauncherPage> createState() => _AppLauncherPageState();
}

class _AppLauncherPageState extends ConsumerState<AppLauncherPage>
    with LifecycleMixin<AppLauncherPage> {
  String _statusMessage = '正在初始化...';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkActivationStatus();
    });
  }

  @override
  void dispose() {
    // 清理任何可能的异步操作或资源
    super.dispose();
  }

  Future<void> _checkActivationStatus() async {
    try {
      AppLogger.info('开始检查激活状态', tag: 'AppLauncher');

      // 添加延迟，确保页面渲染完成
      await Future.delayed(const Duration(milliseconds: 800));

      setState(() {
        _statusMessage = '初始化安全服务...';
      });

      // 🔧 修复：使用新的严格安全服务
      await StrictSecurityService.initialize();

      setState(() {
        _statusMessage = '检查许可证状态...';
      });

      AppLogger.info('开始检查许可证状态', tag: 'AppLauncher');

      // 🔧 修复：使用新的安全检查方法
      final launchPermission = await StrictSecurityService.checkAppLaunchPermission();
      final licenseStatus = await StrictSecurityService.getLicenseStatus();

      AppLogger.info('启动权限: $launchPermission', tag: 'AppLauncher');
      AppLogger.info('许可证状态: $licenseStatus', tag: 'AppLauncher');

      final canLaunch = launchPermission['canLaunch'] as bool;
      final shouldRedirect = launchPermission['shouldRedirectToActivation'] as bool;
      final message = launchPermission['message'] as String;
      final userRole = launchPermission['userRole'] as UserRole;
      final remainingDays = licenseStatus['remainingDays'] as int;

      // 显示许可证信息
      setState(() {
        if (userRole == UserRole.superAdmin) {
          _statusMessage = '超级管理员权限';
        } else if (canLaunch && remainingDays > 0) {
          _statusMessage = '许可证有效，剩余 $remainingDays 天';
        } else {
          _statusMessage = message;
        }
      });
      await Future.delayed(const Duration(milliseconds: 500));

      // 根据状态决定跳转
      if (mounted) {
        if (canLaunch && !shouldRedirect) {
          AppLogger.info('许可证有效，跳转主页', tag: 'AppLauncher');
          setState(() {
            _statusMessage = '启动完成...';
          });
          await Future.delayed(const Duration(milliseconds: 300));
          if (mounted) context.go('/home');
          // 显示首次启动引导
          if (mounted) OnboardingHelper.showFirstLaunchGuide(context);
        } else {
          AppLogger.info('需要激活，跳转激活页', tag: 'AppLauncher');
          setState(() {
            _statusMessage = '跳转到激活页面...';
          });
          await Future.delayed(const Duration(milliseconds: 300));
          if (mounted) context.go('/activation');
        }
      }
    } catch (e, s) {
      AppLogger.error('检查激活状态异常',
          error: e, stackTrace: s, tag: 'AppLauncher');
      setState(() {
        _statusMessage = '检查过程出现异常，跳转激活页面...';
      });

      // 🔧 修复：发生错误时跳转到激活页面，而不是主页
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        AppLogger.info('跳转激活页（异常分支）', tag: 'AppLauncher');
        context.go('/activation');
      }
    }
  }

  void _showSecurityError() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('安全检查失败'),
        content: const Text(
          '检测到应用运行环境异常，可能原因：\n'
          '• 系统时间被篡改\n'
          '• 应用被非法修改\n'
          '• 运行环境不安全\n\n'
          '请联系管理员解决此问题。',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 重新检查
              _checkActivationStatus();
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  void _handleBackNavigation() {
    // 启动页面阻止返回，防止用户误触
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.hourglass_empty, color: Colors.white),
            const SizedBox(width: 8),
            Text('应用正在启动中，请稍候...'),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: ThemeColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: _handleBackNavigation,
      enableSwipeBack: false, // 禁用侧滑，防止启动时误触
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 优化后的品牌Logo
                IndustrialLogo(
                  size: 120,
                  primaryColor: ThemeColors.primary,
                  accentColor: ThemeColors.yellow,
                  showText: true,
                  showTagline: true,
                  fontSize: 32,
                ),
                const SizedBox(height: 32),
                // 主标题
                Text(
                  '装运卫士',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.w900,
                    color: ThemeColors.textOnGradient,
                    letterSpacing: 1.2,
                  ),
                ),
                const SizedBox(height: 10),
                // 副标题
                Text(
                  'Google ML Kit专业版',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                    letterSpacing: 0.8,
                  ),
                ),
                const SizedBox(height: 8),
                // Slogan
                Text(
                  '工业级物流标签智能识别系统',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeColors.textOnGradient.withOpacity(0.8),
                    fontStyle: FontStyle.italic,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 40),
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
                const SizedBox(height: 20),
                Text(
                  _statusMessage,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
