import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/worker_info_data.dart';
import '../utils/theme_colors.dart';
import '../services/workload_assignment_service.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

class WorkloadManagementPage extends ConsumerStatefulWidget {
  const WorkloadManagementPage({Key? key}) : super(key: key);

  @override
  ConsumerState<WorkloadManagementPage> createState() => _WorkloadManagementPageState();
}

class _WorkloadManagementPageState extends ConsumerState<WorkloadManagementPage>
    with LifecycleMixin<WorkloadManagementPage> {
  String _searchText = '';
  Set<String> _selectedWarehouses = {}; // 选中的库区
  Set<String> _selectedGroups = {}; // 选中的小组
  Set<String> _selectedPeople = {}; // 选中的人员
  bool _isLoading = true;
  int _palletCount = 25; // 示例数据，实际应从当日任务统计获取

  // 获取所有库区
  List<String> get _allWarehouses {
    final set = <String>{};
    for (var w in allWorkers) {
      set.add(w.warehouse);
    }
    final list = set.toList();
    list.sort();
    return list;
  }

  // 获取选中库区下的小组分组
  Map<String, List<WorkerInfo>> get _selectedGroupsData {
    final result = <String, List<WorkerInfo>>{};
    for (var w in allWorkers) {
      if (_selectedWarehouses.contains(w.warehouse)) {
        result[w.group] ??= [];
        result[w.group]!.add(w);
      }
    }
    return result;
  }

  @override
  void initState() {
    super.initState();
    // 默认高亮1号库
    _selectedWarehouses.add('1号库');
    _loadSavedAssignment();
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadSavedAssignment() async {
    // 预留：后续实现当天有效的本地保存
    setState(() => _isLoading = false);
  }

  // 搜索过滤
  bool _workerMatch(WorkerInfo w) {
    if (_searchText.isEmpty) return true;
    final q = _searchText.toLowerCase();
    return w.name.toLowerCase().contains(q) ||
        w.warehouse.toLowerCase().contains(q) ||
        w.group.toLowerCase().contains(q);
  }

  // 分组数据结构：库区-小组-人员
  Map<String, Map<String, List<WorkerInfo>>> get _groupedData {
    final filtered = allWorkers.where(_workerMatch).toList();
    final map = <String, Map<String, List<WorkerInfo>>>{};
    for (var w in filtered) {
      map[w.warehouse] ??= {};
      map[w.warehouse]![w.group] ??= [];
      map[w.warehouse]![w.group]!.add(w);
    }
    return map;
  }

  // 选中人员列表
  List<WorkerInfo> get _selectedWorkers {
    final all =
        allWorkers.where((w) => _selectedPeople.contains(w.name)).toList();
    return all;
  }

  // 库区分组色条颜色（可自定义更多颜色）
  Color _warehouseColor(String warehouse) {
    final colors = [
      ThemeColors.primary,
      Colors.green,
      Colors.orange,
      Colors.blue,
      Colors.purple,
      Colors.teal,
      Colors.red,
      Colors.brown,
    ];
    final idx = warehouse.codeUnitAt(0) % colors.length;
    return colors[idx];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: ThemeColors.primaryGradient,
        ),
        child: SafeArea(
          child: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(ThemeColors.primary),
                    backgroundColor: Colors.white24,
                    strokeCap: StrokeCap.round,
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildProfessionalHeader(),
                      const SizedBox(height: 24),
                      _buildSearchBar(),
                      const SizedBox(height: 16),
                      _buildWarehouseSelector(),
                      const SizedBox(height: 20),
                      _buildGroupCardList(),
                      const SizedBox(height: 24),
                      _buildWorkloadSummaryCard(),
                      const SizedBox(height: 16),
                      _buildAssignmentResultCard(),
                      const SizedBox(height: 24),
                      _buildActionButtons(),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildProfessionalHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.verified, color: ThemeColors.primaryHover, size: 28),
              const SizedBox(width: 12),
              const Text('ML Kit V2专业版',
                  style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Colors.white)),
            ],
          ),
          const SizedBox(height: 8),
          const Text('Google AI引擎 | 化工专业识别 | 智能批号验证',
              style: TextStyle(fontSize: 14, color: Colors.white70),
              textAlign: TextAlign.center),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.12),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        style: const TextStyle(color: Colors.white, fontSize: 16),
        decoration: InputDecoration(
          hintText: '搜索库区/小组/姓名...',
          hintStyle: const TextStyle(color: Colors.white54),
          border: InputBorder.none,
          prefixIcon: const Icon(Icons.search, color: Colors.white54),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (v) => setState(() => _searchText = v.trim()),
      ),
    );
  }

  // 1. 修复库区多选弹窗无法选择
  Widget _buildWarehouseSelector() {
    final warehouses = _allWarehouses;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () async {
            final selected = await showModalBottomSheet<List<String>>(
              context: context,
              backgroundColor: Colors.transparent,
              isScrollControlled: true,
              builder: (context) {
                final tempSelected = Set<String>.from(_selectedWarehouses);
                return StatefulBuilder(
                  builder: (context, setState) {
                    return Container(
                      padding: const EdgeInsets.only(
                          top: 24, left: 0, right: 0, bottom: 32),
                      decoration: BoxDecoration(
                        color: ThemeColors.primaryGradient.colors.first
                            .withOpacity(0.98),
                        borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(24)),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Center(
                            child: Icon(Icons.drag_handle,
                                color: Colors.white38, size: 32),
                          ),
                          const SizedBox(height: 12),
                          const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 24),
                            child: Text('选择库区（可多选）',
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold)),
                          ),
                          const SizedBox(height: 18),
                          // 卡片式布局
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Column(
                              children: warehouses.map((warehouse) {
                                final color = _warehouseColor(warehouse);
                                final selected =
                                    tempSelected.contains(warehouse);
                                return Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          if (selected) {
                                            tempSelected.remove(warehouse);
                                          } else {
                                            tempSelected.add(warehouse);
                                          }
                                        });
                                      },
                                      borderRadius: BorderRadius.circular(12),
                                      child: Container(
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: selected
                                              ? color.withOpacity(0.15)
                                              : Colors.white.withOpacity(0.08),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                            color: selected
                                                ? color.withOpacity(0.8)
                                                : Colors.white.withOpacity(0.2),
                                            width: selected ? 2 : 1,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            // 选择状态图标
                                            Container(
                                              width: 24,
                                              height: 24,
                                              decoration: BoxDecoration(
                                                color: selected
                                                    ? color
                                                    : Colors.transparent,
                                                border: Border.all(
                                                  color: selected
                                                      ? color
                                                      : Colors.white
                                                          .withOpacity(0.5),
                                                  width: 2,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: selected
                                                  ? const Icon(Icons.check,
                                                      color: Colors.white,
                                                      size: 16)
                                                  : null,
                                            ),
                                            const SizedBox(width: 16),
                                            // 库区颜色条
                                            Container(
                                              width: 6,
                                              height: 32,
                                              decoration: BoxDecoration(
                                                color: color,
                                                borderRadius:
                                                    BorderRadius.circular(3),
                                              ),
                                            ),
                                            const SizedBox(width: 16),
                                            // 库区名称
                                            Expanded(
                                              child: Text(
                                                warehouse,
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ),
                                            // 选中状态指示器
                                            if (selected)
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 8,
                                                        vertical: 4),
                                                decoration: BoxDecoration(
                                                  color: color,
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                child: const Text(
                                                  '已选',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 12,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                          const SizedBox(height: 24),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Container(
                              width: double.infinity,
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: ThemeColors.primary,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12)),
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 18),
                                  elevation: 4,
                                ),
                                onPressed: () {
                                  Navigator.of(context)
                                      .pop(tempSelected.toList());
                                },
                                child: const Text(
                                  '确定',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            );
            if (selected != null) {
              setState(() {
                _selectedWarehouses = Set<String>.from(selected);
                _selectedGroups.clear();
                _selectedPeople.clear();
              });
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 14),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.10),
              borderRadius: BorderRadius.circular(16),
              border:
                  Border.all(color: Colors.white.withOpacity(0.18), width: 2),
            ),
            child: Row(
              children: [
                const Icon(Icons.warehouse, color: Colors.white70),
                const SizedBox(width: 10),
                Expanded(
                  child: _selectedWarehouses.isEmpty
                      ? const Text('请选择库区（可多选）',
                          style: TextStyle(color: Colors.white70, fontSize: 16))
                      : Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _selectedWarehouses.map((warehouse) {
                            final color = _warehouseColor(warehouse);
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 14, vertical: 6),
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 1.5),
                                boxShadow: [
                                  BoxShadow(
                                    color: color.withOpacity(0.4),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    warehouse,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      shadows: [
                                        Shadow(
                                          color: Colors.black26,
                                          offset: Offset(0.5, 0.5),
                                          blurRadius: 1,
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _selectedWarehouses.remove(warehouse);
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Icon(Icons.close,
                                          color: Colors.white, size: 14),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                ),
                const Icon(Icons.arrow_drop_down, color: Colors.white),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 2. 人员卡片和小组卡片等宽
  Widget _buildGroupCardVertical(
      MapEntry<String, List<WorkerInfo>> groupEntry, Color color) {
    final group = groupEntry.key;
    final members = groupEntry.value;
    final groupSelected = _selectedGroups.contains(group);
    return Container(
      margin: const EdgeInsets.only(bottom: 18),
      decoration: BoxDecoration(
        color: groupSelected
            ? color.withOpacity(0.12)
            : Colors.white.withOpacity(0.08),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.10),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
            child: Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    group.isEmpty ? '未分组' : group,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          offset: Offset(0.5, 0.5),
                          blurRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text('${members.length}人',
                    style:
                        const TextStyle(color: Colors.white70, fontSize: 13)),
                const Spacer(),
                if (groupSelected)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 2),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.check, color: Colors.white, size: 18),
                        SizedBox(width: 4),
                        Text('已选',
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 13)),
                      ],
                    ),
                  )
                else
                  Icon(Icons.radio_button_unchecked, color: Colors.white54),
              ],
            ),
          ),
          // 竖向成员卡片（等宽，左右边距与小组卡片一致）
          ...members.map((worker) {
            final isSelected = _selectedPeople.contains(worker.name);
            return Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      _selectedPeople.remove(worker.name);
                    } else {
                      _selectedPeople.add(worker.name);
                    }
                  });
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  width: double.infinity,
                  margin:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 6),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 18, vertical: 14),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? color.withOpacity(0.9)
                        : Colors.white.withOpacity(0.10),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      if (isSelected)
                        BoxShadow(
                          color: color.withOpacity(0.18),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                    ],
                    border: Border.all(
                        color:
                            isSelected ? color : Colors.white.withOpacity(0.18),
                        width: 2),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                              color: Colors.white.withOpacity(0.3), width: 1),
                        ),
                        child: Text(
                          group.isEmpty ? '未分组' : group,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                color: Colors.black26,
                                offset: Offset(0.5, 0.5),
                                blurRadius: 1,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          worker.name,
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            shadows: [
                              Shadow(
                                color: isSelected
                                    ? Colors.black38
                                    : Colors.black26,
                                offset: const Offset(0.5, 0.5),
                                blurRadius: 1,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Spacer(),
                      if (isSelected)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Icon(Icons.check, color: color, size: 18),
                        )
                      else
                        Icon(Icons.radio_button_unchecked,
                            color: Colors.white54),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  // 下方小组卡片列表（大气竖向成员卡片，调用 _buildGroupCardVertical）
  Widget _buildGroupCardList() {
    final groups = _selectedGroupsData;
    if (groups.isEmpty) {
      return const Center(
          child: Text('请先选择库区',
              style: TextStyle(color: Colors.white70, fontSize: 16)));
    }

    // 按库区分组，保持各自颜色区分
    final warehouseGroups =
        <String, List<MapEntry<String, List<WorkerInfo>>>>{};
    for (final entry in groups.entries) {
      final warehouse = entry.value.first.warehouse;
      warehouseGroups[warehouse] ??= [];
      warehouseGroups[warehouse]!.add(entry);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: warehouseGroups.entries.expand((warehouseEntry) {
        final warehouse = warehouseEntry.key;
        final warehouseColor = _warehouseColor(warehouse);
        final groupEntries = warehouseEntry.value;

        return [
          // 库区分组标题（仅在多个库区时显示）
          if (warehouseGroups.length > 1) ...[
            Container(
              margin: const EdgeInsets.only(bottom: 12, top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: warehouseColor.withOpacity(0.15),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                    color: warehouseColor.withOpacity(0.3), width: 1),
              ),
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: warehouseColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    warehouse,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          offset: const Offset(0.5, 0.5),
                          blurRadius: 1,
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: warehouseColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${groupEntries.fold<int>(0, (sum, entry) => sum + entry.value.length)}人',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          // 各小组卡片
          ...groupEntries.map((groupEntry) =>
              _buildGroupCardVertical(groupEntry, warehouseColor)),
        ];
      }).toList(),
    );
  }

  Widget _buildAssignmentResultCard() {
    // 预留：后续实现自动统计当天任务数量和分配吨数
    final selected = _selectedWorkers;
    if (selected.isEmpty) {
      return const SizedBox();
    }
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('分配结果',
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w600)),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: selected.map((worker) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: ThemeColors.primaryHover,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(worker.group.isEmpty ? '未分组' : worker.group,
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold)),
                    ),
                    const SizedBox(width: 8),
                    Text(worker.name,
                        style: const TextStyle(
                            color: Colors.white, fontWeight: FontWeight.bold)),
                    const SizedBox(width: 8),
                    Text(worker.warehouse,
                        style: const TextStyle(
                            color: Colors.white70, fontSize: 13)),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkloadSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              const Text('工作量统计概览',
                  style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                    '今日任务',
                    '${_selectedPeople.length > 0 ? "模拟 " : ""}${_palletCount}',
                    '个',
                    Colors.blue),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryItem(
                    '参与人员', '${_selectedPeople.length}', '人', Colors.green),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryItem(
                    '预计吨数',
                    '${(_palletCount * 1.5).toStringAsFixed(1)}',
                    '吨',
                    Colors.orange),
              ),
            ],
          ),
          if (_selectedPeople.isNotEmpty) ...[
            const SizedBox(height: 12),
            const Divider(color: Colors.white24),
            const SizedBox(height: 8),
            Text(
              '人均分配: ${((_palletCount * 1.5) / _selectedPeople.length).toStringAsFixed(1)} 吨/人',
              style: const TextStyle(color: Colors.white70, fontSize: 14),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
      String label, String value, String unit, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style:
                TextStyle(color: Colors.white.withOpacity(0.8), fontSize: 12),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 2),
              Text(
                unit,
                style: TextStyle(
                    color: Colors.white.withOpacity(0.7), fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 56,
            child: OutlinedButton.icon(
              onPressed: () {
                // 跳转到工作量统计页面
                context.push('/workload-statistics');
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: BorderSide(color: Colors.white.withOpacity(0.3)),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
              ),
              icon: const Icon(Icons.analytics_outlined),
              label: const Text(
                '查看统计',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Container(
            height: 56,
            child: FilledButton.icon(
              onPressed: () {
                // 跳转到任务创建页面
                context.push('/template-selection');
              },
              style: FilledButton.styleFrom(
                backgroundColor: ThemeColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
                elevation: 0,
              ),
              icon: const Icon(Icons.add_task),
              label: const Text(
                '创建任务',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
