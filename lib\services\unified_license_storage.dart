import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'app_security_service.dart';
import 'logging_service.dart';

/// 🔐 统一许可证存储服务
/// 解决多套存储系统导致的数据不一致问题
class UnifiedLicenseStorage {
  static const String _tag = 'UnifiedLicenseStorage';
  
  // 统一的存储key
  static const String _unifiedLicenseKey = 'unified_license_data';
  static const String _unifiedBackupKey = 'unified_license_backup';
  static const String _unifiedVersionKey = 'unified_license_version';
  static const String _unifiedChecksumKey = 'unified_license_checksum';
  
  static const int _currentVersion = 1;
  
  /// 统一的许可证数据结构
  static const String _salt = 'LoadGuard_Unified_License_2024';
  
  /// 保存许可证数据（统一格式）
  static Future<bool> saveLicense(Map<String, dynamic> licenseData) async {
    try {
      AppLogger.info('💾 保存统一许可证数据', tag: _tag);
      
      final prefs = await SharedPreferences.getInstance();
      
      // 1. 添加统一的元数据
      final unifiedData = Map<String, dynamic>.from(licenseData);
      unifiedData['deviceId'] = await AppSecurityService.getDeviceId();
      unifiedData['storedAt'] = DateTime.now().millisecondsSinceEpoch;
      unifiedData['version'] = _currentVersion;
      
      // 2. 计算校验和
      final checksum = _calculateChecksum(unifiedData);
      unifiedData['checksum'] = checksum;
      
      // 3. 序列化并存储
      final jsonData = jsonEncode(unifiedData);
      await prefs.setString(_unifiedLicenseKey, jsonData);
      await prefs.setInt(_unifiedVersionKey, _currentVersion);
      await prefs.setString(_unifiedChecksumKey, checksum);
      
      // 4. 创建备份
      await prefs.setString(_unifiedBackupKey, jsonData);
      
      // 5. 同步到旧系统（向后兼容）
      await _syncToLegacySystems(unifiedData);
      
      AppLogger.info('✅ 统一许可证数据保存成功', tag: _tag);
      return true;
    } catch (e) {
      AppLogger.error('❌ 统一许可证数据保存失败', error: e, tag: _tag);
      return false;
    }
  }
  
  /// 加载许可证数据（统一格式）
  static Future<Map<String, dynamic>?> loadLicense() async {
    try {
      AppLogger.info('📖 加载统一许可证数据', tag: _tag);
      
      final prefs = await SharedPreferences.getInstance();
      
      // 1. 尝试加载统一格式数据
      String? jsonData = prefs.getString(_unifiedLicenseKey);
      
      // 2. 如果没有统一数据，尝试从旧系统迁移
      if (jsonData == null) {
        AppLogger.info('🔄 未找到统一数据，尝试从旧系统迁移', tag: _tag);
        final migratedData = await _migrateFromLegacySystems();
        if (migratedData != null) {
          await saveLicense(migratedData);
          return migratedData;
        }
        return null;
      }
      
      // 3. 如果主数据损坏，尝试从备份恢复
      if (!await _verifyIntegrity(jsonData)) {
        AppLogger.warning('⚠️ 主数据损坏，尝试从备份恢复', tag: _tag);
        jsonData = prefs.getString(_unifiedBackupKey);
        if (jsonData == null || !await _verifyIntegrity(jsonData)) {
          AppLogger.error('❌ 备份数据也损坏，清除所有数据', tag: _tag);
          await clearLicense();
          return null;
        }
      }
      
      // 4. 解析并验证数据
      final licenseData = jsonDecode(jsonData) as Map<String, dynamic>;
      
      // 5. 验证校验和
      final storedChecksum = licenseData.remove('checksum');
      final calculatedChecksum = _calculateChecksum(licenseData);
      
      if (storedChecksum != calculatedChecksum) {
        AppLogger.error('❌ 数据校验和不匹配', tag: _tag);
        return null;
      }
      
      AppLogger.info('✅ 统一许可证数据加载成功', tag: _tag);
      return licenseData;
    } catch (e) {
      AppLogger.error('❌ 统一许可证数据加载失败', error: e, tag: _tag);
      return null;
    }
  }
  
  /// 清除所有许可证数据
  static Future<bool> clearLicense() async {
    try {
      AppLogger.info('🗑️ 清除所有许可证数据', tag: _tag);
      
      final prefs = await SharedPreferences.getInstance();
      
      // 1. 清除统一存储
      await prefs.remove(_unifiedLicenseKey);
      await prefs.remove(_unifiedBackupKey);
      await prefs.remove(_unifiedVersionKey);
      await prefs.remove(_unifiedChecksumKey);
      
      // 2. 清除旧系统数据（确保完全清理）
      await _clearLegacySystems();
      
      AppLogger.info('✅ 所有许可证数据清除成功', tag: _tag);
      return true;
    } catch (e) {
      AppLogger.error('❌ 清除许可证数据失败', error: e, tag: _tag);
      return false;
    }
  }
  
  /// 计算数据校验和
  static String _calculateChecksum(Map<String, dynamic> data) {
    final dataString = jsonEncode(data) + _salt;
    final bytes = utf8.encode(dataString);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// 验证数据完整性
  static Future<bool> _verifyIntegrity(String jsonData) async {
    try {
      final data = jsonDecode(jsonData) as Map<String, dynamic>;
      final storedChecksum = data['checksum'];
      if (storedChecksum == null) return false;
      
      final dataForCheck = Map<String, dynamic>.from(data);
      dataForCheck.remove('checksum');
      final calculatedChecksum = _calculateChecksum(dataForCheck);
      
      return storedChecksum == calculatedChecksum;
    } catch (e) {
      return false;
    }
  }
  
  /// 同步到旧系统（向后兼容）
  static Future<void> _syncToLegacySystems(Map<String, dynamic> unifiedData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 同步到 AppSecurityService 格式
      if (unifiedData['licenseType'] != null) {
        await prefs.setString('license_type', unifiedData['licenseType']);
      }
      if (unifiedData['userRole'] != null) {
        await prefs.setString('user_role', unifiedData['userRole']);
      }
      if (unifiedData['activatedAt'] != null) {
        await prefs.setInt('activation_time', unifiedData['activatedAt']);
      }
      if (unifiedData['expiryTime'] != null) {
        await prefs.setInt('license_expiry', unifiedData['expiryTime']);
      }
      
      // 同步到 StrictSecurityService 格式
      await prefs.setString('strict_security_license_info', jsonEncode(unifiedData));
      
      AppLogger.info('✅ 数据已同步到旧系统', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 同步到旧系统失败', error: e, tag: _tag);
    }
  }
  
  /// 从旧系统迁移数据
  static Future<Map<String, dynamic>?> _migrateFromLegacySystems() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 1. 尝试从 StrictSecurityService 迁移
      final strictData = prefs.getString('strict_security_license_info');
      if (strictData != null) {
        AppLogger.info('🔄 从 StrictSecurityService 迁移数据', tag: _tag);
        return jsonDecode(strictData) as Map<String, dynamic>;
      }
      
      // 2. 尝试从 AppSecurityService 迁移
      final licenseType = prefs.getString('license_type');
      final userRole = prefs.getString('user_role');
      final activationTime = prefs.getInt('activation_time');
      final licenseExpiry = prefs.getInt('license_expiry');
      
      if (licenseType != null && userRole != null) {
        AppLogger.info('🔄 从 AppSecurityService 迁移数据', tag: _tag);
        return {
          'licenseType': licenseType,
          'userRole': userRole,
          'activatedAt': activationTime ?? DateTime.now().millisecondsSinceEpoch,
          'expiryTime': licenseExpiry,
          'deviceId': await AppSecurityService.getDeviceId(),
        };
      }
      
      AppLogger.info('ℹ️ 未找到可迁移的旧数据', tag: _tag);
      return null;
    } catch (e) {
      AppLogger.error('❌ 从旧系统迁移数据失败', error: e, tag: _tag);
      return null;
    }
  }
  
  /// 清除旧系统数据
  static Future<void> _clearLegacySystems() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 清除 AppSecurityService 数据
      await prefs.remove('license_type');
      await prefs.remove('user_role');
      await prefs.remove('activation_time');
      await prefs.remove('license_expiry');
      await prefs.remove('activation_code');
      
      // 清除 StrictSecurityService 数据
      await prefs.remove('strict_security_license_info');
      
      // 清除其他存储系统
      await prefs.remove('encrypted_license_data_v2');
      await prefs.remove('license_data_v3');
      
      AppLogger.info('✅ 旧系统数据清除完成', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 清除旧系统数据失败', error: e, tag: _tag);
    }
  }
  
  /// 获取存储状态信息
  static Future<Map<String, dynamic>> getStorageStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final hasUnified = prefs.containsKey(_unifiedLicenseKey);
      final hasBackup = prefs.containsKey(_unifiedBackupKey);
      final version = prefs.getInt(_unifiedVersionKey);
      
      // 检查旧系统数据
      final hasAppSecurity = prefs.containsKey('license_type');
      final hasStrictSecurity = prefs.containsKey('strict_security_license_info');
      final hasSecureStorage = prefs.containsKey('encrypted_license_data_v2');
      
      return {
        'hasUnifiedData': hasUnified,
        'hasBackup': hasBackup,
        'version': version,
        'isComplete': hasUnified && hasBackup && version != null,
        'legacySystems': {
          'appSecurity': hasAppSecurity,
          'strictSecurity': hasStrictSecurity,
          'secureStorage': hasSecureStorage,
        },
        'needsMigration': !hasUnified && (hasAppSecurity || hasStrictSecurity || hasSecureStorage),
      };
    } catch (e) {
      AppLogger.error('❌ 获取存储状态失败', error: e, tag: _tag);
      return {'error': e.toString()};
    }
  }
}
