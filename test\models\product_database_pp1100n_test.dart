import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/product_database.dart';

void main() {
  group('PP-1100N系列牌号测试', () {
    test('应该包含PP-1100N牌号', () {
      // 验证PP-1100N存在
      expect(ProductDatabase.presetProducts.containsKey('PP-1100N'), true);
      
      final product = ProductDatabase.presetProducts['PP-1100N']!;
      expect(product.code, 'PP-1100N');
      expect(product.name, '聚丙烯注塑料');
      expect(product.category, 'PP');
      expect(product.priority, 7);
      expect(product.commonBatchPrefixes, ['250615', '250618', '250620']);
    });
    
    test('应该包含PP-1100N-GD牌号', () {
      // 验证PP-1100N-GD存在
      expect(ProductDatabase.presetProducts.containsKey('PP-1100N-GD'), true);
      
      final product = ProductDatabase.presetProducts['PP-1100N-GD']!;
      expect(product.code, 'PP-1100N-GD');
      expect(product.name, '聚丙烯注塑料(过渡料)');
      expect(product.category, 'PP');
      expect(product.priority, 7);
      expect(product.commonBatchPrefixes, ['250615', '250618', '250620']);
    });
    
    test('PP-1100N系列应该在热门产品列表中', () {
      final popularProducts = ProductDatabase.getPopularProducts();
      
      // 检查PP-1100N是否在热门产品中（优先级7及以上）
      final pp1100n = popularProducts.where((p) => p.code == 'PP-1100N').toList();
      expect(pp1100n.length, 1);
      expect(pp1100n.first.priority, 7);
      
      // 检查PP-1100N-GD是否在热门产品中
      final pp1100nGd = popularProducts.where((p) => p.code == 'PP-1100N-GD').toList();
      expect(pp1100nGd.length, 1);
      expect(pp1100nGd.first.priority, 7);
    });
    
    test('应该能够通过类别查找PP-1100N系列', () {
      final ppProducts = ProductDatabase.getPopularProductsByCategory('PP');
      
      final pp1100nProducts = ppProducts.where((p) => 
          p.code == 'PP-1100N' || p.code == 'PP-1100N-GD').toList();
      
      expect(pp1100nProducts.length, 2);
      
      // 验证都是PP类别
      for (final product in pp1100nProducts) {
        expect(product.category, 'PP');
      }
    });
    
    test('智能匹配应该能识别PP-1100N', () {
      // 测试完全匹配
      final exactMatches = ProductDatabase.intelligentMatch('PP-1100N');
      expect(exactMatches.isNotEmpty, true);
      
      final bestMatch = exactMatches.first;
      expect(bestMatch.product?.code, 'PP-1100N');
      expect(bestMatch.confidence, greaterThan(95.0));
      expect(bestMatch.matchType, MatchType.exact);
    });
    
    test('智能匹配应该能识别PP-1100N-GD', () {
      // 测试完全匹配
      final exactMatches = ProductDatabase.intelligentMatch('PP-1100N-GD');
      expect(exactMatches.isNotEmpty, true);
      
      final bestMatch = exactMatches.first;
      expect(bestMatch.product?.code, 'PP-1100N-GD');
      expect(bestMatch.confidence, greaterThan(95.0));
      expect(bestMatch.matchType, MatchType.exact);
    });
    
    test('模糊匹配应该能识别变形的PP-1100N', () {
      // 测试模糊匹配
      final fuzzyTexts = [
        'PP1100N',      // 缺少连字符
        'PP-11OON',     // O替代0
        'PP-1I00N',     // I替代1
        'PP-1100n',     // 小写
        'PP 1100N',     // 空格替代连字符
      ];
      
      for (final text in fuzzyTexts) {
        final matches = ProductDatabase.intelligentMatch(text);
        expect(matches.isNotEmpty, true, reason: '应该能匹配: $text');
        
        final bestMatch = matches.first;
        expect(bestMatch.product?.code, 'PP-1100N');
        expect(bestMatch.confidence, greaterThan(80.0), reason: '置信度应该足够高: $text');
      }
    });
    
    test('hasProduct方法应该能找到PP-1100N系列', () {
      expect(ProductDatabase.hasProduct('PP-1100N'), true);
      expect(ProductDatabase.hasProduct('PP-1100N-GD'), true);
      expect(ProductDatabase.hasProduct('pp-1100n'), true); // 大小写不敏感
      expect(ProductDatabase.hasProduct('PP-1100N-INVALID'), false);
    });
    
    test('findBestMatch应该能找到PP-1100N', () {
      final match = ProductDatabase.findBestMatch('包装上写着PP-1100N的产品');
      expect(match, isNotNull);
      expect(match!.product?.code, 'PP-1100N');
      expect(match.confidence, greaterThan(90.0));
    });
    
    test('验证PP系列总数增加', () {
      final allProducts = ProductDatabase.presetProducts;
      final ppProducts = allProducts.values.where((p) => p.category == 'PP').toList();
      
      // 验证PP系列产品数量（原来13个 + 新增2个 = 15个）
      expect(ppProducts.length, 15);
      
      // 验证包含新增的产品
      final ppCodes = ppProducts.map((p) => p.code).toList();
      expect(ppCodes, contains('PP-1100N'));
      expect(ppCodes, contains('PP-1100N-GD'));
    });
    
    test('验证批次前缀匹配', () {
      final pp1100n = ProductDatabase.presetProducts['PP-1100N']!;
      final pp1100nGd = ProductDatabase.presetProducts['PP-1100N-GD']!;
      
      // 验证批次前缀
      expect(pp1100n.commonBatchPrefixes, contains('250615'));
      expect(pp1100n.commonBatchPrefixes, contains('250618'));
      expect(pp1100n.commonBatchPrefixes, contains('250620'));
      
      expect(pp1100nGd.commonBatchPrefixes, contains('250615'));
      expect(pp1100nGd.commonBatchPrefixes, contains('250618'));
      expect(pp1100nGd.commonBatchPrefixes, contains('250620'));
    });
  });
}
