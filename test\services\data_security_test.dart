import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/encryption_service.dart';
import 'package:loadguard/services/secure_storage_service.dart';
import 'package:loadguard/services/data_security_manager.dart';

void main() {
  group('Data Security Tests', () {
    group('EncryptionService Tests', () {
      late EncryptionService encryptionService;
      
      setUp(() async {
        encryptionService = EncryptionService();
        await encryptionService.initialize(customKey: 'test_key_for_testing_purposes_only');
      });
      
      tearDown(() {
        encryptionService.dispose();
      });
      
      test('should encrypt and decrypt data correctly', () async {
        const plaintext = 'Hello, World! This is a test message.';
        
        final encryptedData = await encryptionService.encrypt(plaintext);
        expect(encryptedData.ciphertext, isNotEmpty);
        expect(encryptedData.iv, isNotEmpty);
        expect(encryptedData.algorithm, 'AES-256-GCM');
        
        final decryptedText = await encryptionService.decrypt(encryptedData);
        expect(decryptedText, plaintext);
      });
      
      test('should encrypt and decrypt JSON data correctly', () async {
        final jsonData = {
          'name': 'Test User',
          'email': '<EMAIL>',
          'age': 30,
          'active': true,
        };
        
        final encryptedData = await encryptionService.encryptJson(jsonData);
        expect(encryptedData.ciphertext, isNotEmpty);
        
        final decryptedJson = await encryptionService.decryptJson(encryptedData);
        expect(decryptedJson, jsonData);
      });
      
      test('should calculate hash correctly', () {
        const input = 'test input';
        
        final md5Hash = encryptionService.calculateHash(input, algorithm: HashAlgorithm.md5);
        expect(md5Hash, isNotEmpty);
        expect(md5Hash.length, 32); // MD5 produces 32 character hex string
        
        final sha256Hash = encryptionService.calculateHash(input, algorithm: HashAlgorithm.sha256);
        expect(sha256Hash, isNotEmpty);
        expect(sha256Hash.length, 64); // SHA256 produces 64 character hex string
        
        // Same input should produce same hash
        final sha256Hash2 = encryptionService.calculateHash(input, algorithm: HashAlgorithm.sha256);
        expect(sha256Hash, sha256Hash2);
      });
      
      test('should calculate HMAC correctly', () {
        const message = 'test message';
        const key = 'test key';
        
        final hmac = encryptionService.calculateHmac(message, key);
        expect(hmac, isNotEmpty);
        
        // Same message and key should produce same HMAC
        final hmac2 = encryptionService.calculateHmac(message, key);
        expect(hmac, hmac2);
        
        // Different key should produce different HMAC
        final hmac3 = encryptionService.calculateHmac(message, 'different key');
        expect(hmac, isNot(hmac3));
      });
      
      test('should generate random keys and salts', () {
        final key1 = encryptionService.generateRandomKey();
        final key2 = encryptionService.generateRandomKey();
        expect(key1, isNot(key2)); // Should be different
        expect(key1.length, greaterThan(0));
        
        final salt1 = encryptionService.generateSalt();
        final salt2 = encryptionService.generateSalt();
        expect(salt1, isNot(salt2)); // Should be different
        expect(salt1.length, greaterThan(0));
      });
      
      test('should hash and verify passwords correctly', () {
        const password = 'mySecurePassword123';
        final salt = encryptionService.generateSalt();
        
        final hashedPassword = encryptionService.hashPassword(password, salt);
        expect(hashedPassword, isNotEmpty);
        
        // Correct password should verify
        final isValid = encryptionService.verifyPassword(password, salt, hashedPassword);
        expect(isValid, true);
        
        // Wrong password should not verify
        final isInvalid = encryptionService.verifyPassword('wrongPassword', salt, hashedPassword);
        expect(isInvalid, false);
      });
      
      test('should provide encryption statistics', () {
        final stats = encryptionService.getStatistics();
        
        expect(stats.algorithm, 'AES-256-GCM');
        expect(stats.keyLength, 32);
        expect(stats.ivLength, 16);
        expect(stats.initialized, true);
        expect(stats.supportedAlgorithms, contains('AES-256-GCM'));
        expect(stats.supportedHashAlgorithms, contains('sha256'));
      });
    });
    
    group('EncryptedData Tests', () {
      test('should serialize and deserialize correctly', () {
        final originalData = EncryptedData(
          ciphertext: 'test_ciphertext',
          iv: 'test_iv',
          algorithm: 'AES-256-GCM',
          timestamp: DateTime.now(),
        );
        
        // Test JSON serialization
        final json = originalData.toJson();
        final fromJson = EncryptedData.fromJson(json);
        
        expect(fromJson.ciphertext, originalData.ciphertext);
        expect(fromJson.iv, originalData.iv);
        expect(fromJson.algorithm, originalData.algorithm);
        expect(fromJson.timestamp, originalData.timestamp);
        
        // Test Base64 serialization
        final base64String = originalData.toBase64();
        final fromBase64 = EncryptedData.fromBase64(base64String);
        
        expect(fromBase64.ciphertext, originalData.ciphertext);
        expect(fromBase64.iv, originalData.iv);
        expect(fromBase64.algorithm, originalData.algorithm);
        expect(fromBase64.timestamp, originalData.timestamp);
      });
      
      test('should have meaningful toString', () {
        final encryptedData = EncryptedData(
          ciphertext: 'test_ciphertext',
          iv: 'test_iv',
          algorithm: 'AES-256-GCM',
          timestamp: DateTime.now(),
        );
        
        final string = encryptedData.toString();
        expect(string, contains('AES-256-GCM'));
        expect(string, contains('15')); // length of 'test_ciphertext'
      });
    });
    
    group('SecurityPolicy Tests', () {
      test('should create default policy correctly', () {
        final policy = SecurityPolicy.defaultPolicy();
        
        expect(policy.defaultSecurityLevel, SecurityLevel.medium);
        expect(policy.enableAccessControl, true);
        expect(policy.enableAuditLogging, true);
        expect(policy.allowedDataTypes, contains('activation_code'));
        expect(policy.dataTypeSecurityLevels['activation_code'], SecurityLevel.critical);
        expect(policy.dataTypeSecurityLevels['device_info'], SecurityLevel.high);
      });
      
      test('should serialize and deserialize correctly', () {
        final originalPolicy = SecurityPolicy.defaultPolicy();
        
        final map = originalPolicy.toMap();
        final fromMap = SecurityPolicy.fromMap(map);
        
        expect(fromMap.defaultSecurityLevel, originalPolicy.defaultSecurityLevel);
        expect(fromMap.enableAccessControl, originalPolicy.enableAccessControl);
        expect(fromMap.enableAuditLogging, originalPolicy.enableAuditLogging);
        expect(fromMap.allowedDataTypes, originalPolicy.allowedDataTypes);
        expect(fromMap.dataTypeSecurityLevels, originalPolicy.dataTypeSecurityLevels);
        expect(fromMap.auditRetentionPeriod, originalPolicy.auditRetentionPeriod);
      });
    });
    
    group('SecurityEvent Tests', () {
      test('should create and serialize security events correctly', () {
        final event = SecurityEvent(
          type: SecurityEventType.dataAccess,
          message: 'Test security event',
          timestamp: DateTime.now(),
          details: {'key': 'value', 'count': 42},
        );
        
        expect(event.type, SecurityEventType.dataAccess);
        expect(event.message, 'Test security event');
        expect(event.details['key'], 'value');
        expect(event.details['count'], 42);
        
        // Test serialization
        final map = event.toMap();
        final fromMap = SecurityEvent.fromMap(map);
        
        expect(fromMap.type, event.type);
        expect(fromMap.message, event.message);
        expect(fromMap.timestamp, event.timestamp);
        expect(fromMap.details, event.details);
      });
    });
    
    group('SecurityLevel and SecurityEventType Enums', () {
      test('should have all expected security levels', () {
        final levels = SecurityLevel.values;
        expect(levels, contains(SecurityLevel.low));
        expect(levels, contains(SecurityLevel.medium));
        expect(levels, contains(SecurityLevel.high));
        expect(levels, contains(SecurityLevel.critical));
        expect(levels.length, 4);
      });
      
      test('should have all expected security event types', () {
        final types = SecurityEventType.values;
        expect(types, contains(SecurityEventType.initialization));
        expect(types, contains(SecurityEventType.dataProtection));
        expect(types, contains(SecurityEventType.dataAccess));
        expect(types, contains(SecurityEventType.dataDeletion));
        expect(types, contains(SecurityEventType.securityAudit));
        expect(types, contains(SecurityEventType.securityViolation));
        expect(types, contains(SecurityEventType.securityWarning));
        expect(types, contains(SecurityEventType.accessDenied));
        expect(types, contains(SecurityEventType.encryptionEvent));
        expect(types, contains(SecurityEventType.integrityCheck));
        expect(types.length, 10);
      });
    });
    
    group('HashAlgorithm Enum', () {
      test('should have all expected hash algorithms', () {
        final algorithms = HashAlgorithm.values;
        expect(algorithms, contains(HashAlgorithm.md5));
        expect(algorithms, contains(HashAlgorithm.sha1));
        expect(algorithms, contains(HashAlgorithm.sha256));
        expect(algorithms, contains(HashAlgorithm.sha512));
        expect(algorithms.length, 4);
      });
    });
    
    group('SecureDataKeys Constants', () {
      test('should have all expected secure data keys', () {
        expect(SecureDataKeys.activationCode, 'activation_code');
        expect(SecureDataKeys.deviceInfo, 'device_info');
        expect(SecureDataKeys.userCredentials, 'user_credentials');
        expect(SecureDataKeys.appConfig, 'app_config');
        expect(SecureDataKeys.securitySettings, 'security_settings');
        expect(SecureDataKeys.encryptionKeys, 'encryption_keys');
        expect(SecureDataKeys.sessionTokens, 'session_tokens');
        expect(SecureDataKeys.biometricData, 'biometric_data');
        expect(SecureDataKeys.backupData, 'backup_data');
        expect(SecureDataKeys.auditLog, 'audit_log');
      });
    });
    
    group('Integration Tests', () {
      test('should handle encryption service lifecycle correctly', () async {
        final service = EncryptionService();

        // Should not be initialized before initialization
        expect(service.isInitialized, false);

        // Initialize
        await service.initialize(customKey: 'test_key');
        expect(service.isInitialized, true);

        // Should work after initialization
        const testData = 'test data';
        final encrypted = await service.encrypt(testData);
        final decrypted = await service.decrypt(encrypted);
        expect(decrypted, testData);

        // Should provide statistics
        final stats = service.getStatistics();
        expect(stats.initialized, true);

        // Dispose
        service.dispose();
        // Note: We can't easily test that operations fail after dispose
        // without exposing internal state
      });
    });
  });
}
