/// 🔧 测试修复的代码逻辑
import 'dart:io';

void main() {
  print('🔧 测试修复代码...');
  
  // 1. 测试null安全检查
  testNullSafety();
  
  // 2. 测试匹配逻辑
  testMatchingLogic();
  
  print('✅ 所有修复测试通过');
}

/// 测试null安全检查
void testNullSafety() {
  print('\n🔍 测试null安全检查...');
  
  // 模拟currentTask为null的情况
  dynamic currentTask;
  
  // 修复前会报错的代码：currentTask!.photos.length
  // 修复后的安全检查：
  if (currentTask != null && currentTask.photos != null && currentTask.photos.isNotEmpty) {
    print('照片数量: ${currentTask.photos.length}');
  } else {
    print('✅ 安全处理了null情况');
  }
}

/// 测试匹配逻辑
void testMatchingLogic() {
  print('\n🔍 测试识别匹配逻辑...');
  
  // 测试用例1：正常匹配
  final result1 = checkPresetMatch(
    'LLD-7042 250712F20440 产品标签', 
    'LLD-7042', 
    '250712F20440'
  );
  print('测试1 - 正常匹配: $result1 (期望: true)');
  
  // 测试用例2：产品匹配但批号不匹配
  final result2 = checkPresetMatch(
    'LLD-7042 999999F20440 产品标签', 
    'LLD-7042', 
    '250712F20440'
  );
  print('测试2 - 批号不匹配: $result2 (期望: false)');
  
  // 测试用例3：大小写和空格处理
  final result3 = checkPresetMatch(
    'lld-7042 250 712 f20 440', 
    'LLD-7042', 
    '250712F20440'
  );
  print('测试3 - 大小写空格: $result3 (期望: true)');
}

/// 模拟匹配检查逻辑
bool checkPresetMatch(String? ocrText, String? presetProductCode, String? presetBatchNumber) {
  print('🔍 开始匹配检查:');
  print('   识别文本: $ocrText');
  print('   预设产品: $presetProductCode');
  print('   预设批号: $presetBatchNumber');
  
  if (ocrText == null || ocrText.isEmpty) {
    print('❌ 识别文本为空，匹配失败');
    return false;
  }

  if (presetProductCode == null && presetBatchNumber == null) {
    print('✅ 无预设信息，默认匹配');
    return true;
  }

  final cleanText = ocrText.replaceAll(RegExp(r'\s+'), '').toUpperCase();
  print('   清理后文本: $cleanText');

  bool productMatch = true;
  bool batchMatch = true;

  if (presetProductCode != null) {
    final cleanProductCode = presetProductCode.replaceAll(RegExp(r'\s+'), '').toUpperCase();
    productMatch = cleanText.contains(cleanProductCode);
    print('   产品匹配: $cleanProductCode -> $productMatch');
  }

  if (presetBatchNumber != null) {
    final cleanBatchNumber = presetBatchNumber.replaceAll(RegExp(r'\s+'), '').toUpperCase();
    batchMatch = cleanText.contains(cleanBatchNumber);
    print('   批号匹配: $cleanBatchNumber -> $batchMatch');
  }

  final result = productMatch && batchMatch;
  print('🎯 最终匹配结果: $result (产品:$productMatch, 批号:$batchMatch)');
  return result;
}