/// ✅ 统一的识别算法枚举定义
/// 解决多个文件中算法枚举不一致的问题
/// 
/// 支持12种专业识别算法，每种算法都有特定的应用场景和优化策略

/// 识别算法枚举 - 统一定义
enum RecognitionAlgorithm {
  // 基础算法
  standardText('标准文本识别', 'Standard Text Recognition'),
  enhancedAccuracy('增强精度识别', 'Enhanced Accuracy Recognition'),
  fastProcessing('快速处理识别', 'Fast Processing Recognition'),
  
  // 语言优化算法
  chineseOptimized('中文优化识别', 'Chinese Optimized Recognition'),
  numberFocused('数字专注识别', 'Number Focused Recognition'),
  mixedContent('混合内容识别', 'Mixed Content Recognition'),
  
  // 环境适应算法
  lowLight('低光照识别', 'Low Light Recognition'),
  highContrast('高对比度识别', 'High Contrast Recognition'),
  
  // 专业算法
  handwrittenText('手写文本识别', 'Handwritten Text Recognition'),
  documentScan('文档扫描识别', 'Document Scan Recognition'),
  barcodeIntegrated('条码集成识别', 'Barcode Integrated Recognition'),
  multiLanguage('多语言识别', 'Multi-Language Recognition');

  const RecognitionAlgorithm(this.chineseName, this.englishName);

  /// 中文名称
  final String chineseName;
  
  /// 英文名称
  final String englishName;

  /// 获取算法描述
  String get description {
    switch (this) {
      case RecognitionAlgorithm.standardText:
        return '适用于标准印刷文本，平衡速度和准确性';
      case RecognitionAlgorithm.enhancedAccuracy:
        return '提供最高识别精度，适用于关键业务场景';
      case RecognitionAlgorithm.fastProcessing:
        return '优化处理速度，适用于大批量识别任务';
      case RecognitionAlgorithm.chineseOptimized:
        return '专门优化中文识别，提升中文字符准确率';
      case RecognitionAlgorithm.numberFocused:
        return '专注数字识别，适用于批号、数量等数字内容';
      case RecognitionAlgorithm.mixedContent:
        return '处理中英文混合内容，适用于复杂标签';
      case RecognitionAlgorithm.lowLight:
        return '优化低光照环境识别，提升暗光条件下的准确性';
      case RecognitionAlgorithm.highContrast:
        return '适用于高对比度图像，优化黑白文本识别';
      case RecognitionAlgorithm.handwrittenText:
        return '识别手写文本，适用于手工填写的标签';
      case RecognitionAlgorithm.documentScan:
        return '文档扫描模式，适用于整页文档识别';
      case RecognitionAlgorithm.barcodeIntegrated:
        return '集成条码识别，同时处理文本和条码';
      case RecognitionAlgorithm.multiLanguage:
        return '多语言支持，自动检测和识别多种语言';
    }
  }

  /// 获取算法优先级（数值越小优先级越高）
  int get priority {
    switch (this) {
      case RecognitionAlgorithm.enhancedAccuracy:
        return 1;
      case RecognitionAlgorithm.chineseOptimized:
        return 2;
      case RecognitionAlgorithm.standardText:
        return 3;
      case RecognitionAlgorithm.numberFocused:
        return 4;
      case RecognitionAlgorithm.mixedContent:
        return 5;
      case RecognitionAlgorithm.highContrast:
        return 6;
      case RecognitionAlgorithm.lowLight:
        return 7;
      case RecognitionAlgorithm.fastProcessing:
        return 8;
      case RecognitionAlgorithm.documentScan:
        return 9;
      case RecognitionAlgorithm.handwrittenText:
        return 10;
      case RecognitionAlgorithm.barcodeIntegrated:
        return 11;
      case RecognitionAlgorithm.multiLanguage:
        return 12;
    }
  }

  /// 获取算法适用场景
  List<String> get applicableScenarios {
    switch (this) {
      case RecognitionAlgorithm.standardText:
        return ['标准标签', '印刷文本', '清晰图像'];
      case RecognitionAlgorithm.enhancedAccuracy:
        return ['关键业务', '高精度要求', '质量控制'];
      case RecognitionAlgorithm.fastProcessing:
        return ['批量处理', '实时识别', '性能优先'];
      case RecognitionAlgorithm.chineseOptimized:
        return ['中文标签', '产品名称', '中文描述'];
      case RecognitionAlgorithm.numberFocused:
        return ['批号识别', '数量统计', '编号处理'];
      case RecognitionAlgorithm.mixedContent:
        return ['混合标签', '多语言内容', '复杂格式'];
      case RecognitionAlgorithm.lowLight:
        return ['暗光环境', '室内拍摄', '光线不足'];
      case RecognitionAlgorithm.highContrast:
        return ['黑白文本', '高对比度', '清晰边界'];
      case RecognitionAlgorithm.handwrittenText:
        return ['手写标签', '人工填写', '手工记录'];
      case RecognitionAlgorithm.documentScan:
        return ['文档扫描', '整页识别', '报告处理'];
      case RecognitionAlgorithm.barcodeIntegrated:
        return ['条码标签', '混合识别', '自动化处理'];
      case RecognitionAlgorithm.multiLanguage:
        return ['国际标签', '多语言环境', '全球化应用'];
    }
  }

  /// 获取算法性能特征
  AlgorithmPerformance get performance {
    switch (this) {
      case RecognitionAlgorithm.standardText:
        return AlgorithmPerformance(speed: 0.8, accuracy: 0.8, resourceUsage: 0.6);
      case RecognitionAlgorithm.enhancedAccuracy:
        return AlgorithmPerformance(speed: 0.6, accuracy: 0.95, resourceUsage: 0.8);
      case RecognitionAlgorithm.fastProcessing:
        return AlgorithmPerformance(speed: 0.95, accuracy: 0.7, resourceUsage: 0.5);
      case RecognitionAlgorithm.chineseOptimized:
        return AlgorithmPerformance(speed: 0.7, accuracy: 0.9, resourceUsage: 0.7);
      case RecognitionAlgorithm.numberFocused:
        return AlgorithmPerformance(speed: 0.85, accuracy: 0.9, resourceUsage: 0.6);
      case RecognitionAlgorithm.mixedContent:
        return AlgorithmPerformance(speed: 0.65, accuracy: 0.85, resourceUsage: 0.75);
      case RecognitionAlgorithm.lowLight:
        return AlgorithmPerformance(speed: 0.6, accuracy: 0.8, resourceUsage: 0.8);
      case RecognitionAlgorithm.highContrast:
        return AlgorithmPerformance(speed: 0.85, accuracy: 0.85, resourceUsage: 0.6);
      case RecognitionAlgorithm.handwrittenText:
        return AlgorithmPerformance(speed: 0.5, accuracy: 0.75, resourceUsage: 0.9);
      case RecognitionAlgorithm.documentScan:
        return AlgorithmPerformance(speed: 0.4, accuracy: 0.85, resourceUsage: 0.9);
      case RecognitionAlgorithm.barcodeIntegrated:
        return AlgorithmPerformance(speed: 0.75, accuracy: 0.9, resourceUsage: 0.7);
      case RecognitionAlgorithm.multiLanguage:
        return AlgorithmPerformance(speed: 0.55, accuracy: 0.8, resourceUsage: 0.85);
    }
  }

  /// 从字符串获取算法（兼容旧版本）
  static RecognitionAlgorithm? fromString(String value) {
    switch (value.toLowerCase()) {
      case 'standardtext':
      case 'standard_text':
      case 'standardlatin':
        return RecognitionAlgorithm.standardText;
      case 'enhancedaccuracy':
      case 'enhanced_accuracy':
      case 'enhancedlatin':
        return RecognitionAlgorithm.enhancedAccuracy;
      case 'fastprocessing':
      case 'fast_processing':
        return RecognitionAlgorithm.fastProcessing;
      case 'chineseoptimized':
      case 'chinese_optimized':
        return RecognitionAlgorithm.chineseOptimized;
      case 'numberfocused':
      case 'number_focused':
        return RecognitionAlgorithm.numberFocused;
      case 'mixedcontent':
      case 'mixed_content':
      case 'mixedlanguage':
        return RecognitionAlgorithm.mixedContent;
      case 'lowlight':
      case 'low_light':
        return RecognitionAlgorithm.lowLight;
      case 'highcontrast':
      case 'high_contrast':
        return RecognitionAlgorithm.highContrast;
      case 'handwrittentext':
      case 'handwritten_text':
        return RecognitionAlgorithm.handwrittenText;
      case 'documentscan':
      case 'document_scan':
        return RecognitionAlgorithm.documentScan;
      case 'barcodeintegrated':
      case 'barcode_integrated':
        return RecognitionAlgorithm.barcodeIntegrated;
      case 'multilanguage':
      case 'multi_language':
        return RecognitionAlgorithm.multiLanguage;
      default:
        return null;
    }
  }
}

/// 算法性能特征
class AlgorithmPerformance {
  final double speed;        // 处理速度 (0.0-1.0)
  final double accuracy;     // 识别精度 (0.0-1.0)
  final double resourceUsage; // 资源占用 (0.0-1.0)

  const AlgorithmPerformance({
    required this.speed,
    required this.accuracy,
    required this.resourceUsage,
  });

  /// 综合评分
  double get overallScore => (speed + accuracy + (1 - resourceUsage)) / 3;
}
