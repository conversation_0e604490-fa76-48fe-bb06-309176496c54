import 'dart:io';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🔍 任务验证服务
/// 
/// 专注于业务规则验证和数据完整性检查
/// 提供统一的验证接口，确保数据质量
class TaskValidationService {
  
  /// 验证任务创建参数
  Future<void> validateTaskCreation({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
  }) async {
    try {
      AppLogger.debug('🔍 验证任务创建参数...', tag: 'TaskValidation');

      // 1. 基础参数验证
      _validateBasicParameters(template, productCode, batchNumber, quantity);

      // 2. 模板验证
      await _validateTemplate(template);

      // 3. 产品代码验证
      await _validateProductCode(productCode);

      // 4. 批次号验证
      await _validateBatchNumber(batchNumber);

      // 5. 数量验证
      _validateQuantity(quantity);

      // 6. 业务规则验证
      await _validateBusinessRules(template, productCode, quantity);

      AppLogger.debug('✅ 任务创建参数验证通过', tag: 'TaskValidation');
    } catch (e) {
      AppLogger.error('❌ 任务创建参数验证失败: $e', tag: 'TaskValidation');
      rethrow;
    }
  }

  /// 验证混装任务创建
  Future<void> validateMixedLoadTask({
    required String template,
    required List<BatchInfo> batches,
  }) async {
    try {
      AppLogger.debug('🔍 验证混装任务创建...', tag: 'TaskValidation');

      // 1. 基础验证
      if (batches.isEmpty) {
        throw TaskValidationException('混装任务至少需要一个批次');
      }

      if (batches.length > 10) {
        throw TaskValidationException('混装任务最多支持10个批次');
      }

      // 2. 模板验证
      await _validateTemplate(template);

      // 3. 批次验证
      for (int i = 0; i < batches.length; i++) {
        final batch = batches[i];
        try {
          await validateTaskCreation(
            template: template,
            productCode: batch.productCode,
            batchNumber: batch.batchNumber,
            quantity: batch.quantity.toInt(),
          );
        } catch (e) {
          throw TaskValidationException('第${i + 1}个批次验证失败: $e');
        }
      }

      // 4. 混装兼容性验证
      await _validateMixedLoadCompatibility(batches);

      // 5. 总量验证
      final totalQuantity = batches.fold<int>(0, (sum, batch) => sum + batch.quantity.toInt());
      _validateTotalQuantity(totalQuantity);

      AppLogger.debug('✅ 混装任务创建验证通过', tag: 'TaskValidation');
    } catch (e) {
      AppLogger.error('❌ 混装任务创建验证失败: $e', tag: 'TaskValidation');
      rethrow;
    }
  }

  /// 验证任务完成
  Future<void> validateTaskCompletion(TaskModel task) async {
    try {
      AppLogger.debug('🔍 验证任务完成: ${task.id}', tag: 'TaskValidation');

      // 1. 任务状态验证
      if (task.status == TaskStatus.completed) {
        throw TaskValidationException('任务已经完成');
      }

      if (task.status == TaskStatus.cancelled) {
        throw TaskValidationException('已取消的任务不能完成');
      }

      // 2. 必要照片验证
      await _validateRequiredPhotos(task);

      // 3. 照片质量验证
      await _validatePhotoQuality(task);

      // 4. 业务完成条件验证
      await _validateCompletionConditions(task);

      AppLogger.debug('✅ 任务完成验证通过: ${task.id}', tag: 'TaskValidation');
    } catch (e) {
      AppLogger.error('❌ 任务完成验证失败: ${task.id}, 错误: $e', tag: 'TaskValidation');
      rethrow;
    }
  }

  /// 验证照片更新
  Future<void> validatePhotoUpdate(TaskModel task, String photoId, String imagePath) async {
    try {
      AppLogger.debug('🔍 验证照片更新: ${task.id}, $photoId', tag: 'TaskValidation');

      // 1. 任务状态验证
      if (task.status == TaskStatus.completed) {
        throw TaskValidationException('已完成的任务不能更新照片');
      }

      if (task.status == TaskStatus.cancelled) {
        throw TaskValidationException('已取消的任务不能更新照片');
      }

      // 2. 照片ID验证
      final photoExists = task.photos.any((photo) => photo.id == photoId);
      if (!photoExists) {
        throw TaskValidationException('照片ID不存在: $photoId');
      }

      // 3. 图片文件验证
      await _validateImageFile(imagePath);

      // 4. 照片业务规则验证
      await _validatePhotoBusinessRules(task, photoId, imagePath);

      AppLogger.debug('✅ 照片更新验证通过: ${task.id}, $photoId', tag: 'TaskValidation');
    } catch (e) {
      AppLogger.error('❌ 照片更新验证失败: ${task.id}, $photoId, 错误: $e', tag: 'TaskValidation');
      rethrow;
    }
  }

  /// 验证批量操作
  Future<void> validateBatchOperation({
    required List<String> taskIds,
    required String operation,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      AppLogger.debug('🔍 验证批量操作: ${taskIds.length}个任务, 操作: $operation', tag: 'TaskValidation');

      // 1. 基础验证
      if (taskIds.isEmpty) {
        throw TaskValidationException('批量操作至少需要一个任务');
      }

      if (taskIds.length > 100) {
        throw TaskValidationException('批量操作最多支持100个任务');
      }

      // 2. 操作类型验证
      _validateOperationType(operation);

      // 3. 参数验证
      _validateOperationParameters(operation, parameters);

      // 4. 任务ID唯一性验证
      final uniqueIds = taskIds.toSet();
      if (uniqueIds.length != taskIds.length) {
        throw TaskValidationException('任务ID列表包含重复项');
      }

      AppLogger.debug('✅ 批量操作验证通过', tag: 'TaskValidation');
    } catch (e) {
      AppLogger.error('❌ 批量操作验证失败: $e', tag: 'TaskValidation');
      rethrow;
    }
  }

  // 私有验证方法

  /// 验证基础参数
  void _validateBasicParameters(String template, String productCode, String batchNumber, int quantity) {
    if (template.trim().isEmpty) {
      throw TaskValidationException('模板不能为空');
    }

    if (productCode.trim().isEmpty) {
      throw TaskValidationException('产品代码不能为空');
    }

    if (batchNumber.trim().isEmpty) {
      throw TaskValidationException('批次号不能为空');
    }

    if (quantity <= 0) {
      throw TaskValidationException('数量必须大于0');
    }
  }

  /// 验证模板
  Future<void> _validateTemplate(String template) async {
    final validTemplates = ['平板车', '集装箱', '货车', '仓库'];
    if (!validTemplates.contains(template)) {
      throw TaskValidationException('不支持的模板类型: $template');
    }
  }

  /// 验证产品代码
  Future<void> _validateProductCode(String productCode) async {
    // 产品代码格式验证
    final regex = RegExp(r'^[A-Z]{2}-\d{4}[A-Z]?$');
    if (!regex.hasMatch(productCode)) {
      throw TaskValidationException('产品代码格式不正确: $productCode');
    }

    // 产品代码存在性验证（这里可以连接产品数据库）
    final validProducts = ['PP-1100N', 'HD-5502S', 'LG-3300A', 'MT-7700B'];
    if (!validProducts.contains(productCode)) {
      throw TaskValidationException('产品代码不存在: $productCode');
    }
  }

  /// 验证批次号
  Future<void> _validateBatchNumber(String batchNumber) async {
    // 批次号格式验证
    if (batchNumber.length < 3 || batchNumber.length > 20) {
      throw TaskValidationException('批次号长度必须在3-20个字符之间');
    }

    // 批次号字符验证
    final regex = RegExp(r'^[A-Z0-9\-_]+$');
    if (!regex.hasMatch(batchNumber)) {
      throw TaskValidationException('批次号只能包含大写字母、数字、连字符和下划线');
    }
  }

  /// 验证数量
  void _validateQuantity(int quantity) {
    if (quantity > 10000) {
      throw TaskValidationException('单次任务数量不能超过10000');
    }
  }

  /// 验证业务规则
  Future<void> _validateBusinessRules(String template, String productCode, int quantity) async {
    // 根据模板和产品类型的特殊规则
    if (template == '集装箱' && quantity > 5000) {
      throw TaskValidationException('集装箱模板单次数量不能超过5000');
    }

    if (template == '平板车' && quantity > 2000) {
      throw TaskValidationException('平板车模板单次数量不能超过2000');
    }

    // 特殊产品的限制
    if (productCode.startsWith('HD-') && quantity > 1000) {
      throw TaskValidationException('HD系列产品单次数量不能超过1000');
    }
  }

  /// 验证混装兼容性
  Future<void> _validateMixedLoadCompatibility(List<BatchInfo> batches) async {
    // 检查产品兼容性
    final productTypes = batches.map((b) => b.productCode.substring(0, 2)).toSet();
    if (productTypes.length > 3) {
      throw TaskValidationException('混装任务最多支持3种不同类型的产品');
    }

    // 检查特殊产品组合
    final hasHD = batches.any((b) => b.productCode.startsWith('HD-'));
    final hasPP = batches.any((b) => b.productCode.startsWith('PP-'));
    if (hasHD && hasPP) {
      throw TaskValidationException('HD系列和PP系列产品不能混装');
    }
  }

  /// 验证总量
  void _validateTotalQuantity(int totalQuantity) {
    if (totalQuantity > 15000) {
      throw TaskValidationException('混装任务总数量不能超过15000');
    }
  }

  /// 验证必要照片
  Future<void> _validateRequiredPhotos(TaskModel task) async {
    final requiredPhotos = task.photos.where((photo) => photo.isRequired).toList();
    final missingPhotos = requiredPhotos.where((photo) => photo.imagePath?.isEmpty ?? true).toList();
    
    if (missingPhotos.isNotEmpty) {
      final missingNames = missingPhotos.map((p) => p.name).join(', ');
      throw TaskValidationException('缺少必要照片: $missingNames');
    }
  }

  /// 验证照片质量
  Future<void> _validatePhotoQuality(TaskModel task) async {
    for (final photo in task.photos) {
      if (photo.imagePath?.isNotEmpty == true) {
        await _validateImageFile(photo.imagePath!);
      }
    }
  }

  /// 验证完成条件
  Future<void> _validateCompletionConditions(TaskModel task) async {
    // 检查是否有识别结果
    final photosWithResults = task.photos.where((photo) =>
      photo.recognitionResult != null && (photo.recognitionResult!.recognizedText?.isNotEmpty ?? false)
    ).toList();

    if (photosWithResults.isEmpty) {
      throw TaskValidationException('至少需要一张照片有识别结果才能完成任务');
    }

    // 检查识别匹配率
    final matchedPhotos = photosWithResults.where((photo) => 
      photo.recognitionResult!.matchesPreset
    ).toList();

    final matchRate = matchedPhotos.length / photosWithResults.length;
    if (matchRate < 0.5) {
      throw TaskValidationException('识别匹配率过低（${(matchRate * 100).toStringAsFixed(1)}%），无法完成任务');
    }
  }

  /// 验证图片文件
  Future<void> _validateImageFile(String imagePath) async {
    final file = File(imagePath);
    
    // 文件存在性验证
    if (!await file.exists()) {
      throw TaskValidationException('图片文件不存在: $imagePath');
    }

    // 文件大小验证
    final fileSize = await file.length();
    if (fileSize > 10 * 1024 * 1024) { // 10MB
      throw TaskValidationException('图片文件过大: ${(fileSize / 1024 / 1024).toStringAsFixed(1)}MB');
    }

    if (fileSize < 1024) { // 1KB
      throw TaskValidationException('图片文件过小: ${fileSize}字节');
    }

    // 文件格式验证
    final extension = imagePath.toLowerCase().split('.').last;
    final validExtensions = ['jpg', 'jpeg', 'png', 'bmp'];
    if (!validExtensions.contains(extension)) {
      throw TaskValidationException('不支持的图片格式: $extension');
    }
  }

  /// 验证照片业务规则
  Future<void> _validatePhotoBusinessRules(TaskModel task, String photoId, String imagePath) async {
    final photo = task.photos.firstWhere((p) => p.id == photoId);
    
    // 检查照片更新频率限制
    if (photo.updateTime != null) {
      final timeSinceLastUpdate = DateTime.now().difference(photo.updateTime!);
      if (timeSinceLastUpdate.inMinutes < 1) {
        throw TaskValidationException('照片更新过于频繁，请稍后再试');
      }
    }

    // 检查照片更新次数限制
    final updateCount = photo.metadata?['updateCount'] as int? ?? 0;
    if (updateCount >= 10) {
      throw TaskValidationException('照片更新次数已达上限');
    }
  }

  /// 验证操作类型
  void _validateOperationType(String operation) {
    final validOperations = ['complete', 'delete', 'archive', 'updateStatus'];
    if (!validOperations.contains(operation)) {
      throw TaskValidationException('不支持的操作类型: $operation');
    }
  }

  /// 验证操作参数
  void _validateOperationParameters(String operation, Map<String, dynamic>? parameters) {
    switch (operation) {
      case 'updateStatus':
        if (parameters == null || !parameters.containsKey('status')) {
          throw TaskValidationException('updateStatus操作需要status参数');
        }
        break;
      // 其他操作的参数验证...
    }
  }
}

/// 任务验证异常
class TaskValidationException implements Exception {
  final String message;
  TaskValidationException(this.message);
  
  @override
  String toString() => 'TaskValidationException: $message';
}
