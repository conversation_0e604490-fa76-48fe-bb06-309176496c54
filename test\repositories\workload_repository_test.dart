import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/workload_models.dart';
import 'package:loadguard/repositories/workload_repository.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/config_models.dart';

/// 模拟的TaskRepository用于测试
class MockTaskRepository implements TaskRepository {
  final List<TaskModel> _tasks = [];

  @override
  Future<void> initialize() async {}

  @override
  Future<List<TaskModel>> getAllTasks() async => List.from(_tasks);

  @override
  Future<TaskModel?> getTaskById(String id) async {
    try {
      return _tasks.firstWhere((t) => t.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> saveTask(TaskModel task) async {
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index >= 0) {
      _tasks[index] = task;
    } else {
      _tasks.add(task);
    }
  }

  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    for (final task in tasks) {
      await saveTask(task);
    }
  }

  @override
  Future<void> deleteTask(String id) async {
    _tasks.removeWhere((t) => t.id == id);
  }

  @override
  Future<void> clearAllTasks() async {
    _tasks.clear();
  }

  @override
  Future<TaskModel?> getCurrentTask() async => null;

  @override
  Future<void> setCurrentTask(TaskModel? task) async {}

  @override
  Future<List<TaskModel>> queryTasks({
    DateTime? startDate,
    DateTime? endDate,
    String? template,
    bool? isCompleted,
  }) async => getAllTasks();

  @override
  Future<Map<String, dynamic>> getTaskStatistics() async => {};

  @override
  Future<void> migrateData() async {}

  @override
  Future<void> backupData() async {}

  @override
  Future<void> restoreData() async {}
}

/// 模拟的ConfigDataSource用于测试
class MockConfigDataSource {
  final Map<String, Map<String, dynamic>> _configs = {};

  Future<void> initialize() async {}

  Future<void> saveConfig(String key, Map<String, dynamic> data) async {
    _configs[key] = Map<String, dynamic>.from(data);
  }

  Future<Map<String, dynamic>?> getConfig(String key) async {
    return _configs[key];
  }

  Future<void> deleteConfig(String key) async {
    _configs.remove(key);
  }

  Future<List<String>> getAllConfigKeys() async {
    return _configs.keys.toList();
  }

  Future<void> clearAllConfigs() async {
    _configs.clear();
  }
}

void main() {
  group('WorkloadRepository Tests', () {
    late WorkloadRepositoryImpl repository;
    late MockTaskRepository mockTaskRepository;
    late MockConfigDataSource mockConfigDataSource;

    setUp(() {
      mockTaskRepository = MockTaskRepository();
      mockConfigDataSource = MockConfigDataSource();
      repository = WorkloadRepositoryImpl(
        taskRepository: mockTaskRepository,
        configDataSource: mockConfigDataSource,
      );
    });

    tearDown(() {
      repository.dispose();
    });

    group('初始化测试', () {
      test('should initialize successfully', () async {
        await repository.initialize();
        // 验证初始化成功
        expect(true, true); // 如果没有异常抛出，说明初始化成功
      });
    });

    group('工作量统计测试', () {
      setUp(() async {
        await repository.initialize();
        
        // 添加测试工作人员配置
        await mockConfigDataSource.saveConfig('worker_001', {
          'id': 'worker001',
          'name': '张三',
          'role': '叉车',
          'warehouse': '1号库',
          'group': '第一组',
          'phone': '13800138001',
          'email': '<EMAIL>',
        });

        await mockConfigDataSource.saveConfig('worker_002', {
          'id': 'worker002',
          'name': '李四',
          'role': '仓管',
          'warehouse': '1号库',
          'group': '第一组',
          'phone': '13800138002',
          'email': '<EMAIL>',
        });
      });

      test('should calculate worker statistics with no tasks', () async {
        final stats = await repository.calculateWorkerStatistics();
        
        expect(stats.length, 2);
        expect(stats['worker001']?.workerName, '张三');
        expect(stats['worker001']?.totalTasks, 0);
        expect(stats['worker001']?.totalTonnage, 0.0);
        expect(stats['worker002']?.workerName, '李四');
        expect(stats['worker002']?.totalTasks, 0);
      });

      test('should calculate worker statistics with tasks', () async {
        // 创建带工作量分配的任务
        final task = TaskModel(
          id: 'task001',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
          recognitionMetadata: {
            'workload': {
              'records': [
                {
                  'workerId': 'worker001',
                  'workerName': '张三',
                  'role': '叉车',
                  'warehouse': '1号库',
                  'group': '第一组',
                  'allocatedTonnage': 75.0,
                  'assignedAt': DateTime.now().toIso8601String(),
                },
                {
                  'workerId': 'worker002',
                  'workerName': '李四',
                  'role': '仓管',
                  'warehouse': '1号库',
                  'group': '第一组',
                  'allocatedTonnage': 75.0,
                  'assignedAt': DateTime.now().toIso8601String(),
                },
              ],
              'totalTonnage': 150.0,
              'palletCount': 100,
              'assignedAt': DateTime.now().toIso8601String(),
              'assignedBy': 'test',
            }
          },
        );

        await mockTaskRepository.saveTask(task);

        final stats = await repository.calculateWorkerStatistics();
        
        expect(stats.length, 2);
        expect(stats['worker001']?.totalTasks, 1);
        expect(stats['worker001']?.totalTonnage, 75.0);
        expect(stats['worker001']?.efficiency, 0.0); // 任务未完成
        expect(stats['worker002']?.totalTasks, 1);
        expect(stats['worker002']?.totalTonnage, 75.0);
      });

      test('should calculate workload overview', () async {
        // 添加一个已完成的任务
        final completedTask = TaskModel(
          id: 'task001',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
          completedAt: DateTime.now(),
          photos: [
            PhotoItem(
              id: 'photo1',
              label: '测试照片',
              isRequired: true,
              needRecognition: false,
              imagePath: '/test/path.jpg', // 设置图片路径表示已完成
            ),
          ],
          recognitionMetadata: {
            'workload': {
              'records': [
                {
                  'workerId': 'worker001',
                  'workerName': '张三',
                  'role': '叉车',
                  'warehouse': '1号库',
                  'group': '第一组',
                  'allocatedTonnage': 150.0,
                  'assignedAt': DateTime.now().toIso8601String(),
                },
              ],
              'totalTonnage': 150.0,
              'palletCount': 100,
              'assignedAt': DateTime.now().toIso8601String(),
              'assignedBy': 'test',
            }
          },
        );

        await mockTaskRepository.saveTask(completedTask);

        final overview = await repository.calculateWorkloadOverview();
        
        expect(overview.totalTasks, 1);
        expect(overview.completedTasks, 1);
        expect(overview.activeWorkers, 1);
        expect(overview.totalWorkers, 2);
        expect(overview.totalTonnage, 150.0);
        expect(overview.completedTonnage, 150.0);
        expect(overview.completionRate, 1.0);
      });

      test('should calculate efficiency ranking', () async {
        // 添加多个任务以测试排名
        final task1 = TaskModel(
          id: 'task001',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
          completedAt: DateTime.now(),
          photos: [
            PhotoItem(
              id: 'photo1',
              label: '测试照片',
              isRequired: true,
              needRecognition: false,
              imagePath: '/test/path.jpg', // 已完成
            ),
          ],
          recognitionMetadata: {
            'workload': {
              'records': [
                {
                  'workerId': 'worker001',
                  'workerName': '张三',
                  'role': '叉车',
                  'warehouse': '1号库',
                  'group': '第一组',
                  'allocatedTonnage': 150.0,
                  'assignedAt': DateTime.now().toIso8601String(),
                },
              ],
              'totalTonnage': 150.0,
              'palletCount': 100,
              'assignedAt': DateTime.now().toIso8601String(),
              'assignedBy': 'test',
            }
          },
        );

        final task2 = TaskModel(
          id: 'task002',
          template: '平板车',
          productCode: 'P002',
          batchNumber: 'B002',
          quantity: 100,
          createTime: DateTime.now(),
          // 未设置completedAt，表示任务未完成
          recognitionMetadata: {
            'workload': {
              'records': [
                {
                  'workerId': 'worker002',
                  'workerName': '李四',
                  'role': '仓管',
                  'warehouse': '1号库',
                  'group': '第一组',
                  'allocatedTonnage': 150.0,
                  'assignedAt': DateTime.now().toIso8601String(),
                },
              ],
              'totalTonnage': 150.0,
              'palletCount': 100,
              'assignedAt': DateTime.now().toIso8601String(),
              'assignedBy': 'test',
            }
          },
        );

        await mockTaskRepository.saveTask(task1);
        await mockTaskRepository.saveTask(task2);

        final ranking = await repository.calculateEfficiencyRanking();
        
        expect(ranking.length, 2);
        expect(ranking.first.workerId, 'worker001'); // 张三效率100%，排第一
        expect(ranking.first.efficiency, 1.0);
        expect(ranking.first.rank, 1);
        expect(ranking.last.workerId, 'worker002'); // 李四效率0%，排第二
        expect(ranking.last.efficiency, 0.0);
        expect(ranking.last.rank, 2);
      });

      test('should get complete workload statistics', () async {
        final statistics = await repository.getWorkloadStatistics();
        
        expect(statistics.workerStats, isNotEmpty);
        expect(statistics.overview, isNotNull);
        expect(statistics.efficiencyRanking, isNotNull);
        expect(statistics.lastUpdated, isNotNull);
      });

      test('should validate workload data', () async {
        final validation = await repository.validateWorkloadData();
        
        expect(validation['totalTasks'], isA<int>());
        expect(validation['tasksWithWorkload'], isA<int>());
        expect(validation['tasksWithoutWorkload'], isA<int>());
        expect(validation['issues'], isA<List>());
        expect(validation['dataIntegrity'], isA<String>());
      });
    });

    group('缓存管理测试', () {
      test('should cache and retrieve statistics', () async {
        await repository.initialize();
        
        final statistics = await repository.getWorkloadStatistics();
        
        // 第二次调用应该使用缓存
        final cachedStatistics = await repository.getWorkloadStatistics();
        expect(cachedStatistics.lastUpdated, statistics.lastUpdated);
        
        // 清除缓存
        await repository.clearCachedStatistics();
        final clearedCache = await repository.getCachedStatistics();
        expect(clearedCache, isNull);
      });
    });
  });
}
