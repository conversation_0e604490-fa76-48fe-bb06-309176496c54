import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/material3_theme.dart';

/// 🎨 Material 3 主题状态管理器
/// 支持亮色/暗色模式动态切换和持久化存储
class ThemeNotifier extends StateNotifier<ThemeMode> {
  static const String _themeKey = 'app_theme_mode';
  
  ThemeNotifier() : super(ThemeMode.system) {
    _loadThemeMode();
  }

  /// 加载保存的主题模式
  Future<void> _loadThemeMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeIndex = prefs.getInt(_themeKey) ?? 0;
      state = ThemeMode.values[themeIndex];
    } catch (e) {
      // 如果加载失败，使用系统默认主题
      state = ThemeMode.system;
    }
  }

  /// 保存主题模式
  Future<void> _saveThemeMode(ThemeMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);
    } catch (e) {
      // 保存失败不影响主题切换
      debugPrint('保存主题模式失败: $e');
    }
  }

  /// 切换到亮色模式
  Future<void> setLightMode() async {
    state = ThemeMode.light;
    await _saveThemeMode(ThemeMode.light);
  }

  /// 切换到暗色模式
  Future<void> setDarkMode() async {
    state = ThemeMode.dark;
    await _saveThemeMode(ThemeMode.dark);
  }

  /// 跟随系统主题
  Future<void> setSystemMode() async {
    state = ThemeMode.system;
    await _saveThemeMode(ThemeMode.system);
  }

  /// 切换主题模式（循环切换）
  Future<void> toggleTheme() async {
    switch (state) {
      case ThemeMode.light:
        await setDarkMode();
        break;
      case ThemeMode.dark:
        await setSystemMode();
        break;
      case ThemeMode.system:
        await setLightMode();
        break;
    }
  }

  /// 获取当前主题模式的显示名称
  String get currentThemeName {
    switch (state) {
      case ThemeMode.light:
        return '亮色模式';
      case ThemeMode.dark:
        return '暗色模式';
      case ThemeMode.system:
        return '跟随系统';
    }
  }

  /// 获取当前主题模式的图标
  IconData get currentThemeIcon {
    switch (state) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  /// 判断当前是否为暗色模式（考虑系统主题）
  bool isDarkMode(BuildContext context) {
    switch (state) {
      case ThemeMode.light:
        return false;
      case ThemeMode.dark:
        return true;
      case ThemeMode.system:
        return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
  }
}

/// 主题状态提供者
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeMode>((ref) {
  return ThemeNotifier();
});

/// 当前主题数据提供者（亮色主题）
final lightThemeProvider = Provider<ThemeData>((ref) {
  return Material3Theme.lightTheme;
});

/// 当前主题数据提供者（暗色主题）
final darkThemeProvider = Provider<ThemeData>((ref) {
  return Material3Theme.darkTheme;
});

/// 主题工具类
class ThemeHelper {
  /// 获取适应当前主题的颜色
  static Color getAdaptiveColor(
    BuildContext context, {
    required Color lightColor,
    required Color darkColor,
  }) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.light ? lightColor : darkColor;
  }

  /// 获取适应当前主题的文本颜色
  static Color getAdaptiveTextColor(BuildContext context) {
    return Theme.of(context).colorScheme.onSurface;
  }

  /// 获取适应当前主题的背景颜色
  static Color getAdaptiveBackgroundColor(BuildContext context) {
    return Theme.of(context).colorScheme.background;
  }

  /// 获取适应当前主题的表面颜色
  static Color getAdaptiveSurfaceColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  /// 获取适应当前主题的主色调
  static Color getAdaptivePrimaryColor(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  /// 获取适应当前主题的次要色调
  static Color getAdaptiveSecondaryColor(BuildContext context) {
    return Theme.of(context).colorScheme.secondary;
  }
}