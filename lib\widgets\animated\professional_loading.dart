import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../theme/material3_theme.dart';

/// 🎯 Material 3 专业级加载动画组件
/// 完全符合 Material Design 3 规范的加载动画系统
class ProfessionalLoading extends StatefulWidget {
  final Material3LoadingType type;
  final Material3LoadingSize size;
  final Color? color;
  final String? message;
  final bool showMessage;
  final Duration animationDuration;
  final double? strokeWidth;
  final Widget? child;

  const ProfessionalLoading({
    Key? key,
    this.type = Material3LoadingType.circular,
    this.size = Material3LoadingSize.medium,
    this.color,
    this.message,
    this.showMessage = false,
    this.animationDuration = const Duration(milliseconds: 1200),
    this.strokeWidth,
    this.child,
  }) : super(key: key);

  /// 创建圆形加载动画
  const ProfessionalLoading.circular({
    Key? key,
    Material3LoadingSize size = Material3LoadingSize.medium,
    Color? color,
    String? message,
    bool showMessage = false,
    Duration animationDuration = const Duration(milliseconds: 1200),
    double? strokeWidth,
  }) : this(
          key: key,
          type: Material3LoadingType.circular,
          size: size,
          color: color,
          message: message,
          showMessage: showMessage,
          animationDuration: animationDuration,
          strokeWidth: strokeWidth,
        );

  /// 创建线性加载动画
  const ProfessionalLoading.linear({
    Key? key,
    Material3LoadingSize size = Material3LoadingSize.medium,
    Color? color,
    String? message,
    bool showMessage = false,
    Duration animationDuration = const Duration(milliseconds: 1200),
  }) : this(
          key: key,
          type: Material3LoadingType.linear,
          size: size,
          color: color,
          message: message,
          showMessage: showMessage,
          animationDuration: animationDuration,
        );

  /// 创建脉冲加载动画
  const ProfessionalLoading.pulse({
    Key? key,
    Material3LoadingSize size = Material3LoadingSize.medium,
    Color? color,
    String? message,
    bool showMessage = false,
    Duration animationDuration = const Duration(milliseconds: 1200),
    Widget? child,
  }) : this(
          key: key,
          type: Material3LoadingType.pulse,
          size: size,
          color: color,
          message: message,
          showMessage: showMessage,
          animationDuration: animationDuration,
          child: child,
        );

  /// 创建波浪加载动画
  const ProfessionalLoading.wave({
    Key? key,
    Material3LoadingSize size = Material3LoadingSize.medium,
    Color? color,
    String? message,
    bool showMessage = false,
    Duration animationDuration = const Duration(milliseconds: 1200),
  }) : this(
          key: key,
          type: Material3LoadingType.wave,
          size: size,
          color: color,
          message: message,
          showMessage: showMessage,
          animationDuration: animationDuration,
        );

  @override
  State<ProfessionalLoading> createState() => _ProfessionalLoadingState();
}

class _ProfessionalLoadingState extends State<ProfessionalLoading>
    with TickerProviderStateMixin {
  late AnimationController _primaryController;
  late AnimationController _secondaryController;
  late Animation<double> _primaryAnimation;
  late Animation<double> _secondaryAnimation;

  @override
  void initState() {
    super.initState();

    // 主动画控制器
    _primaryController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // 辅助动画控制器
    _secondaryController = AnimationController(
      duration: Duration(milliseconds: (widget.animationDuration.inMilliseconds * 0.8).round()),
      vsync: this,
    );

    // 主动画
    _primaryAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _primaryController,
      curve: Material3Theme.standardCurve,
    ));

    // 辅助动画
    _secondaryAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _secondaryController,
      curve: Material3Theme.emphasizedCurve,
    ));

    // 启动动画
    _startAnimation();
  }

  void _startAnimation() {
    switch (widget.type) {
      case Material3LoadingType.circular:
      case Material3LoadingType.linear:
        _primaryController.repeat();
        break;
      case Material3LoadingType.pulse:
        _primaryController.repeat(reverse: true);
        break;
      case Material3LoadingType.wave:
        _primaryController.repeat();
        _secondaryController.repeat();
        break;
    }
  }

  @override
  void dispose() {
    _primaryController.dispose();
    _secondaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Material3Theme.getColorScheme(context);
    final config = _getLoadingConfiguration(colorScheme);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 加载动画
        _buildLoadingAnimation(config),

        // 加载消息
        if (widget.showMessage && widget.message != null) ...[
          SizedBox(height: Material3Tokens.space12),
          Text(
            widget.message!,
            style: Material3Theme.getTextTheme(context).bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  /// 构建加载动画
  Widget _buildLoadingAnimation(_LoadingConfiguration config) {
    switch (widget.type) {
      case Material3LoadingType.circular:
        return _buildCircularLoading(config);
      case Material3LoadingType.linear:
        return _buildLinearLoading(config);
      case Material3LoadingType.pulse:
        return _buildPulseLoading(config);
      case Material3LoadingType.wave:
        return _buildWaveLoading(config);
    }
  }

  /// 构建圆形加载动画
  Widget _buildCircularLoading(_LoadingConfiguration config) {
    return SizedBox(
      width: config.size,
      height: config.size,
      child: AnimatedBuilder(
        animation: _primaryAnimation,
        builder: (context, child) {
          return CustomPaint(
            painter: _CircularLoadingPainter(
              progress: _primaryAnimation.value,
              color: config.color,
              strokeWidth: config.strokeWidth,
            ),
            size: Size(config.size, config.size),
          );
        },
      ),
    );
  }

  /// 构建线性加载动画
  Widget _buildLinearLoading(_LoadingConfiguration config) {
    return SizedBox(
      width: config.size * 2,
      height: config.strokeWidth,
      child: AnimatedBuilder(
        animation: _primaryAnimation,
        builder: (context, child) {
          return CustomPaint(
            painter: _LinearLoadingPainter(
              progress: _primaryAnimation.value,
              color: config.color,
              backgroundColor: Material3Theme.getColorScheme(context).surfaceVariant,
            ),
            size: Size(config.size * 2, config.strokeWidth),
          );
        },
      ),
    );
  }

  /// 构建脉冲加载动画
  Widget _buildPulseLoading(_LoadingConfiguration config) {
    return AnimatedBuilder(
      animation: _primaryAnimation,
      builder: (context, child) {
        final scale = 0.8 + (_primaryAnimation.value * 0.4);
        final opacity = 0.4 + (_primaryAnimation.value * 0.6);

        return Transform.scale(
          scale: scale,
          child: Opacity(
            opacity: opacity,
            child: Container(
              width: config.size,
              height: config.size,
              decoration: BoxDecoration(
                color: config.color,
                shape: BoxShape.circle,
              ),
              child: widget.child != null
                  ? Center(child: widget.child!)
                  : null,
            ),
          ),
        );
      },
    );
  }

  /// 构建波浪加载动画
  Widget _buildWaveLoading(_LoadingConfiguration config) {
    return SizedBox(
      width: config.size * 1.5,
      height: config.size * 0.3,
      child: AnimatedBuilder(
        animation: Listenable.merge([_primaryAnimation, _secondaryAnimation]),
        builder: (context, child) {
          return CustomPaint(
            painter: _WaveLoadingPainter(
              primaryProgress: _primaryAnimation.value,
              secondaryProgress: _secondaryAnimation.value,
              color: config.color,
            ),
            size: Size(config.size * 1.5, config.size * 0.3),
          );
        },
      ),
    );
  }

  /// 获取加载动画配置
  _LoadingConfiguration _getLoadingConfiguration(ColorScheme colorScheme) {
    final color = widget.color ?? colorScheme.primary;

    switch (widget.size) {
      case Material3LoadingSize.small:
        return _LoadingConfiguration(
          color: color,
          size: 24.0,
          strokeWidth: widget.strokeWidth ?? 2.0,
        );

      case Material3LoadingSize.medium:
        return _LoadingConfiguration(
          color: color,
          size: 32.0,
          strokeWidth: widget.strokeWidth ?? 3.0,
        );

      case Material3LoadingSize.large:
        return _LoadingConfiguration(
          color: color,
          size: 48.0,
          strokeWidth: widget.strokeWidth ?? 4.0,
        );
    }
  }
}

/// Material 3 加载动画类型枚举
enum Material3LoadingType {
  circular, // 圆形加载动画
  linear,   // 线性加载动画
  pulse,    // 脉冲加载动画
  wave,     // 波浪加载动画
}

/// Material 3 加载动画尺寸枚举
enum Material3LoadingSize {
  small,   // 小尺寸
  medium,  // 中等尺寸
  large,   // 大尺寸
}

/// 加载动画配置类
class _LoadingConfiguration {
  final Color color;
  final double size;
  final double strokeWidth;

  const _LoadingConfiguration({
    required this.color,
    required this.size,
    required this.strokeWidth,
  });
}

/// 圆形加载动画绘制器
class _CircularLoadingPainter extends CustomPainter {
  final double progress;
  final Color color;
  final double strokeWidth;

  _CircularLoadingPainter({
    required this.progress,
    required this.color,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // 计算起始角度和扫描角度
    final startAngle = progress * 2 * math.pi - math.pi / 2;
    final sweepAngle = math.pi * 1.5;

    // 绘制弧形
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(_CircularLoadingPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}

/// 线性加载动画绘制器
class _LinearLoadingPainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;

  _LinearLoadingPainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final progressPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 绘制背景
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(size.height / 2),
      ),
      backgroundPaint,
    );

    // 计算进度条位置
    final progressWidth = size.width * 0.3;
    final totalDistance = size.width + progressWidth;
    final currentPosition = (progress * totalDistance) - progressWidth;

    if (currentPosition + progressWidth > 0 && currentPosition < size.width) {
      final startX = math.max(0.0, currentPosition);
      final endX = math.min(size.width, currentPosition + progressWidth);
      final width = endX - startX;

      if (width > 0) {
        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(startX, 0, width, size.height),
            Radius.circular(size.height / 2),
          ),
          progressPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(_LinearLoadingPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.backgroundColor != backgroundColor;
  }
}

/// 波浪加载动画绘制器
class _WaveLoadingPainter extends CustomPainter {
  final double primaryProgress;
  final double secondaryProgress;
  final Color color;

  _WaveLoadingPainter({
    required this.primaryProgress,
    required this.secondaryProgress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = size.height * 0.5;
    final centerY = size.height / 2;

    path.moveTo(0, centerY);

    // 绘制波浪
    for (double x = 0; x <= size.width; x += 1) {
      final primaryWave = math.sin((x / size.width * 4 * math.pi) + (primaryProgress * 2 * math.pi));
      final secondaryWave = math.sin((x / size.width * 6 * math.pi) + (secondaryProgress * 2 * math.pi));
      final y = centerY + (primaryWave + secondaryWave * 0.5) * waveHeight * 0.5;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(_WaveLoadingPainter oldDelegate) {
    return oldDelegate.primaryProgress != primaryProgress ||
        oldDelegate.secondaryProgress != secondaryProgress ||
        oldDelegate.color != color;
  }
}

/// 🎯 Material 3 骨架屏组件
/// 用于内容加载时的占位效果
class ProfessionalSkeleton extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration animationDuration;
  final bool isLoading;
  final Widget? child;

  const ProfessionalSkeleton({
    Key? key,
    required this.width,
    required this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
    this.animationDuration = const Duration(milliseconds: 1500),
    this.isLoading = true,
    this.child,
  }) : super(key: key);

  /// 创建文本骨架屏
  const ProfessionalSkeleton.text({
    Key? key,
    double width = 100.0,
    double height = 16.0,
    BorderRadius? borderRadius,
    Color? baseColor,
    Color? highlightColor,
    Duration animationDuration = const Duration(milliseconds: 1500),
    bool isLoading = true,
  }) : this(
          key: key,
          width: width,
          height: height,
          borderRadius: borderRadius ?? const BorderRadius.all(Radius.circular(4.0)),
          baseColor: baseColor,
          highlightColor: highlightColor,
          animationDuration: animationDuration,
          isLoading: isLoading,
        );

  /// 创建圆形骨架屏
  ProfessionalSkeleton.circle({
    Key? key,
    required double size,
    Color? baseColor,
    Color? highlightColor,
    Duration animationDuration = const Duration(milliseconds: 1500),
    bool isLoading = true,
  }) : this(
          key: key,
          width: size,
          height: size,
          borderRadius: BorderRadius.circular(size * 0.5),
          baseColor: baseColor,
          highlightColor: highlightColor,
          animationDuration: animationDuration,
          isLoading: isLoading,
        );

  /// 创建矩形骨架屏
  const ProfessionalSkeleton.rectangle({
    Key? key,
    required double width,
    required double height,
    BorderRadius? borderRadius,
    Color? baseColor,
    Color? highlightColor,
    Duration animationDuration = const Duration(milliseconds: 1500),
    bool isLoading = true,
  }) : this(
          key: key,
          width: width,
          height: height,
          borderRadius: borderRadius ?? const BorderRadius.all(Radius.circular(8.0)),
          baseColor: baseColor,
          highlightColor: highlightColor,
          animationDuration: animationDuration,
          isLoading: isLoading,
        );

  @override
  State<ProfessionalSkeleton> createState() => _ProfessionalSkeletonState();
}

class _ProfessionalSkeletonState extends State<ProfessionalSkeleton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isLoading) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(ProfessionalSkeleton oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading && widget.child != null) {
      return widget.child!;
    }

    final colorScheme = Material3Theme.getColorScheme(context);
    final baseColor = widget.baseColor ?? colorScheme.surfaceVariant;
    final highlightColor = widget.highlightColor ?? colorScheme.surface;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius,
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                math.max(0.0, _animation.value - 0.3),
                _animation.value,
                math.min(1.0, _animation.value + 0.3),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 🎯 Material 3 加载覆盖层组件
/// 用于全屏或局部区域的加载遮罩
class ProfessionalLoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget? child;
  final String? message;
  final Material3LoadingType loadingType;
  final Material3LoadingSize loadingSize;
  final Color? overlayColor;
  final Color? loadingColor;
  final bool dismissible;

  const ProfessionalLoadingOverlay({
    Key? key,
    this.isLoading = true,
    this.child,
    this.message,
    this.loadingType = Material3LoadingType.circular,
    this.loadingSize = Material3LoadingSize.medium,
    this.overlayColor,
    this.loadingColor,
    this.dismissible = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 如果没有子组件，直接返回加载覆盖层
    if (child == null) {
      return Container(
        color: overlayColor ?? 
               Material3Theme.getColorScheme(context).scrim.withOpacity(0.5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(Material3Tokens.space24),
            decoration: BoxDecoration(
              color: Material3Theme.getColorScheme(context).surface,
              borderRadius: BorderRadius.circular(Material3Tokens.radiusL),
              boxShadow: [
                BoxShadow(
                  color: Material3Theme.getColorScheme(context).shadow.withOpacity(0.1),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ProfessionalLoading(
                  type: loadingType,
                  size: loadingSize,
                  color: loadingColor,
                ),
                if (message != null) ...[
                  const SizedBox(height: Material3Tokens.space16),
                  Text(
                    message!,
                    style: Material3Theme.getTextTheme(context).bodyMedium?.copyWith(
                      color: Material3Theme.getColorScheme(context).onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      );
    }

    // 如果有子组件，使用 Stack 布局
    return Stack(
      children: [
        child!,
        if (isLoading)
          Positioned.fill(
            child: GestureDetector(
              onTap: dismissible ? null : () {},
              child: Container(
                color: overlayColor ?? 
                       Material3Theme.getColorScheme(context).scrim.withOpacity(0.5),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(Material3Tokens.space24),
                    decoration: BoxDecoration(
                      color: Material3Theme.getColorScheme(context).surface,
                      borderRadius: BorderRadius.circular(Material3Tokens.radiusL),
                      boxShadow: [
                        BoxShadow(
                          color: Material3Theme.getColorScheme(context).shadow.withOpacity(0.1),
                          blurRadius: 24,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ProfessionalLoading(
                          type: loadingType,
                          size: loadingSize,
                          color: loadingColor,
                        ),
                        if (message != null) ...[
                          const SizedBox(height: Material3Tokens.space16),
                          Text(
                            message!,
                            style: Material3Theme.getTextTheme(context).bodyMedium?.copyWith(
                              color: Material3Theme.getColorScheme(context).onSurface,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// 🎯 Material 3 列表骨架屏组件
/// 用于列表内容的骨架屏效果
class ProfessionalListSkeleton extends StatelessWidget {
  final int itemCount;
  final double itemHeight;
  final EdgeInsetsGeometry? padding;
  final bool hasAvatar;
  final bool hasSubtitle;
  final bool hasTrailing;

  const ProfessionalListSkeleton({
    Key? key,
    this.itemCount = 5,
    this.itemHeight = 72.0,
    this.padding,
    this.hasAvatar = true,
    this.hasSubtitle = true,
    this.hasTrailing = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: padding,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return Container(
          height: itemHeight,
          padding: const EdgeInsets.all(Material3Tokens.space16),
          child: Row(
            children: [
              // 头像骨架
              if (hasAvatar) ...[
                ProfessionalSkeleton.circle(size: 40.0),
                const SizedBox(width: Material3Tokens.space16),
              ],

              // 内容骨架
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 标题骨架
                    ProfessionalSkeleton.text(
                      width: double.infinity,
                      height: 16.0,
                    ),

                    // 副标题骨架
                    if (hasSubtitle) ...[
                      const SizedBox(height: Material3Tokens.space8),
                      ProfessionalSkeleton.text(
                        width: 200.0,
                        height: 14.0,
                      ),
                    ],
                  ],
                ),
              ),

              // 尾随骨架
              if (hasTrailing) ...[
                const SizedBox(width: Material3Tokens.space16),
                const ProfessionalSkeleton.rectangle(
                  width: 24.0,
                  height: 24.0,
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}