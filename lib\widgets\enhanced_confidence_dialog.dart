import 'package:flutter/material.dart';
import 'package:loadguard/services/confidence_evaluation_service.dart';
import 'package:loadguard/models/task_model.dart';

/// 🎯 增强版置信度弹窗组件
/// 专业级UI设计，优化层次感和信息展示
class EnhancedConfidenceDialog extends StatelessWidget {
  final ConfidenceScore confidenceScore;
  final RecognitionResult recognitionResult;
  final String photoLabel;
  final VoidCallback? onRetryRecognition;
  final VoidCallback? onManualConfirm;
  final VoidCallback? onRetakePhoto;

  const EnhancedConfidenceDialog({
    super.key,
    required this.confidenceScore,
    required this.recognitionResult,
    required this.photoLabel,
    this.onRetryRecognition,
    this.onManualConfirm,
    this.onRetakePhoto,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 360,
          maxHeight: MediaQuery.of(context).size.height * 0.85, // 🔧 优化：减少最大高度
        ),
        decoration: BoxDecoration(
          // 🔧 优化：使用更明亮的背景色，增强层次感
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8FAFC), // 浅灰白
              Color(0xFFF1F5F9), // 浅蓝灰
              Color(0xFFE2E8F0), // 中灰
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.15),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部标题栏
            _buildDialogTitle(),

            // 🔧 优化：使用SingleChildScrollView避免内容溢出，但保持紧凑
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    const SizedBox(height: 12), // 🔧 减少间距

                    // 置信度状态卡片
                    _buildConfidenceStatusCard(),
                    const SizedBox(height: 12), // 🔧 减少间距

                    // 识别结果详情
                    _buildRecognitionResults(),
                    const SizedBox(height: 12), // 🔧 减少间距

                    // 置信度详细分析
                    _buildConfidenceAnalysis(),
                  ],
                ),
              ),
            ),

            // 底部操作按钮
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  /// Dialog Title - 优化背景色
  Widget _buildDialogTitle() {
    return Container(
      padding:
          const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 🔧 减少内边距
      decoration: BoxDecoration(
        // 🔧 优化：使用更明亮的背景色
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFE2E8F0), // 中灰
            Color(0xFFCBD5E1), // 深灰
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFF94A3B8).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6), // 🔧 减少内边距
            decoration: BoxDecoration(
              color: Color(confidenceScore.level.colorValue).withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Color(confidenceScore.level.colorValue).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Icon(
              confidenceScore.level == ConfidenceLevel.high
                  ? Icons.check_circle_outline
                  : Icons.info_outline,
              color: Color(confidenceScore.level.colorValue),
              size: 18, // 🔧 减小图标大小
            ),
          ),
          const SizedBox(width: 10), // 🔧 减少间距
          Expanded(
            child: Text(
              photoLabel,
              style: const TextStyle(
                color: Color(0xFF1E293B), // 🔧 使用深色文字
                fontSize: 15, // 🔧 减小字体
                fontWeight: FontWeight.w600,
                letterSpacing: 0.3,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          const SizedBox(width: 10), // 🔧 减少间距
          Container(
            padding: const EdgeInsets.symmetric(
                horizontal: 10, vertical: 4), // 🔧 调整内边距
            decoration: BoxDecoration(
              color: Color(confidenceScore.level.colorValue).withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Color(confidenceScore.level.colorValue).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              confidenceScore.percentageString,
              style: TextStyle(
                color: Color(confidenceScore.level.colorValue),
                fontSize: 13, // 🔧 减小字体
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建置信度状态卡片 - 重新设计
  Widget _buildConfidenceStatusCard() {
    final level = confidenceScore.level;
    final color = Color(level.colorValue);
    final isSuccess = level == ConfidenceLevel.high;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        // 🎨 使用更现代的渐变背景
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.08),
            color.withValues(alpha: 0.04),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 🎨 重新设计的头部区域
          Row(
            children: [
              // 🎨 状态图标
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  isSuccess ? Icons.check_circle : Icons.info_outline,
                  color: color,
                  size: 24,
                ),
              ),

              const SizedBox(width: 16),

              // 🎨 状态信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isSuccess ? '识别成功' : '需要确认',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      isSuccess ? 'AI智能识别结果可信' : '建议人工复核确认',
                      style: TextStyle(
                        fontSize: 13,
                        color: color.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),

              // 🎨 置信度分数
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: color.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  confidenceScore.percentageString,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // 🎨 重新设计的置信度条
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '置信度评分',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF64748B),
                    ),
                  ),
                  Text(
                    '${(confidenceScore.finalScore * 100).toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // 🎨 现代风格的进度条
              Container(
                height: 8,
                decoration: BoxDecoration(
                  color: const Color(0xFFE2E8F0),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: confidenceScore.finalScore.clamp(0.0, 1.0),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          color,
                          color.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // 🎨 置信度等级标签
              Row(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      _getConfidenceLevelText(level),
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getConfidenceDescription(level),
                    style: const TextStyle(
                      fontSize: 11,
                      color: Color(0xFF64748B),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 🎨 获取置信度等级文本
  String _getConfidenceLevelText(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.high:
        return '高置信度';
      case ConfidenceLevel.medium:
        return '中置信度';
      case ConfidenceLevel.low:
        return '低置信度';
      case ConfidenceLevel.veryLow:
        return '极低置信度';
    }
  }

  /// 🎨 获取置信度描述
  String _getConfidenceDescription(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.high:
        return '• 识别结果可信，可直接使用';
      case ConfidenceLevel.medium:
        return '• 建议人工复核确认';
      case ConfidenceLevel.low:
        return '• 识别质量较低，需要重新拍摄';
      case ConfidenceLevel.veryLow:
        return '• 识别质量很差，必须重新拍摄';
    }
  }

  /// 构建识别结果详情 - 重新设计布局
  Widget _buildRecognitionResults() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        // 🎨 使用更柔和的背景色
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFAFBFC),
            Color(0xFFF8FAFC),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 🎨 重新设计的标题栏
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.verified_outlined,
                  color: Color(0xFF10B981),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '识别结果详情',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1E293B),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'AI智能识别分析结果',
                      style: TextStyle(
                        fontSize: 12,
                        color: const Color(0xFF64748B),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // 🎨 重新设计的信息卡片布局 - 优化间距
          _buildModernInfoCard(
              '产品码', recognitionResult.extractedProductCode ?? '未识别', true),
          const SizedBox(height: 16), // 增加间距
          _buildModernInfoCard(
              '批号', recognitionResult.extractedBatchNumber ?? '未识别', true),
          const SizedBox(height: 16), // 增加间距
          _buildModernInfoCard('识别时间',
              _formatRecognitionTime(recognitionResult.recognitionTime), false),

          const SizedBox(height: 20), // 增加与OCR文本区域的间距

          // 🎨 重新设计的OCR文本显示
          _buildOcrTextSection(),
        ],
      ),
    );
  }

  /// 🎨 格式化识别时间
  String _formatRecognitionTime(DateTime time) {
    return '${time.month.toString().padLeft(2, '0')}/${time.day.toString().padLeft(2, '0')} ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 🎨 现代风格的信息卡片 - 表格形式整齐排列
  Widget _buildModernInfoCard(String label, String value, bool isKeyInfo) {
    final isSuccess = isKeyInfo && value.isNotEmpty && value != '未识别';
    final color = isSuccess ? const Color(0xFF10B981) : const Color(0xFF64748B);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.15),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // 🎨 左侧标签 - 固定宽度，左对齐
          SizedBox(
            width: 90, // 固定宽度确保对齐
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14,
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 🎨 中间值 - 左对齐显示
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: const Color(0xFF1E293B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 🎨 右侧状态图标
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              isSuccess ? Icons.check : Icons.info_outline,
              color: color,
              size: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 🎨 重新设计的OCR文本区域
  Widget _buildOcrTextSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.text_fields,
                size: 16,
                color: const Color(0xFF64748B),
              ),
              const SizedBox(width: 8),
              Text(
                'OCR原始文本',
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF64748B),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFE2E8F0),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '${recognitionResult.ocrText?.length ?? 0} 字符',
                  style: const TextStyle(
                    fontSize: 10,
                    color: Color(0xFF64748B),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            constraints: const BoxConstraints(maxHeight: 100),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFE2E8F0),
                width: 1,
              ),
            ),
            child: SingleChildScrollView(
              child: Text(
                recognitionResult.ocrText ?? '无OCR文本',
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: 'monospace',
                  color: Color(0xFF475569),
                  height: 1.4,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔧 新增：统一的OCR文本卡片样式
  Widget _buildOcrTextCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(10), // 🔧 减少内边距
      decoration: BoxDecoration(
        color: const Color(0xFFFEF3C7), // 🔧 使用浅黄色背景
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFF59E0B).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'OCR原文',
                style: TextStyle(
                  fontSize: 12, // 🔧 减小字体
                  color: const Color(0xFF92400E),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 6), // 🔧 减少间距
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 14, // 🔧 减小图标
              ),
            ],
          ),
          const SizedBox(height: 6), // 🔧 减少间距
          Container(
            width: double.infinity,
            constraints: const BoxConstraints(maxHeight: 120), // 🔧 限制最大高度
            child: SingleChildScrollView(
              child: Text(
                recognitionResult.ocrText!,
                style: const TextStyle(
                  fontSize: 11, // 🔧 减小字体
                  fontFamily: 'monospace',
                  color: Color(0xFF92400E),
                  height: 1.3,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔧 新增：统一的信息卡片样式
  Widget _buildInfoCard(String label, String value, [bool? isPositive]) {
    // 🔧 修复：根据识别结果判断是否显示绿色√
    final isSuccess =
        isPositive ?? (value.isNotEmpty && value != '无' && value != '未识别');
    final color = isSuccess ? Colors.green : const Color(0xFF64748B);

    return Container(
      width: double.infinity,
      padding:
          const EdgeInsets.symmetric(horizontal: 10, vertical: 8), // 🔧 统一内边距
      decoration: BoxDecoration(
        color: const Color(0xFFF1F5F9),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 70, // 🔧 统一标签宽度
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12, // 🔧 减小字体
                color: const Color(0xFF64748B),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: 8, vertical: 4), // 🔧 统一内边距
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: color.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 12, // 🔧 减小字体
                  color: const Color(0xFF1E293B),
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          const SizedBox(width: 8), // 🔧 减少间距
          Icon(
            isSuccess
                ? Icons.check_circle
                : Icons.info_outline, // 🔧 修复：成功时显示绿色√
            color: color,
            size: 16, // 🔧 减小图标
          ),
        ],
      ),
    );
  }

  /// 构建置信度详细分析 - 优化布局
  Widget _buildConfidenceAnalysis() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(14), // 🔧 减少内边距
      decoration: BoxDecoration(
        // 🔧 优化：使用更明亮的背景色
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFE2E8F0),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: const Color(0xFF64748B),
                size: 16, // 🔧 减小图标
              ),
              const SizedBox(width: 6), // 🔧 减少间距
              Text(
                '置信度详细分析',
                style: TextStyle(
                  color: const Color(0xFF1E293B),
                  fontSize: 14, // 🔧 减小字体
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10), // 🔧 减少间距

          // 🔧 优化：更紧凑的分数显示
          _buildScoreRow('ML Kit原始分数', confidenceScore.rawMlkitScore, false),
          const SizedBox(height: 6), // 🔧 减少间距
          _buildScoreRow('文本质量评估', confidenceScore.textQualityScore, false),
          const SizedBox(height: 6), // 🔧 减少间距
          _buildScoreRow('预设匹配度', confidenceScore.matchScore, false),
          const SizedBox(height: 6), // 🔧 减少间距
          _buildScoreRow('数据一致性', confidenceScore.consistencyScore, false),
          const SizedBox(height: 8), // 🔧 减少间距
          _buildScoreRow(
              '综合置信度', confidenceScore.finalScore, true), // 🔧 突出显示最终分数
        ],
      ),
    );
  }

  /// 🔧 优化：更紧凑的分数行显示
  Widget _buildScoreRow(String label, double score, bool isHighlighted) {
    final percentage = (score * 100).toStringAsFixed(1);
    final color =
        isHighlighted ? const Color(0xFF10B981) : const Color(0xFF3B82F6);

    return Container(
      padding:
          const EdgeInsets.symmetric(vertical: 6, horizontal: 8), // 🔧 减少内边距
      decoration: BoxDecoration(
        color: isHighlighted ? color.withValues(alpha: 0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12, // 🔧 减小字体
                color: const Color(0xFF64748B),
                fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 4,
            child: Container(
              height: 6, // 🔧 减小进度条高度
              decoration: BoxDecoration(
                color: const Color(0xFFE2E8F0),
                borderRadius: BorderRadius.circular(3),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: score.clamp(0.0, 1.0),
                child: Container(
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 8), // 🔧 减少间距
          SizedBox(
            width: 45, // 🔧 固定宽度
            child: Text(
              '$percentage%',
              style: TextStyle(
                fontSize: 11, // 🔧 减小字体
                color: color,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮 - 修复：所有情况都显示人工确认按钮
  Widget _buildActionButtons(BuildContext context) {
    final level = confidenceScore.level;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        // 🔧 优化：使用更明亮的背景色
        color: const Color(0xFFF8FAFC),
        border: Border(
          top: BorderSide(
            color: const Color(0xFFE2E8F0),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 第一行：重试识别和重新拍照
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: onRetryRecognition,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('重试识别', style: TextStyle(fontSize: 13)),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF64748B),
                    side: const BorderSide(color: Color(0xFFCBD5E1)),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: onRetakePhoto,
                  icon: const Icon(Icons.camera_alt, size: 16),
                  label: const Text('重新拍照', style: TextStyle(fontSize: 13)),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF64748B),
                    side: const BorderSide(color: Color(0xFFCBD5E1)),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          // 第二行：人工确认和关闭
          Row(
            children: [
              Expanded(
                child: FilledButton.icon(
                  onPressed: onManualConfirm,
                  icon: const Icon(Icons.check_circle, size: 16),
                  label: const Text('人工确认', style: TextStyle(fontSize: 13)),
                  style: FilledButton.styleFrom(
                    backgroundColor: const Color(0xFF10B981),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 10),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: FilledButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, size: 16),
                  label: const Text('关闭', style: TextStyle(fontSize: 13)),
                  style: FilledButton.styleFrom(
                    backgroundColor: const Color(0xFF64748B),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 10),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
