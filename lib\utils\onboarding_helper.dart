import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'responsive_helper.dart';
import 'responsive_text_helper.dart';

/// 🚀 首次使用引导助手类
/// 管理用户的首次使用体验和功能引导
class OnboardingHelper {
  static const String _keyFirstLaunch = 'first_launch';
  static const String _keyFeatureIntroShown = 'feature_intro_shown';
  static const String _keyPhotoGuideShown = 'photo_guide_shown';
  static const String _keyWorkloadGuideShown = 'workload_guide_shown';

  /// 检查是否是首次启动
  static Future<bool> isFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyFirstLaunch) ?? true;
  }

  /// 标记首次启动完成
  static Future<void> markFirstLaunchComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyFirstLaunch, false);
  }

  /// 检查功能介绍是否已显示
  static Future<bool> isFeatureIntroShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyFeatureIntroShown) ?? false;
  }

  /// 标记功能介绍已显示
  static Future<void> markFeatureIntroShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyFeatureIntroShown, true);
  }

  /// 检查拍照引导是否已显示
  static Future<bool> isPhotoGuideShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyPhotoGuideShown) ?? false;
  }

  /// 标记拍照引导已显示
  static Future<void> markPhotoGuideShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyPhotoGuideShown, true);
  }

  /// 检查工作量引导是否已显示
  static Future<bool> isWorkloadGuideShown() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyWorkloadGuideShown) ?? false;
  }

  /// 标记工作量引导已显示
  static Future<void> markWorkloadGuideShown() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyWorkloadGuideShown, true);
  }

  /// 重置所有引导状态（用于测试）
  static Future<void> resetAllGuides() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_keyFirstLaunch);
    await prefs.remove(_keyFeatureIntroShown);
    await prefs.remove(_keyPhotoGuideShown);
    await prefs.remove(_keyWorkloadGuideShown);
  }

  /// 显示首次启动引导
  static Future<void> showFirstLaunchGuide(BuildContext context) async {
    if (await isFirstLaunch()) {
      await showWelcomeDialog(context);
      await markFirstLaunchComplete();
    }
  }

  /// 显示欢迎对话框
  static Future<void> showWelcomeDialog(BuildContext context) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const WelcomeDialog(),
    );
  }

  /// 显示功能介绍引导
  static Future<void> showFeatureIntroGuide(BuildContext context) async {
    if (!await isFeatureIntroShown()) {
      await showDialog(
        context: context,
        builder: (context) => const FeatureIntroDialog(),
      );
      await markFeatureIntroShown();
    }
  }

  /// 显示拍照功能引导
  static Future<void> showPhotoGuide(BuildContext context) async {
    if (!await isPhotoGuideShown()) {
      await showDialog(
        context: context,
        builder: (context) => const PhotoGuideDialog(),
      );
      await markPhotoGuideShown();
    }
  }

  /// 显示工作量管理引导
  static Future<void> showWorkloadGuide(BuildContext context) async {
    if (!await isWorkloadGuideShown()) {
      await showDialog(
        context: context,
        builder: (context) => const WorkloadGuideDialog(),
      );
      await markWorkloadGuideShown();
    }
  }
}

/// 🎉 欢迎对话框
class WelcomeDialog extends StatefulWidget {
  const WelcomeDialog({Key? key}) : super(key: key);

  @override
  State<WelcomeDialog> createState() => _WelcomeDialogState();
}

class _WelcomeDialogState extends State<WelcomeDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: isMobile ? double.infinity : 400,
                  maxHeight: isMobile ? double.infinity : 500,
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Logo 或图标
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Theme.of(context).primaryColor,
                            Theme.of(context).primaryColor.withValues(alpha: 0.7),
                          ],
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.security,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // 标题
                    Text(
                      '欢迎使用装运卫士',
                      style: ResponsiveTextHelper.createHeadingStyle(
                        context,
                        level: 3,
                        color: Theme.of(context).textTheme.titleLarge?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // 描述
                    Text(
                      '智能识别、安全管理、效率提升\n让货物装运更安全、更高效',
                      style: ResponsiveTextHelper.createBodyStyle(
                        context,
                        size: 'medium',
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 24),

                    // 功能亮点
                    _buildFeatureList(context),

                    const SizedBox(height: 32),

                    // 开始使用按钮
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          '开始使用',
                          style: ResponsiveTextHelper.createBodyStyle(
                            context,
                            size: 'large',
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFeatureList(BuildContext context) {
    final features = [
      {'icon': Icons.camera_alt, 'text': '智能拍照识别'},
      {'icon': Icons.security, 'text': '数据安全保护'},
      {'icon': Icons.analytics, 'text': '工作量统计'},
      {'icon': Icons.cloud_sync, 'text': '多端同步'},
    ];

    return Column(
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Icon(
                feature['icon'] as IconData,
                size: 20,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 12),
              Text(
                feature['text'] as String,
                style: ResponsiveTextHelper.createBodyStyle(
                  context,
                  size: 'small',
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

/// 📖 功能介绍对话框
class FeatureIntroDialog extends StatelessWidget {
  const FeatureIntroDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.lightbulb,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              '主要功能介绍',
              style: ResponsiveTextHelper.createHeadingStyle(
                context,
                level: 4,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              context,
              Icons.photo_camera,
              '拍照识别',
              '拍摄货物照片，自动识别产品信息',
            ),
            _buildFeatureItem(
              context,
              Icons.assignment,
              '任务管理',
              '创建和管理装运任务，跟踪进度',
            ),
            _buildFeatureItem(
              context,
              Icons.bar_chart,
              '数据统计',
              '查看工作量统计和效率分析',
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('跳过'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('知道了'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(
      BuildContext context, IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              size: 20,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: ResponsiveTextHelper.createBodyStyle(
                    context,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: ResponsiveTextHelper.createBodyStyle(
                    context,
                    size: 'small',
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 📸 拍照引导对话框
class PhotoGuideDialog extends StatelessWidget {
  const PhotoGuideDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.camera_enhance,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              '拍照识别指南',
              style: ResponsiveTextHelper.createHeadingStyle(
                context,
                level: 4,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            const SizedBox(height: 16),
            _buildTipItem('确保光线充足'),
            _buildTipItem('产品标签清晰可见'),
            _buildTipItem('避免反光和阴影'),
            _buildTipItem('保持相机稳定'),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('开始拍照'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(String tip) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(Icons.check_circle, size: 16, color: Colors.green),
          const SizedBox(width: 8),
          Text(tip),
        ],
      ),
    );
  }
}

/// 📊 工作量引导对话框
class WorkloadGuideDialog extends StatelessWidget {
  const WorkloadGuideDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.analytics,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              '工作量统计指南',
              style: ResponsiveTextHelper.createHeadingStyle(
                context,
                level: 4,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '这里可以查看：',
              style: ResponsiveTextHelper.createBodyStyle(context),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem('每日工作量统计'),
            _buildFeatureItem('效率分析报告'),
            _buildFeatureItem('团队表现对比'),
            _buildFeatureItem('趋势预测分析'),
            const SizedBox(height: 24),
            FilledButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('查看统计'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(Icons.fiber_manual_record, size: 8),
          const SizedBox(width: 12),
          Text(feature),
        ],
      ),
    );
  }
}
