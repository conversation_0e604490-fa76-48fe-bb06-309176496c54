# PDF照片显示问题 - 全面验证方案

## 🎯 验证目标

确认以下所有场景的PDF生成都能正确显示照片：

### 1. 平板车模板
- ✅ **平板车单批次任务**
- ✅ **平板车混合任务**

### 2. 集装箱模板  
- ✅ **集装箱单批次任务**
- ✅ **集装箱混合任务**

## 🔍 代码追踪分析

### 任务创建流程

#### 单批次任务创建
```dart
// TaskService.createTask() - 单批次
final task = TaskModel(
  template: template,        // '平板车' 或 '集装箱'
  productCode: productCode,  // 单个产品代码
  batchNumber: batchNumber,  // 单个批次号
  quantity: quantity,        // 单个数量
  photos: photos,           // 从模板配置生成
  participants: participants,
);
```

#### 混合任务创建
```dart
// TaskService.createMixedTask() - 混合任务
final task = TaskModel(
  template: template,        // '平板车' 或 '集装箱'
  batches: batches,         // 多个批次信息
  photos: photos,           // 从模板配置生成（相同）
  participants: participants,
);
```

### 关键发现

1. **照片配置相同**：单批次和混合任务使用相同的照片模板
2. **区别在批次**：混合任务有多个批次，单批次任务只有一个批次
3. **PDF判断逻辑**：通过`task.batches.length > 1`判断是否为混合任务

### PDF生成中的任务类型判断

```dart
String _getTaskTypeText(String template, {TaskModel? task}) {
  // 🔧 优先根据任务实际批次数量判断
  if (task != null && task.batches.length > 1) {
    if (template.contains('平板车')) {
      return '平板车混合任务';
    } else if (template.contains('集装箱')) {
      return '集装箱混合任务';
    }
  }
  
  // 单批次任务处理
  switch (template) {
    case '平板车':
      return '平板车-单批次任务';
    case '集装箱':
      return '集装箱-单批次任务';
  }
}
```

## 🧪 测试验证计划

### 阶段1：基础功能验证

#### 测试用例1：平板车单批次任务
```
模板: '平板车'
批次: 1个 (LLD-7042, 250712F20440, 10托)
预期: PDF显示 "平板车-单批次任务"
照片: 应包含所有已拍摄的照片
```

#### 测试用例2：平板车混合任务
```
模板: '平板车'
批次: 3个 (不同产品代码和批次号)
预期: PDF显示 "平板车混合任务"
照片: 应包含所有已拍摄的照片
```

#### 测试用例3：集装箱单批次任务
```
模板: '集装箱'
批次: 1个
预期: PDF显示 "集装箱-单批次任务"
照片: 应包含所有已拍摄的照片
```

#### 测试用例4：集装箱混合任务
```
模板: '集装箱'
批次: 多个
预期: PDF显示 "集装箱混合任务"
照片: 应包含所有已拍摄的照片
```

### 阶段2：边界情况验证

#### 测试用例5：识别状态混合
```
照片状态: 包含成功、失败、未拍摄的混合状态
预期: 只显示已拍摄的照片（成功+失败）
```

#### 测试用例6：大量照片
```
照片数量: 20+张已拍摄照片
预期: 所有照片都能正确显示在PDF中
```

## 📊 验证检查点

### 1. 日志验证
查看以下关键日志信息：
```
=== 任务X详细分析 ===
任务模板: [模板名称]
批次数量: [数量]
是否混合任务: [true/false]
任务类型文本: [显示文本]
照片总数: [总数]
任务X总结: 有效照片数: [有效数]/[总数]张
```

### 2. PDF内容验证
- ✅ 封面显示正确的任务类型
- ✅ 基本信息卡片显示正确的批次信息
- ✅ 照片页面包含所有已拍摄的照片
- ✅ 工作量统计表格正确显示

### 3. 数据一致性验证
- ✅ PDF中的照片数量与日志中的有效照片数一致
- ✅ 批次信息在PDF中正确显示
- ✅ 任务类型判断正确

## 🔧 已实施的修复

### 1. 数据源统一
```dart
// 直接使用当前任务数据，避免数据同步问题
final pdfBytes = await PdfService().generateTaskReport([_currentTask!], '任务报告');
```

### 2. 照片筛选优化
```dart
// 包含所有有路径的照片，不限制状态
final validPhotos = task.photos.where((photo) {
  return photo.imagePath != null && photo.imagePath!.isNotEmpty;
}).toList();
```

### 3. 详细调试日志
```dart
// 记录任务类型、批次信息、照片状态等详细信息
AppLogger.info('任务模板: ${task.template}');
AppLogger.info('批次数量: ${task.batches.length}');
AppLogger.info('是否混合任务: ${_isMixedTask(task)}');
```

## 🎯 预期结果

修复后，所有四种任务类型都应该：

1. **正确识别任务类型**：单批次 vs 混合任务
2. **正确显示照片**：包含所有已拍摄的照片
3. **正确显示批次信息**：单批次显示一个，混合任务显示多个
4. **数据一致性**：PDF内容与页面显示一致

## 📝 验证步骤

1. **创建测试任务**：分别创建四种类型的任务
2. **拍摄照片**：每种任务拍摄几张照片（包括成功和失败的）
3. **生成PDF**：生成PDF报告并检查内容
4. **对比日志**：验证日志信息与PDF内容的一致性
5. **功能验证**：确认所有功能正常工作

通过这个全面的验证方案，我们可以确保PDF照片显示问题在所有场景下都得到永久解决。
