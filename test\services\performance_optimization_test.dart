import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/performance_manager.dart';
import 'package:loadguard/services/resource_manager.dart';

void main() {
  group('Performance Optimization Tests', () {
    group('PerformanceManager Tests', () {
      // 注意：PerformanceManager是单例，测试需要小心处理
      test('should be singleton', () {
        final instance1 = PerformanceManager.instance;
        final instance2 = PerformanceManager.instance;
        expect(instance1, same(instance2));
      });
      
      test('should provide basic functionality', () {
        final performanceManager = PerformanceManager.instance;
        expect(performanceManager, isNotNull);

        // 测试基本的统计功能
        final stats = performanceManager.getPerformanceStatistics();
        expect(stats, isNotNull);
        expect(stats.totalMetrics, greaterThanOrEqualTo(0));
      });

      test('should get memory info', () async {
        final performanceManager = PerformanceManager.instance;
        final memoryInfo = await performanceManager.getMemoryInfo();

        expect(memoryInfo.usedMemory, greaterThan(0));
        expect(memoryInfo.activeObjects, greaterThanOrEqualTo(0));
        expect(memoryInfo.timestamp, isNotNull);
      });
    });
    
    group('ResourceManager Tests', () {
      late PerformanceManager performanceManager;
      late ResourceManager resourceManager;
      
      setUp(() {
        performanceManager = PerformanceManager.instance;
        performanceManager.initialize();
        resourceManager = ResourceManager(performanceManager);
      });
      
      tearDown(() async {
        await resourceManager.dispose();
        await performanceManager.dispose();
      });
      
      test('should create and manage resource groups', () {
        final group1 = resourceManager.createResourceGroup('test_group_1');
        final group2 = resourceManager.createResourceGroup('test_group_2');
        
        expect(group1.name, 'test_group_1');
        expect(group2.name, 'test_group_2');
        
        final retrievedGroup = resourceManager.getResourceGroup('test_group_1');
        expect(retrievedGroup, same(group1));
        
        final stats = resourceManager.getResourceStatistics();
        expect(stats.resourceGroups, 2);
        expect(stats.groupStatistics['test_group_1'], 0);
        expect(stats.groupStatistics['test_group_2'], 0);
      });
      
      test('should manage resources in groups', () {
        final group = resourceManager.createResourceGroup('test_group');
        
        final resource1 = TestManagedResource();
        final resource2 = TestManagedResource();
        
        group.addResource(resource1);
        group.addResource(resource2);
        
        expect(group.resourceCount, 2);
        
        final stats = resourceManager.getResourceStatistics();
        expect(stats.totalResources, 2);
        expect(stats.groupStatistics['test_group'], 2);
        
        group.removeResource(resource1);
        expect(group.resourceCount, 1);
        
        final updatedStats = resourceManager.getResourceStatistics();
        expect(updatedStats.totalResources, 1);
      });
      
      test('should create managed timers', () {
        final group = resourceManager.createResourceGroup('timer_group');
        
        bool timerExecuted = false;
        final timer = group.createTimer(const Duration(milliseconds: 10), () {
          timerExecuted = true;
        });
        
        expect(timer.isActive, true);
        expect(group.resourceCount, 1);
        
        // 等待timer执行
        return Future.delayed(const Duration(milliseconds: 20)).then((_) {
          expect(timerExecuted, true);
          expect(timer.isDisposed, true);
        });
      });
      
      test('should create managed subscriptions', () async {
        final group = resourceManager.createResourceGroup('subscription_group');
        
        final controller = StreamController<int>();
        final receivedValues = <int>[];
        
        final subscription = group.createSubscription<int>(
          controller.stream,
          (value) => receivedValues.add(value),
        );
        
        expect(group.resourceCount, 1);
        
        controller.add(1);
        controller.add(2);
        controller.add(3);
        
        await Future.delayed(const Duration(milliseconds: 10));
        
        expect(receivedValues, [1, 2, 3]);
        
        await subscription.dispose();
        expect(subscription.isDisposed, true);
        
        await controller.close();
      });
      
      test('should create managed stream controllers', () async {
        final group = resourceManager.createResourceGroup('controller_group');
        
        final managedController = group.createStreamController<String>();
        expect(group.resourceCount, 1);
        
        final receivedValues = <String>[];
        final subscription = managedController.stream.listen(
          (value) => receivedValues.add(value),
        );
        
        managedController.add('test1');
        managedController.add('test2');
        
        await Future.delayed(const Duration(milliseconds: 10));
        
        expect(receivedValues, ['test1', 'test2']);
        
        await managedController.dispose();
        expect(managedController.isDisposed, true);
        
        await subscription.cancel();
      });
      
      test('should dispose resource groups', () async {
        final group = resourceManager.createResourceGroup('disposable_group');
        
        final resource1 = TestManagedResource();
        final resource2 = TestManagedResource();
        
        group.addResource(resource1);
        group.addResource(resource2);
        
        expect(group.resourceCount, 2);
        expect(resource1.isDisposed, false);
        expect(resource2.isDisposed, false);
        
        await resourceManager.disposeResourceGroup('disposable_group');
        
        expect(resource1.isDisposed, true);
        expect(resource2.isDisposed, true);
        
        final stats = resourceManager.getResourceStatistics();
        expect(stats.resourceGroups, 0);
        expect(stats.totalResources, 0);
      });
      
      test('should get resources of specific type', () {
        final group = resourceManager.createResourceGroup('typed_group');
        
        final timer1 = group.createTimer(const Duration(seconds: 1), () {});
        final timer2 = group.createTimer(const Duration(seconds: 2), () {});
        final controller = group.createStreamController<int>();
        
        final timers = group.getResourcesOfType<ManagedTimer>();
        expect(timers.length, 2);
        expect(timers, contains(timer1));
        expect(timers, contains(timer2));
        
        final controllers = group.getResourcesOfType<ManagedStreamController>();
        expect(controllers.length, 1);
        expect(controllers.first, same(controller));
      });
    });
    
    group('Data Model Tests', () {
      test('should create PerformanceMetric correctly', () {
        final metric = PerformanceMetric(
          name: 'test_metric',
          value: 123.45,
          timestamp: DateTime.now(),
          metadata: {'key': 'value'},
        );
        
        expect(metric.name, 'test_metric');
        expect(metric.value, 123.45);
        expect(metric.metadata['key'], 'value');
        
        final map = metric.toMap();
        expect(map['name'], 'test_metric');
        expect(map['value'], 123.45);
        expect(map['metadata']['key'], 'value');
        
        final string = metric.toString();
        expect(string, contains('test_metric'));
        expect(string, contains('123.45'));
      });
      
      test('should create MetricStatistics correctly', () {
        final stats = MetricStatistics(
          name: 'test_metric',
          count: 10,
          min: 1.0,
          max: 10.0,
          average: 5.5,
          median: 5.0,
        );
        
        expect(stats.name, 'test_metric');
        expect(stats.count, 10);
        expect(stats.min, 1.0);
        expect(stats.max, 10.0);
        expect(stats.average, 5.5);
        expect(stats.median, 5.0);
        
        final map = stats.toMap();
        expect(map['name'], 'test_metric');
        expect(map['count'], 10);
        expect(map['average'], 5.5);
      });
      
      test('should create PerformanceStatistics correctly', () {
        final metricStats = {
          'metric1': MetricStatistics(
            name: 'metric1',
            count: 5,
            min: 1.0,
            max: 5.0,
            average: 3.0,
            median: 3.0,
          ),
        };
        
        final stats = PerformanceStatistics(
          timeWindow: const Duration(hours: 1),
          totalMetrics: 5,
          metricStats: metricStats,
          memoryUsage: 100,
          activeResources: 3,
          activeTimers: 2,
          activeSubscriptions: 1,
          generatedAt: DateTime.now(),
        );
        
        expect(stats.totalMetrics, 5);
        expect(stats.memoryUsage, 100);
        expect(stats.activeResources, 3);
        expect(stats.metricStats.length, 1);
        
        final map = stats.toMap();
        expect(map['totalMetrics'], 5);
        expect(map['memoryUsage'], 100);
        expect(map['timeWindow'], 60); // 1 hour in minutes
      });
      
      test('should create empty PerformanceStatistics', () {
        final stats = PerformanceStatistics.empty();
        
        expect(stats.totalMetrics, 0);
        expect(stats.memoryUsage, 0);
        expect(stats.activeResources, 0);
        expect(stats.metricStats.isEmpty, true);
      });
    });
  });
}

/// 测试用的可释放资源
class TestDisposableResource implements Disposable {
  bool _disposed = false;
  
  bool get isDisposed => _disposed;
  
  @override
  Future<void> dispose() async {
    _disposed = true;
  }
}

/// 测试用的管理资源
class TestManagedResource extends ManagedResource {
  @override
  Future<void> dispose() async {
    if (!isDisposed) {
      markDisposed();
    }
  }
}
