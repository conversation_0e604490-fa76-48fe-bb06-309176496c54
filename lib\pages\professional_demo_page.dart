import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/material3_theme.dart';
import '../widgets/animated/professional_button.dart';
import '../widgets/animated/professional_card.dart';
import '../widgets/animated/professional_progress.dart';
import '../widgets/animated/professional_loading.dart';
import '../widgets/animated/professional_page_transition.dart';

/// 🎯 Material 3 专业级演示页面
/// 全面展示重构后的 Material 3 组件和动画效果
class ProfessionalDemoPage extends StatefulWidget {
  const ProfessionalDemoPage({Key? key}) : super(key: key);

  @override
  State<ProfessionalDemoPage> createState() => _ProfessionalDemoPageState();
}

class _ProfessionalDemoPageState extends State<ProfessionalDemoPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;
  
  bool _isDarkMode = false;
  bool _isLoading = false;
  double _progressValue = 0.7;
  int _selectedCardIndex = 0;
  
  final List<String> _demoCategories = [
    '按钮组件',
    '卡片组件', 
    '进度组件',
    '加载组件',
    '动画效果',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _demoCategories.length, vsync: this);
    _pageController = PageController();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _isDarkMode ? Material3Theme.darkTheme : Material3Theme.lightTheme,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.background,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildButtonDemo(),
                  _buildCardDemo(),
                  _buildProgressDemo(),
                  _buildLoadingDemo(),
                  _buildAnimationDemo(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('Material 3 组件演示'),
      centerTitle: true,
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      actions: [
        IconButton(
          icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
          onPressed: () {
            setState(() {
              _isDarkMode = !_isDarkMode;
            });
            HapticFeedback.lightImpact();
          },
          tooltip: _isDarkMode ? '切换到亮色模式' : '切换到暗色模式',
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  /// 构建标签栏
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: Theme.of(context).colorScheme.primary,
        labelColor: Theme.of(context).colorScheme.primary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
        ),
        tabs: _demoCategories.map((category) => Tab(text: category)).toList(),
      ),
    );
  }

  /// 构建按钮演示
  Widget _buildButtonDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Material 3 按钮组件'),
          const SizedBox(height: 16),
          
          // Filled 按钮
          _buildDemoSection(
            title: 'Filled Button（填充按钮）',
            description: '用于主要操作，具有最高的视觉优先级',
            child: Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ProfessionalButton.filled(
                  text: '主要操作',
                  onPressed: () => _showSnackBar('Filled Button 点击'),
                ),
                ProfessionalButton.filled(
                  text: '带图标',
                  icon: Icons.star,
                  onPressed: () => _showSnackBar('带图标按钮点击'),
                ),
                ProfessionalButton.filled(
                  text: '加载中',
                  isLoading: true,
                  onPressed: null,
                ),
                ProfessionalButton.filled(
                  text: '小尺寸',
                  size: Material3ButtonSize.small,
                  onPressed: () => _showSnackBar('小尺寸按钮点击'),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Outlined 按钮
          _buildDemoSection(
            title: 'Outlined Button（轮廓按钮）',
            description: '用于次要操作，具有中等的视觉优先级',
            child: Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ProfessionalButton.outlined(
                  text: '次要操作',
                  onPressed: () => _showSnackBar('Outlined Button 点击'),
                ),
                ProfessionalButton.outlined(
                  text: '带图标',
                  icon: Icons.favorite_border,
                  onPressed: () => _showSnackBar('轮廓按钮点击'),
                ),
                ProfessionalButton.outlined(
                  text: '禁用状态',
                  onPressed: null,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Text 按钮
          _buildDemoSection(
            title: 'Text Button（文本按钮）',
            description: '用于最低优先级的操作',
            child: Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ProfessionalButton.text(
                  text: '文本操作',
                  onPressed: () => _showSnackBar('Text Button 点击'),
                ),
                ProfessionalButton.text(
                  text: '带图标',
                  icon: Icons.info_outline,
                  onPressed: () => _showSnackBar('文本按钮点击'),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Elevated 按钮
          _buildDemoSection(
            title: 'Elevated Button（悬浮按钮）',
            description: '用于需要强调的操作，具有阴影效果',
            child: Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ProfessionalButton.elevated(
                  text: '悬浮操作',
                  onPressed: () => _showSnackBar('Elevated Button 点击'),
                ),
                ProfessionalButton.elevated(
                  text: '大尺寸',
                  size: Material3ButtonSize.large,
                  onPressed: () => _showSnackBar('大尺寸按钮点击'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建卡片演示
  Widget _buildCardDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Material 3 卡片组件'),
          const SizedBox(height: 16),
          
          // Elevated 卡片
          _buildDemoSection(
            title: 'Elevated Card（悬浮卡片）',
            description: '具有阴影效果的卡片，适用于需要突出显示的内容',
            child: ProfessionalCard.elevated(
              onTap: () => _showSnackBar('悬浮卡片点击'),
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '悬浮卡片标题',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '这是一个具有阴影效果的悬浮卡片，适用于展示重要内容。点击卡片可以触发相应的操作。',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Filled 卡片
          _buildDemoSection(
            title: 'Filled Card（填充卡片）',
            description: '具有背景色的卡片，无阴影效果',
            child: ProfessionalCard.filled(
              onTap: () => _showSnackBar('填充卡片点击'),
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '填充卡片标题',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '这是一个填充卡片，具有背景色但没有阴影效果，适用于平面设计风格。',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Outlined 卡片
          _buildDemoSection(
            title: 'Outlined Card（轮廓卡片）',
            description: '具有边框的卡片，无背景色和阴影',
            child: ProfessionalCard.outlined(
              onTap: () => _showSnackBar('轮廓卡片点击'),
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '轮廓卡片标题',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '这是一个轮廓卡片，只有边框没有背景色和阴影，适用于简洁的设计风格。',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 可选择卡片
          _buildDemoSection(
            title: '可选择卡片',
            description: '支持选择状态的卡片组件',
            child: Column(
              children: List.generate(3, (index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: ProfessionalCard.elevated(
                    isSelected: _selectedCardIndex == index,
                    onTap: () {
                      setState(() {
                        _selectedCardIndex = index;
                      });
                      _showSnackBar('选择了卡片 ${index + 1}');
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(
                            Icons.credit_card,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '选项 ${index + 1}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '这是选项 ${index + 1} 的描述信息',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ],
                            ),
                          ),
                          if (_selectedCardIndex == index)
                            Icon(
                              Icons.check_circle,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建进度演示
  Widget _buildProgressDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Material 3 进度组件'),
          const SizedBox(height: 16),
          
          // 线性进度指示器
          _buildDemoSection(
            title: 'Linear Progress（线性进度）',
            description: '用于显示任务的完成进度',
            child: Column(
              children: [
                ProfessionalProgress.linear(
                  value: _progressValue,
                  label: '下载进度',
                  showPercentage: true,
                ),
                const SizedBox(height: 16),
                ProfessionalProgress.linear(
                  value: 0.3,
                  label: '上传进度',
                  color: Theme.of(context).colorScheme.secondary,
                  showPercentage: true,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ProfessionalButton.outlined(
                        text: '减少进度',
                        onPressed: () {
                          setState(() {
                            _progressValue = (_progressValue - 0.1).clamp(0.0, 1.0);
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ProfessionalButton.filled(
                        text: '增加进度',
                        onPressed: () {
                          setState(() {
                            _progressValue = (_progressValue + 0.1).clamp(0.0, 1.0);
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 圆形进度指示器
          _buildDemoSection(
            title: 'Circular Progress（圆形进度）',
            description: '用于显示圆形的进度指示',
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    ProfessionalProgress.circular(
                      value: _progressValue,
                      size: Material3ProgressSize.medium,
                    ),
                    const SizedBox(height: 8),
                    const Text('确定进度'),
                  ],
                ),
                Column(
                  children: [
                    ProfessionalProgress.circular(
                      size: Material3ProgressSize.medium,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    const SizedBox(height: 8),
                    const Text('不确定进度'),
                  ],
                ),
                Column(
                  children: [
                    ProfessionalProgress.circular(
                      value: 0.8,
                      size: Material3ProgressSize.small,
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                    const SizedBox(height: 8),
                    const Text('小尺寸'),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载演示
  Widget _buildLoadingDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Material 3 加载组件'),
          const SizedBox(height: 16),
          
          // 基础加载动画
          _buildDemoSection(
            title: '基础加载动画',
            description: '各种类型的加载动画效果',
            child: Wrap(
              spacing: 24,
              runSpacing: 24,
              alignment: WrapAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    ProfessionalLoading.circular(
                      size: Material3LoadingSize.medium,
                    ),
                    const SizedBox(height: 8),
                    const Text('圆形加载'),
                  ],
                ),
                Column(
                  children: [
                    ProfessionalLoading.pulse(
                      size: Material3LoadingSize.medium,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    const SizedBox(height: 8),
                    const Text('脉冲加载'),
                  ],
                ),
                Column(
                  children: [
                    ProfessionalLoading.wave(
                      size: Material3LoadingSize.medium,
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                    const SizedBox(height: 8),
                    const Text('波浪加载'),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 骨架屏
          _buildDemoSection(
            title: '骨架屏效果',
            description: '用于内容加载时的占位效果',
            child: Column(
              children: [
                ProfessionalSkeleton.text(
                  width: double.infinity,
                  height: 20,
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    ProfessionalSkeleton.circle(size: 40),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        children: [
                          ProfessionalSkeleton.text(
                            width: double.infinity,
                            height: 16,
                          ),
                          const SizedBox(height: 8),
                          ProfessionalSkeleton.text(
                            width: 200,
                            height: 14,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ProfessionalSkeleton.rectangle(
                  width: double.infinity,
                  height: 120,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 加载覆盖层演示
          _buildDemoSection(
            title: '加载覆盖层',
            description: '全屏或局部的加载遮罩效果',
            child: Column(
              children: [
                ProfessionalButton.filled(
                  text: '显示加载覆盖层',
                  onPressed: () {
                    _showLoadingOverlay();
                  },
                ),
                const SizedBox(height: 12),
                ProfessionalButton.outlined(
                  text: '切换加载状态',
                  onPressed: () {
                    setState(() {
                      _isLoading = !_isLoading;
                    });
                  },
                ),
                if (_isLoading) ...[
                  const SizedBox(height: 16),
                  Container(
                    height: 100,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Center(
                      child: ProfessionalLoading.circular(),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建动画演示
  Widget _buildAnimationDemo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Material 3 动画效果'),
          const SizedBox(height: 16),
          
          // 页面转换动画
          _buildDemoSection(
            title: '页面转换动画',
            description: '各种页面切换的动画效果',
            child: Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                ProfessionalButton.filled(
                  text: '共享轴转换',
                  onPressed: () {
                    Material3PageTransitions.pushSharedAxis(
                      context,
                      _buildAnimationDemoPage('共享轴转换'),
                      axisType: Material3SharedAxisType.horizontal,
                    );
                  },
                ),
                ProfessionalButton.outlined(
                  text: '淡入淡出转换',
                  onPressed: () {
                    Material3PageTransitions.pushFadeThrough(
                      context,
                      _buildAnimationDemoPage('淡入淡出转换'),
                    );
                  },
                ),
                ProfessionalButton.text(
                  text: '容器变换转换',
                  onPressed: () {
                    Material3PageTransitions.pushContainerTransform(
                      context,
                      _buildAnimationDemoPage('容器变换转换'),
                    );
                  },
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Hero 动画
          _buildDemoSection(
            title: 'Hero 动画',
            description: '共享元素的转换动画',
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ProfessionalHero(
                  tag: 'hero-demo-1',
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        PageRouteBuilder(
                          pageBuilder: (context, animation, secondaryAnimation) {
                            return Scaffold(
                              appBar: AppBar(title: const Text('Hero 动画演示')),
                              body: Center(
                                child: ProfessionalHero(
                                  tag: 'hero-demo-1',
                                  child: Container(
                                    width: 200,
                                    height: 200,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.primary,
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: const Icon(
                                      Icons.star,
                                      size: 80,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.star,
                        size: 32,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
                ProfessionalHero(
                  tag: 'hero-demo-2',
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        PageRouteBuilder(
                          pageBuilder: (context, animation, secondaryAnimation) {
                            return Scaffold(
                              appBar: AppBar(title: const Text('Hero 动画演示')),
                              body: Center(
                                child: ProfessionalHero(
                                  tag: 'hero-demo-2',
                                  child: Container(
                                    width: 200,
                                    height: 200,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.secondary,
                                      shape: BoxShape.circle,
                                    ),
                                    child: const Icon(
                                      Icons.favorite,
                                      size: 80,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.favorite,
                        size: 32,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 交互反馈
          _buildDemoSection(
            title: '交互反馈',
            description: '触觉反馈和视觉反馈效果',
            child: Column(
              children: [
                ProfessionalButton.filled(
                  text: '轻触反馈',
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    _showSnackBar('轻触反馈已触发');
                  },
                ),
                const SizedBox(height: 12),
                ProfessionalButton.outlined(
                  text: '中等反馈',
                  onPressed: () {
                    HapticFeedback.mediumImpact();
                    _showSnackBar('中等反馈已触发');
                  },
                ),
                const SizedBox(height: 12),
                ProfessionalButton.text(
                  text: '重触反馈',
                  onPressed: () {
                    HapticFeedback.heavyImpact();
                    _showSnackBar('重触反馈已触发');
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
        fontWeight: FontWeight.w700,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  /// 构建演示区块
  Widget _buildDemoSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 12),
        child,
      ],
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        _showSnackBar('浮动操作按钮点击');
      },
      icon: const Icon(Icons.add),
      label: const Text('新建'),
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
    );
  }

  /// 构建动画演示页面
  Widget _buildAnimationDemoPage(String title) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.animation,
              size: 80,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              '这是一个演示页面，展示了 $title 的效果。',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ProfessionalButton.filled(
              text: '返回',
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示提示信息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        backgroundColor: Theme.of(context).colorScheme.inverseSurface,
        action: SnackBarAction(
          label: '确定',
          textColor: Theme.of(context).colorScheme.inversePrimary,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// 显示加载覆盖层
  void _showLoadingOverlay() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ProfessionalLoadingOverlay(
        message: '正在加载...',
      ),
    );
    
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.of(context).pop();
        _showSnackBar('加载完成！');
      }
    });
  }
}
