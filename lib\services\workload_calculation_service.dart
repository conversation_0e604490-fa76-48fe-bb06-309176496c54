import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/task_model_extensions.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 工作量计算服务
/// 统一的工作量计算逻辑，确保数据准确性
class WorkloadCalculationService {
  final TaskRepository _taskRepository;
  
  WorkloadCalculationService(this._taskRepository);

  /// 计算工作人员统计数据
  Future<Map<String, Map<String, dynamic>>> calculateWorkerStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final tasks = await _getFilteredTasks(startDate: startDate, endDate: endDate);
      final stats = _initializeWorkerStats();
      
      AppLogger.info('🔍 开始工作量统计，任务数量: ${tasks.length}');
      
      // 处理任务数据
      for (final task in tasks) {
        final workload = task.recognitionMetadata?['workload'];
        if (workload == null) continue;
        
        try {
          final assignment = WorkloadAssignment.fromMap(workload as Map<String, dynamic>);
          
          for (final record in assignment.records) {
            final workerId = record.workerId;
            if (stats.containsKey(workerId)) {
              final workerStats = stats[workerId]!;
              
              // 更新统计数据
              workerStats['assignedTonnage'] = 
                  (workerStats['assignedTonnage'] as double) + record.allocatedTonnage;
              workerStats['totalTasks'] = (workerStats['totalTasks'] as int) + 1;
              
              // 添加任务详情
              (workerStats['taskDetails'] as List<Map<String, dynamic>>).add({
                'taskId': task.id,
                'productCode': task.productCode,
                'batchNumber': task.batchNumber,
                'quantity': task.quantity,
                'tonnage': record.allocatedTonnage,
                'isCompleted': record.isCompleted,
                'createTime': task.createTime,
                'template': task.template,
              });
              
              // 如果任务完成，更新完成统计
              if (task.isCompleted) {
                workerStats['completedTasks'] = (workerStats['completedTasks'] as int) + 1;
                workerStats['completedTonnage'] = 
                    (workerStats['completedTonnage'] as double) + record.allocatedTonnage;
              }
              
              // 计算效率（完成率）
              final totalTasks = workerStats['totalTasks'] as int;
              final completedTasks = workerStats['completedTasks'] as int;
              workerStats['efficiency'] = totalTasks > 0 ? completedTasks / totalTasks : 0.0;
            }
          }
        } catch (e) {
          AppLogger.error('处理任务工作量数据失败: $e');
        }
      }
      
      AppLogger.info('✅ 工作量统计完成，统计了${stats.length}名工作人员');
      return stats;
    } catch (e) {
      AppLogger.error('❌ 计算工作人员统计失败: $e');
      rethrow;
    }
  }

  /// 获取工作量概览
  Future<Map<String, dynamic>> calculateWorkloadOverview({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final tasks = await _getFilteredTasks(startDate: startDate, endDate: endDate);
      final workerStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);
      final tonnageStats = await calculateActualTonnageStatistics(startDate: startDate, endDate: endDate);
      
      // 计算活跃工人数量
      final activeWorkers = workerStats.values
          .where((stat) => (stat['totalTasks'] as int) > 0)
          .length;
      
      // 计算平均效率
      double totalEfficiency = 0.0;
      int efficientWorkers = 0;
      for (final stat in workerStats.values) {
        final efficiency = stat['efficiency'] as double;
        if (efficiency > 0) {
          totalEfficiency += efficiency;
          efficientWorkers++;
        }
      }
      
      final averageEfficiency = efficientWorkers > 0 ? totalEfficiency / efficientWorkers : 0.0;
      
      return {
        'totalTasks': tasks.length,
        'completedTasks': tasks.where((t) => t.isCompleted).length,
        'activeWorkers': activeWorkers,
        'totalWorkers': workerStats.length,
        'averageEfficiency': averageEfficiency,
        'totalTonnage': tonnageStats['totalTonnage'],
        'completedTonnage': tonnageStats['completedTonnage'],
        'completionRate': tonnageStats['completionRate'],
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('❌ 计算工作量概览失败: $e');
      rethrow;
    }
  }

  /// 获取真实的装车数量统计
  Future<Map<String, double>> calculateActualTonnageStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final tasks = await _getFilteredTasks(startDate: startDate, endDate: endDate);
      double totalTonnage = 0.0;
      int totalTasks = 0;
      int completedTasks = 0;
      double completedTonnage = 0.0;
      
      for (final task in tasks) {
        final taskTonnage = task.quantity * 1.5; // 每托1.5吨
        totalTonnage += taskTonnage;
        totalTasks++;
        
        if (task.isCompleted) {
          completedTasks++;
          completedTonnage += taskTonnage;
        }
      }
      
      return {
        'totalTonnage': totalTonnage,
        'completedTonnage': completedTonnage,
        'totalTasks': totalTasks.toDouble(),
        'completedTasks': completedTasks.toDouble(),
        'completionRate': totalTasks > 0 ? completedTasks / totalTasks : 0.0,
        'averageTonnagePerTask': totalTasks > 0 ? totalTonnage / totalTasks : 0.0,
      };
    } catch (e) {
      AppLogger.error('❌ 计算装车数量统计失败: $e');
      rethrow;
    }
  }

  /// 获取效率排名
  Future<List<Map<String, dynamic>>> calculateEfficiencyRanking({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      final workerStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);
      
      final ranking = workerStats.entries
          .where((entry) => (entry.value['totalTasks'] as int) > 0)
          .map((entry) => {
            'workerId': entry.key,
            'workerName': entry.value['workerName'],
            'role': entry.value['role'],
            'warehouse': entry.value['warehouse'],
            'group': entry.value['group'],
            'efficiency': entry.value['efficiency'],
            'totalTasks': entry.value['totalTasks'],
            'completedTasks': entry.value['completedTasks'],
            'assignedTonnage': entry.value['assignedTonnage'],
            'completedTonnage': entry.value['completedTonnage'],
          })
          .toList();
      
      // 按效率排序
      ranking.sort((a, b) => (b['efficiency'] as double).compareTo(a['efficiency'] as double));
      
      return ranking.take(limit).toList();
    } catch (e) {
      AppLogger.error('❌ 计算效率排名失败: $e');
      rethrow;
    }
  }

  /// 获取个人工作量明细
  Future<Map<String, dynamic>?> getWorkerStatistics(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final allStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);
      return allStats[workerId];
    } catch (e) {
      AppLogger.error('❌ 获取个人工作量明细失败: $e');
      return null;
    }
  }

  /// 获取过滤后的任务列表
  Future<List<TaskModel>> _getFilteredTasks({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final allTasks = await _taskRepository.getAllTasks();
    
    return allTasks.where((task) {
      if (startDate != null && task.createTime.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && task.createTime.isAfter(endDate)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// 初始化工作人员统计数据
  Map<String, Map<String, dynamic>> _initializeWorkerStats() {
    final stats = <String, Map<String, dynamic>>{};
    
    // 为所有已知工作人员初始化统计数据
    for (final worker in allWorkers) {
      stats[worker.id] = {
        'workerId': worker.id,
        'workerName': worker.name,
        'role': worker.role,
        'warehouse': worker.warehouse,
        'group': worker.group,
        'assignedTonnage': 0.0,
        'completedTonnage': 0.0,
        'totalTasks': 0,
        'completedTasks': 0,
        'efficiency': 0.0,
        'taskDetails': <Map<String, dynamic>>[],
      };
    }
    
    return stats;
  }

  /// 验证工作量数据一致性
  Future<Map<String, dynamic>> validateWorkloadConsistency() async {
    try {
      final tasks = await _taskRepository.getAllTasks();
      final issues = <String>[];
      int tasksWithWorkload = 0;
      int tasksWithoutWorkload = 0;
      
      for (final task in tasks) {
        final workload = task.recognitionMetadata?['workload'];
        if (workload != null) {
          tasksWithWorkload++;
          
          try {
            final assignment = WorkloadAssignment.fromMap(workload as Map<String, dynamic>);
            
            // 验证工作量分配是否合理
            final totalAllocated = assignment.records.fold<double>(
              0.0, (sum, record) => sum + record.allocatedTonnage);
            final expectedTotal = task.quantity * 1.5;
            
            if ((totalAllocated - expectedTotal).abs() > 0.1) {
              issues.add('任务${task.id}工作量分配不一致: 分配$totalAllocated吨, 期望$expectedTotal吨');
            }
          } catch (e) {
            issues.add('任务${task.id}工作量数据格式错误: $e');
          }
        } else {
          tasksWithoutWorkload++;
        }
      }
      
      return {
        'totalTasks': tasks.length,
        'tasksWithWorkload': tasksWithWorkload,
        'tasksWithoutWorkload': tasksWithoutWorkload,
        'issues': issues,
        'isConsistent': issues.isEmpty,
        'validationTime': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('❌ 验证工作量数据一致性失败: $e');
      rethrow;
    }
  }
}
