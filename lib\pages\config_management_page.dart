import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/providers/config_notifier.dart';
import 'package:loadguard/models/config_models.dart';

/// 配置管理页面
/// 展示和管理工作人员、仓库、模板等配置数据
class ConfigManagementPage extends ConsumerWidget {
  const ConfigManagementPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final configAsync = ref.watch(configNotifierProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('配置管理'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(configNotifierProvider.notifier).refreshConfigs();
            },
            tooltip: '刷新配置',
          ),
          PopupMenuButton<String>(
            onSelected: (value) async {
              final notifier = ref.read(configNotifierProvider.notifier);
              switch (value) {
                case 'force_migration':
                  await _showForceMigrationDialog(context, notifier);
                  break;
                case 'export':
                  await _exportConfigs(context, notifier);
                  break;
                case 'import':
                  await _showImportDialog(context, notifier);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'force_migration',
                child: ListTile(
                  leading: Icon(Icons.sync_alt),
                  title: Text('强制重新迁移'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('导出配置'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('导入配置'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: configAsync.when(
        data: (config) => _buildConfigContent(context, ref, config),
        loading: () => const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在加载配置数据...'),
            ],
          ),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[400]),
              const SizedBox(height: 16),
              Text('加载配置失败: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.read(configNotifierProvider.notifier).refreshConfigs();
                },
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConfigContent(BuildContext context, WidgetRef ref, ConfigState config) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 配置概览卡片
          _buildOverviewCard(config),
          const SizedBox(height: 16),
          
          // 工作人员配置
          _buildWorkerConfigCard(context, ref, config),
          const SizedBox(height: 16),
          
          // 仓库配置
          _buildWarehouseConfigCard(context, ref, config),
          const SizedBox(height: 16),
          
          // 模板配置
          _buildTemplateConfigCard(context, ref, config),
        ],
      ),
    );
  }

  Widget _buildOverviewCard(ConfigState config) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.dashboard, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  '配置概览',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem('工作人员', config.workers.length, config.activeWorkers.length),
                ),
                Expanded(
                  child: _buildStatItem('仓库', config.warehouses.length, config.activeWarehouses.length),
                ),
                Expanded(
                  child: _buildStatItem('模板', config.templates.length, config.templates.where((t) => t.isActive).length),
                ),
                Expanded(
                  child: _buildStatItem('工作组', config.groups.length, config.groups.where((g) => g.isActive).length),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '最后更新: ${_formatDateTime(config.lastUpdated)}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, int total, int active) {
    return Column(
      children: [
        Text(
          '$active/$total',
          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: TextStyle(color: Colors.grey[600], fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildWorkerConfigCard(BuildContext context, WidgetRef ref, ConfigState config) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.people, color: Colors.green[600]),
                    const SizedBox(width: 8),
                    const Text(
                      '工作人员配置',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () => _showAddWorkerDialog(context, ref),
                  icon: const Icon(Icons.add),
                  label: const Text('添加'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (config.workers.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('暂无工作人员配置'),
                ),
              )
            else
              SizedBox(
                height: 200,
                child: ListView.builder(
                  itemCount: config.workers.take(5).length, // 只显示前5个
                  itemBuilder: (context, index) {
                    final worker = config.workers[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: worker.isActive ? Colors.green : Colors.grey,
                        child: Text(worker.name.substring(0, 1)),
                      ),
                      title: Text(worker.name),
                      subtitle: Text('${worker.role} - ${worker.warehouse} - ${worker.group}'),
                      trailing: worker.isActive 
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : const Icon(Icons.pause_circle, color: Colors.grey),
                      onTap: () => _showWorkerDetailDialog(context, ref, worker),
                    );
                  },
                ),
              ),
            if (config.workers.length > 5)
              Center(
                child: TextButton(
                  onPressed: () => _showAllWorkersDialog(context, config.workers),
                  child: Text('查看全部 ${config.workers.length} 名工作人员'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarehouseConfigCard(BuildContext context, WidgetRef ref, ConfigState config) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.warehouse, color: Colors.orange[600]),
                    const SizedBox(width: 8),
                    const Text(
                      '仓库配置',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                TextButton.icon(
                  onPressed: () => _showAddWarehouseDialog(context, ref),
                  icon: const Icon(Icons.add),
                  label: const Text('添加'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (config.warehouses.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('暂无仓库配置'),
                ),
              )
            else
              ...config.warehouses.map((warehouse) => ListTile(
                leading: Icon(
                  Icons.business,
                  color: warehouse.isActive ? Colors.orange : Colors.grey,
                ),
                title: Text(warehouse.name),
                subtitle: Text(warehouse.location),
                trailing: warehouse.isActive 
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : const Icon(Icons.pause_circle, color: Colors.grey),
                onTap: () => _showWarehouseDetailDialog(context, ref, warehouse),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateConfigCard(BuildContext context, WidgetRef ref, ConfigState config) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.description_outlined, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  '模板配置',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (config.templates.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('暂无模板配置'),
                ),
              )
            else
              ...config.templates.map((template) => ListTile(
                leading: Icon(
                  Icons.photo_camera,
                  color: template.isActive ? Colors.purple : Colors.grey,
                ),
                title: Text(template.name),
                subtitle: Text('${template.totalPhotos}张照片 (${template.requiredPhotos}张必需)'),
                trailing: template.isActive 
                    ? const Icon(Icons.check_circle, color: Colors.green)
                    : const Icon(Icons.pause_circle, color: Colors.grey),
                onTap: () => _showTemplateDetailDialog(context, template),
              )),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 对话框方法（简化实现）
  Future<void> _showForceMigrationDialog(BuildContext context, ConfigNotifier notifier) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('强制重新迁移'),
        content: const Text('这将重新迁移所有配置数据，确定继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      await notifier.forceMigration();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('强制迁移完成')),
        );
      }
    }
  }

  Future<void> _exportConfigs(BuildContext context, ConfigNotifier notifier) async {
    try {
      final exportData = await notifier.exportConfigs();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出成功: ${exportData.keys.length}项配置')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e')),
        );
      }
    }
  }

  Future<void> _showImportDialog(BuildContext context, ConfigNotifier notifier) async {
    // 简化实现 - 实际应用中需要文件选择器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('导入功能待实现')),
    );
  }

  Future<void> _showAddWorkerDialog(BuildContext context, WidgetRef ref) async {
    // 简化实现 - 实际应用中需要完整的表单
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加工作人员功能待实现')),
    );
  }

  Future<void> _showWorkerDetailDialog(BuildContext context, WidgetRef ref, WorkerConfig worker) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(worker.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${worker.id}'),
            Text('角色: ${worker.role}'),
            Text('仓库: ${worker.warehouse}'),
            Text('工作组: ${worker.group}'),
            Text('状态: ${worker.isActive ? "活跃" : "非活跃"}'),
            Text('创建时间: ${worker.createdAt != null ? _formatDateTime(worker.createdAt!) : "未知"}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAllWorkersDialog(BuildContext context, List<WorkerConfig> workers) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('所有工作人员 (${workers.length}名)'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: workers.length,
            itemBuilder: (context, index) {
              final worker = workers[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: worker.isActive ? Colors.green : Colors.grey,
                  child: Text(worker.name.substring(0, 1)),
                ),
                title: Text(worker.name),
                subtitle: Text('${worker.role} - ${worker.warehouse}'),
                trailing: worker.isActive 
                    ? const Icon(Icons.check_circle, color: Colors.green, size: 16)
                    : const Icon(Icons.pause_circle, color: Colors.grey, size: 16),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddWarehouseDialog(BuildContext context, WidgetRef ref) async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('添加仓库功能待实现')),
    );
  }

  Future<void> _showWarehouseDetailDialog(BuildContext context, WidgetRef ref, WarehouseConfig warehouse) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(warehouse.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${warehouse.id}'),
            Text('位置: ${warehouse.location}'),
            Text('描述: ${warehouse.description}'),
            Text('状态: ${warehouse.isActive ? "活跃" : "非活跃"}'),
            Text('支持模板: ${warehouse.supportedTemplates.join(", ")}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Future<void> _showTemplateDetailDialog(BuildContext context, TemplateConfigModel template) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(template.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${template.id}'),
            Text('类型: ${template.type}'),
            Text('描述: ${template.description}'),
            Text('总照片数: ${template.totalPhotos}'),
            Text('必需照片数: ${template.requiredPhotos}'),
            Text('识别照片数: ${template.recognitionPhotos}'),
            Text('状态: ${template.isActive ? "活跃" : "非活跃"}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
