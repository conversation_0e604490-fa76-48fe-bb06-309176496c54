import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../theme/material3_theme.dart';

/// 🎯 Material 3 专业级进度指示器组件
/// 完全符合 Material Design 3 规范的进度指示器系统
class ProfessionalProgress extends StatefulWidget {
  final double? value;
  final Material3ProgressType type;
  final Material3ProgressSize size;
  final Color? color;
  final Color? backgroundColor;
  final double? strokeWidth;
  final String? label;
  final bool showPercentage;
  final Duration animationDuration;
  final Widget? child;

  const ProfessionalProgress({
    Key? key,
    this.value,
    this.type = Material3ProgressType.linear,
    this.size = Material3ProgressSize.medium,
    this.color,
    this.backgroundColor,
    this.strokeWidth,
    this.label,
    this.showPercentage = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.child,
  }) : super(key: key);

  /// 创建线性进度指示器
  const ProfessionalProgress.linear({
    Key? key,
    double? value,
    Material3ProgressSize size = Material3ProgressSize.medium,
    Color? color,
    Color? backgroundColor,
    double? strokeWidth,
    String? label,
    bool showPercentage = false,
    Duration animationDuration = const Duration(milliseconds: 300),
  }) : this(
          key: key,
          value: value,
          type: Material3ProgressType.linear,
          size: size,
          color: color,
          backgroundColor: backgroundColor,
          strokeWidth: strokeWidth,
          label: label,
          showPercentage: showPercentage,
          animationDuration: animationDuration,
        );

  /// 创建圆形进度指示器
  const ProfessionalProgress.circular({
    Key? key,
    double? value,
    Material3ProgressSize size = Material3ProgressSize.medium,
    Color? color,
    Color? backgroundColor,
    double? strokeWidth,
    String? label,
    bool showPercentage = false,
    Duration animationDuration = const Duration(milliseconds: 300),
    Widget? child,
  }) : this(
          key: key,
          value: value,
          type: Material3ProgressType.circular,
          size: size,
          color: color,
          backgroundColor: backgroundColor,
          strokeWidth: strokeWidth,
          label: label,
          showPercentage: showPercentage,
          animationDuration: animationDuration,
          child: child,
        );

  @override
  State<ProfessionalProgress> createState() => _ProfessionalProgressState();
}

class _ProfessionalProgressState extends State<ProfessionalProgress>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _indeterminateController;
  late Animation<double> _progressAnimation;
  late Animation<double> _indeterminateAnimation;

  double? _previousValue;

  @override
  void initState() {
    super.initState();

    // 进度值动画控制器
    _progressController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // 不确定进度动画控制器
    _indeterminateController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 进度值动画
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.value ?? 0.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Material3Theme.emphasizedCurve,
    ));

    // 不确定进度动画
    _indeterminateAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _indeterminateController,
      curve: Curves.linear,
    ));

    // 初始化动画
    if (widget.value != null) {
      _progressController.animateTo(widget.value!);
    } else {
      _indeterminateController.repeat();
    }
  }

  @override
  void didUpdateWidget(ProfessionalProgress oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 处理进度值变化
    if (widget.value != oldWidget.value) {
      if (widget.value != null) {
        // 确定进度模式
        _indeterminateController.stop();
        _progressAnimation = Tween<double>(
          begin: _previousValue ?? 0.0,
          end: widget.value!,
        ).animate(CurvedAnimation(
          parent: _progressController,
          curve: Material3Theme.emphasizedCurve,
        ));
        _progressController.reset();
        _progressController.forward();
        _previousValue = widget.value;
      } else {
        // 不确定进度模式
        _progressController.stop();
        _indeterminateController.repeat();
      }
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _indeterminateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Material3Theme.getColorScheme(context);
    final config = _getProgressConfiguration(colorScheme);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: Material3Theme.getTextTheme(context).bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: Material3Tokens.space8),
        ],

        // 进度指示器
        widget.type == Material3ProgressType.linear
            ? _buildLinearProgress(config)
            : _buildCircularProgress(config),

        // 百分比显示
        if (widget.showPercentage && widget.value != null) ...[
          const SizedBox(height: Material3Tokens.space4),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return Text(
                '${(_progressAnimation.value * 100).toInt()}%',
                style: Material3Theme.getTextTheme(context).bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  /// 构建线性进度指示器
  Widget _buildLinearProgress(_ProgressConfiguration config) {
    return SizedBox(
      height: config.height,
      child: widget.value != null
          ? AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return LinearProgressIndicator(
                  value: _progressAnimation.value,
                  backgroundColor: config.backgroundColor,
                  valueColor: AlwaysStoppedAnimation<Color>(config.color),
                  minHeight: config.height,
                );
              },
            )
          : AnimatedBuilder(
              animation: _indeterminateAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: _IndeterminateLinearProgressPainter(
                    progress: _indeterminateAnimation.value,
                    color: config.color,
                    backgroundColor: config.backgroundColor,
                  ),
                  size: Size(double.infinity, config.height),
                );
              },
            ),
    );
  }

  /// 构建圆形进度指示器
  Widget _buildCircularProgress(_ProgressConfiguration config) {
    return SizedBox(
      width: config.size,
      height: config.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 进度指示器
          widget.value != null
              ? AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return CircularProgressIndicator(
                      value: _progressAnimation.value,
                      backgroundColor: config.backgroundColor,
                      valueColor: AlwaysStoppedAnimation<Color>(config.color),
                      strokeWidth: config.strokeWidth,
                    );
                  },
                )
              : AnimatedBuilder(
                  animation: _indeterminateAnimation,
                  builder: (context, child) {
                    return CustomPaint(
                      painter: _IndeterminateCircularProgressPainter(
                        progress: _indeterminateAnimation.value,
                        color: config.color,
                        backgroundColor: config.backgroundColor,
                        strokeWidth: config.strokeWidth,
                      ),
                      size: Size(config.size, config.size),
                    );
                  },
                ),

          // 中心内容
          if (widget.child != null)
            widget.child!
          else if (widget.showPercentage && widget.value != null)
            AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return Text(
                  '${(_progressAnimation.value * 100).toInt()}%',
                  style: Material3Theme.getTextTheme(context).labelMedium?.copyWith(
                    color: Material3Theme.getColorScheme(context).onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  /// 获取进度指示器配置
  _ProgressConfiguration _getProgressConfiguration(ColorScheme colorScheme) {
    final color = widget.color ?? colorScheme.primary;
    final backgroundColor = widget.backgroundColor ?? colorScheme.surfaceVariant;

    switch (widget.size) {
      case Material3ProgressSize.small:
        return _ProgressConfiguration(
          color: color,
          backgroundColor: backgroundColor,
          height: widget.type == Material3ProgressType.linear ? 2.0 : 4.0,
          size: 24.0,
          strokeWidth: widget.strokeWidth ?? 2.0,
        );

      case Material3ProgressSize.medium:
        return _ProgressConfiguration(
          color: color,
          backgroundColor: backgroundColor,
          height: widget.type == Material3ProgressType.linear ? 4.0 : 6.0,
          size: 32.0,
          strokeWidth: widget.strokeWidth ?? 3.0,
        );

      case Material3ProgressSize.large:
        return _ProgressConfiguration(
          color: color,
          backgroundColor: backgroundColor,
          height: widget.type == Material3ProgressType.linear ? 6.0 : 8.0,
          size: 48.0,
          strokeWidth: widget.strokeWidth ?? 4.0,
        );
    }
  }
}

/// Material 3 进度指示器类型枚举
enum Material3ProgressType {
  linear,   // 线性进度指示器
  circular, // 圆形进度指示器
}

/// Material 3 进度指示器尺寸枚举
enum Material3ProgressSize {
  small,   // 小尺寸
  medium,  // 中等尺寸
  large,   // 大尺寸
}

/// 进度指示器配置类
class _ProgressConfiguration {
  final Color color;
  final Color backgroundColor;
  final double height;
  final double size;
  final double strokeWidth;

  const _ProgressConfiguration({
    required this.color,
    required this.backgroundColor,
    required this.height,
    required this.size,
    required this.strokeWidth,
  });
}

/// 不确定线性进度绘制器
class _IndeterminateLinearProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;

  _IndeterminateLinearProgressPainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;

    final progressPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 绘制背景
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Radius.circular(size.height / 2),
      ),
      backgroundPaint,
    );

    // 计算进度条位置
    final progressWidth = size.width * 0.3;
    final totalDistance = size.width + progressWidth;
    final currentPosition = (progress * totalDistance) - progressWidth;

    if (currentPosition + progressWidth > 0 && currentPosition < size.width) {
      final startX = math.max(0.0, currentPosition);
      final endX = math.min(size.width, currentPosition + progressWidth);
      final width = endX - startX;

      if (width > 0) {
        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(startX, 0, width, size.height),
            Radius.circular(size.height / 2),
          ),
          progressPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(_IndeterminateLinearProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.backgroundColor != backgroundColor;
  }
}

/// 不确定圆形进度绘制器
class _IndeterminateCircularProgressPainter extends CustomPainter {
  final double progress;
  final Color color;
  final Color backgroundColor;
  final double strokeWidth;

  _IndeterminateCircularProgressPainter({
    required this.progress,
    required this.color,
    required this.backgroundColor,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final progressPaint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // 绘制背景圆环
    canvas.drawCircle(center, radius, backgroundPaint);

    // 计算进度弧度
    final sweepAngle = math.pi * 0.75; // 270度的弧
    final startAngle = progress * 2 * math.pi - math.pi / 2;

    // 绘制进度弧
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(_IndeterminateCircularProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.color != color ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}

/// 🎯 Material 3 步骤进度指示器组件
/// 用于显示多步骤流程的进度
class ProfessionalStepProgress extends StatefulWidget {
  final int currentStep;
  final int totalSteps;
  final List<String>? stepLabels;
  final Color? activeColor;
  final Color? inactiveColor;
  final Color? completedColor;
  final double? stepSize;
  final bool showLabels;
  final Duration animationDuration;

  const ProfessionalStepProgress({
    Key? key,
    required this.currentStep,
    required this.totalSteps,
    this.stepLabels,
    this.activeColor,
    this.inactiveColor,
    this.completedColor,
    this.stepSize,
    this.showLabels = true,
    this.animationDuration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  State<ProfessionalStepProgress> createState() => _ProfessionalStepProgressState();
}

class _ProfessionalStepProgressState extends State<ProfessionalStepProgress>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: widget.currentStep.toDouble(),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Material3Theme.emphasizedCurve,
    ));

    _animationController.forward();
  }

  @override
  void didUpdateWidget(ProfessionalStepProgress oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.currentStep != oldWidget.currentStep) {
      _animation = Tween<double>(
        begin: oldWidget.currentStep.toDouble(),
        end: widget.currentStep.toDouble(),
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Material3Theme.emphasizedCurve,
      ));

      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Material3Theme.getColorScheme(context);
    final textTheme = Material3Theme.getTextTheme(context);

    final activeColor = widget.activeColor ?? colorScheme.primary;
    final inactiveColor = widget.inactiveColor ?? colorScheme.outline;
    final completedColor = widget.completedColor ?? colorScheme.primary;
    final stepSize = widget.stepSize ?? 32.0;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Column(
          children: [
            // 步骤指示器
            Row(
              children: List.generate(widget.totalSteps, (index) {
                final isCompleted = index < _animation.value;
                final isActive = index == _animation.value.floor();
                final isInactive = index > _animation.value;

                return Expanded(
                  child: Row(
                    children: [
                      // 步骤圆圈
                      Container(
                        width: stepSize,
                        height: stepSize,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isCompleted
                              ? completedColor
                              : isActive
                                  ? activeColor
                                  : Colors.transparent,
                          border: Border.all(
                            color: isInactive ? inactiveColor : activeColor,
                            width: 2.0,
                          ),
                        ),
                        child: Center(
                          child: isCompleted
                              ? Icon(
                                  Icons.check,
                                  size: stepSize * 0.6,
                                  color: colorScheme.onPrimary,
                                )
                              : Text(
                                  '${index + 1}',
                                  style: textTheme.labelMedium?.copyWith(
                                    color: isActive
                                        ? colorScheme.onPrimary
                                        : isInactive
                                            ? inactiveColor
                                            : activeColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),

                      // 连接线
                      if (index < widget.totalSteps - 1)
                        Expanded(
                          child: Container(
                            height: 2.0,
                            margin: const EdgeInsets.symmetric(horizontal: Material3Tokens.space8),
                            decoration: BoxDecoration(
                              color: index < _animation.value - 1
                                  ? completedColor
                                  : inactiveColor,
                              borderRadius: BorderRadius.circular(1.0),
                            ),
                          ),
                        ),
                    ],
                  ),
                );
              }),
            ),

            // 步骤标签
            if (widget.showLabels && widget.stepLabels != null) ...[
              const SizedBox(height: Material3Tokens.space12),
              Row(
                children: List.generate(widget.totalSteps, (index) {
                  final isActive = index == _animation.value.floor();
                  final label = index < widget.stepLabels!.length
                      ? widget.stepLabels![index]
                      : '步骤 ${index + 1}';

                  return Expanded(
                    child: Text(
                      label,
                      textAlign: TextAlign.center,
                      style: textTheme.bodySmall?.copyWith(
                        color: isActive
                            ? colorScheme.onSurface
                            : colorScheme.onSurfaceVariant,
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }),
              ),
            ],
          ],
        );
      },
    );
  }
}

/// 🎯 Material 3 环形进度指示器组件
/// 带有渐变效果和动画的环形进度指示器
class ProfessionalRingProgress extends StatefulWidget {
  final double value;
  final double size;
  final double strokeWidth;
  final List<Color>? gradientColors;
  final Color? backgroundColor;
  final Widget? child;
  final bool showPercentage;
  final Duration animationDuration;

  const ProfessionalRingProgress({
    Key? key,
    required this.value,
    this.size = 120.0,
    this.strokeWidth = 8.0,
    this.gradientColors,
    this.backgroundColor,
    this.child,
    this.showPercentage = false,
    this.animationDuration = const Duration(milliseconds: 1000),
  }) : super(key: key);

  @override
  State<ProfessionalRingProgress> createState() => _ProfessionalRingProgressState();
}

class _ProfessionalRingProgressState extends State<ProfessionalRingProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: widget.value,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Material3Theme.emphasizedCurve,
    ));

    _controller.forward();
  }

  @override
  void didUpdateWidget(ProfessionalRingProgress oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.value != oldWidget.value) {
      _animation = Tween<double>(
        begin: _animation.value,
        end: widget.value,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Material3Theme.emphasizedCurve,
      ));

      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Material3Theme.getColorScheme(context);
    final gradientColors = widget.gradientColors ?? [
      colorScheme.primary,
      colorScheme.tertiary,
    ];

    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 进度环
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                painter: _RingProgressPainter(
                  progress: _animation.value,
                  strokeWidth: widget.strokeWidth,
                  gradientColors: gradientColors,
                  backgroundColor: widget.backgroundColor ?? colorScheme.surfaceVariant,
                ),
                size: Size(widget.size, widget.size),
              );
            },
          ),

          // 中心内容
          if (widget.child != null)
            widget.child!
          else if (widget.showPercentage)
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${(_animation.value * 100).toInt()}%',
                      style: Material3Theme.getTextTheme(context).headlineSmall?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      '完成',
                      style: Material3Theme.getTextTheme(context).bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                );
              },
            ),
        ],
      ),
    );
  }
}

/// 环形进度绘制器
class _RingProgressPainter extends CustomPainter {
  final double progress;
  final double strokeWidth;
  final List<Color> gradientColors;
  final Color backgroundColor;

  _RingProgressPainter({
    required this.progress,
    required this.strokeWidth,
    required this.gradientColors,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;

    // 背景圆环
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // 进度圆环
    if (progress > 0) {
      final rect = Rect.fromCircle(center: center, radius: radius);
      final gradient = SweepGradient(
        colors: gradientColors,
        startAngle: -math.pi / 2,
        endAngle: -math.pi / 2 + 2 * math.pi * progress,
      );

      final progressPaint = Paint()
        ..shader = gradient.createShader(rect)
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      canvas.drawArc(
        rect,
        -math.pi / 2,
        2 * math.pi * progress,
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(_RingProgressPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.gradientColors != gradientColors ||
        oldDelegate.backgroundColor != backgroundColor;
  }
}