import 'dart:io';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/async_upload_service.dart';
// ✅ 更新：使用统一的日志系统
import 'package:loadguard/utils/app_logger.dart';

/// 🔍 识别流程诊断工具
/// 用于调试和验证图片识别流程是否正常工作
class RecognitionDiagnostics {
  static final MLKitTextRecognitionService _recognitionService =
      MLKitTextRecognitionService();
  static final AsyncUploadService _uploadService = AsyncUploadService();

  /// 🚀 完整诊断识别流程
  static Future<Map<String, dynamic>> diagnoseRecognitionFlow(
      String imagePath) async {
    final diagnosticResult = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'imagePath': imagePath,
      'steps': <Map<String, dynamic>>[],
      'errors': <String>[],
      'success': false,
      'totalDuration': 0,
    };

    final stopwatch = Stopwatch()..start();

    try {
      // 🔍 Step 1: 检查图片文件
      await _addStep(diagnosticResult, 'file_check', () async {
        final file = File(imagePath);
        if (!await file.exists()) {
          throw Exception('图片文件不存在');
        }
        final fileSize = await file.length();
        if (fileSize == 0) {
          throw Exception('图片文件为空');
        }
        return {
          'fileExists': true,
          'fileSize': fileSize,
          'fileSizeKB': (fileSize / 1024).toStringAsFixed(2),
        };
      });

      // 🔍 Step 2: 初始化服务
      await _addStep(diagnosticResult, 'service_init', () async {
        await _recognitionService.initialize();
        await _uploadService.initialize();
        return {
          'recognitionServiceInitialized': true,
          'uploadServiceInitialized': true,
        };
      });

      // 🔍 Step 3: 测试MLKit识别
      await _addStep(diagnosticResult, 'mlkit_recognition', () async {
        final results = await _recognitionService.processImage(
          imagePath,
          onProgress: (progress, status) {
            AppLogger.debug('诊断进度: ${(progress * 100).toInt()}% - $status',
                tag: 'Diagnostics');
          },
        );

        return {
          'resultsCount': results.length,
          'hasResults': results.isNotEmpty,
          'firstResult': results.isNotEmpty
              ? {
                  'text': results.first.ocrText,
                  'confidence': results.first.confidence,
                  'boundingBox': results.first.boundingBox,
                }
              : null,
        };
      });

      // 🔍 Step 4: 测试异步上传流程
      await _addStep(diagnosticResult, 'async_upload_flow', () async {
        bool recognitionSuccessTriggered = false;
        bool recognitionFailureTriggered = false;
        String? recognitionResult;
        String? recognitionError;

        final uploadId = await _uploadService.uploadPhotoAsync(
          taskId: 'diagnostic_task',
          photoId: 'diagnostic_photo',
          imagePath: imagePath,
          needRecognition: true,
          onRecognitionSuccess: (photoId, result) {
            recognitionSuccessTriggered = true;
            recognitionResult = result.ocrText;
            Log.i('诊断成功回调触发: $recognitionResult', tag: 'Diagnostics');
          },
          onRecognitionFailure: (photoId, error) {
            recognitionFailureTriggered = true;
            recognitionError = error;
            Log.w('诊断失败回调触发: $error', tag: 'Diagnostics');
          },
        );

        // 等待回调触发
        int waitCount = 0;
        while (!recognitionSuccessTriggered &&
            !recognitionFailureTriggered &&
            waitCount < 60) {
          await Future.delayed(const Duration(seconds: 1));
          waitCount++;
        }

        return {
          'uploadId': uploadId,
          'successTriggered': recognitionSuccessTriggered,
          'failureTriggered': recognitionFailureTriggered,
          'recognitionResult': recognitionResult,
          'recognitionError': recognitionError,
          'waitTime': waitCount,
          'timedOut': waitCount >= 60,
        };
      });

      diagnosticResult['success'] = true;
    } catch (e, stackTrace) {
      diagnosticResult['errors'].add('诊断过程异常: $e');
      Log.e('识别流程诊断失败', error: e, stackTrace: stackTrace, tag: 'Diagnostics');
    }

    stopwatch.stop();
    diagnosticResult['totalDuration'] = stopwatch.elapsedMilliseconds;

    return diagnosticResult;
  }

  /// 🔧 添加诊断步骤
  static Future<void> _addStep(
    Map<String, dynamic> diagnosticResult,
    String stepName,
    Future<Map<String, dynamic>> Function() stepFunction,
  ) async {
    final stepStopwatch = Stopwatch()..start();

    try {
      AppLogger.info('开始诊断步骤: $stepName', tag: 'Diagnostics');
      final stepResult = await stepFunction();
      stepStopwatch.stop();

      diagnosticResult['steps'].add({
        'name': stepName,
        'success': true,
        'duration': stepStopwatch.elapsedMilliseconds,
        'result': stepResult,
      });

      AppLogger.info('诊断步骤完成: $stepName (${stepStopwatch.elapsedMilliseconds}ms)',
          tag: 'Diagnostics');
    } catch (e) {
      stepStopwatch.stop();

      diagnosticResult['steps'].add({
        'name': stepName,
        'success': false,
        'duration': stepStopwatch.elapsedMilliseconds,
        'error': e.toString(),
      });

      diagnosticResult['errors'].add('步骤 $stepName 失败: $e');
      AppLogger.error('诊断步骤失败: $stepName', error: e, tag: 'Diagnostics');
      rethrow;
    }
  }

  /// 📊 生成诊断报告
  static String generateReport(Map<String, dynamic> diagnosticResult) {
    final buffer = StringBuffer();

    buffer.writeln('🔍 图片识别流程诊断报告');
    buffer.writeln('=' * 50);
    buffer.writeln('时间: ${diagnosticResult['timestamp']}');
    buffer.writeln('图片路径: ${diagnosticResult['imagePath']}');
    buffer.writeln('总耗时: ${diagnosticResult['totalDuration']}ms');
    buffer.writeln('诊断结果: ${diagnosticResult['success'] ? '✅ 成功' : '❌ 失败'}');
    buffer.writeln();

    // 步骤详情
    buffer.writeln('📋 诊断步骤:');
    for (final step in diagnosticResult['steps']) {
      final status = step['success'] ? '✅' : '❌';
      buffer.writeln('  $status ${step['name']} (${step['duration']}ms)');

      if (step['success'] && step['result'] != null) {
        final result = step['result'] as Map<String, dynamic>;
        result.forEach((key, value) {
          buffer.writeln('    $key: $value');
        });
      } else if (!step['success']) {
        buffer.writeln('    错误: ${step['error']}');
      }
      buffer.writeln();
    }

    // 错误汇总
    if (diagnosticResult['errors'].isNotEmpty) {
      buffer.writeln('🚨 错误汇总:');
      for (final error in diagnosticResult['errors']) {
        buffer.writeln('  ❌ $error');
      }
    }

    return buffer.toString();
  }
}
