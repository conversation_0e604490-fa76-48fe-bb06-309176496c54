import 'dart:async';
import 'dart:collection';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🗄️ 任务缓存服务
/// 
/// 专注于智能缓存管理，提供高性能的数据访问
/// 实现LRU缓存策略和自动过期机制
class TaskCacheService {
  // LRU缓存实现
  final LinkedHashMap<String, CacheEntry<TaskModel>> _taskCache = LinkedHashMap();
  final LinkedHashMap<String, CacheEntry<List<TaskModel>>> _queryCache = LinkedHashMap();
  
  // 缓存配置
  static const int _maxTaskCacheSize = 200;
  static const int _maxQueryCacheSize = 50;
  static const Duration _defaultTtl = Duration(minutes: 10);
  static const Duration _queryTtl = Duration(minutes: 5);
  
  // 缓存统计
  int _hitCount = 0;
  int _missCount = 0;
  int _evictionCount = 0;
  
  // 自动清理定时器
  Timer? _cleanupTimer;

  TaskCacheService() {
    _startCleanupTimer();
  }

  /// 获取任务（带缓存）
  TaskModel? getTask(String taskId) {
    final entry = _taskCache[taskId];
    
    if (entry == null) {
      _missCount++;
      AppLogger.debug('🗄️ 缓存未命中: $taskId', tag: 'TaskCache');
      return null;
    }
    
    if (entry.isExpired) {
      _taskCache.remove(taskId);
      _missCount++;
      AppLogger.debug('🗄️ 缓存已过期: $taskId', tag: 'TaskCache');
      return null;
    }
    
    // LRU: 移动到末尾
    _taskCache.remove(taskId);
    _taskCache[taskId] = entry;
    
    _hitCount++;
    AppLogger.debug('🗄️ 缓存命中: $taskId', tag: 'TaskCache');
    return entry.value;
  }

  /// 缓存任务
  void putTask(TaskModel task, {Duration? ttl}) {
    final entry = CacheEntry(
      value: task,
      expireTime: DateTime.now().add(ttl ?? _defaultTtl),
    );
    
    _taskCache[task.id] = entry;
    
    // LRU: 检查缓存大小
    _evictTasksIfNeeded();
    
    AppLogger.debug('🗄️ 缓存任务: ${task.id}', tag: 'TaskCache');
  }

  /// 批量缓存任务
  void putTasks(List<TaskModel> tasks, {Duration? ttl}) {
    for (final task in tasks) {
      putTask(task, ttl: ttl);
    }
    AppLogger.debug('🗄️ 批量缓存任务: ${tasks.length}个', tag: 'TaskCache');
  }

  /// 移除任务缓存
  void removeTask(String taskId) {
    final removed = _taskCache.remove(taskId);
    if (removed != null) {
      AppLogger.debug('🗄️ 移除任务缓存: $taskId', tag: 'TaskCache');
    }
  }

  /// 获取查询结果（带缓存）
  List<TaskModel>? getQueryResult(String queryKey) {
    final entry = _queryCache[queryKey];
    
    if (entry == null) {
      _missCount++;
      AppLogger.debug('🗄️ 查询缓存未命中: $queryKey', tag: 'TaskCache');
      return null;
    }
    
    if (entry.isExpired) {
      _queryCache.remove(queryKey);
      _missCount++;
      AppLogger.debug('🗄️ 查询缓存已过期: $queryKey', tag: 'TaskCache');
      return null;
    }
    
    // LRU: 移动到末尾
    _queryCache.remove(queryKey);
    _queryCache[queryKey] = entry;
    
    _hitCount++;
    AppLogger.debug('🗄️ 查询缓存命中: $queryKey', tag: 'TaskCache');
    return List.from(entry.value);
  }

  /// 缓存查询结果
  void putQueryResult(String queryKey, List<TaskModel> result, {Duration? ttl}) {
    final entry = CacheEntry<List<TaskModel>>(
      value: List<TaskModel>.from(result),
      expireTime: DateTime.now().add(ttl ?? _queryTtl),
    );
    
    _queryCache[queryKey] = entry;
    
    // LRU: 检查缓存大小
    _evictQueriesIfNeeded();
    
    AppLogger.debug('🗄️ 缓存查询结果: $queryKey, ${result.length}个任务', tag: 'TaskCache');
  }

  /// 生成查询缓存键
  String generateQueryKey({
    String? template,
    String? productCode,
    TaskStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) {
    final parts = <String>[];
    if (template != null) parts.add('template:$template');
    if (productCode != null) parts.add('product:$productCode');
    if (status != null) parts.add('status:$status');
    if (startDate != null) parts.add('start:${startDate.millisecondsSinceEpoch}');
    if (endDate != null) parts.add('end:${endDate.millisecondsSinceEpoch}');
    if (limit != null) parts.add('limit:$limit');
    
    return parts.join('|');
  }

  /// 使任务相关的查询缓存失效
  void invalidateTaskQueries(String taskId) {
    final keysToRemove = <String>[];
    
    for (final key in _queryCache.keys) {
      final entry = _queryCache[key]!;
      if (entry.value.any((task) => task.id == taskId)) {
        keysToRemove.add(key);
      }
    }
    
    for (final key in keysToRemove) {
      _queryCache.remove(key);
    }
    
    if (keysToRemove.isNotEmpty) {
      AppLogger.debug('🗄️ 使查询缓存失效: ${keysToRemove.length}个', tag: 'TaskCache');
    }
  }

  /// 使所有查询缓存失效
  void invalidateAllQueries() {
    final count = _queryCache.length;
    _queryCache.clear();
    AppLogger.debug('🗄️ 清除所有查询缓存: ${count}个', tag: 'TaskCache');
  }

  /// 预热缓存
  Future<void> warmupCache(List<TaskModel> tasks) async {
    AppLogger.info('🗄️ 开始预热缓存: ${tasks.length}个任务', tag: 'TaskCache');
    
    // 缓存热点任务
    final hotTasks = tasks.take(50).toList();
    putTasks(hotTasks, ttl: Duration(hours: 1));
    
    // 缓存常用查询
    final recentTasks = tasks.where((task) {
      final daysSinceCreated = DateTime.now().difference(task.createTime).inDays;
      return daysSinceCreated <= 7;
    }).toList();
    
    putQueryResult('recent:7days', recentTasks, ttl: Duration(minutes: 30));
    
    // 按状态分组缓存
    for (final status in TaskStatus.values) {
      final statusTasks = tasks.where((task) => task.status == status).toList();
      if (statusTasks.isNotEmpty) {
        putQueryResult('status:$status', statusTasks, ttl: Duration(minutes: 15));
      }
    }
    
    AppLogger.info('✅ 缓存预热完成', tag: 'TaskCache');
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getStats() {
    final totalRequests = _hitCount + _missCount;
    final hitRate = totalRequests > 0 ? _hitCount / totalRequests : 0.0;
    
    return {
      'taskCacheSize': _taskCache.length,
      'queryCacheSize': _queryCache.length,
      'hitCount': _hitCount,
      'missCount': _missCount,
      'evictionCount': _evictionCount,
      'hitRate': hitRate,
      'totalRequests': totalRequests,
      'maxTaskCacheSize': _maxTaskCacheSize,
      'maxQueryCacheSize': _maxQueryCacheSize,
    };
  }

  /// 清除所有缓存
  void clearAll() {
    final taskCount = _taskCache.length;
    final queryCount = _queryCache.length;
    
    _taskCache.clear();
    _queryCache.clear();
    
    AppLogger.info('🗄️ 清除所有缓存: ${taskCount}个任务, ${queryCount}个查询', tag: 'TaskCache');
  }

  /// 清除过期缓存
  void clearExpired() {
    final now = DateTime.now();
    int expiredCount = 0;
    
    // 清除过期任务缓存
    _taskCache.removeWhere((key, entry) {
      if (entry.expireTime.isBefore(now)) {
        expiredCount++;
        return true;
      }
      return false;
    });
    
    // 清除过期查询缓存
    _queryCache.removeWhere((key, entry) {
      if (entry.expireTime.isBefore(now)) {
        expiredCount++;
        return true;
      }
      return false;
    });
    
    if (expiredCount > 0) {
      AppLogger.debug('🗄️ 清除过期缓存: ${expiredCount}个', tag: 'TaskCache');
    }
  }

  /// 释放资源
  void dispose() {
    _cleanupTimer?.cancel();
    clearAll();
    AppLogger.info('🗄️ 释放TaskCacheService资源', tag: 'TaskCache');
  }

  // 私有方法

  /// 启动清理定时器
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(Duration(minutes: 5), (_) {
      clearExpired();
    });
  }

  /// LRU淘汰任务缓存
  void _evictTasksIfNeeded() {
    while (_taskCache.length > _maxTaskCacheSize) {
      final firstKey = _taskCache.keys.first;
      _taskCache.remove(firstKey);
      _evictionCount++;
    }
  }

  /// LRU淘汰查询缓存
  void _evictQueriesIfNeeded() {
    while (_queryCache.length > _maxQueryCacheSize) {
      final firstKey = _queryCache.keys.first;
      _queryCache.remove(firstKey);
      _evictionCount++;
    }
  }
}

/// 缓存条目
class CacheEntry<T> {
  final T value;
  final DateTime expireTime;
  final DateTime createTime;

  CacheEntry({
    required this.value,
    required this.expireTime,
  }) : createTime = DateTime.now();

  bool get isExpired => DateTime.now().isAfter(expireTime);
  
  Duration get age => DateTime.now().difference(createTime);
  
  Duration get timeToLive => expireTime.difference(DateTime.now());
}

/// 缓存策略
enum CacheStrategy {
  /// 写入时缓存
  writeThrough,
  /// 写入后缓存
  writeBack,
  /// 只读缓存
  readOnly,
}

/// 缓存配置
class CacheConfig {
  final int maxSize;
  final Duration defaultTtl;
  final CacheStrategy strategy;
  final bool enableStats;

  const CacheConfig({
    this.maxSize = 100,
    this.defaultTtl = const Duration(minutes: 10),
    this.strategy = CacheStrategy.writeThrough,
    this.enableStats = true,
  });
}
