import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/unified_recognition_service.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🎯 智能降级识别服务
/// 
/// 自动尝试多种识别策略，从快到慢，直到成功：
/// 1. 快速MLKit识别（500ms）
/// 2. 如果失败，尝试标准优化（1500ms）
/// 3. 如果仍失败，使用完整统一识别（3000ms）
class SmartFallbackRecognition {
  static SmartFallbackRecognition? _instance;
  static SmartFallbackRecognition get instance => _instance ??= SmartFallbackRecognition._();
  
  SmartFallbackRecognition._();
  
  late final MLKitTextRecognitionService _mlkitService;
  late final UnifiedRecognitionService _unifiedService;
  bool _isInitialized = false;
  
  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _mlkitService = MLKitTextRecognitionService.instance;
    await _mlkitService.initialize();
    
    _unifiedService = UnifiedRecognitionService.instance;
    // 延迟初始化统一服务
    
    _isInitialized = true;
    AppLogger.info('✅ 智能降级识别服务初始化完成');
  }
  
  /// 🚀 智能降级识别
  Future<RecognitionResult?> recognizeWithFallback({
    required String imagePath,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
    Function(double progress, String status)? onProgress,
    Duration maxTimeout = const Duration(seconds: 10), // 最大超时时间
  }) async {
    if (!_isInitialized) await initialize();
    
    final startTime = DateTime.now();
    AppLogger.info('🎯 开始智能降级识别');
    
    try {
      // 第一步：快速MLKit识别（500-1000ms）
      onProgress?.call(0.1, '尝试快速识别...');
      final fastResult = await _tryFastRecognition(
        imagePath, presetProductCode, presetBatchNumber, allBatches);
      
      if (fastResult != null && fastResult.matchesPreset) {
        onProgress?.call(1.0, '快速识别成功');
        AppLogger.info('✅ 快速识别成功，耗时: ${DateTime.now().difference(startTime).inMilliseconds}ms');
        return fastResult;
      }
      
      // 检查超时
      if (DateTime.now().difference(startTime) > maxTimeout) {
        onProgress?.call(1.0, '识别超时');
        return fastResult; // 返回快速识别结果，即使不匹配
      }
      
      // 第二步：标准优化识别（1000-2000ms）
      onProgress?.call(0.4, '尝试标准优化识别...');
      final standardResult = await _tryStandardRecognition(
        imagePath, presetProductCode, presetBatchNumber, allBatches);
      
      if (standardResult != null && standardResult.matchesPreset) {
        onProgress?.call(1.0, '标准识别成功');
        AppLogger.info('✅ 标准识别成功，耗时: ${DateTime.now().difference(startTime).inMilliseconds}ms');
        return standardResult;
      }
      
      // 检查超时
      if (DateTime.now().difference(startTime) > maxTimeout) {
        onProgress?.call(1.0, '识别超时');
        return standardResult ?? fastResult;
      }
      
      // 第三步：完整统一识别（2000-5000ms）
      onProgress?.call(0.7, '尝试高精度识别...');
      final precisionResult = await _tryPrecisionRecognition(
        imagePath, presetProductCode, presetBatchNumber, allBatches, onProgress);
      
      onProgress?.call(1.0, '识别完成');
      AppLogger.info('✅ 降级识别完成，耗时: ${DateTime.now().difference(startTime).inMilliseconds}ms');
      
      // 返回最佳结果
      return precisionResult ?? standardResult ?? fastResult;
      
    } catch (e) {
      AppLogger.error('❌ 智能降级识别失败: $e');
      onProgress?.call(1.0, '识别失败');
      return null;
    }
  }
  
  /// 快速识别尝试
  Future<RecognitionResult?> _tryFastRecognition(
    String imagePath,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
  ) async {
    try {
      if (allBatches != null && allBatches.length > 1) {
        // 混合任务
        return await _mlkitService.processImageWithSmartMatching(
          imagePath: imagePath,
          matchConfig: RecognitionMatchConfig(
            batches: allBatches,
            isMixedLoad: true,
            minConfidence: 0.6,
            enableFuzzyMatch: false,
          ),
        ).timeout(const Duration(seconds: 2));
      } else {
        // 单批次任务
        final results = await _mlkitService.processImage(
          imagePath,
          presetProductCode: presetProductCode,
          presetBatchNumber: presetBatchNumber,
        ).timeout(const Duration(seconds: 2));
        return results.isNotEmpty ? results.first : null;
      }
    } catch (e) {
      AppLogger.warning('快速识别失败: $e');
      return null;
    }
  }
  
  /// 标准识别尝试
  Future<RecognitionResult?> _tryStandardRecognition(
    String imagePath,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
  ) async {
    try {
      // TODO: 实现轻量级图像优化
      // 例如：简单的对比度增强
      
      if (allBatches != null && allBatches.length > 1) {
        return await _mlkitService.processImageWithSmartMatching(
          imagePath: imagePath,
          matchConfig: RecognitionMatchConfig(
            batches: allBatches,
            isMixedLoad: true,
            minConfidence: 0.7,
            enableFuzzyMatch: true,
          ),
        ).timeout(const Duration(seconds: 3));
      } else {
        final results = await _mlkitService.processImage(
          imagePath,
          presetProductCode: presetProductCode,
          presetBatchNumber: presetBatchNumber,
        ).timeout(const Duration(seconds: 3));
        return results.isNotEmpty ? results.first : null;
      }
    } catch (e) {
      AppLogger.warning('标准识别失败: $e');
      return null;
    }
  }
  
  /// 高精度识别尝试
  Future<RecognitionResult?> _tryPrecisionRecognition(
    String imagePath,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
    Function(double progress, String status)? onProgress,
  ) async {
    try {
      // 初始化统一识别服务
      if (!_unifiedService._isInitialized) {
        await _unifiedService.initialize();
      }
      
      final unifiedResult = await _unifiedService.recognizeUnified(
        imagePath,
        enableQRCode: true,
        enableNetworkValidation: false,
        onProgress: (progress, status) {
          onProgress?.call(0.7 + progress * 0.3, status);
        },
      ).timeout(const Duration(seconds: 8));
      
      // 转换为RecognitionResult
      if (unifiedResult.hasText && unifiedResult.textResults.isNotEmpty) {
        final textResult = unifiedResult.textResults.first;
        final matchesPreset = _checkMatch(textResult.ocrText, presetProductCode, presetBatchNumber, allBatches);
        
        return RecognitionResult(
          ocrText: textResult.ocrText,
          extractedProductCode: presetProductCode,
          extractedBatchNumber: presetBatchNumber,
          matchesPreset: matchesPreset,
          confidence: unifiedResult.overallConfidence,
          isQrOcrConsistent: unifiedResult.isConsistent,
          status: matchesPreset ? RecognitionStatus.completed : RecognitionStatus.failed,
          metadata: {
            'unifiedRecognition': true,
            'fallbackLevel': 'precision',
            'hasQRCode': unifiedResult.hasQRCode,
            'isConsistent': unifiedResult.isConsistent,
            'processingTime': unifiedResult.processingTime,
          },
        );
      } else if (unifiedResult.hasQRCode && unifiedResult.qrResults.isNotEmpty) {
        final qrResult = unifiedResult.qrResults.first;
        final matchesPreset = _checkMatch(qrResult.ocrText, presetProductCode, presetBatchNumber, allBatches);
        
        return RecognitionResult(
          ocrText: qrResult.ocrText,
          qrCode: qrResult.qrCode,
          extractedProductCode: presetProductCode,
          extractedBatchNumber: presetBatchNumber,
          matchesPreset: matchesPreset,
          confidence: unifiedResult.overallConfidence,
          isQrOcrConsistent: true,
          status: matchesPreset ? RecognitionStatus.completed : RecognitionStatus.failed,
          metadata: {
            'unifiedRecognition': true,
            'fallbackLevel': 'precision',
            'fromQRCode': true,
            'processingTime': unifiedResult.processingTime,
          },
        );
      }
      
      return null;
    } catch (e) {
      AppLogger.warning('高精度识别失败: $e');
      return null;
    }
  }
  
  /// 检查匹配
  bool _checkMatch(String? ocrText, String? presetProductCode, String? presetBatchNumber, List<BatchInfo>? allBatches) {
    if (ocrText == null || ocrText.isEmpty) return false;
    
    if (allBatches != null && allBatches.isNotEmpty) {
      for (final batch in allBatches) {
        if (_checkPresetMatch(ocrText, batch.productCode, batch.batchNumber)) {
          return true;
        }
      }
      return false;
    } else {
      return _checkPresetMatch(ocrText, presetProductCode, presetBatchNumber);
    }
  }
  
  /// 检查预设匹配
  bool _checkPresetMatch(String ocrText, String? productCode, String? batchNumber) {
    if (productCode == null && batchNumber == null) return true;
    
    final cleanText = ocrText.replaceAll(RegExp(r'\s+'), '').toUpperCase();
    
    bool productMatch = true;
    bool batchMatch = true;
    
    if (productCode != null) {
      final cleanProductCode = productCode.replaceAll(RegExp(r'\s+'), '').toUpperCase();
      productMatch = cleanText.contains(cleanProductCode);
    }
    
    if (batchNumber != null) {
      final cleanBatchNumber = batchNumber.replaceAll(RegExp(r'\s+'), '').toUpperCase();
      batchMatch = cleanText.contains(cleanBatchNumber);
    }
    
    return productMatch && batchMatch;
  }
}
