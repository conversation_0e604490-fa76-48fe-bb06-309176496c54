import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/performance_manager.dart';
import 'package:loadguard/services/app_security_service.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/providers/task_notifier.dart';

/// 🎯 核心服务Providers - 确保单例模式和正确的生命周期管理

/// 📋 任务服务Provider - 纯业务逻辑服务，不再管理状态
final taskServiceProvider = Provider<TaskService>((ref) {
  ref.keepAlive(); // 现代keepAlive语法

  final service = TaskService();

  // 立即初始化服务
  service.initialize().catchError((e) {
    // 使用更好的日志记录方式
    debugPrint('❌ TaskService初始化失败: $e');
  });

  // 确保服务在Provider销毁时正确清理
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// 🤖 ML Kit文本识别服务Provider - 手动实现
final mlkitServiceProvider = Provider<MLKitTextRecognitionService>((ref) {
  final service = MLKitTextRecognitionService();

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// ⚡ 性能管理服务Provider - 手动实现
final performanceManagerProvider = Provider<PerformanceManager>((ref) {
  final manager = PerformanceManager.instance;

  ref.onDispose(() {
    manager.dispose();
  });

  return manager;
});

/// 🔒 应用安全服务Provider - 手动实现
final appSecurityServiceProvider = Provider<AppSecurityService>((ref) {
  return AppSecurityService();
});

// TaskNotifier已移动到 lib/providers/task_notifier.dart
// 这里保留注释以避免混淆

/// 🔄 当前任务Provider - 手动实现FamilyNotifier
class CurrentTaskNotifier extends FamilyNotifier<TaskModel?, String?> {
  @override
  TaskModel? build(String? taskId) {
    if (taskId == null) return null;

    // 从任务状态中获取当前任务
    final taskStateAsync = ref.read(taskNotifierProvider);
    return taskStateAsync.when(
      data: (taskState) => taskState.tasks.cast<TaskModel?>().firstWhere(
        (task) => task?.id == taskId,
        orElse: () => null,
      ),
      loading: () => null,
      error: (error, stackTrace) => null,
    );
  }
  
  /// 更新当前任务
  void updateCurrentTask(TaskModel task) {
    state = task;
    
    // 同时更新任务状态
    ref.read(taskNotifierProvider.notifier).updateTask(task);
  }
  
  /// 更新照片路径
  void updatePhotoPath(String photoId, String imagePath) {
    if (state == null) return;
    
    final updatedPhotos = state!.photos.map((photo) {
      if (photo.id == photoId) {
        return photo.copyWith(imagePath: imagePath);
      }
      return photo;
    }).toList();
    
    final updatedTask = state!.copyWith(photos: updatedPhotos);
    updateCurrentTask(updatedTask);
  }
}

/// 🎯 应用状态Provider - 手动实现Notifier
class AppStateNotifier extends Notifier<AppState> {
  @override
  AppState build() {
    return const AppState(
      isLoading: false,
      isOnline: true,
      currentRoute: '/',
    );
  }
  
  void setLoading(bool isLoading) {
    state = state.copyWith(isLoading: isLoading);
  }
  
  void setOnlineStatus(bool isOnline) {
    state = state.copyWith(isOnline: isOnline);
  }
  
  void setCurrentRoute(String route) {
    state = state.copyWith(currentRoute: route);
  }
}

/// 🎯 应用状态数据类
class AppState {
  final bool isLoading;
  final bool isOnline;
  final String currentRoute;
  
  const AppState({
    required this.isLoading,
    required this.isOnline,
    required this.currentRoute,
  });
  
  AppState copyWith({
    bool? isLoading,
    bool? isOnline,
    String? currentRoute,
  }) {
    return AppState(
      isLoading: isLoading ?? this.isLoading,
      isOnline: isOnline ?? this.isOnline,
      currentRoute: currentRoute ?? this.currentRoute,
    );
  }
}

/// 🔧 Provider桥接器 - 用于渐进式迁移
class ProviderBridge {
  static ProviderContainer? _container;
  
  static void initialize(ProviderContainer container) {
    _container = container;
  }
  
  static T read<T>(ProviderListenable<T> provider) {
    if (_container == null) {
      throw StateError('ProviderBridge not initialized');
    }
    return _container!.read(provider);
  }
  
  static void dispose() {
    _container?.dispose();
    _container = null;
  }
}

/// 🎯 手动Provider定义 - 替代代码生成器

// 📋 taskNotifierProvider已移动到 lib/providers/task_notifier.dart

/// 🔄 当前任务Provider定义
final currentTaskNotifierProvider = NotifierProvider.family<CurrentTaskNotifier, TaskModel?, String?>(
  CurrentTaskNotifier.new,
);

/// 🎯 应用状态Provider定义
final appStateNotifierProvider = NotifierProvider<AppStateNotifier, AppState>(
  AppStateNotifier.new,
);
