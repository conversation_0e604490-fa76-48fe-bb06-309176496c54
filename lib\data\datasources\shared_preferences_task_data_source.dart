import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/data/datasources/task_data_source.dart';

/// 📱 SharedPreferences任务数据源
/// 
/// 使用SharedPreferences作为备用存储方案
class SharedPreferencesTaskDataSource implements TaskDataSource {
  static const String _tasksKey = 'tasks_sp_v2';
  static const String _taskStatsKey = 'task_stats_sp';
  static const String _taskHistoryKey = 'task_history_sp';
  
  @override
  Future<List<TaskModel>> getAllTasks() async {
    try {
      AppLogger.info('📖 从SharedPreferences读取所有任务数据');
      
      final prefs = await SharedPreferences.getInstance();
      final tasksJson = prefs.getString(_tasksKey);
      
      if (tasksJson == null || tasksJson.isEmpty) {
        AppLogger.info('📝 未找到任务数据，返回空列表');
        return [];
      }
      
      final tasksList = jsonDecode(tasksJson) as List<dynamic>;
      final tasks = tasksList
          .map((taskData) => TaskModel.fromJson(taskData as Map<String, dynamic>))
          .toList();
      
      AppLogger.info('✅ 成功读取 ${tasks.length} 个任务');
      return tasks;
      
    } catch (e) {
      AppLogger.error('❌ 读取任务数据失败: $e');
      return [];
    }
  }
  
  @override
  Future<bool> saveAllTasks(List<TaskModel> tasks) async {
    try {
      AppLogger.info('💾 保存 ${tasks.length} 个任务到SharedPreferences');
      
      final prefs = await SharedPreferences.getInstance();
      final tasksData = tasks.map((task) => task.toJson()).toList();
      final tasksJson = jsonEncode(tasksData);
      
      final success = await prefs.setString(_tasksKey, tasksJson);
      
      if (success) {
        AppLogger.info('✅ 任务数据保存成功');
      } else {
        AppLogger.error('❌ 任务数据保存失败');
      }
      
      return success;
      
    } catch (e) {
      AppLogger.error('❌ 保存任务数据失败: $e');
      return false;
    }
  }
  
  @override
  Future<TaskModel?> getTask(String taskId) async {
    try {
      final tasks = await getAllTasks();
      return tasks.firstWhere(
        (task) => task.id == taskId,
        orElse: () => throw StateError('Task not found'),
      );
    } catch (e) {
      AppLogger.warning('⚠️ 未找到任务: $taskId');
      return null;
    }
  }
  
  @override
  Future<bool> saveTask(TaskModel task) async {
    try {
      final tasks = await getAllTasks();
      
      // 查找并更新现有任务，或添加新任务
      final existingIndex = tasks.indexWhere((t) => t.id == task.id);
      if (existingIndex >= 0) {
        tasks[existingIndex] = task;
        AppLogger.info('🔄 更新任务: ${task.id}');
      } else {
        tasks.add(task);
        AppLogger.info('➕ 添加新任务: ${task.id}');
      }
      
      return await saveAllTasks(tasks);
      
    } catch (e) {
      AppLogger.error('❌ 保存任务失败: $e');
      return false;
    }
  }
  
  @override
  Future<bool> deleteTask(String taskId) async {
    try {
      final tasks = await getAllTasks();
      final initialLength = tasks.length;
      
      tasks.removeWhere((task) => task.id == taskId);
      
      if (tasks.length < initialLength) {
        AppLogger.info('🗑️ 删除任务: $taskId');
        return await saveAllTasks(tasks);
      } else {
        AppLogger.warning('⚠️ 未找到要删除的任务: $taskId');
        return false;
      }
      
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $e');
      return false;
    }
  }
  
  @override
  Future<Map<String, dynamic>> getTaskStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_taskStatsKey);
      
      if (statsJson == null || statsJson.isEmpty) {
        return {};
      }
      
      return jsonDecode(statsJson) as Map<String, dynamic>;
    } catch (e) {
      AppLogger.error('❌ 读取任务统计失败: $e');
      return {};
    }
  }
  
  @override
  Future<bool> saveTaskStats(Map<String, dynamic> stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = jsonEncode(stats);
      return await prefs.setString(_taskStatsKey, statsJson);
    } catch (e) {
      AppLogger.error('❌ 保存任务统计失败: $e');
      return false;
    }
  }
  
  @override
  Future<List<Map<String, dynamic>>> getTaskHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_taskHistoryKey);
      
      if (historyJson == null || historyJson.isEmpty) {
        return [];
      }
      
      final historyList = jsonDecode(historyJson) as List<dynamic>;
      return historyList.cast<Map<String, dynamic>>();
    } catch (e) {
      AppLogger.error('❌ 读取任务历史失败: $e');
      return [];
    }
  }
  
  @override
  Future<bool> addTaskHistory(Map<String, dynamic> historyItem) async {
    try {
      final history = await getTaskHistory();
      history.add(historyItem);
      
      // 限制历史记录数量（保留最近500条，因为SharedPreferences有大小限制）
      if (history.length > 500) {
        history.removeRange(0, history.length - 500);
      }
      
      final prefs = await SharedPreferences.getInstance();
      final historyJson = jsonEncode(history);
      return await prefs.setString(_taskHistoryKey, historyJson);
    } catch (e) {
      AppLogger.error('❌ 添加任务历史失败: $e');
      return false;
    }
  }
  
  @override
  Future<bool> cleanupExpiredData({int daysToKeep = 30}) async {
    try {
      AppLogger.info('🧹 开始清理过期数据（保留${daysToKeep}天）');
      
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      final tasks = await getAllTasks();
      
      // 过滤掉过期的已完成任务
      final filteredTasks = tasks.where((task) {
        if (task.status != TaskStatus.completed) {
          return true; // 保留未完成的任务
        }
        
        // 检查任务的最后更新时间
        final lastUpdate = task.completedAt ?? task.createdAt;
        return lastUpdate.isAfter(cutoffDate);
      }).toList();
      
      if (filteredTasks.length < tasks.length) {
        final removedCount = tasks.length - filteredTasks.length;
        AppLogger.info('🗑️ 清理了 $removedCount 个过期任务');
        await saveAllTasks(filteredTasks);
      }
      
      // 清理过期的历史记录
      final history = await getTaskHistory();
      final filteredHistory = history.where((item) {
        final timestamp = item['timestamp'] as String?;
        if (timestamp == null) return false;
        
        try {
          final date = DateTime.parse(timestamp);
          return date.isAfter(cutoffDate);
        } catch (e) {
          return false;
        }
      }).toList();
      
      if (filteredHistory.length < history.length) {
        final removedCount = history.length - filteredHistory.length;
        AppLogger.info('🗑️ 清理了 $removedCount 条过期历史记录');
        
        final prefs = await SharedPreferences.getInstance();
        final historyJson = jsonEncode(filteredHistory);
        await prefs.setString(_taskHistoryKey, historyJson);
      }
      
      AppLogger.info('✅ 数据清理完成');
      return true;
      
    } catch (e) {
      AppLogger.error('❌ 数据清理失败: $e');
      return false;
    }
  }
  
  @override
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final tasks = await getAllTasks();
      final history = await getTaskHistory();
      final stats = await getTaskStats();
      
      return {
        'tasksCount': tasks.length,
        'historyCount': history.length,
        'statsKeys': stats.keys.length,
        'lastUpdate': DateTime.now().toIso8601String(),
        'storageType': 'SharedPreferences',
      };
    } catch (e) {
      AppLogger.error('❌ 获取存储统计失败: $e');
      return {};
    }
  }

  /// 初始化数据源
  @override
  Future<void> initialize() async {
    // SharedPreferences数据源不需要特殊初始化
    AppLogger.info('📱 SharedPreferencesTaskDataSource 已初始化');
  }

  /// 批量保存任务
  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    await saveAllTasks(tasks);
  }

  /// 清空所有任务
  @override
  Future<void> clearAllTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tasksKey);
      await prefs.remove(_taskStatsKey);
      await prefs.remove(_taskHistoryKey);
      AppLogger.info('🗑️ 已清空所有任务 (SharedPreferences)');
    } catch (e) {
      AppLogger.error('❌ 清空任务失败: $e');
      rethrow;
    }
  }

  /// 获取当前任务ID
  @override
  Future<String?> getCurrentTaskId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('current_task_id');
    } catch (e) {
      AppLogger.error('❌ 获取当前任务ID失败: $e');
      return null;
    }
  }

  /// 设置当前任务ID
  @override
  Future<void> setCurrentTaskId(String? taskId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (taskId != null) {
        await prefs.setString('current_task_id', taskId);
      } else {
        await prefs.remove('current_task_id');
      }
      AppLogger.info('📌 设置当前任务ID: $taskId (SharedPreferences)');
    } catch (e) {
      AppLogger.error('❌ 设置当前任务ID失败: $e');
      rethrow;
    }
  }

  /// 检查数据源是否可用
  @override
  Future<bool> isAvailable() async {
    try {
      // 检查SharedPreferences是否可用
      await SharedPreferences.getInstance();
      return true;
    } catch (e) {
      AppLogger.error('❌ 检查数据源可用性失败: $e');
      return false;
    }
  }
}
