import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:loadguard/services/ml_kit_algorithm_optimizer.dart';
import 'package:loadguard/services/intelligent_recognition_strategy.dart';
// ✅ 更新：使用统一的性能管理器
import 'package:loadguard/services/performance_manager.dart';
import 'package:loadguard/models/task_model.dart';

void main() {
  group('ML Kit V2 0.15.0 算法优化测试', () {
    group('算法选择测试', () {
      test('should select standard algorithm for high quality images', () {
        final metrics = ImageQualityMetrics(
          brightness: 0.8,
          contrast: 0.9,
          sharpness: 0.85,
          noiseLevel: 0.2,
          textDensity: 0.6,
          overallQuality: 0.9,
          hasRotation: false,
          hasPerspectiveDistortion: false,
          hasBlur: false,
          hasLowContrast: false,
          imageSize: const Size(1920, 1080),
        );
        
        expect(metrics.qualityLevel, '优秀');
        expect(metrics.needsPreprocessing, false);
        expect(metrics.overallQuality, greaterThan(0.8));
      });
      
      test('should select enhanced algorithm for medium quality images', () {
        final metrics = ImageQualityMetrics(
          brightness: 0.6,
          contrast: 0.5,
          sharpness: 0.4,
          noiseLevel: 0.4,
          textDensity: 0.5,
          overallQuality: 0.6,
          hasRotation: false,
          hasPerspectiveDistortion: false,
          hasBlur: true,
          hasLowContrast: false,
          imageSize: const Size(1280, 720),
        );
        
        expect(metrics.qualityLevel, '良好');
        expect(metrics.needsPreprocessing, true);
        expect(metrics.hasBlur, true);
      });
      
      test('should select specialized algorithm for poor quality images', () {
        final metrics = ImageQualityMetrics(
          brightness: 0.2,
          contrast: 0.3,
          sharpness: 0.2,
          noiseLevel: 0.8,
          textDensity: 0.3,
          overallQuality: 0.3,
          hasRotation: true,
          hasPerspectiveDistortion: true,
          hasBlur: true,
          hasLowContrast: true,
          imageSize: const Size(640, 480),
        );
        
        expect(metrics.qualityLevel, '较差');
        expect(metrics.needsPreprocessing, true);
        expect(metrics.hasRotation, true);
        expect(metrics.hasPerspectiveDistortion, true);
        expect(metrics.hasBlur, true);
        expect(metrics.hasLowContrast, true);
      });
    });
    
    group('算法枚举测试', () {
      test('should have all 12 recognition algorithms', () {
        final algorithms = RecognitionAlgorithm.values;
        expect(algorithms.length, 12);
        
        // 验证所有算法都有显示名称
        for (final algorithm in algorithms) {
          expect(algorithm.displayName, isNotEmpty);
        }
        
        // 验证特定算法存在
        expect(algorithms, contains(RecognitionAlgorithm.standardLatin));
        expect(algorithms, contains(RecognitionAlgorithm.enhancedLatin));
        expect(algorithms, contains(RecognitionAlgorithm.chineseOptimized));
        expect(algorithms, contains(RecognitionAlgorithm.mixedLanguage));
        expect(algorithms, contains(RecognitionAlgorithm.highContrast));
        expect(algorithms, contains(RecognitionAlgorithm.lowLight));
        expect(algorithms, contains(RecognitionAlgorithm.blurryText));
        expect(algorithms, contains(RecognitionAlgorithm.smallText));
        expect(algorithms, contains(RecognitionAlgorithm.rotatedText));
        expect(algorithms, contains(RecognitionAlgorithm.perspectiveCorrected));
        expect(algorithms, contains(RecognitionAlgorithm.noiseReduction));
        expect(algorithms, contains(RecognitionAlgorithm.adaptiveThreshold));
      });
      
      test('should have correct display names', () {
        expect(RecognitionAlgorithm.standardLatin.displayName, '标准拉丁文识别');
        expect(RecognitionAlgorithm.chineseOptimized.displayName, '中文优化识别');
        expect(RecognitionAlgorithm.highContrast.displayName, '高对比度识别');
        expect(RecognitionAlgorithm.lowLight.displayName, '低光照识别');
      });
    });
    
    group('优化识别结果测试', () {
      test('should create optimized recognition result correctly', () {
        final recognitionResult = RecognitionResult(
          ocrText: 'TEST123',
          isQrOcrConsistent: true,
          matchesPreset: true,
          recognitionTime: DateTime.now(),
          status: RecognitionStatus.completed,
        );
        
        final qualityMetrics = ImageQualityMetrics.defaultMetrics();
        
        final optimizedResult = OptimizedRecognitionResult(
          recognitionResult: recognitionResult,
          algorithmUsed: RecognitionAlgorithm.standardLatin,
          qualityMetrics: qualityMetrics,
          processingTime: const Duration(milliseconds: 1500),
          optimizationApplied: false,
          fallbackUsed: false,
        );
        
        expect(optimizedResult.isSuccessful, true);
        expect(optimizedResult.algorithmUsed, RecognitionAlgorithm.standardLatin);
        expect(optimizedResult.processingTime.inMilliseconds, 1500);
        expect(optimizedResult.optimizationApplied, false);
        expect(optimizedResult.fallbackUsed, false);
        
        final analyticsData = optimizedResult.toAnalyticsData();
        expect(analyticsData['algorithm'], 'standardLatin');
        expect(analyticsData['processingTimeMs'], 1500);
        expect(analyticsData['successful'], true);
      });
    });
  });
  
  group('智能识别策略测试', () {
    late IntelligentRecognitionStrategy strategy;
    
    setUp(() {
      strategy = IntelligentRecognitionStrategy();
    });
    
    test('should create intelligent strategy correctly', () {
      final intelligentStrategy = IntelligentStrategy(
        primaryAlgorithm: RecognitionAlgorithm.enhancedLatin,
        fallbackAlgorithms: [RecognitionAlgorithm.standardLatin, RecognitionAlgorithm.adaptiveThreshold],
        qualityMetrics: ImageQualityMetrics.defaultMetrics(),
        performanceInsights: PerformanceInsights.defaultInsights(),
        optimizationSuggestions: ['使用增强算法', '应用预处理'],
        confidence: 0.85,
        estimatedProcessingTime: const Duration(milliseconds: 1800),
        recommendedRetries: 2,
      );
      
      expect(intelligentStrategy.primaryAlgorithm, RecognitionAlgorithm.enhancedLatin);
      expect(intelligentStrategy.fallbackAlgorithms.length, 2);
      expect(intelligentStrategy.confidence, 0.85);
      expect(intelligentStrategy.estimatedProcessingTime.inMilliseconds, 1800);
      expect(intelligentStrategy.recommendedRetries, 2);
    });
    
    test('should create performance insights correctly', () {
      final insights = PerformanceInsights(
        bestPerformingAlgorithm: RecognitionAlgorithm.enhancedLatin,
        averageSuccessRate: 0.92,
        totalSamples: 150,
        recommendationStrength: 'strong',
      );
      
      expect(insights.bestPerformingAlgorithm, RecognitionAlgorithm.enhancedLatin);
      expect(insights.averageSuccessRate, 0.92);
      expect(insights.totalSamples, 150);
      expect(insights.recommendationStrength, 'strong');
    });
    
    test('should create default insights when no data available', () {
      final defaultInsights = PerformanceInsights.defaultInsights();
      
      expect(defaultInsights.bestPerformingAlgorithm, RecognitionAlgorithm.standardLatin);
      expect(defaultInsights.averageSuccessRate, 0.7);
      expect(defaultInsights.totalSamples, 0);
      expect(defaultInsights.recommendationStrength, 'weak');
    });
    
    test('should get performance statistics correctly', () {
      final stats = strategy.getPerformanceStatistics();
      
      expect(stats, isA<Map<String, dynamic>>());
      expect(stats.containsKey('totalRecognitions'), true);
      expect(stats.containsKey('averageSuccessRate'), true);
      expect(stats.containsKey('averageProcessingTime'), true);
      expect(stats.containsKey('algorithmUsage'), true);
      expect(stats.containsKey('performanceHistory'), true);
    });
  });
  
  group('性能监控测试', () {
    late MLKitPerformanceMonitor monitor;
    
    setUp(() {
      monitor = MLKitPerformanceMonitor();
      monitor.initialize();
    });
    
    tearDown(() {
      monitor.dispose();
    });
    
    test('should record performance metrics correctly', () {
      final qualityMetrics = ImageQualityMetrics.defaultMetrics();
      
      monitor.recordRecognitionPerformance(
        algorithm: RecognitionAlgorithm.standardLatin,
        processingTime: const Duration(milliseconds: 1200),
        successful: true,
        qualityMetrics: qualityMetrics,
        confidence: 0.85,
        additionalData: {'testData': 'value'},
      );
      
      final stats = monitor.getPerformanceStatistics();
      expect(stats.totalRecognitions, 1);
      expect(stats.successRate, 1.0);
      expect(stats.averageProcessingTime.inMilliseconds, 1200);
      expect(stats.averageConfidence, 0.85);
    });
    
    test('should calculate statistics correctly with multiple metrics', () {
      final qualityMetrics = ImageQualityMetrics.defaultMetrics();
      
      // 记录多个性能指标
      monitor.recordRecognitionPerformance(
        algorithm: RecognitionAlgorithm.standardLatin,
        processingTime: const Duration(milliseconds: 1000),
        successful: true,
        qualityMetrics: qualityMetrics,
        confidence: 0.9,
      );
      
      monitor.recordRecognitionPerformance(
        algorithm: RecognitionAlgorithm.enhancedLatin,
        processingTime: const Duration(milliseconds: 1500),
        successful: false,
        qualityMetrics: qualityMetrics,
        confidence: 0.6,
      );
      
      monitor.recordRecognitionPerformance(
        algorithm: RecognitionAlgorithm.standardLatin,
        processingTime: const Duration(milliseconds: 1200),
        successful: true,
        qualityMetrics: qualityMetrics,
        confidence: 0.8,
      );
      
      final stats = monitor.getPerformanceStatistics();
      expect(stats.totalRecognitions, 3);
      expect(stats.successRate, closeTo(2/3, 0.01));
      expect(stats.averageProcessingTime.inMilliseconds, closeTo(1233, 50));
      expect(stats.averageConfidence, closeTo(0.77, 0.01));
      expect(stats.algorithmBreakdown.length, 2);
    });
    
    test('should generate optimization recommendations', () {
      final qualityMetrics = ImageQualityMetrics(
        brightness: 0.3,
        contrast: 0.2,
        sharpness: 0.1,
        noiseLevel: 0.8,
        textDensity: 0.3,
        overallQuality: 0.2,
        hasRotation: false,
        hasPerspectiveDistortion: false,
        hasBlur: true,
        hasLowContrast: true,
        imageSize: const Size(640, 480),
      );
      
      // 记录一些低质量的识别结果
      for (int i = 0; i < 10; i++) {
        monitor.recordRecognitionPerformance(
          algorithm: RecognitionAlgorithm.standardLatin,
          processingTime: const Duration(milliseconds: 4000),
          successful: i < 3, // 30%成功率
          qualityMetrics: qualityMetrics,
          confidence: 0.3,
        );
      }
      
      final recommendations = monitor.getOptimizationRecommendations();
      expect(recommendations, isNotEmpty);
      
      // 应该有成功率相关的建议
      final successRateRec = recommendations.where((r) => r.type == RecommendationType.successRate);
      expect(successRateRec, isNotEmpty);
      
      // 应该有性能相关的建议
      final performanceRec = recommendations.where((r) => r.type == RecommendationType.performance);
      expect(performanceRec, isNotEmpty);
    });
    
    test('should handle empty statistics correctly', () {
      final emptyStats = PerformanceStatistics.empty();
      
      expect(emptyStats.totalRecognitions, 0);
      expect(emptyStats.successRate, 0.0);
      expect(emptyStats.averageProcessingTime, Duration.zero);
      expect(emptyStats.averageConfidence, 0.0);
      expect(emptyStats.algorithmBreakdown.isEmpty, true);
    });
  });
  
  group('ML Kit V2 0.15.0 兼容性测试', () {
    test('should maintain ML Kit version compatibility', () {
      // 验证版本信息
      const expectedVersion = 'ML Kit V2 0.15.0';
      
      // 这里可以添加更多版本兼容性测试
      expect(expectedVersion, contains('0.15.0'));
      expect(expectedVersion, contains('ML Kit V2'));
    });
    
    test('should support all required recognition scripts', () {
      // 验证支持的识别脚本
      final supportedScripts = [
        'Latin',
        'Chinese',
        'Devanagari',
        'Japanese',
        'Korean'
      ];
      
      expect(supportedScripts, contains('Latin'));
      expect(supportedScripts, contains('Chinese'));
      expect(supportedScripts.length, greaterThanOrEqualTo(5));
    });
  });
}
