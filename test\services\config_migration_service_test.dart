import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/config_migration_service.dart';
import 'package:loadguard/repositories/config_repository.dart';
import 'package:loadguard/models/config_models.dart';

/// 简化的ConfigRepository Mock，只实现核心方法

/// 模拟的ConfigRepository用于测试
class MockConfigRepository implements ConfigRepository {
  final Map<String, WorkerConfig> _workers = {};
  final Map<String, WarehouseConfig> _warehouses = {};
  final Map<String, GroupConfig> _groups = {};
  final Map<String, TemplateConfigModel> _templates = {};
  final Map<String, RoleConfig> _roles = {};
  final Map<String, SystemConfig> _systemConfigs = {};
  
  bool _migrationCalled = false;
  
  @override
  Future<void> initialize() async {}
  
  @override
  Future<void> dispose() async {}
  
  // ==================== 工作人员配置 ====================
  
  @override
  Future<List<WorkerConfig>> getAllWorkers() async {
    return _workers.values.toList();
  }
  
  @override
  Future<WorkerConfig?> getWorkerById(String id) async {
    return _workers[id];
  }
  
  @override
  Future<void> saveWorker(WorkerConfig worker) async {
    _workers[worker.id] = worker;
  }
  
  @override
  Future<void> saveWorkers(List<WorkerConfig> workers) async {
    for (final worker in workers) {
      _workers[worker.id] = worker;
    }
  }
  
  @override
  Future<void> deleteWorker(String id) async {
    _workers.remove(id);
  }
  
  @override
  Future<List<WorkerConfig>> queryWorkers({
    String? warehouse,
    String? group,
    String? role,
    bool? isActive,
  }) async {
    var workers = _workers.values.toList();
    
    if (warehouse != null) {
      workers = workers.where((w) => w.warehouse == warehouse).toList();
    }
    if (group != null) {
      workers = workers.where((w) => w.group == group).toList();
    }
    if (role != null) {
      workers = workers.where((w) => w.role == role).toList();
    }
    if (isActive != null) {
      workers = workers.where((w) => w.isActive == isActive).toList();
    }
    
    return workers;
  }
  
  // ==================== 仓库配置 ====================
  
  @override
  Future<List<WarehouseConfig>> getAllWarehouses() async {
    return _warehouses.values.toList();
  }
  
  @override
  Future<WarehouseConfig?> getWarehouseById(String id) async {
    return _warehouses[id];
  }
  
  @override
  Future<void> saveWarehouse(WarehouseConfig warehouse) async {
    _warehouses[warehouse.id] = warehouse;
  }
  
  @override
  Future<void> deleteWarehouse(String id) async {
    _warehouses.remove(id);
  }
  
  // ==================== 工作组配置 ====================
  
  @override
  Future<List<GroupConfig>> getAllGroups() async {
    return _groups.values.toList();
  }
  
  @override
  Future<GroupConfig?> getGroupById(String id) async {
    return _groups[id];
  }
  
  @override
  Future<void> saveGroup(GroupConfig group) async {
    _groups[group.id] = group;
  }
  
  @override
  Future<void> deleteGroup(String id) async {
    _groups.remove(id);
  }
  
  // ==================== 模板配置 ====================
  
  @override
  Future<List<TemplateConfigModel>> getAllTemplates() async {
    return _templates.values.toList();
  }
  
  @override
  Future<TemplateConfigModel?> getTemplateById(String id) async {
    return _templates[id];
  }
  
  @override
  Future<void> saveTemplate(TemplateConfigModel template) async {
    _templates[template.id] = template;
  }
  
  @override
  Future<void> deleteTemplate(String id) async {
    _templates.remove(id);
  }
  
  // ==================== 角色配置 ====================
  
  @override
  Future<List<RoleConfig>> getAllRoles() async {
    return _roles.values.toList();
  }
  
  @override
  Future<RoleConfig?> getRoleById(String id) async {
    return _roles[id];
  }
  
  @override
  Future<void> saveRole(RoleConfig role) async {
    _roles[role.id] = role;
  }
  
  @override
  Future<void> deleteRole(String id) async {
    _roles.remove(id);
  }
  
  // ==================== 系统配置 ====================
  
  @override
  Future<List<SystemConfig>> getAllSystemConfigs() async {
    return _systemConfigs.values.toList();
  }
  
  @override
  Future<SystemConfig?> getSystemConfigByKey(String key) async {
    return _systemConfigs[key];
  }
  
  @override
  Future<void> saveSystemConfig(SystemConfig config) async {
    _systemConfigs[config.key] = config;
  }
  
  @override
  Future<void> deleteSystemConfig(String key) async {
    _systemConfigs.remove(key);
  }

  @override
  Future<List<SystemConfig>> getSystemConfigsByCategory(String category) async {
    return _systemConfigs.values.where((config) => config.category == category).toList();
  }

  @override
  Future<void> backupConfigs() async {
    // 模拟备份
  }

  @override
  Future<void> restoreConfigs(String backupId) async {
    // 模拟恢复
  }
  
  // ==================== 数据迁移 ====================
  
  @override
  Future<void> migrateFromLegacyData() async {
    _migrationCalled = true;
    
    // 模拟迁移88名工作人员
    for (int i = 1; i <= 88; i++) {
      final worker = WorkerConfig(
        id: i.toString(),
        name: '工作人员$i',
        role: i % 2 == 0 ? '叉车' : '仓管',
        warehouse: '${(i % 4) + 1}号库',
        group: '${(i % 6) + 1}组',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      _workers[worker.id] = worker;
    }
    
    // 模拟迁移仓库
    for (int i = 1; i <= 4; i++) {
      final warehouse = WarehouseConfig(
        id: i.toString(),
        name: '${i}号库',
        location: '${i}号库位置',
        isActive: true,
        description: '模拟仓库$i',
        supportedTemplates: ['平板车', '集装箱'],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      _warehouses[warehouse.id] = warehouse;
    }
    
    // 模拟迁移模板
    final template1 = TemplateConfigModel(
      id: 'flatbed',
      name: '平板车',
      type: '平板车',
      description: '平板车拍照模板',
      isActive: true,
      photoConfigs: [],
      photoGroups: [],
      totalPhotos: 12,
      requiredPhotos: 8,
      recognitionPhotos: 4,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _templates[template1.id] = template1;
    
    final template2 = TemplateConfigModel(
      id: 'container',
      name: '集装箱',
      type: '集装箱',
      description: '集装箱拍照模板',
      isActive: true,
      photoConfigs: [],
      photoGroups: [],
      totalPhotos: 10,
      requiredPhotos: 6,
      recognitionPhotos: 3,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    _templates[template2.id] = template2;
  }
  
  // ==================== 数据导入导出 ====================
  
  @override
  Future<Map<String, dynamic>> exportConfigs({
    bool includeWorkers = true,
    bool includeWarehouses = true,
    bool includeGroups = true,
    bool includeTemplates = true,
    bool includeRoles = true,
    bool includeSystemConfigs = true,
  }) async {
    final export = <String, dynamic>{
      'version': '1.0.0',
      'exportTime': DateTime.now().toIso8601String(),
    };
    
    if (includeWorkers) {
      export['workers'] = _workers.values.map((w) => w.toJson()).toList();
    }
    if (includeWarehouses) {
      export['warehouses'] = _warehouses.values.map((w) => w.toJson()).toList();
    }
    if (includeTemplates) {
      export['templates'] = _templates.values.map((t) => t.toJson()).toList();
    }
    
    return export;
  }
  
  @override
  Future<void> importConfigs(Map<String, dynamic> data, {
    bool overwriteExisting = false,
  }) async {
    // 模拟导入逻辑
    if (data.containsKey('workers')) {
      final workersData = data['workers'] as List;
      for (final workerData in workersData) {
        final worker = WorkerConfig.fromJson(workerData);
        _workers[worker.id] = worker;
      }
    }
  }
  
  // 测试辅助方法
  bool get migrationCalled => _migrationCalled;
  void reset() {
    _workers.clear();
    _warehouses.clear();
    _groups.clear();
    _templates.clear();
    _roles.clear();
    _systemConfigs.clear();
    _migrationCalled = false;
  }
}

void main() {
  group('ConfigMigrationService Tests', () {
    late ConfigMigrationService migrationService;
    late MockConfigRepository mockRepository;
    
    setUp(() {
      mockRepository = MockConfigRepository();
      migrationService = ConfigMigrationService(mockRepository);
    });
    
    tearDown(() {
      mockRepository.reset();
    });
    
    group('数据迁移检查', () {
      test('should detect need for migration when no data exists', () async {
        // 空数据库，应该需要迁移
        final result = await migrationService.checkAndMigrate();
        
        expect(result.success, true);
        expect(result.migratedItems, 90); // 88名工作人员 + 4个仓库 + 2个模板
        expect(mockRepository.migrationCalled, true);
        
        // 验证迁移的数据
        final workers = await mockRepository.getAllWorkers();
        expect(workers.length, 88);
        
        final warehouses = await mockRepository.getAllWarehouses();
        expect(warehouses.length, 4);
        
        final templates = await mockRepository.getAllTemplates();
        expect(templates.length, 2);
      });
      
      test('should not migrate when data already exists', () async {
        // 先执行一次迁移
        await migrationService.checkAndMigrate();
        mockRepository._migrationCalled = false; // 重置标记
        
        // 再次检查，应该不需要迁移
        final result = await migrationService.checkAndMigrate();
        
        expect(result.success, true);
        expect(result.migratedItems, 0);
        expect(mockRepository.migrationCalled, false);
      });
    });
    
    group('强制迁移', () {
      test('should perform force migration', () async {
        // 先添加一些数据
        await mockRepository.saveWorker(WorkerConfig(
          id: 'test1',
          name: '测试工作人员',
          role: '测试',
          warehouse: '测试库',
          group: '测试组',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));
        
        // 强制迁移
        final result = await migrationService.forceMigration();
        
        expect(result.success, true);
        expect(result.migratedItems, 88); // 应该重新迁移88名工作人员
        expect(mockRepository.migrationCalled, true);
        
        // 验证数据
        final workers = await mockRepository.getAllWorkers();
        expect(workers.length, 88); // 应该是迁移的88人，不包括之前的测试数据
      });
    });
    
    group('数据查询', () {
      test('should query workers by warehouse', () async {
        // 先迁移数据
        await migrationService.checkAndMigrate();
        
        // 查询1号库的工作人员
        final workers = await mockRepository.queryWorkers(warehouse: '1号库');
        
        expect(workers.isNotEmpty, true);
        expect(workers.every((w) => w.warehouse == '1号库'), true);
      });
      
      test('should query workers by role', () async {
        // 先迁移数据
        await migrationService.checkAndMigrate();
        
        // 查询叉车工作人员
        final workers = await mockRepository.queryWorkers(role: '叉车');
        
        expect(workers.isNotEmpty, true);
        expect(workers.every((w) => w.role == '叉车'), true);
      });
      
      test('should query active workers only', () async {
        // 先迁移数据
        await migrationService.checkAndMigrate();
        
        // 添加一个非活跃工作人员
        await mockRepository.saveWorker(WorkerConfig(
          id: 'inactive1',
          name: '非活跃工作人员',
          role: '测试',
          warehouse: '测试库',
          group: '测试组',
          isActive: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));
        
        // 查询活跃工作人员
        final activeWorkers = await mockRepository.queryWorkers(isActive: true);
        final inactiveWorkers = await mockRepository.queryWorkers(isActive: false);
        
        expect(activeWorkers.length, 88); // 88名迁移的活跃工作人员
        expect(inactiveWorkers.length, 1); // 1名非活跃工作人员
        expect(activeWorkers.every((w) => w.isActive), true);
        expect(inactiveWorkers.every((w) => !w.isActive), true);
      });
    });
    
    group('数据导入导出', () {
      test('should export configuration data', () async {
        // 先迁移数据
        await migrationService.checkAndMigrate();
        
        // 导出配置
        final exportData = await mockRepository.exportConfigs();
        
        expect(exportData['version'], '1.0.0');
        expect(exportData['workers'], isA<List>());
        expect(exportData['warehouses'], isA<List>());
        expect(exportData['templates'], isA<List>());
        
        final workers = exportData['workers'] as List;
        expect(workers.length, 88);
        
        final warehouses = exportData['warehouses'] as List;
        expect(warehouses.length, 4);
        
        final templates = exportData['templates'] as List;
        expect(templates.length, 2);
      });
      
      test('should import configuration data', () async {
        // 准备导入数据
        final importData = {
          'version': '1.0.0',
          'workers': [
            {
              'id': 'import1',
              'name': '导入工作人员1',
              'role': '叉车',
              'warehouse': '1号库',
              'group': '1组',
              'isActive': true,
              'createdAt': DateTime.now().toIso8601String(),
              'updatedAt': DateTime.now().toIso8601String(),
            },
            {
              'id': 'import2',
              'name': '导入工作人员2',
              'role': '仓管',
              'warehouse': '2号库',
              'group': '2组',
              'isActive': true,
              'createdAt': DateTime.now().toIso8601String(),
              'updatedAt': DateTime.now().toIso8601String(),
            },
          ],
        };
        
        // 导入配置
        await mockRepository.importConfigs(importData);
        
        // 验证导入结果
        final workers = await mockRepository.getAllWorkers();
        expect(workers.length, 2);
        expect(workers.any((w) => w.name == '导入工作人员1'), true);
        expect(workers.any((w) => w.name == '导入工作人员2'), true);
      });
    });
  });
}
