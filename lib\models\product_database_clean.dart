import 'package:flutter/material.dart';

/// 📊 匹配类型
enum MatchType { exact, preset, fuzzy }

/// 🎯 产品信息
class ProductInfo {
  final String code;
  final String name;
  final String category;
  final List<String> commonBatchPrefixes;
  final int priority;

  const ProductInfo({
    required this.code,
    required this.name,
    required this.category,
    required this.commonBatchPrefixes,
    required this.priority,
  });
}

/// 🎯 产品匹配结果
class ProductMatch {
  final ProductInfo? product;
  final double similarity;
  final MatchType matchType;
  final double confidence;
  final String matchedText;

  ProductMatch({
    this.product,
    required this.similarity,
    required this.matchType,
    required this.confidence,
    required this.matchedText,
  });
}

/// 🎯 产品牌号数据库 - 简化版
class ProductDatabase {
  // 预设产品数据 - 包含新增的牌号
  static const Map<String, ProductInfo> presetProducts = {
    // LLDPE系列
    'LLD-7042': ProductInfo(
      code: 'LLD-7042',
      name: '线性低密度聚乙烯',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 10,
    ),
    'LLD-7042-GD': ProductInfo(
      code: 'LLD-7042-GD',
      name: '线性低密度聚乙烯(过渡料)',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 9,
    ),
    'LLD-7047': ProductInfo(
      code: 'LLD-7047',
      name: '线性低密度聚乙烯',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 8,
    ),
    'LLD-7050': ProductInfo(
      code: 'LLD-7050',
      name: '线性低密度聚乙烯',
      category: 'LLDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 8,
    ),

    // HDPE系列 - 包含新增的HD-5502S系列
    'HD-5000S': ProductInfo(
      code: 'HD-5000S',
      name: '高密度聚乙烯',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'HD-5000S-FP': ProductInfo(
      code: 'HD-5000S-FP',
      name: '高密度聚乙烯(副牌)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'HD-5000S-GD': ProductInfo(
      code: 'HD-5000S-GD',
      name: '高密度聚乙烯(过渡料)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'HD-5502S': ProductInfo(
      code: 'HD-5502S',
      name: '高密度聚乙烯',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),
    'HD-5502S-GD': ProductInfo(
      code: 'HD-5502S-GD',
      name: '高密度聚乙烯(过渡料)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),
    'HDPE-开工料': ProductInfo(
      code: 'HDPE-开工料',
      name: '高密度聚乙烯开工料',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 8,
    ),
    'HD-6098-GD': ProductInfo(
      code: 'HD-6098-GD',
      name: '高密度聚乙烯(过渡料)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'HD-6081H': ProductInfo(
      code: 'HD-6081H',
      name: '高密度聚乙烯',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'HD-6081-GD': ProductInfo(
      code: 'HD-6081-GD',
      name: '高密度聚乙烯(过渡料)',
      category: 'HDPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),

    // mPE系列
    'mPE-1018': ProductInfo(
      code: 'mPE-1018',
      name: '茂金属聚乙烯',
      category: 'mPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 5,
    ),
    'mPE-1018-GD': ProductInfo(
      code: 'mPE-1018-GD',
      name: '茂金属聚乙烯(过渡料)',
      category: 'mPE',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 5,
    ),

    // PP系列 - 包含新增的PP-1100N系列
    'PP-1100N': ProductInfo(
      code: 'PP-1100N',
      name: '聚丙烯注塑料',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),
    'PP-1100N-GD': ProductInfo(
      code: 'PP-1100N-GD',
      name: '聚丙烯注塑料(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618', '250620'],
      priority: 7,
    ),
    'PP-2500HY': ProductInfo(
      code: 'PP-2500HY',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'PP-2500HY-GD': ProductInfo(
      code: 'PP-2500HY-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'PP-2500HW': ProductInfo(
      code: 'PP-2500HW',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'PP-2500HW-GD': ProductInfo(
      code: 'PP-2500HW-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 7,
    ),
    'PP-K1015': ProductInfo(
      code: 'PP-K1015',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250612', '250618', '251225'],
      priority: 6,
    ),
    'PP-K7930': ProductInfo(
      code: 'PP-K7930',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'PP-K7930-GD': ProductInfo(
      code: 'PP-K7930-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'PP-K8003': ProductInfo(
      code: 'PP-K8003',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'PP-K8003-GD': ProductInfo(
      code: 'PP-K8003-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'PP-K8009': ProductInfo(
      code: 'PP-K8009',
      name: '聚丙烯',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'PP-K8009-GD': ProductInfo(
      code: 'PP-K8009-GD',
      name: '聚丙烯(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 6,
    ),
    'PP-T30F': ProductInfo(
      code: 'PP-T30F',
      name: '聚丙烯纤维料',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 5,
    ),
    'PP-T30S': ProductInfo(
      code: 'PP-T30S',
      name: '聚丙烯纺丝料',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 5,
    ),
    'PP-T30S-GD': ProductInfo(
      code: 'PP-T30S-GD',
      name: '聚丙烯纺丝料(过渡料)',
      category: 'PP',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 5,
    ),

    // SAN系列
    'A-2020': ProductInfo(
      code: 'A-2020',
      name: 'SAN树脂',
      category: 'SAN',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 4,
    ),
    'A-2020-GD': ProductInfo(
      code: 'A-2020-GD',
      name: 'SAN树脂(过渡料)',
      category: 'SAN',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 4,
    ),
    'A-2021': ProductInfo(
      code: 'A-2021',
      name: 'SAN树脂',
      category: 'SAN',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 4,
    ),
    'A-2021-GD': ProductInfo(
      code: 'A-2021-GD',
      name: 'SAN树脂(过渡料)',
      category: 'SAN',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 4,
    ),

    // PS系列
    'S-0130': ProductInfo(
      code: 'S-0130',
      name: '苯乙烯',
      category: 'PS',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 3,
    ),
    'S-0130-FP': ProductInfo(
      code: 'S-0130-FP',
      name: '苯乙烯(副牌)',
      category: 'PS',
      commonBatchPrefixes: ['250615', '250618'],
      priority: 3,
    ),
  };

  /// 🔍 检查产品是否存在
  static bool hasProduct(String productCode) {
    return presetProducts.containsKey(productCode.toUpperCase());
  }

  /// 🔥 获取热门产品列表
  static List<ProductInfo> getPopularProducts({int limit = 0}) {
    final products = presetProducts.values
        .where((ProductInfo product) => product.priority >= 7)
        .toList()
      ..sort((ProductInfo a, ProductInfo b) => b.priority.compareTo(a.priority));

    if (limit > 0) {
      return products.take(limit).toList();
    }
    return products;
  }

  /// 🔥 按类别获取热门产品列表
  static List<ProductInfo> getPopularProductsByCategory(String category, {int limit = 0}) {
    final products = presetProducts.values.where((ProductInfo product) {
      return product.category == category;
    }).toList()
      ..sort((ProductInfo a, ProductInfo b) => b.priority.compareTo(a.priority));
    
    if (limit > 0) {
      return products.take(limit).toList();
    }
    return products;
  }

  /// 🔍 智能牌号匹配
  static List<ProductMatch> intelligentMatch(String recognizedText) {
    final matches = <ProductMatch>[];
    final cleanText = recognizedText.toUpperCase().replaceAll(RegExp(r'[^A-Z0-9-]'), '');

    // 精确匹配预设牌号
    for (final product in presetProducts.values) {
      final similarity = _calculateSimilarity(cleanText, product.code);
      if (similarity >= 0.8) {
        matches.add(ProductMatch(
          product: product,
          similarity: similarity,
          confidence: similarity * 100,
          matchType: similarity >= 0.95 ? MatchType.exact : MatchType.preset,
          matchedText: cleanText,
        ));
      }
    }

    // 按置信度排序
    matches.sort((ProductMatch a, ProductMatch b) {
      final confidenceDiff = b.confidence.compareTo(a.confidence);
      if (confidenceDiff != 0) return confidenceDiff;
      return (b.product?.priority ?? 0).compareTo(a.product?.priority ?? 0);
    });

    return matches.take(5).toList();
  }

  /// 📊 计算字符串相似度
  static double _calculateSimilarity(String a, String b) {
    if (a == b) return 1.0;
    if (a.isEmpty || b.isEmpty) return 0.0;

    final distance = _levenshteinDistance(a, b);
    final maxLength = [a.length, b.length].reduce((curr, next) => curr > next ? curr : next);
    
    return 1.0 - (distance / maxLength);
  }

  /// 计算Levenshtein距离
  static int _levenshteinDistance(String s1, String s2) {
    if (s1.isEmpty) return s2.length;
    if (s2.isEmpty) return s1.length;

    List<int> v0 = List<int>.filled(s2.length + 1, 0);
    List<int> v1 = List<int>.filled(s2.length + 1, 0);

    for (int i = 0; i <= s2.length; i++) {
      v0[i] = i;
    }

    for (int i = 0; i < s1.length; i++) {
      v1[0] = i + 1;

      for (int j = 0; j < s2.length; j++) {
        int cost = s1[i] == s2[j] ? 0 : 1;
        v1[j + 1] = [v1[j] + 1, v0[j + 1] + 1, v0[j] + cost]
            .reduce((curr, next) => curr < next ? curr : next);
      }

      List<int> temp = v0;
      v0 = v1;
      v1 = temp;
    }

    return v0[s2.length];
  }
}
