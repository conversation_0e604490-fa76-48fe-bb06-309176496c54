import 'package:loadguard/models/task_model.dart';

/// 📋 任务数据源接口
///
/// 定义任务数据访问的标准接口，支持多种存储实现
abstract class TaskDataSource {
  /// 初始化数据源
  Future<void> initialize();

  /// 获取所有任务
  Future<List<TaskModel>> getAllTasks();

  /// 保存所有任务
  Future<bool> saveAllTasks(List<TaskModel> tasks);

  /// 批量保存任务
  Future<void> saveTasks(List<TaskModel> tasks);

  /// 获取单个任务
  Future<TaskModel?> getTask(String taskId);

  /// 保存单个任务
  Future<bool> saveTask(TaskModel task);

  /// 删除任务
  Future<bool> deleteTask(String taskId);

  /// 清空所有任务
  Future<void> clearAllTasks();

  /// 获取当前任务ID
  Future<String?> getCurrentTaskId();

  /// 设置当前任务ID
  Future<void> setCurrentTaskId(String? taskId);

  /// 检查数据源是否可用
  Future<bool> isAvailable();

  /// 获取任务统计信息
  Future<Map<String, dynamic>> getTaskStats();

  /// 保存任务统计信息
  Future<bool> saveTaskStats(Map<String, dynamic> stats);

  /// 获取任务历史记录
  Future<List<Map<String, dynamic>>> getTaskHistory();

  /// 添加任务历史记录
  Future<bool> addTaskHistory(Map<String, dynamic> historyItem);

  /// 清理过期数据
  Future<bool> cleanupExpiredData({int daysToKeep = 30});

  /// 获取存储统计信息
  Future<Map<String, dynamic>> getStorageStats();
}
