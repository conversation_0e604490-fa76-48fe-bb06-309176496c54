# LoadGuard ProGuard 规则 - 仅保留必要的配置
# 保留 JNI 方法
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留 Flutter 插件相关
-keep class io.flutter.plugins.** { *; }

# 保留基本属性
-keepattributes *Annotation*
-keepattributes InnerClasses
-keepattributes Signature

# Flutter 相关规则
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class io.flutter.plugin.editing.** { *; }

# ML Kit 相关规则 (替代过时的MediaPipe)
-keep class com.google.mlkit.** { *; }
-keep class com.google.android.gms.** { *; }

# 保持 R 文件中的所有字段
-keepclassmembers class **.R$* {
    public static <fields>;
}

# 保持 Parcelable 类不被混淆
-keepclassmembers class * implements android.os.Parcelable {
    static ** CREATOR;
}

# 保持 Serializable 类不被混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# LoadGuard 应用特定规则

# LoadGuard specific
-keep class com.example.loadguard.** { *; }
-keepclassmembers class com.example.loadguard.** {
    native <methods>;
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep R8 rules
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keepattributes Signature
-keepattributes Exceptions

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep JavaScript interface methods
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# Keep LoadGuard model classes
-keep class com.example.loadguard.models.** { *; }
-keep class com.example.loadguard.services.** { *; }

# 移除过时的MediaPipe规则，现在使用ML Kit V2

# 🔧 修复Flutter Play Store分割安装错误
# 保留Flutter Play Store相关类（解决R8编译错误）
-keep class io.flutter.embedding.android.FlutterPlayStoreSplitApplication { *; }
-keep class io.flutter.embedding.engine.deferredcomponents.** { *; }

# 保留Google Play Core库相关类（如果不使用Play Store分割，可以安全忽略）
-dontwarn com.google.android.play.core.**
-keep class com.google.android.play.core.** { *; }

# 允许R8跳过缺失的Play Core类（本地应用不需要）
-dontnote com.google.android.play.core.**
-ignorewarnings

# 保留Flutter引擎核心功能
-keep class io.flutter.embedding.engine.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.util.** { *; }