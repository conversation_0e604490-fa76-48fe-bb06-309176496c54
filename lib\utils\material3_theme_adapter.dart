import 'package:flutter/material.dart';
import 'theme_colors.dart';

/// 🎨 Material 3 主题适配器
/// 统一管理主题色彩映射和组件适配，确保整个应用使用一致的Material 3主题
class Material3ThemeAdapter {
  /// 🎯 获取主题色彩方案
  static ColorScheme getColorScheme(BuildContext context) {
    return Theme.of(context).colorScheme;
  }

  /// 🌈 获取功能色彩（基于主题）
  static Map<String, Color> getFunctionalColors(BuildContext context) {
    final theme = Theme.of(context);

    return {
      'primary': ThemeColors.primary,
      'secondary': ThemeColors.secondary,
      'accent': ThemeColors.accent,
      'success': ThemeColors.success,
      'warning': ThemeColors.warning,
      'error': ThemeColors.error,
      'info': ThemeColors.info,
      'surface': theme.colorScheme.surface,
      'background': theme.colorScheme.surface,
      'onSurface': theme.colorScheme.onSurface,
      'onBackground': theme.colorScheme.onSurface,
    };
  }

  /// 🎨 获取状态色彩
  static Color getStatusColor(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'available':
        return ThemeColors.success;
      case 'warning':
      case 'pending':
      case 'queued':
      case 'busy':
        return ThemeColors.warning;
      case 'error':
      case 'failed':
      case 'offline':
        return ThemeColors.error;
      case 'info':
      case 'processing':
      case 'uploading':
        return ThemeColors.accent;
      case 'primary':
        return ThemeColors.primary;
      default:
        return ThemeColors.textMedium;
    }
  }

  /// 🎨 获取角色色彩
  static Color getRoleColor(String role) {
    switch (role) {
      case '叉车':
        return ThemeColors.primary;
      case '仓管':
        return ThemeColors.success;
      case '主管':
        return ThemeColors.warning;
      case '班长':
        return ThemeColors.accent;
      default:
        return ThemeColors.textMedium;
    }
  }

  /// 🎨 获取类别色彩
  static Color getCategoryColor(String category) {
    // 根据类别返回对应的颜色
    switch (category.toLowerCase()) {
      case 'electronics':
      case '电子':
        return ThemeColors.primary;
      case 'clothing':
      case '服装':
        return ThemeColors.secondary;
      case 'food':
      case '食品':
        return ThemeColors.success;
      case 'furniture':
      case '家具':
        return ThemeColors.warning;
      case 'books':
      case '图书':
        return ThemeColors.accent;
      default:
        return ThemeColors.primary;
    }
  }

  /// 🎨 获取渐变色彩
  static List<LinearGradient> getGradients() {
    return [
      ThemeColors.primaryGradient,
      ThemeColors.blueGradient,
      ThemeColors.greenGradient,
      ThemeColors.yellowGradient,
      ThemeColors.orangeGradient,
      ThemeColors.purpleGradient,
      ThemeColors.tealGradient,
    ];
  }

  /// 🎨 获取现代化渐变
  static List<LinearGradient> getModernGradients() {
    return [
      ThemeColors.modernPrimaryGradient,
      ThemeColors.modernGlassGradient,
      ThemeColors.modernSuccessGradient,
      ThemeColors.modernWarningGradient,
    ];
  }

  /// 🎨 获取卡片装饰
  static BoxDecoration getCardDecoration(
    BuildContext context, {
    bool elevated = true,
    bool useGradient = false,
    bool useGlass = false,
  }) {
    final theme = Theme.of(context);

    if (useGradient) {
      return BoxDecoration(
        gradient: ThemeColors.primaryGradient,
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        boxShadow: elevated ? ThemeColors.cardShadow : null,
      );
    }

    if (useGlass) {
      return BoxDecoration(
        color: ThemeColors.glassBackground,
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        border: Border.all(color: ThemeColors.glassBorder),
        boxShadow: elevated ? ThemeColors.cardShadow : null,
      );
    }

    return BoxDecoration(
      color: theme.colorScheme.surface,
      borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
      boxShadow: elevated
          ? [
              BoxShadow(
                color: theme.shadowColor.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ]
          : null,
    );
  }

  /// 🎨 获取按钮样式
  static ButtonStyle getButtonStyle(
    BuildContext context, {
    ButtonType type = ButtonType.primary,
    bool isOutlined = false,
  }) {
    final theme = Theme.of(context);

    switch (type) {
      case ButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor:
              isOutlined ? Colors.transparent : ThemeColors.primary,
          foregroundColor:
              isOutlined ? ThemeColors.primary : ThemeColors.textOnDark,
          elevation: isOutlined ? 0 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
            side: isOutlined
                ? BorderSide(color: ThemeColors.primary)
                : BorderSide.none,
          ),
        );
      case ButtonType.success:
        return ElevatedButton.styleFrom(
          backgroundColor:
              isOutlined ? Colors.transparent : ThemeColors.success,
          foregroundColor:
              isOutlined ? ThemeColors.success : ThemeColors.textOnDark,
          elevation: isOutlined ? 0 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
            side: isOutlined
                ? BorderSide(color: ThemeColors.success)
                : BorderSide.none,
          ),
        );
      case ButtonType.warning:
        return ElevatedButton.styleFrom(
          backgroundColor:
              isOutlined ? Colors.transparent : ThemeColors.warning,
          foregroundColor:
              isOutlined ? ThemeColors.warning : ThemeColors.textOnDark,
          elevation: isOutlined ? 0 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
            side: isOutlined
                ? BorderSide(color: ThemeColors.warning)
                : BorderSide.none,
          ),
        );
      case ButtonType.danger:
        return ElevatedButton.styleFrom(
          backgroundColor: isOutlined ? Colors.transparent : ThemeColors.error,
          foregroundColor:
              isOutlined ? ThemeColors.error : ThemeColors.textOnDark,
          elevation: isOutlined ? 0 : 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
            side: isOutlined
                ? BorderSide(color: ThemeColors.error)
                : BorderSide.none,
          ),
        );
      case ButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: isOutlined
              ? Colors.transparent
              : theme.colorScheme.secondaryContainer,
          foregroundColor: isOutlined
              ? theme.colorScheme.onSurface
              : theme.colorScheme.onSecondaryContainer,
          elevation: isOutlined ? 0 : 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
            side: isOutlined
                ? BorderSide(color: theme.colorScheme.outline)
                : BorderSide.none,
          ),
        );
    }
  }

  /// 🎨 获取输入框装饰
  static InputDecoration getInputDecoration(
    BuildContext context, {
    String? labelText,
    String? hintText,
    String? helperText,
    String? errorText,
    IconData? prefixIcon,
    IconData? suffixIcon,
  }) {
    final theme = Theme.of(context);

    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon != null
          ? Icon(prefixIcon, color: theme.colorScheme.onSurfaceVariant)
          : null,
      suffixIcon: suffixIcon != null
          ? Icon(suffixIcon, color: theme.colorScheme.onSurfaceVariant)
          : null,
      filled: true,
      fillColor: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        borderSide: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        borderSide: BorderSide(
          color: theme.colorScheme.primary,
          width: 2,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        borderSide: BorderSide(
          color: theme.colorScheme.error,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        borderSide: BorderSide(
          color: theme.colorScheme.error,
          width: 2,
        ),
      ),
      labelStyle: TextStyle(
        color: theme.colorScheme.onSurfaceVariant,
        fontSize: 16,
        fontWeight: FontWeight.w400,
      ),
      hintStyle: TextStyle(
        color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
        fontSize: 16,
        fontWeight: FontWeight.w400,
      ),
      helperStyle: TextStyle(
        color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
        fontSize: 12,
      ),
      errorStyle: TextStyle(
        color: theme.colorScheme.error,
        fontSize: 12,
      ),
    );
  }

  /// 🎨 获取文本样式
  static TextStyle getTextStyle(
    BuildContext context, {
    TextType type = TextType.bodyLarge,
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
  }) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.colorScheme.onSurface;

    switch (type) {
      case TextType.displayLarge:
        return TextStyle(
          fontSize: fontSize ?? 32,
          fontWeight: fontWeight ?? FontWeight.bold,
          color: effectiveColor,
        );
      case TextType.displayMedium:
        return TextStyle(
          fontSize: fontSize ?? 28,
          fontWeight: fontWeight ?? FontWeight.bold,
          color: effectiveColor,
        );
      case TextType.displaySmall:
        return TextStyle(
          fontSize: fontSize ?? 24,
          fontWeight: fontWeight ?? FontWeight.bold,
          color: effectiveColor,
        );
      case TextType.headlineLarge:
        return TextStyle(
          fontSize: fontSize ?? 22,
          fontWeight: fontWeight ?? FontWeight.w600,
          color: effectiveColor,
        );
      case TextType.headlineMedium:
        return TextStyle(
          fontSize: fontSize ?? 20,
          fontWeight: fontWeight ?? FontWeight.w600,
          color: effectiveColor,
        );
      case TextType.headlineSmall:
        return TextStyle(
          fontSize: fontSize ?? 18,
          fontWeight: fontWeight ?? FontWeight.w600,
          color: effectiveColor,
        );
      case TextType.titleLarge:
        return TextStyle(
          fontSize: fontSize ?? 16,
          fontWeight: fontWeight ?? FontWeight.w500,
          color: effectiveColor,
        );
      case TextType.titleMedium:
        return TextStyle(
          fontSize: fontSize ?? 14,
          fontWeight: fontWeight ?? FontWeight.w500,
          color: effectiveColor,
        );
      case TextType.titleSmall:
        return TextStyle(
          fontSize: fontSize ?? 12,
          fontWeight: fontWeight ?? FontWeight.w500,
          color: effectiveColor,
        );
      case TextType.bodyLarge:
        return TextStyle(
          fontSize: fontSize ?? 16,
          fontWeight: fontWeight ?? FontWeight.w400,
          color: effectiveColor,
        );
      case TextType.bodyMedium:
        return TextStyle(
          fontSize: fontSize ?? 14,
          fontWeight: fontWeight ?? FontWeight.w400,
          color: effectiveColor,
        );
      case TextType.bodySmall:
        return TextStyle(
          fontSize: fontSize ?? 12,
          fontWeight: fontWeight ?? FontWeight.w400,
          color: effectiveColor,
        );
      case TextType.labelLarge:
        return TextStyle(
          fontSize: fontSize ?? 14,
          fontWeight: fontWeight ?? FontWeight.w500,
          color: effectiveColor,
        );
      case TextType.labelMedium:
        return TextStyle(
          fontSize: fontSize ?? 12,
          fontWeight: fontWeight ?? FontWeight.w500,
          color: effectiveColor,
        );
      case TextType.labelSmall:
        return TextStyle(
          fontSize: fontSize ?? 11,
          fontWeight: fontWeight ?? FontWeight.w500,
          color: effectiveColor,
        );
    }
  }
}

/// 🎨 按钮类型枚举
enum ButtonType {
  primary,
  secondary,
  success,
  warning,
  danger,
}

/// 🎨 文本类型枚举
enum TextType {
  displayLarge,
  displayMedium,
  displaySmall,
  headlineLarge,
  headlineMedium,
  headlineSmall,
  titleLarge,
  titleMedium,
  titleSmall,
  bodyLarge,
  bodyMedium,
  bodySmall,
  labelLarge,
  labelMedium,
  labelSmall,
}
