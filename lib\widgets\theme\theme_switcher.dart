import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/theme_provider.dart';
import '../animated/professional_button.dart';

/// 🎨 Material 3 主题切换器组件
/// 支持多种显示样式和交互方式
class ThemeSwitcher extends ConsumerWidget {
  final ThemeSwitcherStyle style;
  final bool showLabel;
  final String? tooltip;
  final VoidCallback? onThemeChanged;

  const ThemeSwitcher({
    Key? key,
    this.style = ThemeSwitcherStyle.iconButton,
    this.showLabel = false,
    this.tooltip,
    this.onThemeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);
    final themeNotifier = ref.read(themeProvider.notifier);

    switch (style) {
      case ThemeSwitcherStyle.iconButton:
        return _buildIconButton(context, themeNotifier, themeMode);
      case ThemeSwitcherStyle.segmentedButton:
        return _buildSegmentedButton(context, themeNotifier, themeMode);
      case ThemeSwitcherStyle.dropdown:
        return _buildDropdown(context, themeNotifier, themeMode);
      case ThemeSwitcherStyle.card:
        return _buildCard(context, themeNotifier, themeMode);
      case ThemeSwitcherStyle.listTile:
        return _buildListTile(context, themeNotifier, themeMode);
    }
  }

  /// 构建图标按钮样式
  Widget _buildIconButton(
    BuildContext context,
    ThemeNotifier themeNotifier,
    ThemeMode themeMode,
  ) {
    return IconButton(
      icon: Icon(themeNotifier.currentThemeIcon),
      tooltip: tooltip ?? '切换主题 (${themeNotifier.currentThemeName})',
      onPressed: () async {
        await themeNotifier.toggleTheme();
        HapticFeedback.lightImpact();
        onThemeChanged?.call();
      },
    );
  }

  /// 构建分段按钮样式
  Widget _buildSegmentedButton(
    BuildContext context,
    ThemeNotifier themeNotifier,
    ThemeMode themeMode,
  ) {
    return SegmentedButton<ThemeMode>(
      segments: const [
        ButtonSegment<ThemeMode>(
          value: ThemeMode.light,
          icon: Icon(Icons.light_mode),
          label: Text('亮色'),
        ),
        ButtonSegment<ThemeMode>(
          value: ThemeMode.dark,
          icon: Icon(Icons.dark_mode),
          label: Text('暗色'),
        ),
        ButtonSegment<ThemeMode>(
          value: ThemeMode.system,
          icon: Icon(Icons.brightness_auto),
          label: Text('自动'),
        ),
      ],
      selected: {themeMode},
      onSelectionChanged: (Set<ThemeMode> selection) async {
        final selectedMode = selection.first;
        switch (selectedMode) {
          case ThemeMode.light:
            await themeNotifier.setLightMode();
            break;
          case ThemeMode.dark:
            await themeNotifier.setDarkMode();
            break;
          case ThemeMode.system:
            await themeNotifier.setSystemMode();
            break;
        }
        HapticFeedback.lightImpact();
        onThemeChanged?.call();
      },
    );
  }

  /// 构建下拉菜单样式
  Widget _buildDropdown(
    BuildContext context,
    ThemeNotifier themeNotifier,
    ThemeMode themeMode,
  ) {
    return DropdownButton<ThemeMode>(
      value: themeMode,
      icon: const Icon(Icons.arrow_drop_down),
      underline: Container(),
      items: const [
        DropdownMenuItem(
          value: ThemeMode.light,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.light_mode),
              SizedBox(width: 8),
              Text('亮色模式'),
            ],
          ),
        ),
        DropdownMenuItem(
          value: ThemeMode.dark,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.dark_mode),
              SizedBox(width: 8),
              Text('暗色模式'),
            ],
          ),
        ),
        DropdownMenuItem(
          value: ThemeMode.system,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.brightness_auto),
              SizedBox(width: 8),
              Text('跟随系统'),
            ],
          ),
        ),
      ],
      onChanged: (ThemeMode? newMode) async {
        if (newMode != null) {
          switch (newMode) {
            case ThemeMode.light:
              await themeNotifier.setLightMode();
              break;
            case ThemeMode.dark:
              await themeNotifier.setDarkMode();
              break;
            case ThemeMode.system:
              await themeNotifier.setSystemMode();
              break;
          }
          HapticFeedback.lightImpact();
          onThemeChanged?.call();
        }
      },
    );
  }

  /// 构建卡片样式
  Widget _buildCard(
    BuildContext context,
    ThemeNotifier themeNotifier,
    ThemeMode themeMode,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  Icons.palette,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  '主题设置',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSegmentedButton(context, themeNotifier, themeMode),
          ],
        ),
      ),
    );
  }

  /// 构建列表项样式
  Widget _buildListTile(
    BuildContext context,
    ThemeNotifier themeNotifier,
    ThemeMode themeMode,
  ) {
    return ListTile(
      leading: Icon(themeNotifier.currentThemeIcon),
      title: const Text('主题模式'),
      subtitle: Text(themeNotifier.currentThemeName),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        _showThemeDialog(context, themeNotifier, themeMode);
      },
    );
  }

  /// 显示主题选择对话框
  void _showThemeDialog(
    BuildContext context,
    ThemeNotifier themeNotifier,
    ThemeMode currentMode,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择主题'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<ThemeMode>(
              title: const Text('亮色模式'),
              subtitle: const Text('始终使用亮色主题'),
              value: ThemeMode.light,
              groupValue: currentMode,
              onChanged: (value) async {
                if (value != null) {
                  await themeNotifier.setLightMode();
                  Navigator.of(context).pop();
                  HapticFeedback.lightImpact();
                  onThemeChanged?.call();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('暗色模式'),
              subtitle: const Text('始终使用暗色主题'),
              value: ThemeMode.dark,
              groupValue: currentMode,
              onChanged: (value) async {
                if (value != null) {
                  await themeNotifier.setDarkMode();
                  Navigator.of(context).pop();
                  HapticFeedback.lightImpact();
                  onThemeChanged?.call();
                }
              },
            ),
            RadioListTile<ThemeMode>(
              title: const Text('跟随系统'),
              subtitle: const Text('根据系统设置自动切换'),
              value: ThemeMode.system,
              groupValue: currentMode,
              onChanged: (value) async {
                if (value != null) {
                  await themeNotifier.setSystemMode();
                  Navigator.of(context).pop();
                  HapticFeedback.lightImpact();
                  onThemeChanged?.call();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }
}

/// 主题切换器样式枚举
enum ThemeSwitcherStyle {
  /// 图标按钮样式
  iconButton,
  /// 分段按钮样式
  segmentedButton,
  /// 下拉菜单样式
  dropdown,
  /// 卡片样式
  card,
  /// 列表项样式
  listTile,
}

/// 🎨 快速主题切换浮动按钮
class ThemeToggleFAB extends ConsumerWidget {
  final VoidCallback? onThemeChanged;

  const ThemeToggleFAB({
    Key? key,
    this.onThemeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeNotifier = ref.read(themeProvider.notifier);

    return FloatingActionButton.small(
      onPressed: () async {
        await themeNotifier.toggleTheme();
        HapticFeedback.lightImpact();
        onThemeChanged?.call();
      },
      tooltip: '切换主题',
      child: Icon(themeNotifier.currentThemeIcon),
    );
  }
}

/// 🎨 主题状态指示器
class ThemeIndicator extends ConsumerWidget {
  final bool showText;

  const ThemeIndicator({
    Key? key,
    this.showText = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeNotifier = ref.read(themeProvider.notifier);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            themeNotifier.currentThemeIcon,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          if (showText) ...[
            const SizedBox(width: 6),
            Text(
              themeNotifier.currentThemeName,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }
}