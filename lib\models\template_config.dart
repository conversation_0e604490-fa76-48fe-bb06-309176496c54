/// 拍照模板配置 - ML Kit V2专业版
class TemplateConfig {
  /// 获取指定模板的照片配置
  static List<PhotoConfig> getPhotoConfigs(String template) {
    switch (template) {
      case '平板车':
        return _flatbedTruckPhotos;
      case '集装箱':
        return _containerPhotos;
      default:
        return [];
    }
  }

  /// 获取模板的分组信息
  static List<PhotoGroup> getPhotoGroups(String template) {
    switch (template) {
      case '平板车':
        return _flatbedTruckGroups;
      case '集装箱':
        return _containerGroups;
      default:
        return [];
    }
  }

  /// 平板车分组配置
  static const List<PhotoGroup> _flatbedTruckGroups = [
    PhotoGroup(
      id: 'documents',
      name: '必拍文档组',
      description: '车辆证件与提货单合照（不计入识别统计）',
      icon: '📋',
      color: 0xFF4CAF50,
      isRequired: true,
      includeInStatistics: false,
      stage: 1,
    ),
    PhotoGroup(
      id: 'cargo_recognition',
      name: '货物识别组',
      description: '每托货物一张标签识别（重点统计）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 2,
    ),
    PhotoGroup(
      id: 'loading_verification',
      name: '装载验证组',
      description: '装载完成状态验证（不计入识别统计）',
      icon: '🚛',
      color: 0xFFFF9800,
      isRequired: true,
      includeInStatistics: false,
      stage: 3,
    ),
    PhotoGroup(
      id: 'additional',
      name: '扩展补充组',
      description: '应急补充材料（可选）',
      icon: '📸',
      color: 0xFF9C27B0,
      isRequired: false,
      includeInStatistics: false,
      stage: 4,
    ),
  ];

  /// 📷 平板车照片配置 - **最终版，根据用户详细要求**
  static const List<PhotoConfig> _flatbedTruckPhotos = [
    // **阶段1：文档组 (4张) - 不需要识别**
    PhotoConfig(
      id: 'head_plate_doc',
      label: '1. 车头车牌与提货单合照',
      description: '车头车牌清晰可见，与提货单同时入镜',
      groupId: 'documents',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1,
      order: 1,
      tips: '确保车牌号码清晰，提货单信息可读',
    ),
    PhotoConfig(
      id: 'tail_plate_doc',
      label: '2. 车尾挂牌与提货单合照',
      description: '车尾及挂车牌照与提货单合影',
      groupId: 'documents',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1,
      order: 2,
      tips: '包含主车和挂车牌照，提货单需清晰',
    ),
    PhotoConfig(
      id: 'batch_doc',
      label: '3. 货物批号与提货单合照',
      description: '货物批号标识与提货单对应拍摄',
      groupId: 'documents',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1,
      order: 3,
      tips: '货物批号标签清晰，与提货单信息对应',
    ),
    PhotoConfig(
      id: 'certificate',
      label: '4. 合格证照片',
      description: '产品合格证或质检证明',
      groupId: 'documents',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1,
      order: 4,
      tips: '合格证信息完整，字迹清晰可读',
    ),

    // **阶段2：Professional货物识别组 (20托货物) - 全部需要识别**
    PhotoConfig(
      id: 'cargo_label_1',
      label: '5. 第1托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 5,
      tips: '拍摄第1托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_2',
      label: '6. 第2托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 6,
      tips: '拍摄第2托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_3',
      label: '7. 第3托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 7,
      tips: '拍摄第3托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_4',
      label: '8. 第4托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 8,
      tips: '拍摄第4托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_5',
      label: '9. 第5托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 9,
      tips: '拍摄第5托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_6',
      label: '10. 第6托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 10,
      tips: '拍摄第6托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_7',
      label: '11. 第7托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 11,
      tips: '拍摄第7托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_8',
      label: '12. 第8托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 12,
      tips: '拍摄第8托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_9',
      label: '13. 第9托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 13,
      tips: '拍摄第9托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_10',
      label: '14. 第10托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 14,
      tips: '拍摄第10托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_11',
      label: '15. 第11托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 15,
      tips: '拍摄第11托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_12',
      label: '16. 第12托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 16,
      tips: '拍摄第12托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_13',
      label: '17. 第13托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 17,
      tips: '拍摄第13托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_14',
      label: '18. 第14托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 18,
      tips: '拍摄第14托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_15',
      label: '19. 第15托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 19,
      tips: '拍摄第15托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_16',
      label: '20. 第16托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 20,
      tips: '拍摄第16托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_17',
      label: '21. 第17托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 21,
      tips: '拍摄第17托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_18',
      label: '22. 第18托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 22,
      tips: '拍摄第18托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_19',
      label: '23. 第19托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 23,
      tips: '拍摄第19托货物标签',
    ),
    PhotoConfig(
      id: 'cargo_label_20',
      label: '24. 第20托货物标签',
      description: 'Professional混合识别',
      groupId: 'cargo_recognition',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2,
      order: 24,
      tips: '拍摄第20托货物标签',
    ),

    // **阶段3: 装载验证组 (2张) - 不需要识别**
    PhotoConfig(
      id: 'loading_left',
      label: '25. 左侧装载完成照片',
      description: '车辆左侧装载满载状态全景',
      groupId: 'loading_verification',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 3,
      order: 25,
      tips: '拍摄车辆左侧全貌，显示装载状态',
    ),
    PhotoConfig(
      id: 'loading_right',
      label: '26. 右侧装载完成照片',
      description: '车辆右侧装载满载状态全景',
      groupId: 'loading_verification',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 3,
      order: 26,
      tips: '拍摄车辆右侧全貌，确认装载完成',
    ),

    // **阶段4: 扩展补充组 (可选)**
    PhotoConfig(
      id: 'additional_custom',
      label: '添加更多',
      description: '自定义补充照片，应对特殊情况',
      groupId: 'additional',
      isRequired: false,
      needRecognition: false,
      recognitionType: RecognitionType.none,
      stage: 4,
      order: 999,
      tips: '可添加任何需要补充的照片材料',
      isCustom: true,
    ),
  ];

  /// 集装箱分组配置 - 完整35张照片流程
  static const List<PhotoGroup> _containerGroups = [
    PhotoGroup(
      id: 'container_prep',
      name: '前期准备组',
      description: '车辆信息与集装箱准备（5张必拍）',
      icon: '🚛',
      color: 0xFF4CAF50,
      isRequired: true,
      includeInStatistics: false,
      stage: 1,
    ),
    PhotoGroup(
      id: 'first_layer_cargo',
      name: '第一层货物组',
      description: '第一层货物识别（3张：合照+2个货物识别）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 2,
    ),
    PhotoGroup(
      id: 'second_layer_cargo',
      name: '第二层货物组',
      description: '第二层货物识别（3张：合照+2个货物识别）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 3,
    ),
    PhotoGroup(
      id: 'third_layer_cargo',
      name: '第三层货物组',
      description: '第三层货物识别（3张：合照+2个货物识别）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 4,
    ),
    PhotoGroup(
      id: 'fourth_layer_cargo',
      name: '第四层货物组',
      description: '第四层货物识别（3张：半箱照+2个货物识别）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 5,
    ),
    PhotoGroup(
      id: 'fifth_layer_cargo',
      name: '第五层货物组',
      description: '第五层货物识别（3张：合照+2个货物识别）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 6,
    ),
    PhotoGroup(
      id: 'sixth_layer_cargo',
      name: '第六层货物组',
      description: '第六层货物识别（3张：合照+2个货物识别）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 7,
    ),
    PhotoGroup(
      id: 'seventh_layer_cargo',
      name: '第七至九层货物组',
      description: '第七至九层货物识别（8张：6个货物识别+2张合照）',
      icon: '🔍',
      color: 0xFF2196F3,
      isRequired: true,
      includeInStatistics: true,
      stage: 8,
    ),
    PhotoGroup(
      id: 'container_closure',
      name: '封箱验证组',
      description: '封箱过程记录（4张必拍）',
      icon: '📦',
      color: 0xFFFF9800,
      isRequired: true,
      includeInStatistics: false,
      stage: 9,
    ),
    PhotoGroup(
      id: 'additional_container',
      name: '扩展补充组',
      description: '应急补充材料（可选）',
      icon: '📸',
      color: 0xFF9C27B0,
      isRequired: false,
      includeInStatistics: false,
      stage: 10,
    ),
  ];

  /// 📦 集装箱照片配置 - **最终版，根据用户详细要求**
  static const List<PhotoConfig> _containerPhotos = [
    // **前期准备组 (5张) - 不需要识别**
    PhotoConfig(
      id: 'head_plate_doc',
      label: '1.车头车牌与提货单合照',
      groupId: 'container_prep',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1, order: 1, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'tail_plate_doc',
      label: '2.车尾挂牌与提货单合照',
      groupId: 'container_prep',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1, order: 2, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'empty_container',
      label: '3.集装箱空箱照片',
      groupId: 'container_prep',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1, order: 3, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'container_number',
      label: '4.集装箱箱号照片',
      groupId: 'container_prep',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1, order: 4, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'certificate',
      label: '5.合格证照片',
      groupId: 'container_prep',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 1, order: 5, description: '', tips: '',
    ),

    // **第一层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'first_layer_cargo_1',
      label: '6.第一层货物标签-1',
      groupId: 'first_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2, order: 6, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'first_layer_cargo_2',
      label: '7.第一层货物标签-2',
      groupId: 'first_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 2, order: 7, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'first_layer_group',
      label: '8.第一排合照',
      groupId: 'first_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 2, order: 8, description: '', tips: '',
    ),

    // **第二层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'second_layer_cargo_1',
      label: '9.第二层货物标签-1',
      groupId: 'second_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 3, order: 9, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'second_layer_cargo_2',
      label: '10.第二层货物标签-2',
      groupId: 'second_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 3, order: 10, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'second_layer_group',
      label: '11.第二排合照',
      groupId: 'second_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 3, order: 11, description: '', tips: '',
    ),

    // **第三层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'third_layer_cargo_1',
      label: '12.第三层货物标签-1',
      groupId: 'third_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 4, order: 12, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'third_layer_cargo_2',
      label: '13.第三层货物标签-2',
      groupId: 'third_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 4, order: 13, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'third_layer_group',
      label: '14.第三排合照',
      groupId: 'third_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 4, order: 14, description: '', tips: '',
    ),

    // **第四层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'fourth_layer_cargo_1',
      label: '15.第四层货物标签-1',
      groupId: 'fourth_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 5, order: 15, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'fourth_layer_cargo_2',
      label: '16.第四层货物标签-2',
      groupId: 'fourth_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 5, order: 16, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'half_container',
      label: '17.半箱照',
      groupId: 'fourth_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 5, order: 17, description: '', tips: '',
    ),

    // **第五层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'fifth_layer_cargo_1',
      label: '18.第五层货物标签-1',
      groupId: 'fifth_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 6, order: 18, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'fifth_layer_cargo_2',
      label: '19.第五层货物标签-2',
      groupId: 'fifth_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 6, order: 19, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'fifth_layer_group',
      label: '20.第五排合照',
      groupId: 'fifth_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 6, order: 20, description: '', tips: '',
    ),

    // **第六层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'sixth_layer_cargo_1',
      label: '21.第六层货物标签-1',
      groupId: 'sixth_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 7, order: 21, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'sixth_layer_cargo_2',
      label: '22.第六层货物标签-2',
      groupId: 'sixth_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 7, order: 22, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'sixth_layer_group',
      label: '23.第六排合照',
      groupId: 'sixth_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 7, order: 23, description: '', tips: '',
    ),

    // **第七层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'seventh_layer_cargo_1',
      label: '24.第七层货物标签-1',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 8, order: 24, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'seventh_layer_cargo_2',
      label: '25.第七层货物标签-2',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 8, order: 25, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'seventh_layer_group',
      label: '26.第七排合照',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 8, order: 26, description: '', tips: '',
    ),

    // **第八层 (3张) - 2张需要识别**
    PhotoConfig(
      id: 'eighth_layer_cargo_1',
      label: '27.第八层货物标签-1',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 8, order: 27, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'eighth_layer_cargo_2',
      label: '28.第八层货物标签-2',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 8, order: 28, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'eighth_layer_group',
      label: '29.第八排合照',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 8, order: 29, description: '', tips: '',
    ),

    // **第九层 (2张) - 2张需要识别**
    PhotoConfig(
      id: 'ninth_layer_cargo_1',
      label: '30.第九层货物标签-1',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 8, order: 30, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'ninth_layer_cargo_2',
      label: '31.第九层货物标签-2',
      groupId: 'seventh_layer_cargo',
      isRequired: true,
      needRecognition: true, // 用户指定需要识别
      recognitionType: RecognitionType.mixed,
      stage: 8, order: 31, description: '', tips: '',
    ),

    // **封箱组 (4张) - 不需要识别**
    PhotoConfig(
      id: 'full_container',
      label: '32.满箱照',
      groupId: 'container_closure',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 9, order: 32, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'half_door_close',
      label: '33.集装箱半关门照片',
      groupId: 'container_closure',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 9, order: 33, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'full_door_close',
      label: '34.集装箱全关门照片',
      groupId: 'container_closure',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 9, order: 34, description: '', tips: '',
    ),
    PhotoConfig(
      id: 'lead_seal',
      label: '35.铅封照片',
      groupId: 'container_closure',
      isRequired: true,
      needRecognition: false, // 用户指定不识别
      recognitionType: RecognitionType.none,
      stage: 9, order: 35, description: '', tips: '',
    ),

    // **扩展补充组 (可选)**
    PhotoConfig(
      id: 'additional_custom',
      label: '添加更多',
      groupId: 'additional_container',
      isRequired: false,
      needRecognition: false,
      recognitionType: RecognitionType.none,
      stage: 10,
      order: 999,
      tips: '可添加任何需要补充的照片材料',
      isCustom: true,
      description: '',
    ),
  ];
}

/// 照片分组配置
class PhotoGroup {
  final String id;
  final String name;
  final String description;
  final String icon;
  final int color;
  final bool isRequired;
  final bool includeInStatistics; // 是否计入统计
  final int stage; // 拍摄阶段

  const PhotoGroup({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.isRequired,
    required this.includeInStatistics,
    required this.stage,
  });
}

/// 照片配置 - ML Kit V2专业版
class PhotoConfig {
  final String id;
  final String label;
  final String description;
  final String groupId;
  final bool isRequired;
  final bool needRecognition;
  final RecognitionType recognitionType;
  final int stage;
  final int order;
  final String tips;
  final String? retryPrompt;
  final bool isCustom;

  const PhotoConfig({
    required this.id,
    required this.label,
    required this.description,
    required this.groupId,
    required this.isRequired,
    required this.needRecognition,
    required this.recognitionType,
    required this.stage,
    required this.order,
    required this.tips,
    this.retryPrompt,
    this.isCustom = false,
  });
}

/// Professional识别类型
enum RecognitionType {
  none, // 不识别
  qrCode, // 二维码识别
  barcode, // 条形码识别
  text, // 文字识别(OCR)
  mixed, // 混合识别(支持多种类型)
}
