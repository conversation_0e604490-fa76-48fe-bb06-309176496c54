import 'dart:async';
import 'package:flutter/material.dart';
import 'package:loadguard/services/performance_manager.dart';
import 'package:loadguard/services/async_upload_service.dart';
import 'package:loadguard/services/resource_manager.dart';

/// 📊 性能监控面板组件
class PerformanceDashboardWidget extends StatefulWidget {
  const PerformanceDashboardWidget({Key? key}) : super(key: key);

  @override
  State<PerformanceDashboardWidget> createState() =>
      _PerformanceDashboardWidgetState();
}

class _PerformanceDashboardWidgetState
    extends State<PerformanceDashboardWidget> {
  final _performanceMonitor = PerformanceMonitorService();

  Map<String, dynamic> _performanceData = {};
  bool _isMonitoring = false;
  Timer? _updateTimer; // 🔧 添加Timer引用

  @override
  void initState() {
    super.initState();
    _initializeMonitoring();
  }

  Future<void> _initializeMonitoring() async {
    try {
      await _performanceMonitor.initialize();
      await _performanceMonitor.startMonitoring();
      setState(() {
        _isMonitoring = true;
      });

      // 🔧 修复：保存Timer引用并检查mounted状态
      _updateTimer = Timer.periodic(const Duration(seconds: 5), (_) {
        if (mounted) _updatePerformanceData();
      });
    } catch (e) {
      // 初始化失败时的处理
      setState(() {
        _isMonitoring = false;
      });
    }
  }

  void _updatePerformanceData() {
    if (!mounted) return;
    
    try {
      setState(() {
        _performanceData = _performanceMonitor.getPerformanceSnapshot();
      });
    } catch (e) {
      // 更新失败时的处理
    }
  }

  @override
  void dispose() {
    // 🔧 修复：正确取消Timer
    _updateTimer?.cancel();
    _updateTimer = null;
    
    // 停止性能监控
    if (_isMonitoring) {
      _performanceMonitor.stopMonitoring();
    }
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildMetricsGrid(),
            const SizedBox(height: 16),
            _buildControlButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.analytics, color: Colors.blue, size: 28),
        const SizedBox(width: 12),
        const Text(
          '性能监控面板',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _isMonitoring ? Colors.green : Colors.red,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _isMonitoring ? '监控中' : '已停止',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildMetricsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 2.5,
      children: [
        _buildMetricCard(
            '内存使用率',
            _performanceData['memory_usage']?['value']?.toString() ?? '0',
            '%',
            Colors.orange),
        _buildMetricCard(
            '活跃上传',
            _performanceData['active_uploads']?['value']?.toString() ?? '0',
            '个',
            Colors.blue),
        _buildMetricCard(
            '网络连接',
            _performanceData['active_connections']?['value']?.toString() ?? '0',
            '个',
            Colors.green),
        _buildMetricCard(
            'CPU使用率',
            _performanceData['cpu_usage']?['value']?.toString() ?? '0',
            '%',
            Colors.red),
      ],
    );
  }

  Widget _buildMetricCard(
      String title, String value, String unit, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                unit,
                style: TextStyle(
                  fontSize: 12,
                  color: color.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Row(
      children: [
        Expanded(
          child: FilledButton.icon(
            onPressed: _isMonitoring
                ? null
                : () async {
                    await _performanceMonitor.startMonitoring();
                    setState(() {
                      _isMonitoring = true;
                    });
                  },
            icon: const Icon(Icons.play_arrow),
            label: const Text('开始监控'),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: FilledButton.icon(
            onPressed: _isMonitoring
                ? () {
                    _performanceMonitor.stopMonitoring();
                    setState(() {
                      _isMonitoring = false;
                    });
                  }
                : null,
            icon: const Icon(Icons.stop),
            label: const Text('停止监控'),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: FilledButton.icon(
            onPressed: () async {
              await _performanceMonitor.establishBaselines();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('性能基线已建立')),
                );
              }
            },
            icon: const Icon(Icons.analytics),
            label: const Text('建立基线'),
          ),
        ),
      ],
    );
  }


}
