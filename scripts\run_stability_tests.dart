#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;

/// 🤖 CI/CD自动化稳定性测试脚本
/// 
/// 功能：
/// 1. 自动运行Isolate稳定性测试
/// 2. 生成详细的测试报告
/// 3. 支持CI/CD集成
/// 4. 自动判断测试通过/失败
class StabilityTestRunner {
  static const String TEST_RESULTS_DIR = 'test_results';
  static const String REPORT_FILE = 'stability_test_report.json';
  static const String LOG_FILE = 'stability_test.log';
  
  Future<void> main(List<String> args) async {
    print('🤖 LoadGuard Isolate稳定性测试启动器');
    print('=' * 50);
    
    try {
      // 1. 环境检查
      await _checkEnvironment();
      
      // 2. 准备测试环境
      await _prepareTestEnvironment();
      
      // 3. 运行稳定性测试
      final testResult = await _runStabilityTests();
      
      // 4. 生成报告
      await _generateReports(testResult);
      
      // 5. 判断测试结果
      final passed = _evaluateTestResult(testResult);
      
      if (passed) {
        print('✅ 所有稳定性测试通过！');
        exit(0);
      } else {
        print('❌ 稳定性测试失败！');
        exit(1);
      }
      
    } catch (e) {
      print('💥 测试执行失败: $e');
      exit(2);
    }
  }
  
  /// 🔍 环境检查
  Future<void> _checkEnvironment() async {
    print('🔍 检查测试环境...');
    
    // 检查Flutter环境
    final flutterResult = await Process.run('flutter', ['--version']);
    if (flutterResult.exitCode != 0) {
      throw Exception('Flutter环境未正确配置');
    }
    
    // 检查Dart环境
    final dartResult = await Process.run('dart', ['--version']);
    if (dartResult.exitCode != 0) {
      throw Exception('Dart环境未正确配置');
    }
    
    // 检查项目依赖
    final pubGetResult = await Process.run('flutter', ['pub', 'get']);
    if (pubGetResult.exitCode != 0) {
      throw Exception('依赖安装失败');
    }
    
    print('✅ 环境检查通过');
  }
  
  /// 🛠️ 准备测试环境
  Future<void> _prepareTestEnvironment() async {
    print('🛠️ 准备测试环境...');
    
    // 创建测试结果目录
    final resultsDir = Directory(TEST_RESULTS_DIR);
    if (await resultsDir.exists()) {
      await resultsDir.delete(recursive: true);
    }
    await resultsDir.create(recursive: true);
    
    // 创建测试资源目录
    final testAssetsDir = Directory('test_assets');
    if (!await testAssetsDir.exists()) {
      await testAssetsDir.create(recursive: true);
    }
    
    print('✅ 测试环境准备完成');
  }
  
  /// 🚀 运行稳定性测试
  Future<Map<String, dynamic>> _runStabilityTests() async {
    print('🚀 开始执行稳定性测试...');
    
    final startTime = DateTime.now();
    
    // 运行Flutter测试
    final testProcess = await Process.start(
      'flutter',
      ['test', 'test/automated_stability_test.dart', '--reporter=json'],
      workingDirectory: Directory.current.path,
    );
    
    final testOutput = StringBuffer();
    final testErrors = StringBuffer();
    
    testProcess.stdout
        .transform(utf8.decoder)
        .listen((data) {
      testOutput.write(data);
      print(data.trim());
    });
    
    testProcess.stderr
        .transform(utf8.decoder)
        .listen((data) {
      testErrors.write(data);
      print('ERROR: ${data.trim()}');
    });
    
    final exitCode = await testProcess.exitCode;
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);
    
    return {
      'exitCode': exitCode,
      'duration': duration.inMilliseconds,
      'output': testOutput.toString(),
      'errors': testErrors.toString(),
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
    };
  }
  
  /// 📊 生成测试报告
  Future<void> _generateReports(Map<String, dynamic> testResult) async {
    print('📊 生成测试报告...');
    
    // 1. 生成JSON报告
    await _generateJsonReport(testResult);
    
    // 2. 生成HTML报告
    await _generateHtmlReport(testResult);
    
    // 3. 生成日志文件
    await _generateLogFile(testResult);
    
    print('✅ 测试报告生成完成');
  }
  
  /// 📄 生成JSON报告
  Future<void> _generateJsonReport(Map<String, dynamic> testResult) async {
    final reportData = {
      'testSuite': 'Isolate稳定性测试',
      'version': '1.0.0',
      'timestamp': DateTime.now().toIso8601String(),
      'environment': await _getEnvironmentInfo(),
      'testResult': testResult,
      'summary': _generateSummary(testResult),
    };
    
    final reportFile = File(path.join(TEST_RESULTS_DIR, REPORT_FILE));
    await reportFile.writeAsString(
      JsonEncoder.withIndent('  ').convert(reportData)
    );
  }
  
  /// 🌐 生成HTML报告
  Future<void> _generateHtmlReport(Map<String, dynamic> testResult) async {
    final htmlContent = '''
<!DOCTYPE html>
<html>
<head>
    <title>LoadGuard Isolate稳定性测试报告</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2196F3; color: white; padding: 20px; border-radius: 5px; }
        .summary { background: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 LoadGuard Isolate稳定性测试报告</h1>
        <p>生成时间: ${DateTime.now().toString()}</p>
    </div>
    
    <div class="summary">
        <h2>📊 测试摘要</h2>
        <div class="metric">
            <strong>测试状态:</strong> 
            <span class="${testResult['exitCode'] == 0 ? 'success' : 'error'}">
                ${testResult['exitCode'] == 0 ? '✅ 通过' : '❌ 失败'}
            </span>
        </div>
        <div class="metric">
            <strong>执行时间:</strong> ${(testResult['duration'] / 1000).toStringAsFixed(1)}秒
        </div>
        <div class="metric">
            <strong>开始时间:</strong> ${testResult['startTime']}
        </div>
        <div class="metric">
            <strong>结束时间:</strong> ${testResult['endTime']}
        </div>
    </div>
    
    <div class="summary">
        <h2>🔧 环境信息</h2>
        <pre>${await _getEnvironmentInfo()}</pre>
    </div>
    
    <div class="summary">
        <h2>📝 测试输出</h2>
        <pre>${testResult['output']}</pre>
    </div>
    
    ${testResult['errors'].toString().isNotEmpty ? '''
    <div class="summary">
        <h2>⚠️ 错误信息</h2>
        <pre class="error">${testResult['errors']}</pre>
    </div>
    ''' : ''}
</body>
</html>
    ''';
    
    final htmlFile = File(path.join(TEST_RESULTS_DIR, 'stability_test_report.html'));
    await htmlFile.writeAsString(htmlContent);
  }
  
  /// 📋 生成日志文件
  Future<void> _generateLogFile(Map<String, dynamic> testResult) async {
    final logContent = '''
LoadGuard Isolate稳定性测试日志
================================

测试时间: ${DateTime.now()}
测试状态: ${testResult['exitCode'] == 0 ? '通过' : '失败'}
执行时间: ${(testResult['duration'] / 1000).toStringAsFixed(1)}秒

环境信息:
${await _getEnvironmentInfo()}

测试输出:
${testResult['output']}

${testResult['errors'].toString().isNotEmpty ? '''
错误信息:
${testResult['errors']}
''' : ''}
    ''';
    
    final logFile = File(path.join(TEST_RESULTS_DIR, LOG_FILE));
    await logFile.writeAsString(logContent);
  }
  
  /// 🌍 获取环境信息
  Future<String> _getEnvironmentInfo() async {
    final info = StringBuffer();
    
    try {
      final flutterVersion = await Process.run('flutter', ['--version']);
      info.writeln('Flutter版本:');
      info.writeln(flutterVersion.stdout);
    } catch (e) {
      info.writeln('Flutter版本: 获取失败');
    }
    
    try {
      final dartVersion = await Process.run('dart', ['--version']);
      info.writeln('Dart版本:');
      info.writeln(dartVersion.stdout);
    } catch (e) {
      info.writeln('Dart版本: 获取失败');
    }
    
    info.writeln('操作系统: ${Platform.operatingSystem}');
    info.writeln('操作系统版本: ${Platform.operatingSystemVersion}');
    info.writeln('处理器架构: ${Platform.localHostname}');
    
    return info.toString();
  }
  
  /// 📈 生成测试摘要
  Map<String, dynamic> _generateSummary(Map<String, dynamic> testResult) {
    return {
      'passed': testResult['exitCode'] == 0,
      'duration_seconds': (testResult['duration'] / 1000).toStringAsFixed(1),
      'has_errors': testResult['errors'].toString().isNotEmpty,
      'recommendation': testResult['exitCode'] == 0 
          ? '✅ 系统稳定，可以部署到生产环境'
          : '❌ 发现稳定性问题，需要修复后重新测试',
    };
  }
  
  /// ✅ 评估测试结果
  bool _evaluateTestResult(Map<String, dynamic> testResult) {
    return testResult['exitCode'] == 0;
  }
}

void main(List<String> args) async {
  final runner = StabilityTestRunner();
  await runner.main(args);
}
