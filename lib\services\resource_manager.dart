import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/performance_manager.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 资源管理器
/// 自动管理应用资源的生命周期，防止内存泄漏
class ResourceManager implements Disposable {
  final PerformanceManager _performanceManager;
  final Map<String, ResourceGroup> _resourceGroups = {};
  final List<ManagedResource> _allResources = [];
  
  bool _disposed = false;
  
  ResourceManager(this._performanceManager) {
    _performanceManager.registerDisposable(this);
  }
  
  /// 创建资源组
  ResourceGroup createResourceGroup(String name) {
    if (_disposed) {
      throw StateError('ResourceManager已被释放');
    }
    
    final group = ResourceGroup._(name, this);
    _resourceGroups[name] = group;
    
    AppLogger.debug('📦 创建资源组: $name');
    return group;
  }
  
  /// 获取资源组
  ResourceGroup? getResourceGroup(String name) {
    return _resourceGroups[name];
  }
  
  /// 注册资源
  void _registerResource(ManagedResource resource) {
    if (_disposed) return;
    
    _allResources.add(resource);
    _performanceManager.recordMetric('resource_registered', 1.0, metadata: {
      'resourceType': resource.runtimeType.toString(),
      'totalResources': _allResources.length,
    });
  }
  
  /// 注销资源
  void _unregisterResource(ManagedResource resource) {
    _allResources.remove(resource);
    _performanceManager.recordMetric('resource_unregistered', 1.0, metadata: {
      'resourceType': resource.runtimeType.toString(),
      'totalResources': _allResources.length,
    });
  }
  
  /// 清理资源组
  Future<void> disposeResourceGroup(String name) async {
    final group = _resourceGroups.remove(name);
    if (group != null) {
      await group.dispose();
      AppLogger.debug('🗑️ 资源组已清理: $name');
    }
  }
  
  /// 获取资源统计
  ResourceStatistics getResourceStatistics() {
    final groupStats = <String, int>{};
    for (final entry in _resourceGroups.entries) {
      groupStats[entry.key] = entry.value._resources.length;
    }
    
    return ResourceStatistics(
      totalResources: _allResources.length,
      resourceGroups: _resourceGroups.length,
      groupStatistics: groupStats,
      timestamp: DateTime.now(),
    );
  }
  
  @override
  Future<void> dispose() async {
    if (_disposed) return;
    _disposed = true;
    
    try {
      AppLogger.info('🗑️ 开始清理所有资源...');
      
      // 清理所有资源组
      final groups = _resourceGroups.values.toList();
      for (final group in groups) {
        await group.dispose();
      }
      _resourceGroups.clear();
      
      // 清理剩余资源
      final resources = _allResources.toList();
      for (final resource in resources) {
        try {
          await resource.dispose();
        } catch (e) {
          AppLogger.warning('⚠️ 清理资源失败: ${resource.runtimeType} - $e');
        }
      }
      _allResources.clear();
      
      AppLogger.info('✅ 所有资源清理完成');
    } catch (e) {
      AppLogger.error('❌ 资源清理失败', error: e);
    }
  }
}

/// 资源组
/// 管理一组相关的资源，支持批量操作
class ResourceGroup implements Disposable {
  final String name;
  final ResourceManager _manager;
  final List<ManagedResource> _resources = [];
  
  bool _disposed = false;
  
  ResourceGroup._(this.name, this._manager);
  
  /// 添加资源
  T addResource<T extends ManagedResource>(T resource) {
    if (_disposed) {
      throw StateError('ResourceGroup已被释放');
    }
    
    _resources.add(resource);
    _manager._registerResource(resource);
    resource._setGroup(this);
    
    AppLogger.debug('➕ 添加资源到组 $name: ${resource.runtimeType}');
    return resource;
  }
  
  /// 移除资源
  void removeResource(ManagedResource resource) {
    if (_resources.remove(resource)) {
      _manager._unregisterResource(resource);
      resource._setGroup(null);
      AppLogger.debug('➖ 从组 $name 移除资源: ${resource.runtimeType}');
    }
  }
  
  /// 创建管理的Timer
  ManagedTimer createTimer(Duration duration, VoidCallback callback) {
    return addResource(ManagedTimer._(duration, callback));
  }
  
  /// 创建管理的StreamSubscription
  ManagedSubscription<T> createSubscription<T>(Stream<T> stream, void Function(T) onData) {
    return addResource(ManagedSubscription._(stream, onData));
  }
  
  /// 创建管理的StreamController
  ManagedStreamController<T> createStreamController<T>() {
    return addResource(ManagedStreamController<T>._());
  }
  
  /// 获取资源数量
  int get resourceCount => _resources.length;
  
  /// 获取特定类型的资源
  List<T> getResourcesOfType<T extends ManagedResource>() {
    return _resources.whereType<T>().toList();
  }
  
  @override
  Future<void> dispose() async {
    if (_disposed) return;
    _disposed = true;
    
    try {
      AppLogger.debug('🗑️ 清理资源组: $name');
      
      final resources = _resources.toList();
      for (final resource in resources) {
        try {
          await resource.dispose();
        } catch (e) {
          AppLogger.warning('⚠️ 清理资源失败: ${resource.runtimeType} - $e');
        }
      }
      _resources.clear();
      
      AppLogger.debug('✅ 资源组清理完成: $name');
    } catch (e) {
      AppLogger.error('❌ 资源组清理失败: $name', error: e);
    }
  }
}

/// 管理的资源基类
abstract class ManagedResource implements Disposable {
  ResourceGroup? _group;
  bool _disposed = false;
  
  /// 设置所属资源组
  void _setGroup(ResourceGroup? group) {
    _group = group;
  }
  
  /// 检查是否已释放
  bool get isDisposed => _disposed;
  
  /// 标记为已释放
  void _markDisposed() {
    _disposed = true;
    _group?.removeResource(this);
  }

  /// 标记为已释放（受保护方法，供子类使用）
  @protected
  void markDisposed() {
    _markDisposed();
  }
}

/// 管理的Timer
class ManagedTimer extends ManagedResource {
  late final Timer _timer;
  
  ManagedTimer._(Duration duration, VoidCallback callback) {
    _timer = Timer(duration, () {
      if (!_disposed) {
        callback();
        _markDisposed();
      }
    });
  }
  
  /// 创建周期性Timer
  ManagedTimer.periodic(Duration duration, void Function(Timer) callback) {
    _timer = Timer.periodic(duration, (timer) {
      if (_disposed) {
        timer.cancel();
        return;
      }
      callback(timer);
    });
  }
  
  /// 取消Timer
  void cancel() {
    if (!_disposed) {
      _timer.cancel();
      _markDisposed();
    }
  }
  
  /// Timer是否活跃
  bool get isActive => !_disposed && _timer.isActive;
  
  @override
  Future<void> dispose() async {
    if (!_disposed) {
      _timer.cancel();
      _markDisposed();
    }
  }
}

/// 管理的StreamSubscription
class ManagedSubscription<T> extends ManagedResource {
  late final StreamSubscription<T> _subscription;
  
  ManagedSubscription._(Stream<T> stream, void Function(T) onData) {
    _subscription = stream.listen(
      onData,
      onError: (error) {
        AppLogger.warning('⚠️ 流订阅错误: $error');
      },
      onDone: () {
        _markDisposed();
      },
    );
  }
  
  /// 暂停订阅
  void pause() {
    if (!_disposed) {
      _subscription.pause();
    }
  }
  
  /// 恢复订阅
  void resume() {
    if (!_disposed) {
      _subscription.resume();
    }
  }
  
  @override
  Future<void> dispose() async {
    if (!_disposed) {
      await _subscription.cancel();
      _markDisposed();
    }
  }
}

/// 管理的StreamController
class ManagedStreamController<T> extends ManagedResource {
  late final StreamController<T> _controller;
  
  ManagedStreamController._() {
    _controller = StreamController<T>.broadcast();
  }
  
  /// 获取Stream
  Stream<T> get stream => _controller.stream;
  
  /// 获取Sink
  StreamSink<T> get sink => _controller.sink;
  
  /// 添加数据
  void add(T data) {
    if (!_disposed && !_controller.isClosed) {
      _controller.add(data);
    }
  }
  
  /// 添加错误
  void addError(Object error, [StackTrace? stackTrace]) {
    if (!_disposed && !_controller.isClosed) {
      _controller.addError(error, stackTrace);
    }
  }
  
  /// 关闭控制器
  Future<void> close() async {
    if (!_disposed && !_controller.isClosed) {
      await _controller.close();
      _markDisposed();
    }
  }
  
  @override
  Future<void> dispose() async {
    await close();
  }
}

/// 资源统计
class ResourceStatistics {
  final int totalResources;
  final int resourceGroups;
  final Map<String, int> groupStatistics;
  final DateTime timestamp;
  
  const ResourceStatistics({
    required this.totalResources,
    required this.resourceGroups,
    required this.groupStatistics,
    required this.timestamp,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'totalResources': totalResources,
      'resourceGroups': resourceGroups,
      'groupStatistics': groupStatistics,
      'timestamp': timestamp.toIso8601String(),
    };
  }
  
  @override
  String toString() {
    return 'ResourceStatistics(total: $totalResources, groups: $resourceGroups)';
  }
}

/// 资源管理器Provider
final resourceManagerProvider = Provider<ResourceManager>((ref) {
  final performanceManager = ref.read(performanceManagerProvider);
  final manager = ResourceManager(performanceManager);
  ref.onDispose(() => manager.dispose());
  return manager;
});
