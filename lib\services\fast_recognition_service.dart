import 'dart:io';
import 'package:flutter/scheduler.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/utils/qr_data_parser.dart';

/// 🚀 快速识别服务
/// 
/// 专门解决主线程阻塞问题的轻量级识别服务
/// 避免复杂的处理链条，直接使用核心算法进行识别
/// 
/// 核心优势：
/// 1. 直接调用MLKit，避免多层封装
/// 2. 简化的处理流程，减少耗时
/// 3. 智能降级策略，优先保证速度
/// 4. 实时进度反馈，避免UI冻结
class FastRecognitionService {
  static FastRecognitionService? _instance;
  static FastRecognitionService get instance {
    _instance ??= FastRecognitionService._();
    return _instance!;
  }
  
  FastRecognitionService._();
  
  bool _isInitialized = false;
  TextRecognizer? _textRecognizer;
  BarcodeScanner? _barcodeScanner;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('🚀 初始化快速识别服务...');
    
    try {
      // 只初始化必要的组件
      _textRecognizer = TextRecognizer();
      
      // 只保留2种最重要的二维码格式
      _barcodeScanner = BarcodeScanner(formats: [
        BarcodeFormat.qrCode,        // QR码 - 主要格式
        BarcodeFormat.dataMatrix,    // Data Matrix - 工业常用
      ]);
      
      _isInitialized = true;
      AppLogger.info('✅ 快速识别服务初始化完成');
    } catch (e) {
      AppLogger.error('❌ 快速识别服务初始化失败: $e');
      rethrow;
    }
  }
  
  /// 🚀 快速识别主方法
  Future<FastRecognitionResult> recognizeFast(
    String imagePath, {
    Function(double progress, String status)? onProgress,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final stopwatch = Stopwatch()..start();
    AppLogger.info('🚀 开始快速识别: $imagePath');
    
    try {
      // 1. 加载图像
      onProgress?.call(0.1, '加载图像...');
      final inputImage = InputImage.fromFilePath(imagePath);
      
      // 2. 并行执行文字和二维码识别
      onProgress?.call(0.3, '并行识别中...');
      
      final textFuture = _recognizeText(inputImage);
      final qrFuture = _recognizeQRCode(inputImage);
      
      final results = await Future.wait([textFuture, qrFuture]);
      final textResults = results[0] as List<RecognitionResult>;
      final qrResults = results[1] as List<RecognitionResult>;
      
      // 3. 快速结果处理
      onProgress?.call(0.9, '处理结果...');
      
      stopwatch.stop();
      onProgress?.call(1.0, '识别完成');
      
      AppLogger.info('✅ 快速识别完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
      
      return FastRecognitionResult(
        textResults: textResults,
        qrResults: qrResults,
        processingTime: stopwatch.elapsedMilliseconds,
        hasText: textResults.isNotEmpty,
        hasQRCode: qrResults.isNotEmpty,
      );
      
    } catch (e) {
      stopwatch.stop();
      AppLogger.error('❌ 快速识别失败: $e');
      onProgress?.call(1.0, '识别失败: $e');
      rethrow;
    }
  }
  
  /// 文字识别
  Future<List<RecognitionResult>> _recognizeText(InputImage inputImage) async {
    try {
      final recognizedText = await _textRecognizer!.processImage(inputImage);
      
      if (recognizedText.text.isEmpty) {
        return [];
      }
      
      return [RecognitionResult(
        ocrText: recognizedText.text,
        confidence: 0.9, // MLKit内部置信度
        isQrOcrConsistent: false,
        matchesPreset: false, // 快速识别阶段不进行预设匹配
        status: RecognitionStatus.completed,
        metadata: {
          'engine': 'MLKit-Direct',
          'blocks': recognizedText.blocks.length,
        },
      )];
    } catch (e) {
      AppLogger.error('文字识别失败: $e');
      return [];
    }
  }
  
  /// 二维码识别
  Future<List<RecognitionResult>> _recognizeQRCode(InputImage inputImage) async {
    try {
      final barcodes = await _barcodeScanner!.processImage(inputImage);
      
      final results = <RecognitionResult>[];
      
      for (final barcode in barcodes) {
        if (barcode.rawValue != null) {
          // 简化二维码数据处理，避免复杂解析
          final displayText = barcode.rawValue!;
          
          results.add(RecognitionResult(
            ocrText: displayText,
            qrCode: barcode.rawValue!,
            confidence: 0.95, // 二维码识别通常很可靠
            isQrOcrConsistent: true,
            matchesPreset: false, // 快速识别阶段不进行预设匹配
            status: RecognitionStatus.completed,
            metadata: {
              'engine': 'MLKit-Barcode',
              'format': barcode.format.name,
              'rawValue': barcode.rawValue!,
            },
          ));
        }
      }
      
      return results;
    } catch (e) {
      AppLogger.error('二维码识别失败: $e');
      return [];
    }
  }
  
  /// 检查初始化状态
  bool get isInitialized => _isInitialized;
  
  /// 释放资源
  void dispose() {
    _textRecognizer?.close();
    _barcodeScanner?.close();
    _isInitialized = false;
    AppLogger.info('快速识别服务已释放');
  }
}

/// 快速识别结果
class FastRecognitionResult {
  final List<RecognitionResult> textResults;
  final List<RecognitionResult> qrResults;
  final int processingTime;
  final bool hasText;
  final bool hasQRCode;
  
  const FastRecognitionResult({
    required this.textResults,
    required this.qrResults,
    required this.processingTime,
    required this.hasText,
    required this.hasQRCode,
  });
  
  /// 获取最佳结果
  RecognitionResult? getBestResult() {
    // 优先返回二维码结果（通常更准确）
    if (qrResults.isNotEmpty) {
      return qrResults.first;
    }
    
    // 其次返回文字识别结果
    if (textResults.isNotEmpty) {
      return textResults.first;
    }
    
    return null;
  }
  
  /// 是否有任何识别结果
  bool get hasAnyResult => hasText || hasQRCode;
  
  /// 整体置信度
  double get overallConfidence {
    if (!hasAnyResult) return 0.0;
    
    double totalConfidence = 0.0;
    int count = 0;
    
    for (final result in [...textResults, ...qrResults]) {
      totalConfidence += result.confidence ?? 0.0;
      count++;
    }
    
    return count > 0 ? totalConfidence / count : 0.0;
  }
}