import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:loadguard/services/encryption_service.dart';
import 'package:loadguard/exceptions/app_exceptions.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 安全存储服务
/// 提供加密的本地数据存储，保护敏感信息如激活码、设备信息等
class SecureStorageService {
  static const String _secureBoxName = 'secure_storage';
  static const String _sensitiveDataBoxName = 'sensitive_data';
  
  late final Box _secureBox;
  late final Box _sensitiveDataBox;
  late final EncryptionService _encryptionService;
  
  bool _initialized = false;
  
  /// 初始化安全存储
  Future<void> initialize(EncryptionService encryptionService) async {
    try {
      _encryptionService = encryptionService;
      
      // 确保加密服务已初始化
      if (!_encryptionService.isInitialized) {
        await _encryptionService.initialize();
      }
      
      // 初始化Hive boxes
      _secureBox = await Hive.openBox(_secureBoxName);
      _sensitiveDataBox = await Hive.openBox(_sensitiveDataBoxName);
      
      _initialized = true;
      AppLogger.info('🔒 安全存储服务已初始化');
    } catch (e) {
      AppLogger.error('❌ 安全存储服务初始化失败', error: e);
      throw StorageException(
        message: 'Failed to initialize secure storage',
        code: 'SECURE_STORAGE_INIT_FAILED',
        originalError: e,
      );
    }
  }
  
  /// 检查是否已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StorageException(
        message: 'SecureStorageService not initialized',
        code: 'SECURE_STORAGE_NOT_INITIALIZED',
      );
    }
  }
  
  /// 存储敏感数据（加密）
  Future<void> storeSensitiveData(String key, dynamic value) async {
    _ensureInitialized();
    
    try {
      final jsonString = jsonEncode(value);
      final encryptedData = await _encryptionService.encrypt(jsonString);
      
      await _sensitiveDataBox.put(key, encryptedData.toJson());
      
      AppLogger.debug('🔒 敏感数据已加密存储: $key');
    } catch (e) {
      AppLogger.error('❌ 敏感数据存储失败: $key', error: e);
      throw StorageException(
        message: 'Failed to store sensitive data',
        code: 'SENSITIVE_DATA_STORE_FAILED',
        originalError: e,
        details: {'key': key},
      );
    }
  }
  
  /// 读取敏感数据（解密）
  Future<T?> getSensitiveData<T>(String key) async {
    _ensureInitialized();
    
    try {
      final encryptedJson = _sensitiveDataBox.get(key);
      if (encryptedJson == null) {
        return null;
      }
      
      final encryptedData = EncryptedData.fromJson(encryptedJson as Map<String, dynamic>);
      final jsonString = await _encryptionService.decrypt(encryptedData);
      final value = jsonDecode(jsonString);
      
      AppLogger.debug('🔓 敏感数据已解密读取: $key');
      return value as T;
    } catch (e) {
      AppLogger.error('❌ 敏感数据读取失败: $key', error: e);
      throw StorageException(
        message: 'Failed to get sensitive data',
        code: 'SENSITIVE_DATA_GET_FAILED',
        originalError: e,
        details: {'key': key},
      );
    }
  }
  
  /// 删除敏感数据
  Future<void> deleteSensitiveData(String key) async {
    _ensureInitialized();
    
    try {
      await _sensitiveDataBox.delete(key);
      AppLogger.debug('🗑️ 敏感数据已删除: $key');
    } catch (e) {
      AppLogger.error('❌ 敏感数据删除失败: $key', error: e);
      throw StorageException(
        message: 'Failed to delete sensitive data',
        code: 'SENSITIVE_DATA_DELETE_FAILED',
        originalError: e,
        details: {'key': key},
      );
    }
  }
  
  /// 存储普通安全数据（哈希验证）
  Future<void> storeSecureData(String key, dynamic value) async {
    _ensureInitialized();
    
    try {
      final jsonString = jsonEncode(value);
      final hash = _encryptionService.calculateHash(jsonString);
      
      final secureData = {
        'data': value,
        'hash': hash,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      await _secureBox.put(key, secureData);
      AppLogger.debug('🔐 安全数据已存储: $key');
    } catch (e) {
      AppLogger.error('❌ 安全数据存储失败: $key', error: e);
      throw StorageException(
        message: 'Failed to store secure data',
        code: 'SECURE_DATA_STORE_FAILED',
        originalError: e,
        details: {'key': key},
      );
    }
  }
  
  /// 读取普通安全数据（验证哈希）
  Future<T?> getSecureData<T>(String key) async {
    _ensureInitialized();
    
    try {
      final secureData = _secureBox.get(key);
      if (secureData == null) {
        return null;
      }
      
      final data = secureData['data'];
      final storedHash = secureData['hash'] as String;
      
      // 验证数据完整性
      final jsonString = jsonEncode(data);
      final computedHash = _encryptionService.calculateHash(jsonString);
      
      if (storedHash != computedHash) {
        AppLogger.warning('⚠️ 数据完整性验证失败: $key');
        throw StorageException(
          message: 'Data integrity check failed',
          code: 'DATA_INTEGRITY_FAILED',
          details: {'key': key},
        );
      }
      
      AppLogger.debug('✅ 安全数据已验证读取: $key');
      return data as T;
    } catch (e) {
      AppLogger.error('❌ 安全数据读取失败: $key', error: e);
      throw StorageException(
        message: 'Failed to get secure data',
        code: 'SECURE_DATA_GET_FAILED',
        originalError: e,
        details: {'key': key},
      );
    }
  }
  
  /// 删除安全数据
  Future<void> deleteSecureData(String key) async {
    _ensureInitialized();
    
    try {
      await _secureBox.delete(key);
      AppLogger.debug('🗑️ 安全数据已删除: $key');
    } catch (e) {
      AppLogger.error('❌ 安全数据删除失败: $key', error: e);
      throw StorageException(
        message: 'Failed to delete secure data',
        code: 'SECURE_DATA_DELETE_FAILED',
        originalError: e,
        details: {'key': key},
      );
    }
  }
  
  /// 存储激活码
  Future<void> storeActivationCode(String activationCode) async {
    await storeSensitiveData(SecureDataKeys.activationCode, activationCode);
    AppLogger.info('🔑 激活码已安全存储');
  }
  
  /// 获取激活码
  Future<String?> getActivationCode() async {
    return await getSensitiveData<String>(SecureDataKeys.activationCode);
  }
  
  /// 存储设备信息
  Future<void> storeDeviceInfo(Map<String, dynamic> deviceInfo) async {
    await storeSensitiveData(SecureDataKeys.deviceInfo, deviceInfo);
    AppLogger.info('📱 设备信息已安全存储');
  }
  
  /// 获取设备信息
  Future<Map<String, dynamic>?> getDeviceInfo() async {
    return await getSensitiveData<Map<String, dynamic>>(SecureDataKeys.deviceInfo);
  }
  
  /// 存储用户凭据
  Future<void> storeUserCredentials(Map<String, dynamic> credentials) async {
    await storeSensitiveData(SecureDataKeys.userCredentials, credentials);
    AppLogger.info('👤 用户凭据已安全存储');
  }
  
  /// 获取用户凭据
  Future<Map<String, dynamic>?> getUserCredentials() async {
    return await getSensitiveData<Map<String, dynamic>>(SecureDataKeys.userCredentials);
  }
  
  /// 存储应用配置
  Future<void> storeAppConfig(Map<String, dynamic> config) async {
    await storeSecureData(SecureDataKeys.appConfig, config);
    AppLogger.info('⚙️ 应用配置已安全存储');
  }
  
  /// 获取应用配置
  Future<Map<String, dynamic>?> getAppConfig() async {
    return await getSecureData<Map<String, dynamic>>(SecureDataKeys.appConfig);
  }
  
  /// 存储安全设置
  Future<void> storeSecuritySettings(Map<String, dynamic> settings) async {
    await storeSecureData(SecureDataKeys.securitySettings, settings);
    AppLogger.info('🛡️ 安全设置已存储');
  }
  
  /// 获取安全设置
  Future<Map<String, dynamic>?> getSecuritySettings() async {
    return await getSecureData<Map<String, dynamic>>(SecureDataKeys.securitySettings);
  }
  
  /// 检查数据是否存在
  bool hasSensitiveData(String key) {
    _ensureInitialized();
    return _sensitiveDataBox.containsKey(key);
  }
  
  bool hasSecureData(String key) {
    _ensureInitialized();
    return _secureBox.containsKey(key);
  }
  
  /// 获取所有敏感数据键
  List<String> getSensitiveDataKeys() {
    _ensureInitialized();
    return _sensitiveDataBox.keys.cast<String>().toList();
  }
  
  /// 获取所有安全数据键
  List<String> getSecureDataKeys() {
    _ensureInitialized();
    return _secureBox.keys.cast<String>().toList();
  }
  
  /// 清除所有敏感数据
  Future<void> clearAllSensitiveData() async {
    _ensureInitialized();
    
    try {
      await _sensitiveDataBox.clear();
      AppLogger.info('🧹 所有敏感数据已清除');
    } catch (e) {
      AppLogger.error('❌ 清除敏感数据失败', error: e);
      throw StorageException(
        message: 'Failed to clear sensitive data',
        code: 'CLEAR_SENSITIVE_DATA_FAILED',
        originalError: e,
      );
    }
  }
  
  /// 清除所有安全数据
  Future<void> clearAllSecureData() async {
    _ensureInitialized();
    
    try {
      await _secureBox.clear();
      AppLogger.info('🧹 所有安全数据已清除');
    } catch (e) {
      AppLogger.error('❌ 清除安全数据失败', error: e);
      throw StorageException(
        message: 'Failed to clear secure data',
        code: 'CLEAR_SECURE_DATA_FAILED',
        originalError: e,
      );
    }
  }
  
  /// 获取存储统计信息
  SecureStorageStatistics getStatistics() {
    _ensureInitialized();
    
    return SecureStorageStatistics(
      sensitiveDataCount: _sensitiveDataBox.length,
      secureDataCount: _secureBox.length,
      totalSize: _calculateTotalSize(),
      initialized: _initialized,
      encryptionEnabled: true,
      integrityCheckEnabled: true,
    );
  }
  
  /// 计算总大小（估算）
  int _calculateTotalSize() {
    try {
      int size = 0;
      
      // 估算敏感数据大小
      for (final value in _sensitiveDataBox.values) {
        size += jsonEncode(value).length;
      }
      
      // 估算安全数据大小
      for (final value in _secureBox.values) {
        size += jsonEncode(value).length;
      }
      
      return size;
    } catch (e) {
      return 0;
    }
  }
  
  /// 释放资源
  Future<void> dispose() async {
    if (_initialized) {
      try {
        await _secureBox.close();
        await _sensitiveDataBox.close();
        _initialized = false;
        AppLogger.info('🔒 安全存储服务已释放');
      } catch (e) {
        AppLogger.error('❌ 安全存储服务释放失败', error: e);
      }
    }
  }
}

/// 安全数据键常量
class SecureDataKeys {
  static const String activationCode = 'activation_code';
  static const String deviceInfo = 'device_info';
  static const String userCredentials = 'user_credentials';
  static const String appConfig = 'app_config';
  static const String securitySettings = 'security_settings';
  static const String encryptionKeys = 'encryption_keys';
  static const String sessionTokens = 'session_tokens';
  static const String biometricData = 'biometric_data';
  static const String backupData = 'backup_data';
  static const String auditLog = 'audit_log';
}

/// 安全存储统计信息
class SecureStorageStatistics {
  final int sensitiveDataCount;
  final int secureDataCount;
  final int totalSize;
  final bool initialized;
  final bool encryptionEnabled;
  final bool integrityCheckEnabled;
  
  const SecureStorageStatistics({
    required this.sensitiveDataCount,
    required this.secureDataCount,
    required this.totalSize,
    required this.initialized,
    required this.encryptionEnabled,
    required this.integrityCheckEnabled,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'sensitiveDataCount': sensitiveDataCount,
      'secureDataCount': secureDataCount,
      'totalSize': totalSize,
      'initialized': initialized,
      'encryptionEnabled': encryptionEnabled,
      'integrityCheckEnabled': integrityCheckEnabled,
    };
  }
  
  @override
  String toString() {
    return 'SecureStorageStatistics(sensitive: $sensitiveDataCount, secure: $secureDataCount, size: ${totalSize}B)';
  }
}

/// 安全存储服务Provider
final secureStorageServiceProvider = Provider<SecureStorageService>((ref) {
  final service = SecureStorageService();
  ref.onDispose(() => service.dispose());
  return service;
});
