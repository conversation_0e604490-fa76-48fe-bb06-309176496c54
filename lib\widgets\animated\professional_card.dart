import 'package:flutter/material.dart';
import '../../theme/material3_theme.dart';

/// 🎯 Material 3 专业级卡片组件
/// 完全符合 Material Design 3 规范的卡片系统
class ProfessionalCard extends StatefulWidget {
  final Widget child;
  final Material3CardType type;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;
  final bool isClickable;
  final bool isSelected;
  final bool isDraggable;
  final String? heroTag;
  final List<Widget>? actions;
  final Widget? header;
  final Widget? footer;

  const ProfessionalCard({
    Key? key,
    required this.child,
    this.type = Material3CardType.elevated,
    this.onTap,
    this.onLongPress,
    this.margin,
    this.padding,
    this.width,
    this.height,
    this.isClickable = false,
    this.isSelected = false,
    this.isDraggable = false,
    this.heroTag,
    this.actions,
    this.header,
    this.footer,
  }) : super(key: key);

  /// 创建 Elevated 卡片（默认样式，带阴影）
  const ProfessionalCard.elevated({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    bool isClickable = false,
    bool isSelected = false,
    bool isDraggable = false,
    String? heroTag,
    List<Widget>? actions,
    Widget? header,
    Widget? footer,
  }) : this(
          key: key,
          child: child,
          type: Material3CardType.elevated,
          onTap: onTap,
          onLongPress: onLongPress,
          margin: margin,
          padding: padding,
          width: width,
          height: height,
          isClickable: isClickable,
          isSelected: isSelected,
          isDraggable: isDraggable,
          heroTag: heroTag,
          actions: actions,
          header: header,
          footer: footer,
        );

  /// 创建 Filled 卡片（填充样式，无阴影）
  const ProfessionalCard.filled({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    bool isClickable = false,
    bool isSelected = false,
    bool isDraggable = false,
    String? heroTag,
    List<Widget>? actions,
    Widget? header,
    Widget? footer,
  }) : this(
          key: key,
          child: child,
          type: Material3CardType.filled,
          onTap: onTap,
          onLongPress: onLongPress,
          margin: margin,
          padding: padding,
          width: width,
          height: height,
          isClickable: isClickable,
          isSelected: isSelected,
          isDraggable: isDraggable,
          heroTag: heroTag,
          actions: actions,
          header: header,
          footer: footer,
        );

  /// 创建 Outlined 卡片（轮廓样式，带边框）
  const ProfessionalCard.outlined({
    Key? key,
    required Widget child,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    bool isClickable = false,
    bool isSelected = false,
    bool isDraggable = false,
    String? heroTag,
    List<Widget>? actions,
    Widget? header,
    Widget? footer,
  }) : this(
          key: key,
          child: child,
          type: Material3CardType.outlined,
          onTap: onTap,
          onLongPress: onLongPress,
          margin: margin,
          padding: padding,
          width: width,
          height: height,
          isClickable: isClickable,
          isSelected: isSelected,
          isDraggable: isDraggable,
          heroTag: heroTag,
          actions: actions,
          header: header,
          footer: footer,
        );

  @override
  State<ProfessionalCard> createState() => _ProfessionalCardState();
}

class _ProfessionalCardState extends State<ProfessionalCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _pressController;
  late AnimationController _selectionController;
  
  late Animation<double> _elevationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _stateLayerAnimation;
  late Animation<double> _selectionAnimation;
  
  bool _isHovered = false;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    // 悬停动画控制器
    _hoverController = AnimationController(
      duration: Material3Theme.mediumDuration,
      vsync: this,
    );
    
    // 按压动画控制器
    _pressController = AnimationController(
      duration: Material3Theme.shortDuration,
      vsync: this,
    );
    
    // 选择状态动画控制器
    _selectionController = AnimationController(
      duration: Material3Theme.mediumDuration,
      vsync: this,
    );
    
    // 阴影高度动画
    _elevationAnimation = Tween<double>(
      begin: 0.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Material3Theme.standardCurve,
    ));
    
    // 缩放动画
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: Material3Theme.accelerateCurve,
    ));
    
    // 状态层动画
    _stateLayerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Material3Theme.standardCurve,
    ));
    
    // 选择状态动画
    _selectionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _selectionController,
      curve: Material3Theme.emphasizedCurve,
    ));
    
    // 如果已选中，初始化选择动画
    if (widget.isSelected) {
      _selectionController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(ProfessionalCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 处理选择状态变化
    if (widget.isSelected != oldWidget.isSelected) {
      if (widget.isSelected) {
        _selectionController.forward();
      } else {
        _selectionController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _pressController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  void _handleHoverEnter() {
    if (!_isClickable) return;
    setState(() => _isHovered = true);
    _hoverController.forward();
  }

  void _handleHoverExit() {
    setState(() => _isHovered = false);
    _hoverController.reverse();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!_isClickable) return;
    setState(() => _isPressed = true);
    _pressController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!_isClickable) return;
    setState(() => _isPressed = false);
    _pressController.reverse();
    widget.onTap?.call();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _pressController.reverse();
  }

  void _handleLongPress() {
    if (!_isClickable) return;
    widget.onLongPress?.call();
  }

  bool get _isClickable => widget.isClickable || widget.onTap != null || widget.onLongPress != null;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Material3Theme.getColorScheme(context);
    final cardConfig = _getCardConfiguration(colorScheme);
    
    Widget cardContent = _buildCardContent(cardConfig);
    
    // 如果有 heroTag，包装 Hero 组件
    if (widget.heroTag != null) {
      cardContent = Hero(
        tag: widget.heroTag!,
        child: cardContent,
      );
    }
    
    // 如果可拖拽，包装 Draggable 组件
    if (widget.isDraggable) {
      cardContent = Draggable(
        data: widget.heroTag ?? widget.hashCode,
        feedback: Material(
          elevation: 8.0,
          borderRadius: BorderRadius.circular(Material3Tokens.radiusM),
          child: Container(
            width: widget.width,
            height: widget.height,
            child: widget.child,
          ),
        ),
        childWhenDragging: Opacity(
          opacity: 0.5,
          child: cardContent,
        ),
        child: cardContent,
      );
    }
    
    return Container(
      margin: widget.margin ?? const EdgeInsets.all(Material3Tokens.space8),
      child: cardContent,
    );
  }

  /// 构建卡片内容
  Widget _buildCardContent(_CardConfiguration config) {
    return MouseRegion(
      onEnter: (_) => _handleHoverEnter(),
      onExit: (_) => _handleHoverExit(),
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onLongPress: _handleLongPress,
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _elevationAnimation,
            _scaleAnimation,
            _stateLayerAnimation,
            _selectionAnimation,
          ]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: widget.width,
                height: widget.height,
                child: Material(
                  type: MaterialType.card,
                  color: config.backgroundColor,
                  elevation: config.baseElevation + _elevationAnimation.value,
                  shadowColor: config.shadowColor,
                  surfaceTintColor: config.surfaceTintColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(Material3Tokens.radiusM),
                    side: config.borderSide ?? BorderSide.none,
                  ),
                  child: InkWell(
                    onTap: _isClickable ? () {} : null,
                    borderRadius: BorderRadius.circular(Material3Tokens.radiusM),
                    splashColor: config.splashColor,
                    highlightColor: config.highlightColor,
                    child: Stack(
                      children: [
                        // 状态层
                        if (_stateLayerAnimation.value > 0 || widget.isSelected)
                          Positioned.fill(
                            child: Container(
                              decoration: BoxDecoration(
                                color: _getStateLayerColor(config),
                                borderRadius: BorderRadius.circular(Material3Tokens.radiusM),
                              ),
                            ),
                          ),
                        
                        // 选择指示器
                        if (widget.isSelected)
                          Positioned(
                            top: Material3Tokens.space12,
                            right: Material3Tokens.space12,
                            child: AnimatedBuilder(
                              animation: _selectionAnimation,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _selectionAnimation.value,
                                  child: Container(
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: Material3Theme.getColorScheme(context).primary,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.check,
                                      size: 16,
                                      color: Material3Theme.getColorScheme(context).onPrimary,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        
                        // 卡片内容
                        _buildCardLayout(),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// 构建卡片布局
  Widget _buildCardLayout() {
    final padding = widget.padding ?? const EdgeInsets.all(Material3Tokens.space16);
    
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部区域
          if (widget.header != null) ...[
            widget.header!,
            const SizedBox(height: Material3Tokens.space12),
          ],
          
          // 主要内容
          Flexible(child: widget.child),
          
          // 底部区域
          if (widget.footer != null) ...[
            const SizedBox(height: Material3Tokens.space12),
            widget.footer!,
          ],
          
          // 操作按钮区域
          if (widget.actions != null && widget.actions!.isNotEmpty) ...[
            const SizedBox(height: Material3Tokens.space16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: widget.actions!
                  .map((action) => Padding(
                        padding: const EdgeInsets.only(left: Material3Tokens.space8),
                        child: action,
                      ))
                  .toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取卡片配置
  _CardConfiguration _getCardConfiguration(ColorScheme colorScheme) {
    switch (widget.type) {
      case Material3CardType.elevated:
        return _CardConfiguration(
          backgroundColor: colorScheme.surface,
          baseElevation: Material3Tokens.elevation1,
          shadowColor: colorScheme.shadow,
          surfaceTintColor: colorScheme.surfaceTint,
          splashColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity12),
          highlightColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity08),
          stateLayerColor: colorScheme.onSurface,
        );
        
      case Material3CardType.filled:
        return _CardConfiguration(
          backgroundColor: colorScheme.surfaceVariant,
          baseElevation: Material3Tokens.elevation0,
          shadowColor: Colors.transparent,
          surfaceTintColor: null,
          splashColor: colorScheme.onSurfaceVariant.withOpacity(Material3Tokens.opacity12),
          highlightColor: colorScheme.onSurfaceVariant.withOpacity(Material3Tokens.opacity08),
          stateLayerColor: colorScheme.onSurfaceVariant,
        );
        
      case Material3CardType.outlined:
        return _CardConfiguration(
          backgroundColor: colorScheme.surface,
          baseElevation: Material3Tokens.elevation0,
          shadowColor: Colors.transparent,
          surfaceTintColor: null,
          borderSide: BorderSide(
            color: colorScheme.outlineVariant,
            width: 1.0,
          ),
          splashColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity12),
          highlightColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity08),
          stateLayerColor: colorScheme.onSurface,
        );
    }
  }

  /// 获取状态层颜色
  Color _getStateLayerColor(_CardConfiguration config) {
    double opacity = 0.0;
    
    if (widget.isSelected) {
      opacity = Material3Tokens.opacity08;
    } else if (_isPressed) {
      opacity = Material3Tokens.opacity12;
    } else if (_isHovered) {
      opacity = Material3Tokens.opacity08;
    }
    
    return config.stateLayerColor.withOpacity(opacity);
  }
}

/// Material 3 卡片类型枚举
enum Material3CardType {
  elevated,  // 悬浮卡片 - 带阴影
  filled,    // 填充卡片 - 无阴影，有背景色
  outlined,  // 轮廓卡片 - 带边框
}

/// 卡片配置类
class _CardConfiguration {
  final Color backgroundColor;
  final double baseElevation;
  final Color shadowColor;
  final Color? surfaceTintColor;
  final BorderSide? borderSide;
  final Color splashColor;
  final Color highlightColor;
  final Color stateLayerColor;

  const _CardConfiguration({
    required this.backgroundColor,
    required this.baseElevation,
    required this.shadowColor,
    this.surfaceTintColor,
    this.borderSide,
    required this.splashColor,
    required this.highlightColor,
    required this.stateLayerColor,
  });
}

/// 🎯 Material 3 信息卡片组件
/// 专门用于展示信息的卡片，包含标题、副标题、内容等
class ProfessionalInfoCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? description;
  final Widget? leading;
  final Widget? trailing;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final Material3CardType type;
  final bool isSelected;

  const ProfessionalInfoCard({
    Key? key,
    required this.title,
    this.subtitle,
    this.description,
    this.leading,
    this.trailing,
    this.actions,
    this.onTap,
    this.type = Material3CardType.elevated,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final textTheme = Material3Theme.getTextTheme(context);
    final colorScheme = Material3Theme.getColorScheme(context);

    return ProfessionalCard(
      type: type,
      onTap: onTap,
      isClickable: onTap != null,
      isSelected: isSelected,
      actions: actions,
      child: Row(
        children: [
          // 前导图标/图片
          if (leading != null) ...[
            leading!,
            const SizedBox(width: Material3Tokens.space16),
          ],
          
          // 主要内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Text(
                  title,
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                // 副标题
                if (subtitle != null) ...[
                  const SizedBox(height: Material3Tokens.space4),
                  Text(
                    subtitle!,
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                
                // 描述
                if (description != null) ...[
                  const SizedBox(height: Material3Tokens.space8),
                  Text(
                    description!,
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          
          // 尾随图标/组件
          if (trailing != null) ...[
            const SizedBox(width: Material3Tokens.space16),
            trailing!,
          ],
        ],
      ),
    );
  }
}

/// 🎯 Material 3 媒体卡片组件
/// 专门用于展示媒体内容的卡片，包含图片、标题、描述等
class ProfessionalMediaCard extends StatelessWidget {
  final Widget media;
  final String title;
  final String? subtitle;
  final String? description;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final Material3CardType type;
  final double? mediaHeight;

  const ProfessionalMediaCard({
    Key? key,
    required this.media,
    required this.title,
    this.subtitle,
    this.description,
    this.actions,
    this.onTap,
    this.type = Material3CardType.elevated,
    this.mediaHeight = 200.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final textTheme = Material3Theme.getTextTheme(context);
    final colorScheme = Material3Theme.getColorScheme(context);

    return ProfessionalCard(
      type: type,
      onTap: onTap,
      isClickable: onTap != null,
      padding: EdgeInsets.zero,
      actions: actions,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 媒体区域
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(Material3Tokens.radiusM),
              topRight: Radius.circular(Material3Tokens.radiusM),
            ),
            child: SizedBox(
              height: mediaHeight,
              width: double.infinity,
              child: media,
            ),
          ),
          
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(Material3Tokens.space16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Text(
                  title,
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                // 副标题
                if (subtitle != null) ...[
                  const SizedBox(height: Material3Tokens.space4),
                  Text(
                    subtitle!,
                    style: textTheme.bodyMedium?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                
                // 描述
                if (description != null) ...[
                  const SizedBox(height: Material3Tokens.space8),
                  Text(
                    description!,
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}