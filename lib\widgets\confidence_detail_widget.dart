import 'package:flutter/material.dart';
import 'package:loadguard/services/confidence_evaluation_service.dart';
import 'package:loadguard/models/task_model.dart';

/// 🎯 置信度详情显示组件
/// 专业的置信度分析和展示界面
class ConfidenceDetailWidget extends StatefulWidget {
  final ConfidenceScore confidenceScore;
  final RecognitionResult recognitionResult;
  final String photoLabel;
  final VoidCallback? onRetryRecognition;
  final VoidCallback? onManualConfirm;
  final VoidCallback? onRetakePhoto;

  const ConfidenceDetailWidget({
    super.key,
    required this.confidenceScore,
    required this.recognitionResult,
    required this.photoLabel,
    this.onRetryRecognition,
    this.onManualConfirm,
    this.onRetakePhoto,
  });

  @override
  State<ConfidenceDetailWidget> createState() => _ConfidenceDetailWidgetState();
}

class _ConfidenceDetailWidgetState extends State<ConfidenceDetailWidget> {
  @override
  Widget build(BuildContext context) {
    final level = widget.confidenceScore.level;
    // 修复数据一致性判断逻辑
    final result = widget.recognitionResult;
    bool consistencyBool = false;

    // 如果批号和牌号都识别成功，数据一致性应该是100%
    if (result.extractedProductCode != null &&
        result.extractedProductCode!.isNotEmpty &&
        result.extractedBatchNumber != null &&
        result.extractedBatchNumber!.isNotEmpty) {
      // 两个都识别成功，一致性为100%
      consistencyBool = true;
    } else if (result.isQrOcrConsistent) {
      // QR码和OCR一致
      consistencyBool = true;
    } else {
      // 使用原始的consistencyScore判断
      consistencyBool = widget.confidenceScore.consistencyScore >= 0.99;
    }

    final consistencyText = consistencyBool ? '一致' : '不一致';

    return Container(
      constraints: const BoxConstraints(maxWidth: 400), // 🔧 移除固定高度限制
      child: SingleChildScrollView(
        // 🔧 添加滚动支持
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 状态卡片 - 保留直观的颜色指示
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
              child: _buildStatusCard(level),
            ),
            // 置信度详细分析卡片
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildCompactConfidenceAnalysis(
                  consistencyText, consistencyBool),
            ),
            const SizedBox(height: 12),
            // 识别结果详情卡片
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: _buildCompactRecognitionResults(
                  consistencyText, consistencyBool),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 状态卡片 - 显示置信度等级
  Widget _buildStatusCard(ConfidenceLevel level) {
    final color = Color(level.colorValue);
    final levelText = level.displayName;
    final levelRange = level.rangeDescription;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getStatusIcon(level),
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  levelText,
                  style: TextStyle(
                    color: color,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '等级范围: $levelRange',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 置信度分析卡片
  Widget _buildCompactConfidenceAnalysis(
      String consistencyText, bool consistencyBool) {
    final score = widget.confidenceScore;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '置信度详细分析',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
          _buildCompactAnalysisRow('ML Kit原始分数', score.rawMlkitScore),
          const SizedBox(height: 4),
          _buildCompactAnalysisRow('文本质量评估', score.textQualityScore),
          const SizedBox(height: 4),
          _buildCompactAnalysisRow('预设匹配度', score.matchScore),
          const SizedBox(height: 4),
          // 数据一致性与下方卡片同步
          _buildCompactAnalysisRow('数据一致性', consistencyBool ? 1.0 : 0.0),
          const SizedBox(height: 8),
          const Divider(height: 1),
          const SizedBox(height: 6),
          _buildCompactAnalysisRow('综合置信度', score.finalScore, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildCompactAnalysisRow(String label, double value,
      {bool isTotal = false}) {
    final percentage = (value * 100).toStringAsFixed(1);
    return Row(
      children: [
        SizedBox(
          width: isTotal ? 80 : 60,
          child: Text(
            label,
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: isTotal ? 13 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: value,
              minHeight: isTotal ? 6 : 4,
              backgroundColor: Colors.grey[300],
              color: isTotal ? Colors.green : Colors.blue,
            ),
          ),
        ),
        const SizedBox(width: 6),
        SizedBox(
          width: 40,
          child: Text(
            '$percentage%',
            style: TextStyle(
              fontSize: isTotal ? 13 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.green[700] : Colors.blue[800],
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  // 识别结果详情卡片
  Widget _buildCompactRecognitionResults(
      String consistencyText, bool consistencyBool) {
    final result = widget.recognitionResult;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '识别结果详情',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
          // OCR原文
          if (result.ocrText != null && result.ocrText!.isNotEmpty) ...[
            _buildOcrTextRow('OCR原文', result.ocrText!),
            const SizedBox(height: 8),
          ],
          // 数据一致性
          _buildCompactDetailRow('数据一致性', consistencyText, consistencyBool),
          _buildCompactDetailRow('预设匹配', result.matchesPreset ? '匹配' : '不匹配',
              result.matchesPreset),
        ],
      ),
    );
  }

  Widget _buildOcrTextRow(String label, String ocrText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 16,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          constraints: const BoxConstraints(maxHeight: 200), // 🔧 限制最大高度
          child: SingleChildScrollView(
            // 🔧 添加滚动支持
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Text(
                ocrText,
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: 'monospace',
                  color: Color(0xFF333333),
                  height: 1.3,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompactDetailRow(String label, String value, bool isSuccess) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        children: [
          SizedBox(
            width: 70,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ),
          Icon(
            isSuccess ? Icons.check_circle : Icons.cancel,
            color: isSuccess ? Colors.green : Colors.red,
            size: 16,
          ),
        ],
      ),
    );
  }

  // 获取状态图标
  IconData _getStatusIcon(ConfidenceLevel level) {
    switch (level) {
      case ConfidenceLevel.high:
        return Icons.check_circle;
      case ConfidenceLevel.medium:
        return Icons.verified;
      case ConfidenceLevel.low:
        return Icons.warning_amber_rounded;
      case ConfidenceLevel.veryLow:
        return Icons.error_outline;
    }
  }
}
