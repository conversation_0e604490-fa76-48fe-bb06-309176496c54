/// 🚨 LoadGuard应用异常体系
/// 
/// 提供统一的异常分类和处理机制
/// 支持错误码、本地化消息和恢复建议

/// 应用异常基类
abstract class AppException implements Exception {
  /// 错误码
  final String code;
  
  /// 错误消息
  final String message;
  
  /// 原始错误
  final Object? originalError;
  
  /// 错误级别
  final ErrorLevel level;
  
  /// 是否可恢复
  final bool isRecoverable;
  
  /// 恢复建议
  final List<String> recoverySuggestions;
  
  /// 错误上下文
  final Map<String, dynamic> context;

  const AppException({
    required this.code,
    required this.message,
    this.originalError,
    this.level = ErrorLevel.error,
    this.isRecoverable = true,
    this.recoverySuggestions = const [],
    this.context = const {},
  });

  @override
  String toString() {
    return '$runtimeType($code): $message';
  }

  /// 获取用户友好的错误消息
  String getUserFriendlyMessage() {
    return message;
  }

  /// 获取技术详情
  String getTechnicalDetails() {
    final buffer = StringBuffer();
    buffer.writeln('错误码: $code');
    buffer.writeln('错误消息: $message');
    buffer.writeln('错误级别: $level');
    buffer.writeln('可恢复: $isRecoverable');
    
    if (originalError != null) {
      buffer.writeln('原始错误: $originalError');
    }
    
    if (context.isNotEmpty) {
      buffer.writeln('错误上下文: $context');
    }
    
    if (recoverySuggestions.isNotEmpty) {
      buffer.writeln('恢复建议:');
      for (int i = 0; i < recoverySuggestions.length; i++) {
        buffer.writeln('  ${i + 1}. ${recoverySuggestions[i]}');
      }
    }
    
    return buffer.toString();
  }
}

/// 业务异常
class BusinessException extends AppException {
  const BusinessException({
    required String code,
    required String message,
    Object? originalError,
    bool isRecoverable = true,
    List<String> recoverySuggestions = const [],
    Map<String, dynamic> context = const {},
  }) : super(
    code: code,
    message: message,
    originalError: originalError,
    level: ErrorLevel.warning,
    isRecoverable: isRecoverable,
    recoverySuggestions: recoverySuggestions,
    context: context,
  );

  /// 任务相关业务异常
  factory BusinessException.taskRelated({
    required String code,
    required String message,
    String? taskId,
    Object? originalError,
  }) {
    return BusinessException(
      code: 'TASK_$code',
      message: message,
      originalError: originalError,
      context: {'taskId': taskId},
      recoverySuggestions: [
        '检查任务参数是否正确',
        '确认任务状态是否允许此操作',
        '重试操作或联系技术支持',
      ],
    );
  }

  /// 验证异常
  factory BusinessException.validation({
    required String field,
    required String message,
    Object? value,
  }) {
    return BusinessException(
      code: 'VALIDATION_ERROR',
      message: '字段验证失败: $field - $message',
      context: {'field': field, 'value': value},
      recoverySuggestions: [
        '检查输入数据格式',
        '确认必填字段已填写',
        '参考帮助文档中的格式要求',
      ],
    );
  }
}

/// 数据异常
class DataException extends AppException {
  const DataException({
    required String code,
    required String message,
    Object? originalError,
    bool isRecoverable = true,
    List<String> recoverySuggestions = const [],
    Map<String, dynamic> context = const {},
  }) : super(
    code: code,
    message: message,
    originalError: originalError,
    level: ErrorLevel.error,
    isRecoverable: isRecoverable,
    recoverySuggestions: recoverySuggestions,
    context: context,
  );

  /// 数据库异常
  factory DataException.database({
    required String operation,
    required String message,
    Object? originalError,
  }) {
    return DataException(
      code: 'DATABASE_ERROR',
      message: '数据库操作失败: $operation - $message',
      originalError: originalError,
      recoverySuggestions: [
        '检查数据库连接',
        '确认数据格式正确',
        '重试操作',
        '如问题持续，请联系技术支持',
      ],
    );
  }

  /// 数据不一致异常
  factory DataException.inconsistency({
    required String description,
    Map<String, dynamic>? details,
  }) {
    return DataException(
      code: 'DATA_INCONSISTENCY',
      message: '数据不一致: $description',
      context: details ?? {},
      recoverySuggestions: [
        '刷新数据',
        '重新同步',
        '检查数据完整性',
        '联系技术支持进行数据修复',
      ],
    );
  }
}

/// 网络异常
class NetworkException extends AppException {
  final int? statusCode;
  final String? endpoint;

  const NetworkException({
    required String code,
    required String message,
    this.statusCode,
    this.endpoint,
    Object? originalError,
    bool isRecoverable = true,
    List<String> recoverySuggestions = const [],
    Map<String, dynamic> context = const {},
  }) : super(
    code: code,
    message: message,
    originalError: originalError,
    level: ErrorLevel.warning,
    isRecoverable: isRecoverable,
    recoverySuggestions: recoverySuggestions,
    context: context,
  );

  /// 连接超时
  factory NetworkException.timeout({
    String? endpoint,
    Duration? timeout,
  }) {
    return NetworkException(
      code: 'NETWORK_TIMEOUT',
      message: '网络连接超时${endpoint != null ? ': $endpoint' : ''}',
      endpoint: endpoint,
      context: {'timeout': timeout?.inSeconds},
      recoverySuggestions: [
        '检查网络连接',
        '重试操作',
        '切换网络环境',
        '联系网络管理员',
      ],
    );
  }

  /// 服务器错误
  factory NetworkException.serverError({
    required int statusCode,
    String? endpoint,
    String? serverMessage,
  }) {
    return NetworkException(
      code: 'SERVER_ERROR',
      message: '服务器错误 ($statusCode)${serverMessage != null ? ': $serverMessage' : ''}',
      statusCode: statusCode,
      endpoint: endpoint,
      isRecoverable: statusCode < 500,
      recoverySuggestions: [
        if (statusCode >= 500) '服务器内部错误，请稍后重试',
        if (statusCode == 404) '请求的资源不存在',
        if (statusCode == 403) '权限不足，请联系管理员',
        if (statusCode == 401) '身份验证失败，请重新登录',
        '联系技术支持',
      ],
    );
  }
}

/// 权限异常
class PermissionException extends AppException {
  final String? requiredPermission;
  final String? userRole;

  const PermissionException({
    required String code,
    required String message,
    this.requiredPermission,
    this.userRole,
    Object? originalError,
    List<String> recoverySuggestions = const [],
    Map<String, dynamic> context = const {},
  }) : super(
    code: code,
    message: message,
    originalError: originalError,
    level: ErrorLevel.warning,
    isRecoverable: false,
    recoverySuggestions: recoverySuggestions,
    context: context,
  );

  /// 功能权限不足
  factory PermissionException.featureAccess({
    required String feature,
    String? requiredPermission,
    String? userRole,
  }) {
    return PermissionException(
      code: 'FEATURE_ACCESS_DENIED',
      message: '无权访问功能: $feature',
      requiredPermission: requiredPermission,
      userRole: userRole,
      context: {
        'feature': feature,
        'requiredPermission': requiredPermission,
        'userRole': userRole,
      },
      recoverySuggestions: [
        '联系管理员申请权限',
        '确认用户角色是否正确',
        '检查许可证是否有效',
      ],
    );
  }
}

/// 系统异常
class SystemException extends AppException {
  const SystemException({
    required String code,
    required String message,
    Object? originalError,
    bool isRecoverable = false,
    List<String> recoverySuggestions = const [],
    Map<String, dynamic> context = const {},
  }) : super(
    code: code,
    message: message,
    originalError: originalError,
    level: ErrorLevel.critical,
    isRecoverable: isRecoverable,
    recoverySuggestions: recoverySuggestions,
    context: context,
  );

  /// 初始化失败
  factory SystemException.initializationFailed({
    required String component,
    Object? originalError,
  }) {
    return SystemException(
      code: 'INITIALIZATION_FAILED',
      message: '组件初始化失败: $component',
      originalError: originalError,
      context: {'component': component},
      recoverySuggestions: [
        '重启应用',
        '检查系统环境',
        '清除应用数据',
        '重新安装应用',
        '联系技术支持',
      ],
    );
  }
}

/// 错误级别
enum ErrorLevel {
  /// 信息
  info,
  /// 警告
  warning,
  /// 错误
  error,
  /// 严重错误
  critical,
}

/// 错误恢复策略
enum RecoveryStrategy {
  /// 重试
  retry,
  /// 回滚
  rollback,
  /// 忽略
  ignore,
  /// 用户干预
  userIntervention,
  /// 系统重启
  systemRestart,
}

/// 错误上下文构建器
class ErrorContextBuilder {
  final Map<String, dynamic> _context = {};

  ErrorContextBuilder addTaskId(String taskId) {
    _context['taskId'] = taskId;
    return this;
  }

  ErrorContextBuilder addUserId(String userId) {
    _context['userId'] = userId;
    return this;
  }

  ErrorContextBuilder addOperation(String operation) {
    _context['operation'] = operation;
    return this;
  }

  ErrorContextBuilder addTimestamp() {
    _context['timestamp'] = DateTime.now().toIso8601String();
    return this;
  }

  ErrorContextBuilder addCustom(String key, dynamic value) {
    _context[key] = value;
    return this;
  }

  Map<String, dynamic> build() => Map.from(_context);
}
