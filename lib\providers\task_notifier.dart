import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/task_business_service.dart';
import 'package:loadguard/services/task_data_service.dart';
import 'package:loadguard/services/task_cache_service.dart';
import 'package:loadguard/core/state/immutable_state_manager.dart';
import 'package:loadguard/core/state/optimistic_update_manager.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🎯 现代化任务状态管理器
///
/// 使用Riverpod AsyncNotifier模式，支持乐观更新和错误恢复
/// 完全替换ChangeNotifier，提供类型安全的状态管理
class TaskNotifier extends AsyncNotifier<TaskState> {
  late final TaskBusinessService _businessService;
  late final TaskDataService _dataService;
  late final TaskCacheService _cacheService;

  // 🆕 状态管理优化组件
  late final ImmutableStateManager _stateManager;
  late final OptimisticUpdateManager _optimisticManager;

  @override
  Future<TaskState> build() async {
    try {
      AppLogger.info('🎯 初始化现代化TaskNotifier...', tag: 'TaskNotifier');

      // 初始化服务层
      _businessService = TaskBusinessService();
      _dataService = TaskDataService();
      _cacheService = TaskCacheService();

      // 🆕 初始化状态管理优化组件
      _stateManager = ImmutableStateManager();
      _optimisticManager = OptimisticUpdateManager();

      // 设置乐观更新回调
      _optimisticManager.registerStateCallback('taskNotifier', (newState) {
        if (newState is TaskModel) {
          _updateCurrentTaskOptimistically(newState);
        }
      });

      await _dataService.initialize();

      // 加载初始数据
      final initialState = await _loadInitialState();

      // 预热缓存
      await _cacheService.warmupCache(initialState.tasks);

      AppLogger.info('✅ 现代化TaskNotifier初始化完成: ${initialState.tasks.length}个任务', tag: 'TaskNotifier');
      return initialState;
    } catch (e) {
      AppLogger.error('❌ TaskNotifier初始化失败: $e', tag: 'TaskNotifier');
      rethrow;
    }
  }

  /// 🔄 加载初始状态
  Future<TaskState> _loadInitialState() async {
    try {
      final tasks = await _dataService.getAllTasks();
      final currentTask = await _dataService.getCurrentTask();

      return TaskState(
        tasks: tasks,
        currentTask: currentTask,
        isLoading: false,
        error: null,
        lastUpdateTime: DateTime.now(),
        cacheStats: _cacheService.getStats(),
      );
    } catch (e) {
      AppLogger.error('❌ 加载初始状态失败: $e', tag: 'TaskNotifier');
      return TaskState(
        tasks: [],
        currentTask: null,
        isLoading: false,
        error: e.toString(),
        lastUpdateTime: DateTime.now(),
        cacheStats: {},
      );
    }
  }

  /// 🔄 刷新任务列表
  Future<void> refreshTasks() async {
    try {
      AppLogger.info('🔄 刷新任务列表...', tag: 'TaskNotifier');

      // 强制从数据源刷新
      final tasks = await _dataService.getAllTasks(forceRefresh: true);
      final currentTask = await _dataService.getCurrentTask();

      // 更新缓存
      _cacheService.putTasks(tasks);

      // 更新状态
      state = AsyncValue.data(TaskState(
        tasks: tasks,
        currentTask: currentTask,
        isLoading: false,
        error: null,
        lastUpdateTime: DateTime.now(),
        cacheStats: _cacheService.getStats(),
      ));

      AppLogger.info('✅ 任务列表刷新完成: ${tasks.length}个任务', tag: 'TaskNotifier');
    } catch (e) {
      AppLogger.error('❌ 任务列表刷新失败: $e', tag: 'TaskNotifier');
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// 🎯 乐观更新 - 创建任务
  Future<TaskModel> createTaskOptimistic({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    List<String> participants = const [],
  }) async {
    try {
      AppLogger.info('🎯 乐观创建任务: $template, $productCode', tag: 'TaskNotifier');

      // 1. 立即创建临时任务（乐观更新）
      final tempTask = _createTemporaryTask(
        template: template,
        productCode: productCode,
        batchNumber: batchNumber,
        quantity: quantity,
        participants: participants,
      );

      // 2. 立即更新UI状态
      final currentState = state.value!;
      final optimisticTasks = [tempTask, ...currentState.tasks];

      state = AsyncValue.data(currentState.copyWith(
        tasks: optimisticTasks,
        currentTask: tempTask,
        lastUpdateTime: DateTime.now(),
      ));

      // 3. 后台执行实际创建
      try {
        final realTask = await _businessService.createSingleBatchTask(
          template: template,
          productCode: productCode,
          batchNumber: batchNumber,
          quantity: quantity,
          participants: participants,
        );

        // 4. 保存到数据层
        await _dataService.saveTask(realTask);
        await _dataService.setCurrentTask(realTask);

        // 5. 替换临时任务为真实任务
        final updatedTasks = optimisticTasks.map((task) {
          return task.id == tempTask.id ? realTask : task;
        }).toList();

        // 6. 更新缓存
        _cacheService.putTask(realTask);
        _cacheService.invalidateAllQueries();

        // 7. 更新最终状态
        state = AsyncValue.data(currentState.copyWith(
          tasks: updatedTasks,
          currentTask: realTask,
          lastUpdateTime: DateTime.now(),
          cacheStats: _cacheService.getStats(),
        ));

        AppLogger.info('✅ 任务创建成功: ${realTask.id}', tag: 'TaskNotifier');
        return realTask;
      } catch (e) {
        // 8. 创建失败，回滚乐观更新
        AppLogger.error('❌ 任务创建失败，回滚: $e', tag: 'TaskNotifier');

        final rolledBackTasks = optimisticTasks.where((task) => task.id != tempTask.id).toList();
        state = AsyncValue.data(currentState.copyWith(
          tasks: rolledBackTasks,
          currentTask: currentState.currentTask,
          error: '任务创建失败: $e',
          lastUpdateTime: DateTime.now(),
        ));

        rethrow;
      }
    } catch (e) {
      AppLogger.error('❌ 乐观创建任务失败: $e', tag: 'TaskNotifier');
      rethrow;
    }
  }

  /// 创建混装任务
  Future<TaskModel> createMixedTask({
    required String template,
    required List<BatchInfo> batches,
    List<String>? participants,
  }) async {
    try {
      final task = TaskModel(
        template: template,
        batches: batches,
        participants: participants ?? [],
      );

      // 保存任务
      await _dataService.saveTask(task);

      // 设置为当前任务
      await _dataService.setCurrentTask(task);
      
      // 乐观更新状态
      final currentState = state.value;
      if (currentState != null) {
        final updatedTasks = [task, ...currentState.tasks];
        state = AsyncValue.data(currentState.copyWith(
          tasks: updatedTasks,
          currentTask: task,
        ));
      }
      
      AppLogger.info('✅ 创建混装任务成功: ${task.id}');
      return task;
    } catch (e) {
      AppLogger.error('❌ 创建混装任务失败: $e');
      rethrow;
    }
  }

  /// 更新任务
  Future<void> updateTask(TaskModel task) async {
    try {
      await _dataService.saveTask(task);
      
      // 乐观更新状态
      final currentState = state.value;
      if (currentState != null) {
        final updatedTasks = currentState.tasks.map((t) {
          return t.id == task.id ? task : t;
        }).toList();
        
        final updatedCurrentTask = currentState.currentTask?.id == task.id 
            ? task 
            : currentState.currentTask;
        
        state = AsyncValue.data(currentState.copyWith(
          tasks: updatedTasks,
          currentTask: updatedCurrentTask,
        ));
      }
      
      AppLogger.info('✅ 更新任务成功: ${task.id}');
    } catch (e) {
      AppLogger.error('❌ 更新任务失败: $e');
      rethrow;
    }
  }

  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      await _dataService.deleteTask(taskId);
      
      // 乐观更新状态
      final currentState = state.value;
      if (currentState != null) {
        final updatedTasks = currentState.tasks.where((t) => t.id != taskId).toList();
        final updatedCurrentTask = currentState.currentTask?.id == taskId 
            ? null 
            : currentState.currentTask;
        
        state = AsyncValue.data(currentState.copyWith(
          tasks: updatedTasks,
          currentTask: updatedCurrentTask,
        ));
      }
      
      AppLogger.info('✅ 删除任务成功: $taskId');
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $e');
      rethrow;
    }
  }

  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task) async {
    try {
      await _dataService.setCurrentTask(task);
      
      // 乐观更新状态
      final currentState = state.value;
      if (currentState != null) {
        state = AsyncValue.data(currentState.copyWith(
          currentTask: task,
        ));
      }
      
      AppLogger.info('📌 设置当前任务: ${task?.id}');
    } catch (e) {
      AppLogger.error('❌ 设置当前任务失败: $e');
      rethrow;
    }
  }

  /// 清除当前任务
  Future<void> clearCurrentTask() async {
    await setCurrentTask(null);
  }



  /// 查询任务
  Future<List<TaskModel>> queryTasks({
    DateTime? startDate,
    DateTime? endDate,
    String? template,
    bool? isCompleted,
  }) async {
    try {
      final allTasks = await _dataService.queryTasks(
        startDate: startDate,
        endDate: endDate,
        template: template,
      );

      // 手动过滤完成状态
      if (isCompleted != null) {
        return allTasks.where((task) => task.isCompleted == isCompleted).toList();
      }

      return allTasks;
    } catch (e) {
      AppLogger.error('❌ 查询任务失败: $e');
      rethrow;
    }
  }

  /// 获取任务统计
  Future<Map<String, dynamic>> getTaskStatistics() async {
    try {
      return await _dataService.getTaskStatistics();
    } catch (e) {
      AppLogger.error('❌ 获取任务统计失败: $e');
      rethrow;
    }
  }

  /// 备份数据
  Future<void> backupData() async {
    try {
      await _dataService.backupData();
      AppLogger.info('📦 数据备份成功');
    } catch (e) {
      AppLogger.error('❌ 数据备份失败: $e');
      rethrow;
    }
  }

  /// 恢复数据
  Future<void> restoreData() async {
    try {
      await _dataService.restoreData();

      // 重新加载状态
      await refreshTasks();
      
      AppLogger.info('🔄 数据恢复成功');
    } catch (e) {
      AppLogger.error('❌ 数据恢复失败: $e');
      rethrow;
    }
  }
  // 🔧 辅助方法

  /// 创建临时任务（用于乐观更新）
  TaskModel _createTemporaryTask({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    required List<String> participants,
  }) {
    return TaskModel(
      id: 'TEMP_${DateTime.now().millisecondsSinceEpoch}',
      template: template,
      productCode: productCode,
      batchNumber: batchNumber,
      quantity: quantity,
      participants: participants,
      createTime: DateTime.now(),
      photos: _generatePhotosFromTemplate(template),
      status: TaskStatus.pending,
      recognitionMetadata: {'isTemporary': true},
    );
  }

  /// 从模板生成照片配置
  List<PhotoItem> _generatePhotosFromTemplate(String template) {
    // 根据模板生成照片配置的逻辑
    switch (template) {
      case '平板车':
        return [
          PhotoItem(id: 'photo_1', label: '装车前', isRequired: true, needRecognition: false),
          PhotoItem(id: 'photo_2', label: '装车中', isRequired: true, needRecognition: false),
          PhotoItem(id: 'photo_3', label: '装车后', isRequired: true, needRecognition: false),
        ];
      case '集装箱':
        return [
          PhotoItem(id: 'photo_1', label: '集装箱外观', isRequired: true, needRecognition: false),
          PhotoItem(id: 'photo_2', label: '装箱过程', isRequired: true, needRecognition: false),
          PhotoItem(id: 'photo_3', label: '封箱完成', isRequired: true, needRecognition: false),
        ];
      default:
        return [
          PhotoItem(id: 'photo_1', label: '开始', isRequired: true, needRecognition: false),
          PhotoItem(id: 'photo_2', label: '过程', isRequired: false, needRecognition: false),
          PhotoItem(id: 'photo_3', label: '完成', isRequired: true, needRecognition: false),
        ];
    }
  }

  // ===== 🆕 乐观更新方法 =====

  /// 乐观更新照片
  Future<void> updatePhotoOptimistic(
    String photoId, {
    String? imagePath,
    RecognitionResult? recognitionResult,
    RecognitionStatus? recognitionStatus,
    bool? isVerified,
  }) async {
    final currentState = state.value;
    if (currentState?.currentTask == null) return;

    try {
      AppLogger.info('⚡ 乐观更新照片: $photoId', tag: 'TaskNotifier');

      final result = await _optimisticManager.optimisticUpdatePhoto(
        currentState!.currentTask!,
        photoId,
        imagePath: imagePath,
        recognitionResult: recognitionResult,
        recognitionStatus: recognitionStatus,
        isVerified: isVerified,
        persistOperation: () async {
          // 后台持久化操作
          // TODO: 实现具体的持久化逻辑
          AppLogger.info('📝 执行照片持久化操作: $photoId', tag: 'TaskNotifier');
          return currentState.currentTask!;
        },
      );

      if (!result.success) {
        AppLogger.error('❌ 乐观更新照片失败: ${result.errorMessage}', tag: 'TaskNotifier');
      }
    } catch (e) {
      AppLogger.error('❌ 乐观更新照片异常: $e', tag: 'TaskNotifier');
    }
  }

  /// 私有方法：乐观更新当前任务
  void _updateCurrentTaskOptimistically(TaskModel newTask) {
    final currentState = state.value;
    if (currentState != null) {
      final newState = currentState.copyWith(currentTask: newTask);
      state = AsyncValue.data(newState);
    }
  }

  /// 获取状态管理统计
  Map<String, dynamic> getStateManagementStats() {
    return {
      'immutableState': _stateManager.getStateStatistics(),
      'optimisticUpdates': _optimisticManager.getStatistics(),
    };
  }

  /// 释放资源
  void dispose() {
    _optimisticManager.dispose();
    _cacheService.dispose();
    _dataService.dispose();
    AppLogger.info('🔄 释放TaskNotifier资源', tag: 'TaskNotifier');
  }
}

/// 🎯 现代化任务状态数据类
class TaskState {
  final List<TaskModel> tasks;
  final TaskModel? currentTask;
  final bool isLoading;
  final String? error;
  final DateTime lastUpdateTime;
  final Map<String, dynamic> cacheStats;

  const TaskState({
    required this.tasks,
    this.currentTask,
    this.isLoading = false,
    this.error,
    required this.lastUpdateTime,
    required this.cacheStats,
  });

  TaskState copyWith({
    List<TaskModel>? tasks,
    TaskModel? currentTask,
    bool? isLoading,
    String? error,
    DateTime? lastUpdateTime,
    Map<String, dynamic>? cacheStats,
  }) {
    return TaskState(
      tasks: tasks ?? this.tasks,
      currentTask: currentTask ?? this.currentTask,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      lastUpdateTime: lastUpdateTime ?? this.lastUpdateTime,
      cacheStats: cacheStats ?? this.cacheStats,
    );
  }

  /// 获取任务统计信息
  Map<String, int> get taskStats {
    final stats = <String, int>{};
    for (final status in TaskStatus.values) {
      stats[status.toString()] = tasks.where((task) => task.status == status).length;
    }
    return stats;
  }

  /// 获取缓存命中率
  double get cacheHitRate {
    final hitCount = cacheStats['hitCount'] as int? ?? 0;
    final totalRequests = cacheStats['totalRequests'] as int? ?? 0;
    return totalRequests > 0 ? hitCount / totalRequests : 0.0;
  }

  @override
  String toString() {
    return 'TaskState{tasks: ${tasks.length}, currentTask: ${currentTask?.id}, '
           'isLoading: $isLoading, error: $error, lastUpdate: $lastUpdateTime}';
  }
}

/// TaskNotifier的Provider
final taskNotifierProvider = AsyncNotifierProvider<TaskNotifier, TaskState>(() {
  return TaskNotifier();
});

/// 便捷的Provider，用于获取当前任务
final currentTaskProvider = Provider<TaskModel?>((ref) {
  final taskState = ref.watch(taskNotifierProvider);
  return taskState.when(
    data: (state) => state.currentTask,
    loading: () => null,
    error: (_, __) => null,
  );
});

/// 便捷的Provider，用于获取任务列表
final tasksProvider = Provider<List<TaskModel>>((ref) {
  final taskState = ref.watch(taskNotifierProvider);
  return taskState.when(
    data: (state) => state.tasks,
    loading: () => [],
    error: (_, __) => [],
  );
});
