import 'dart:convert';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/hive_storage_service.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/data/datasources/task_data_source.dart';

/// 🗄️ Hive任务数据源
///
/// 负责任务数据的Hive存储操作，与HiveStorageService集成
class HiveTaskDataSource implements TaskDataSource {
  static const String _tasksKey = 'tasks_v2';
  static const String _taskStatsKey = 'task_stats';
  static const String _taskHistoryKey = 'task_history';
  
  /// 获取所有任务
  Future<List<TaskModel>> getAllTasks() async {
    try {
      AppLogger.info('📖 从Hive读取所有任务数据');

      // 使用HiveStorageService获取任务数据
      final tasks = HiveStorageService.getAllTasks();

      AppLogger.info('✅ 成功读取 ${tasks.length} 个任务');
      return tasks;

    } catch (e) {
      AppLogger.error('❌ 读取任务数据失败: $e');
      return [];
    }
  }
  
  /// 保存所有任务
  Future<bool> saveAllTasks(List<TaskModel> tasks) async {
    try {
      AppLogger.info('💾 保存 ${tasks.length} 个任务到Hive');

      // 使用HiveStorageService保存
      await HiveStorageService.saveTasks(tasks);

      AppLogger.info('✅ 任务数据保存成功');
      return true;

    } catch (e) {
      AppLogger.error('❌ 保存任务数据失败: $e');
      return false;
    }
  }
  
  /// 获取单个任务
  Future<TaskModel?> getTask(String taskId) async {
    try {
      final task = HiveStorageService.getTask(taskId);
      return task;
    } catch (e) {
      AppLogger.warning('⚠️ 未找到任务: $taskId');
      return null;
    }
  }
  
  /// 保存单个任务
  Future<bool> saveTask(TaskModel task) async {
    try {
      await HiveStorageService.saveTask(task);
      AppLogger.info('✅ 保存任务成功: ${task.id}');
      return true;
    } catch (e) {
      AppLogger.error('❌ 保存任务失败: $e');
      return false;
    }
  }
  
  /// 删除任务
  Future<bool> deleteTask(String taskId) async {
    try {
      await HiveStorageService.deleteTask(taskId);
      AppLogger.info('🗑️ 删除任务: $taskId');
      return true;
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $e');
      return false;
    }
  }
  
  /// 获取任务统计信息
  Future<Map<String, dynamic>> getTaskStats() async {
    try {
      final stats = HiveStorageService.getSetting<Map<String, dynamic>>(_taskStatsKey);
      return stats ?? {};
    } catch (e) {
      AppLogger.error('❌ 读取任务统计失败: $e');
      return {};
    }
  }

  /// 保存任务统计信息
  Future<bool> saveTaskStats(Map<String, dynamic> stats) async {
    try {
      await HiveStorageService.saveSetting(_taskStatsKey, stats);
      return true;
    } catch (e) {
      AppLogger.error('❌ 保存任务统计失败: $e');
      return false;
    }
  }
  
  /// 获取任务历史记录
  Future<List<Map<String, dynamic>>> getTaskHistory() async {
    try {
      final history = HiveStorageService.getSetting<List<dynamic>>(_taskHistoryKey);
      return history?.cast<Map<String, dynamic>>() ?? [];
    } catch (e) {
      AppLogger.error('❌ 读取任务历史失败: $e');
      return [];
    }
  }

  /// 添加任务历史记录
  Future<bool> addTaskHistory(Map<String, dynamic> historyItem) async {
    try {
      final history = await getTaskHistory();
      history.add(historyItem);

      // 限制历史记录数量（保留最近1000条）
      if (history.length > 1000) {
        history.removeRange(0, history.length - 1000);
      }

      await HiveStorageService.saveSetting(_taskHistoryKey, history);
      return true;
    } catch (e) {
      AppLogger.error('❌ 添加任务历史失败: $e');
      return false;
    }
  }
  
  /// 清理过期数据
  Future<bool> cleanupExpiredData({int daysToKeep = 30}) async {
    try {
      AppLogger.info('🧹 开始清理过期数据（保留${daysToKeep}天）');
      
      final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
      final tasks = await getAllTasks();
      
      // 过滤掉过期的已完成任务
      final filteredTasks = tasks.where((task) {
        if (task.status != TaskStatus.completed) {
          return true; // 保留未完成的任务
        }
        
        // 检查任务的最后更新时间
        final lastUpdate = task.completedAt ?? task.createdAt;
        return lastUpdate.isAfter(cutoffDate);
      }).toList();
      
      if (filteredTasks.length < tasks.length) {
        final removedCount = tasks.length - filteredTasks.length;
        AppLogger.info('🗑️ 清理了 $removedCount 个过期任务');
        await saveAllTasks(filteredTasks);
      }
      
      // 清理过期的历史记录
      final history = await getTaskHistory();
      final filteredHistory = history.where((item) {
        final timestamp = item['timestamp'] as String?;
        if (timestamp == null) return false;
        
        try {
          final date = DateTime.parse(timestamp);
          return date.isAfter(cutoffDate);
        } catch (e) {
          return false;
        }
      }).toList();
      
      if (filteredHistory.length < history.length) {
        final removedCount = history.length - filteredHistory.length;
        AppLogger.info('🗑️ 清理了 $removedCount 条过期历史记录');
        await HiveStorageService.saveSetting(_taskHistoryKey, filteredHistory);
      }
      
      AppLogger.info('✅ 数据清理完成');
      return true;
      
    } catch (e) {
      AppLogger.error('❌ 数据清理失败: $e');
      return false;
    }
  }
  
  /// 获取存储统计信息
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final tasks = await getAllTasks();
      final history = await getTaskHistory();
      final stats = await getTaskStats();
      
      return {
        'tasksCount': tasks.length,
        'historyCount': history.length,
        'statsKeys': stats.keys.length,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('❌ 获取存储统计失败: $e');
      return {};
    }
  }

  /// 初始化数据源
  @override
  Future<void> initialize() async {
    // Hive数据源不需要特殊初始化
    AppLogger.info('🗄️ HiveTaskDataSource 已初始化');
  }

  /// 批量保存任务
  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    await saveAllTasks(tasks);
  }

  /// 清空所有任务
  @override
  Future<void> clearAllTasks() async {
    try {
      await HiveStorageService.clearTasks();
      AppLogger.info('🗑️ 已清空所有任务');
    } catch (e) {
      AppLogger.error('❌ 清空任务失败: $e');
      rethrow;
    }
  }

  /// 获取当前任务ID
  @override
  Future<String?> getCurrentTaskId() async {
    try {
      return await HiveStorageService.getCurrentTaskId();
    } catch (e) {
      AppLogger.error('❌ 获取当前任务ID失败: $e');
      return null;
    }
  }

  /// 设置当前任务ID
  @override
  Future<void> setCurrentTaskId(String? taskId) async {
    try {
      await HiveStorageService.setCurrentTaskId(taskId);
      AppLogger.info('📌 设置当前任务ID: $taskId');
    } catch (e) {
      AppLogger.error('❌ 设置当前任务ID失败: $e');
      rethrow;
    }
  }

  /// 检查数据源是否可用
  @override
  Future<bool> isAvailable() async {
    try {
      // 检查Hive是否可用
      return Hive.isBoxOpen('tasks') || await Hive.openBox('tasks') != null;
    } catch (e) {
      AppLogger.error('❌ 检查数据源可用性失败: $e');
      return false;
    }
  }
}
