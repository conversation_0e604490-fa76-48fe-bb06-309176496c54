/// ⚡ 乐观更新管理器
/// 
/// 实现乐观更新机制，提升用户体验
/// - 立即更新UI状态
/// - 后台同步数据
/// - 失败时回滚状态
library optimistic_update_manager;

import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 乐观更新操作类型
enum OptimisticOperationType {
  updatePhoto,
  updateBatch,
  updateTask,
  createTask,
  deleteTask,
}

/// 乐观更新操作
class OptimisticOperation<T> {
  final String id;
  final OptimisticOperationType type;
  final T optimisticState;
  final T? originalState;
  final Future<T> Function() persistOperation;
  final DateTime timestamp;
  final String? description;

  OptimisticOperation({
    required this.id,
    required this.type,
    required this.optimisticState,
    this.originalState,
    required this.persistOperation,
    DateTime? timestamp,
    this.description,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// 乐观更新结果
class OptimisticResult<T> {
  final bool success;
  final T? finalState;
  final String? errorMessage;
  final Duration duration;

  OptimisticResult({
    required this.success,
    this.finalState,
    this.errorMessage,
    required this.duration,
  });
}

/// 乐观更新管理器
class OptimisticUpdateManager {
  static const String _tag = 'OptimisticUpdate';
  
  // 待处理的乐观更新操作
  final Map<String, OptimisticOperation> _pendingOperations = {};
  
  // 状态变更回调
  final Map<String, Function(dynamic)> _stateCallbacks = {};
  
  // 错误处理回调
  Function(String operationId, String error)? _errorCallback;

  /// 设置错误处理回调
  void setErrorCallback(Function(String operationId, String error) callback) {
    _errorCallback = callback;
  }

  /// 注册状态变更回调
  void registerStateCallback(String key, Function(dynamic) callback) {
    _stateCallbacks[key] = callback;
    AppLogger.debug('📝 注册状态回调: $key', tag: _tag);
  }

  /// 注销状态变更回调
  void unregisterStateCallback(String key) {
    _stateCallbacks.remove(key);
    AppLogger.debug('🗑️ 注销状态回调: $key', tag: _tag);
  }

  /// 执行乐观更新
  Future<OptimisticResult<T>> performOptimisticUpdate<T>(
    OptimisticOperation<T> operation,
  ) async {
    final startTime = DateTime.now();
    
    try {
      AppLogger.info('⚡ 开始乐观更新: ${operation.type} - ${operation.id}', tag: _tag);
      
      // 1. 立即应用乐观状态
      _applyOptimisticState(operation);
      
      // 2. 记录待处理操作
      _pendingOperations[operation.id] = operation;
      
      // 3. 执行后台持久化
      final finalState = await operation.persistOperation();
      
      // 4. 持久化成功，移除待处理操作
      _pendingOperations.remove(operation.id);
      
      // 5. 应用最终状态
      _applyFinalState(operation.id, finalState);
      
      final duration = DateTime.now().difference(startTime);
      AppLogger.info('✅ 乐观更新成功: ${operation.id} (${duration.inMilliseconds}ms)', tag: _tag);
      
      return OptimisticResult<T>(
        success: true,
        finalState: finalState,
        duration: duration,
      );
      
    } catch (e) {
      AppLogger.error('❌ 乐观更新失败: ${operation.id} - $e', tag: _tag);
      
      // 回滚到原始状态
      await _rollbackOperation(operation);
      
      final duration = DateTime.now().difference(startTime);
      
      // 通知错误
      _errorCallback?.call(operation.id, e.toString());
      
      return OptimisticResult<T>(
        success: false,
        errorMessage: e.toString(),
        duration: duration,
      );
    }
  }

  /// 乐观更新照片
  Future<OptimisticResult<TaskModel>> optimisticUpdatePhoto(
    TaskModel currentTask,
    String photoId, {
    String? imagePath,
    RecognitionResult? recognitionResult,
    RecognitionStatus? recognitionStatus,
    bool? isVerified,
    required Future<TaskModel> Function() persistOperation,
  }) async {
    // 创建乐观状态
    final photoIndex = currentTask.photos.indexWhere((p) => p.id == photoId);
    if (photoIndex == -1) {
      throw Exception('照片不存在: $photoId');
    }

    final updatedPhotos = List<PhotoItem>.from(currentTask.photos);
    updatedPhotos[photoIndex] = updatedPhotos[photoIndex].copyWith(
      imagePath: imagePath,
      recognitionResult: recognitionResult,
      recognitionStatus: recognitionStatus,
      isVerified: isVerified,
    );

    final optimisticTask = currentTask.copyWith(
      photos: updatedPhotos,
    );

    final operation = OptimisticOperation<TaskModel>(
      id: 'photo_${photoId}_${DateTime.now().millisecondsSinceEpoch}',
      type: OptimisticOperationType.updatePhoto,
      optimisticState: optimisticTask,
      originalState: currentTask,
      persistOperation: persistOperation,
      description: 'Update photo: $photoId',
    );

    return performOptimisticUpdate(operation);
  }

  /// 乐观更新批次
  Future<OptimisticResult<TaskModel>> optimisticUpdateBatch(
    TaskModel currentTask,
    String batchId, {
    int? recognizedQuantity,
    List<String>? recognizedItems,
    required Future<TaskModel> Function() persistOperation,
  }) async {
    // 创建乐观状态
    final batchIndex = currentTask.batches.indexWhere((b) => b.id == batchId);
    if (batchIndex == -1) {
      throw Exception('批次不存在: $batchId');
    }

    final updatedBatches = List<BatchInfo>.from(currentTask.batches);
    updatedBatches[batchIndex] = updatedBatches[batchIndex].copyWith(
      recognizedQuantity: recognizedQuantity,
      recognizedItems: recognizedItems,
    );

    final optimisticTask = currentTask.copyWith(
      batches: updatedBatches,
    );

    final operation = OptimisticOperation<TaskModel>(
      id: 'batch_${batchId}_${DateTime.now().millisecondsSinceEpoch}',
      type: OptimisticOperationType.updateBatch,
      optimisticState: optimisticTask,
      originalState: currentTask,
      persistOperation: persistOperation,
      description: 'Update batch: $batchId',
    );

    return performOptimisticUpdate(operation);
  }

  /// 乐观创建任务
  Future<OptimisticResult<TaskModel>> optimisticCreateTask(
    TaskModel newTask, {
    required Future<TaskModel> Function() persistOperation,
  }) async {
    final operation = OptimisticOperation<TaskModel>(
      id: 'create_${newTask.id}',
      type: OptimisticOperationType.createTask,
      optimisticState: newTask,
      persistOperation: persistOperation,
      description: 'Create task: ${newTask.template}',
    );

    return performOptimisticUpdate(operation);
  }

  /// 获取待处理操作数量
  int get pendingOperationsCount => _pendingOperations.length;

  /// 获取待处理操作列表
  List<OptimisticOperation> get pendingOperations => 
      List.unmodifiable(_pendingOperations.values);

  /// 取消待处理操作
  Future<void> cancelOperation(String operationId) async {
    final operation = _pendingOperations.remove(operationId);
    if (operation != null) {
      AppLogger.info('❌ 取消乐观更新操作: $operationId', tag: _tag);
      await _rollbackOperation(operation);
    }
  }

  /// 取消所有待处理操作
  Future<void> cancelAllOperations() async {
    AppLogger.info('❌ 取消所有乐观更新操作: ${_pendingOperations.length}个', tag: _tag);
    
    final operations = List<OptimisticOperation>.from(_pendingOperations.values);
    _pendingOperations.clear();
    
    for (final operation in operations) {
      await _rollbackOperation(operation);
    }
  }

  /// 获取统计信息
  Map<String, dynamic> getStatistics() {
    final operationTypes = <OptimisticOperationType, int>{};
    for (final operation in _pendingOperations.values) {
      operationTypes[operation.type] = (operationTypes[operation.type] ?? 0) + 1;
    }

    return {
      'pendingOperations': _pendingOperations.length,
      'registeredCallbacks': _stateCallbacks.length,
      'operationTypes': operationTypes.map((k, v) => MapEntry(k.toString(), v)),
    };
  }

  /// 清理资源
  void dispose() {
    _pendingOperations.clear();
    _stateCallbacks.clear();
    _errorCallback = null;
    AppLogger.info('🧹 乐观更新管理器已清理', tag: _tag);
  }

  /// 私有方法：应用乐观状态
  void _applyOptimisticState<T>(OptimisticOperation<T> operation) {
    AppLogger.debug('⚡ 应用乐观状态: ${operation.id}', tag: _tag);
    
    // 通知所有状态回调
    for (final callback in _stateCallbacks.values) {
      try {
        callback(operation.optimisticState);
      } catch (e) {
        AppLogger.error('❌ 状态回调执行失败: $e', tag: _tag);
      }
    }
  }

  /// 私有方法：应用最终状态
  void _applyFinalState<T>(String operationId, T finalState) {
    AppLogger.debug('✅ 应用最终状态: $operationId', tag: _tag);
    
    // 通知所有状态回调
    for (final callback in _stateCallbacks.values) {
      try {
        callback(finalState);
      } catch (e) {
        AppLogger.error('❌ 状态回调执行失败: $e', tag: _tag);
      }
    }
  }

  /// 私有方法：回滚操作
  Future<void> _rollbackOperation<T>(OptimisticOperation<T> operation) async {
    AppLogger.warning('🔄 回滚乐观更新: ${operation.id}', tag: _tag);
    
    if (operation.originalState != null) {
      // 通知所有状态回调回滚到原始状态
      for (final callback in _stateCallbacks.values) {
        try {
          callback(operation.originalState);
        } catch (e) {
          AppLogger.error('❌ 回滚状态回调执行失败: $e', tag: _tag);
        }
      }
    }
  }
}
