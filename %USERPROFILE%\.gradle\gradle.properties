# Gradle全局配置 - 优化网络连接和构建性能

# 网络连接优化
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.maxConnectionsPerRoute=5

# 连接超时设置（毫秒）
systemProp.http.connectionTimeout=60000
systemProp.http.socketTimeout=60000

# 代理设置（如果需要）
# systemProp.http.proxyHost=127.0.0.1
# systemProp.http.proxyPort=7890
# systemProp.https.proxyHost=127.0.0.1
# systemProp.https.proxyPort=7890

# SSL/TLS优化
systemProp.https.protocols=TLSv1.2,TLSv1.3
systemProp.jsse.enableSNIExtension=true

# 构建性能优化
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true
org.gradle.caching=true

# JVM内存优化
org.gradle.jvmargs=-Xmx2048m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# Android构建优化
android.useAndroidX=true
android.enableJetifier=true
android.enableR8.fullMode=true

# Kotlin编译优化
kotlin.incremental=true
kotlin.incremental.android=true
kotlin.code.style=official

# Flutter相关
flutter.compileSdkVersion=34
flutter.targetSdkVersion=34
flutter.ndkVersion=25.1.8937393