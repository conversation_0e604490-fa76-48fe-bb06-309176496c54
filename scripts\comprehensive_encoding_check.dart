import 'dart:io';
import 'dart:convert';

/// 全面的编码检查和修复工具
void main() async {
  print('🔍 开始全面编码检查...');
  
  final results = <String, dynamic>{
    'totalFiles': 0,
    'fixedFiles': [],
    'problematicFiles': [],
    'bomFiles': [],
    'invalidUtf8Files': [],
    'mixedEncodingFiles': [],
  };
  
  // 检查所有相关目录
  final directories = ['lib', 'test', 'android/app/src/main', 'web'];
  final fileExtensions = ['.dart', '.yaml', '.json', '.xml', '.html', '.js', '.md'];
  
  for (final dirPath in directories) {
    final dir = Directory(dirPath);
    if (await dir.exists()) {
      await _scanDirectory(dir, fileExtensions, results);
    }
  }
  
  // 检查根目录文件
  final rootFiles = [
    'pubspec.yaml',
    'analysis_options.yaml', 
    'README.md',
    'web/index.html',
    'web/manifest.json'
  ];
  
  for (final filePath in rootFiles) {
    final file = File(filePath);
    if (await file.exists()) {
      await _checkFile(file, results);
    }
  }
  
  // 输出详细报告
  _printReport(results);
  
  // 如果有问题文件，提供修复建议
  if (results['problematicFiles'].isNotEmpty || 
      results['bomFiles'].isNotEmpty ||
      results['invalidUtf8Files'].isNotEmpty) {
    print('\n🔧 修复建议:');
    print('1. 运行: dart run scripts/fix_encoding_issues.dart');
    print('2. 使用VS Code重新保存问题文件（确保UTF-8编码）');
    print('3. 检查是否有特殊字符或emoji需要处理');
  }
}

Future<void> _scanDirectory(
  Directory dir,
  List<String> extensions,
  Map<String, dynamic> results,
) async {
  try {
    await for (final entity in dir.list(recursive: true)) {
      if (entity is File) {
        final ext = _getExtension(entity.path);
        if (extensions.contains(ext)) {
          await _checkFile(entity, results);
        }
      }
    }
  } catch (e) {
    print('⚠️ 扫描目录失败: ${dir.path} - $e');
  }
}

Future<void> _checkFile(File file, Map<String, dynamic> results) async {
  try {
    results['totalFiles']++;
    
    final bytes = await file.readAsBytes();
    final path = file.path;
    
    // 检查BOM
    if (bytes.length >= 3 && 
        bytes[0] == 0xEF && 
        bytes[1] == 0xBB && 
        bytes[2] == 0xBF) {
      results['bomFiles'].add(path);
      print('📝 发现BOM: $path');
    }
    
    // 检查UTF-8有效性
    try {
      final content = utf8.decode(bytes, allowMalformed: false);
      
      // 检查是否包含替换字符
      if (content.contains('\uFFFD')) {
        results['invalidUtf8Files'].add(path);
        print('❌ 无效UTF-8: $path');
      }
      
      // 检查是否包含控制字符
      final controlChars = RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]');
      if (controlChars.hasMatch(content)) {
        results['mixedEncodingFiles'].add(path);
        print('⚠️ 包含控制字符: $path');
      }
      
    } catch (e) {
      results['problematicFiles'].add(path);
      print('❌ UTF-8解码失败: $path - $e');
    }
    
  } catch (e) {
    results['problematicFiles'].add(file.path);
    print('❌ 文件读取失败: ${file.path} - $e');
  }
}

void _printReport(Map<String, dynamic> results) {
  print('\n📊 编码检查报告:');
  print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  print('总文件数: ${results['totalFiles']}');
  print('BOM文件: ${results['bomFiles'].length}');
  print('无效UTF-8文件: ${results['invalidUtf8Files'].length}');
  print('包含控制字符文件: ${results['mixedEncodingFiles'].length}');
  print('其他问题文件: ${results['problematicFiles'].length}');
  
  if (results['bomFiles'].isNotEmpty) {
    print('\n📝 BOM文件列表:');
    for (final file in results['bomFiles']) {
      print('  - $file');
    }
  }
  
  if (results['invalidUtf8Files'].isNotEmpty) {
    print('\n❌ 无效UTF-8文件:');
    for (final file in results['invalidUtf8Files']) {
      print('  - $file');
    }
  }
  
  if (results['mixedEncodingFiles'].isNotEmpty) {
    print('\n⚠️ 包含控制字符文件:');
    for (final file in results['mixedEncodingFiles']) {
      print('  - $file');
    }
  }
  
  if (results['problematicFiles'].isNotEmpty) {
    print('\n❌ 其他问题文件:');
    for (final file in results['problematicFiles']) {
      print('  - $file');
    }
  }
}

String _getExtension(String path) {
  final lastDot = path.lastIndexOf('.');
  return lastDot == -1 ? '' : path.substring(lastDot);
}
