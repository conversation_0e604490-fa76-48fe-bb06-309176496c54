import 'package:loadguard/models/task_model.dart';

/// 任务数据仓库接口
/// 定义任务数据的CRUD操作，实现数据访问层的抽象
abstract class TaskRepository {
  /// 初始化Repository
  Future<void> initialize();

  /// 获取所有任务
  Future<List<TaskModel>> getAllTasks();
  
  /// 根据ID获取任务
  Future<TaskModel?> getTaskById(String id);
  
  /// 保存任务（新增或更新）
  Future<void> saveTask(TaskModel task);
  
  /// 批量保存任务
  Future<void> saveTasks(List<TaskModel> tasks);
  
  /// 删除任务
  Future<void> deleteTask(String id);
  
  /// 清空所有任务
  Future<void> clearAllTasks();
  
  /// 获取当前任务
  Future<TaskModel?> getCurrentTask();
  
  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task);
  
  /// 根据条件查询任务
  Future<List<TaskModel>> queryTasks({
    DateTime? startDate,
    DateTime? endDate,
    String? template,
    bool? isCompleted,
  });
  
  /// 获取任务统计信息
  Future<Map<String, dynamic>> getTaskStatistics();
  
  /// 数据迁移方法
  Future<void> migrateData();
  
  /// 备份数据
  Future<void> backupData();
  
  /// 恢复数据
  Future<void> restoreData();
}

// TaskDataSource已移动到 lib/data/datasources/task_data_source.dart

/// Repository异常类
class RepositoryException implements Exception {
  final String message;
  final dynamic originalError;
  
  const RepositoryException(this.message, {this.originalError});
  
  @override
  String toString() => 'RepositoryException: $message';
}

/// 数据一致性异常
class DataConsistencyException extends RepositoryException {
  const DataConsistencyException(super.message, {super.originalError});
}

/// 数据源不可用异常
class DataSourceUnavailableException extends RepositoryException {
  const DataSourceUnavailableException(super.message, {super.originalError});
}
