# 🛠️ 开发指南

## 🎯 为什么不降低依赖版本？

### 保持现代化的优势

1. **性能优化** - 新版本通常包含性能改进
2. **安全修复** - 最新版本修复了已知安全漏洞
3. **功能完整** - 新特性和API改进
4. **长期维护** - 避免技术债务积累

### 正确的解决方案

❌ **错误做法**: 降低依赖版本来回避问题
✅ **正确做法**: 配置正确的开发环境

## 🚀 环境配置最佳实践

### 1. Flutter版本管理

使用FVM (Flutter Version Management) 管理多个Flutter版本：

```bash
# 安装FVM
dart pub global activate fvm

# 安装项目所需的Flutter版本
fvm install 3.24.0

# 在项目中使用指定版本
fvm use 3.24.0

# 运行命令
fvm flutter pub get
fvm flutter run
```

### 2. 环境变量配置

创建 `.env` 文件（不提交到版本控制）：

```bash
# Android配置
ANDROID_HOME=/path/to/android/sdk
JAVA_HOME=/path/to/java/jdk

# Flutter配置
FLUTTER_ROOT=/path/to/flutter

# 网络配置（中国大陆用户）
PUB_HOSTED_URL=https://pub.flutter-io.cn
FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
```

### 3. IDE配置

#### VS Code
创建 `.vscode/settings.json`:

```json
{
  "dart.flutterSdkPath": "/path/to/flutter",
  "dart.checkForSdkUpdates": false,
  "dart.warnWhenEditingFilesOutsideWorkspace": false
}
```

#### Android Studio
1. 设置Flutter SDK路径
2. 配置Dart SDK路径
3. 启用Flutter插件

## 🔧 常见环境问题解决

### 问题1: Gradle依赖下载失败

**原因**: 网络连接问题或镜像源配置不当

**解决方案**:
1. 检查网络连接
2. 验证镜像源配置
3. 清理Gradle缓存

```bash
cd android
./gradlew clean
./gradlew --refresh-dependencies
```

### 问题2: 代码生成失败

**原因**: build_runner版本不兼容或配置错误

**解决方案**:
1. 更新build_runner
2. 清理生成文件
3. 重新生成

```bash
flutter packages pub run build_runner clean
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 问题3: 版本冲突

**原因**: 依赖版本约束冲突

**解决方案**:
1. 分析依赖树
2. 使用dependency_overrides（谨慎使用）
3. 更新到兼容版本

```bash
flutter pub deps
flutter pub upgrade
```

## 📋 开发工作流

### 1. 项目初始化

```bash
# 1. 检查环境
dart check_environment.dart

# 2. 安装依赖
flutter pub get

# 3. 生成代码
flutter packages pub run build_runner build

# 4. 运行项目
flutter run
```

### 2. 日常开发

```bash
# 热重载开发
flutter run

# 代码分析
flutter analyze

# 运行测试
flutter test

# 格式化代码
dart format .
```

### 3. 构建发布

```bash
# 清理缓存
flutter clean

# 重新安装依赖
flutter pub get

# 构建APK
flutter build apk --release

# 构建App Bundle
flutter build appbundle --release
```

## 🧪 测试策略

### 单元测试
```bash
flutter test test/unit/
```

### 集成测试
```bash
flutter test integration_test/
```

### 性能测试
```bash
flutter drive --target=test_driver/perf_test.dart
```

## 📊 性能优化

### 1. 构建优化
- 启用R8代码压缩
- 使用App Bundle
- 优化资源文件

### 2. 运行时优化
- 使用const构造函数
- 避免不必要的重建
- 合理使用Provider

### 3. 内存优化
- 及时释放资源
- 使用对象池
- 监控内存使用

## 🔍 调试技巧

### 1. Flutter Inspector
使用Flutter Inspector查看widget树和性能

### 2. 日志调试
```dart
import 'dart:developer' as developer;

developer.log('Debug message', name: 'MyApp');
```

### 3. 性能分析
```bash
flutter run --profile
```

## 📚 学习资源

- [Flutter官方文档](https://flutter.dev/docs)
- [Dart语言指南](https://dart.dev/guides)
- [Flutter性能最佳实践](https://flutter.dev/docs/perf)
- [状态管理指南](https://flutter.dev/docs/development/data-and-backend/state-mgmt)

## 🤝 团队协作

### 1. 代码规范
- 使用dart format格式化代码
- 遵循Flutter代码规范
- 编写清晰的注释

### 2. 版本控制
- 使用语义化版本
- 编写清晰的commit信息
- 定期更新依赖

### 3. 持续集成
- 自动化测试
- 代码质量检查
- 自动化构建

## 🆘 获取帮助

1. **查看文档** - 首先查看官方文档
2. **搜索Issues** - 在GitHub上搜索相关问题
3. **社区支持** - Flutter中文社区、Stack Overflow
4. **创建Issue** - 提供详细的错误信息和重现步骤
