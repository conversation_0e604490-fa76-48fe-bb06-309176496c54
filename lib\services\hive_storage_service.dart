import 'dart:convert';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🗄️ Hive存储服务 - 高性能本地数据存储
/// 
/// 用于存储复杂数据结构，替代SharedPreferences的部分功能
/// 提供更好的性能和类型安全
class HiveStorageService {
  static const String _tasksBoxName = 'tasks';
  static const String _settingsBoxName = 'settings';
  static const String _cacheBoxName = 'cache';
  
  static Box<Map>? _tasksBox;
  static Box<dynamic>? _settingsBox;
  static Box<dynamic>? _cacheBox;
  
  static bool _isInitialized = false;
  
  /// 初始化Hive存储
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      AppLogger.info('🗄️ 初始化Hive存储服务...');
      
      // 初始化Hive
      await Hive.initFlutter();
      
      // 打开数据盒子
      _tasksBox = await Hive.openBox<Map>(_tasksBoxName);
      _settingsBox = await Hive.openBox(_settingsBoxName);
      _cacheBox = await Hive.openBox(_cacheBoxName);
      
      _isInitialized = true;
      AppLogger.info('✅ Hive存储服务初始化完成');
      
      // 打印存储统计
      AppLogger.info('📊 存储统计: 任务=${_tasksBox?.length ?? 0}, 设置=${_settingsBox?.length ?? 0}, 缓存=${_cacheBox?.length ?? 0}');
      
    } catch (e) {
      AppLogger.error('❌ Hive存储服务初始化失败: $e');
      rethrow;
    }
  }
  
  /// 确保已初始化
  static void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('HiveStorageService not initialized. Call initialize() first.');
    }
  }
  
  // ==================== 任务存储 ====================
  
  /// 保存任务
  static Future<void> saveTask(TaskModel task) async {
    _ensureInitialized();
    
    try {
      final taskData = task.toJson();
      await _tasksBox!.put(task.id, taskData);
      AppLogger.debug('💾 任务已保存: ${task.id}');
    } catch (e) {
      AppLogger.error('❌ 保存任务失败: $e');
      rethrow;
    }
  }
  
  /// 批量保存任务
  static Future<void> saveTasks(List<TaskModel> tasks) async {
    _ensureInitialized();
    
    try {
      final Map<String, Map<String, dynamic>> taskMap = {};
      for (final task in tasks) {
        taskMap[task.id] = task.toJson();
      }
      
      await _tasksBox!.putAll(taskMap);
      AppLogger.debug('💾 批量保存任务: ${tasks.length}个');
    } catch (e) {
      AppLogger.error('❌ 批量保存任务失败: $e');
      rethrow;
    }
  }
  
  /// 获取任务
  static TaskModel? getTask(String taskId) {
    _ensureInitialized();
    
    try {
      final taskData = _tasksBox!.get(taskId);
      if (taskData == null) return null;
      
      return TaskModel.fromJson(Map<String, dynamic>.from(taskData));
    } catch (e) {
      AppLogger.error('❌ 获取任务失败: $e');
      return null;
    }
  }
  
  /// 🔧 递归转换Map<dynamic, dynamic>为Map<String, dynamic>
  static Map<String, dynamic> _convertMapToStringDynamic(Map map) {
    final result = <String, dynamic>{};

    for (final entry in map.entries) {
      final key = entry.key.toString();
      final value = entry.value;

      if (value is Map) {
        result[key] = _convertMapToStringDynamic(value);
      } else if (value is List) {
        result[key] = _convertListToStringDynamic(value);
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  /// 🔧 递归转换List中的Map类型
  static List<dynamic> _convertListToStringDynamic(List list) {
    return list.map((item) {
      if (item is Map) {
        return _convertMapToStringDynamic(item);
      } else if (item is List) {
        return _convertListToStringDynamic(item);
      } else {
        return item;
      }
    }).toList();
  }

  /// 获取所有任务
  static List<TaskModel> getAllTasks() {
    _ensureInitialized();

    try {
      final tasks = <TaskModel>[];

      Log.i('🔍 [getAllTasks] Hive box中的数据数量: ${_tasksBox!.length}', tag: 'HiveStorage');

      for (final taskData in _tasksBox!.values) {
        try {
          // 🔧 修复类型转换问题：递归转换所有dynamic类型为正确类型
          Map<String, dynamic> convertedData;
          if (taskData is Map<String, dynamic>) {
            convertedData = taskData;
          } else if (taskData is Map) {
            convertedData = _convertMapToStringDynamic(taskData);
          } else {
            throw Exception('任务数据不是Map类型: ${taskData.runtimeType}');
          }

          final task = TaskModel.fromJson(convertedData);
          tasks.add(task);
        } catch (e) {
          AppLogger.warning('⚠️ 跳过损坏的任务数据: $e');
        }
      }

      // 按创建时间排序
      tasks.sort((a, b) => b.createTime.compareTo(a.createTime));

      Log.i('🔍 [getAllTasks] 最终解析成功的任务数量: ${tasks.length}', tag: 'HiveStorage');
      AppLogger.debug('📋 获取任务列表: ${tasks.length}个');
      return tasks;
    } catch (e) {
      AppLogger.error('❌ 获取任务列表失败: $e');
      return [];
    }
  }
  
  /// 删除任务
  static Future<void> deleteTask(String taskId) async {
    _ensureInitialized();
    
    try {
      await _tasksBox!.delete(taskId);
      AppLogger.debug('🗑️ 任务已删除: $taskId');
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $e');
      rethrow;
    }
  }
  
  /// 清空所有任务
  static Future<void> clearAllTasks() async {
    _ensureInitialized();
    
    try {
      await _tasksBox!.clear();
      AppLogger.info('🧹 所有任务已清空');
    } catch (e) {
      AppLogger.error('❌ 清空任务失败: $e');
      rethrow;
    }
  }
  
  // ==================== 设置存储 ====================
  
  /// 保存设置
  static Future<void> saveSetting(String key, dynamic value) async {
    _ensureInitialized();
    
    try {
      await _settingsBox!.put(key, value);
      AppLogger.debug('⚙️ 设置已保存: $key');
    } catch (e) {
      AppLogger.error('❌ 保存设置失败: $e');
      rethrow;
    }
  }
  
  /// 获取设置
  static T? getSetting<T>(String key, {T? defaultValue}) {
    _ensureInitialized();
    
    try {
      final value = _settingsBox!.get(key, defaultValue: defaultValue);
      return value as T?;
    } catch (e) {
      AppLogger.error('❌ 获取设置失败: $e');
      return defaultValue;
    }
  }
  
  /// 删除设置
  static Future<void> deleteSetting(String key) async {
    _ensureInitialized();
    
    try {
      await _settingsBox!.delete(key);
      AppLogger.debug('🗑️ 设置已删除: $key');
    } catch (e) {
      AppLogger.error('❌ 删除设置失败: $e');
      rethrow;
    }
  }
  
  // ==================== 缓存存储 ====================
  
  /// 保存缓存
  static Future<void> saveCache(String key, dynamic value, {Duration? expiry}) async {
    _ensureInitialized();
    
    try {
      final cacheData = {
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'expiry': expiry?.inMilliseconds,
      };
      
      await _cacheBox!.put(key, cacheData);
      AppLogger.debug('💾 缓存已保存: $key');
    } catch (e) {
      AppLogger.error('❌ 保存缓存失败: $e');
      rethrow;
    }
  }
  
  /// 获取缓存
  static T? getCache<T>(String key) {
    _ensureInitialized();
    
    try {
      final cacheData = _cacheBox!.get(key);
      if (cacheData == null) return null;
      
      final data = Map<String, dynamic>.from(cacheData);
      final timestamp = data['timestamp'] as int;
      final expiry = data['expiry'] as int?;
      
      // 检查是否过期
      if (expiry != null) {
        final expiryTime = timestamp + expiry;
        if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
          // 缓存已过期，删除并返回null
          _cacheBox!.delete(key);
          return null;
        }
      }
      
      return data['value'] as T?;
    } catch (e) {
      AppLogger.error('❌ 获取缓存失败: $e');
      return null;
    }
  }
  
  /// 清理过期缓存
  static Future<void> cleanExpiredCache() async {
    _ensureInitialized();
    
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final keysToDelete = <String>[];
      
      for (final entry in _cacheBox!.toMap().entries) {
        try {
          final data = Map<String, dynamic>.from(entry.value);
          final timestamp = data['timestamp'] as int;
          final expiry = data['expiry'] as int?;
          
          if (expiry != null && now > timestamp + expiry) {
            keysToDelete.add(entry.key.toString());
          }
        } catch (e) {
          // 损坏的缓存数据，也删除
          keysToDelete.add(entry.key.toString());
        }
      }
      
      for (final key in keysToDelete) {
        await _cacheBox!.delete(key);
      }
      
      AppLogger.info('🧹 清理过期缓存: ${keysToDelete.length}个');
    } catch (e) {
      AppLogger.error('❌ 清理缓存失败: $e');
    }
  }
  
  // ==================== 工具方法 ====================
  
  /// 获取存储统计信息
  static Map<String, dynamic> getStorageStats() {
    _ensureInitialized();
    
    return {
      'tasks_count': _tasksBox?.length ?? 0,
      'settings_count': _settingsBox?.length ?? 0,
      'cache_count': _cacheBox?.length ?? 0,
      'is_initialized': _isInitialized,
    };
  }

  /// 清空所有任务
  static Future<void> clearTasks() async {
    try {
      _ensureInitialized();
      await _tasksBox?.clear();
      AppLogger.info('🗑️ 已清空所有任务');
    } catch (e) {
      AppLogger.error('❌ 清空任务失败: $e');
      rethrow;
    }
  }

  /// 获取当前任务ID
  static Future<String?> getCurrentTaskId() async {
    try {
      _ensureInitialized();
      return _settingsBox?.get('current_task_id');
    } catch (e) {
      AppLogger.error('❌ 获取当前任务ID失败: $e');
      return null;
    }
  }

  /// 设置当前任务ID
  static Future<void> setCurrentTaskId(String? taskId) async {
    try {
      _ensureInitialized();
      if (taskId != null) {
        await _settingsBox?.put('current_task_id', taskId);
      } else {
        await _settingsBox?.delete('current_task_id');
      }
      AppLogger.info('📌 设置当前任务ID: $taskId');
    } catch (e) {
      AppLogger.error('❌ 设置当前任务ID失败: $e');
      rethrow;
    }
  }

  /// 关闭所有数据盒子
  static Future<void> close() async {
    try {
      await _tasksBox?.close();
      await _settingsBox?.close();
      await _cacheBox?.close();
      
      _tasksBox = null;
      _settingsBox = null;
      _cacheBox = null;
      _isInitialized = false;
      
      AppLogger.info('🔒 Hive存储服务已关闭');
    } catch (e) {
      AppLogger.error('❌ 关闭Hive存储失败: $e');
    }
  }
}
