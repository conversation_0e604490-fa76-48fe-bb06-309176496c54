import 'package:flutter_test/flutter_test.dart';
import 'package:hive/hive.dart';
import 'package:loadguard/models/task_model.dart';

void main() {
  group('🗄️ Hive适配器功能测试', () {
    test('验证适配器生成成功', () {
      // 验证适配器类存在
      expect(TaskStatusAdapter, isNotNull);
      expect(BatchInfoAdapter, isNotNull);
      expect(PhotoItemAdapter, isNotNull);
      expect(RecognitionStatusAdapter, isNotNull);
      expect(RecognitionResultAdapter, isNotNull);
      expect(TaskModelAdapter, isNotNull);
      expect(BatchMatchResultAdapter, isNotNull);
      
      print('✅ 所有Hive适配器类都已生成');
    });
    
    test('验证适配器TypeId正确', () {
      expect(TaskStatusAdapter().typeId, 0);
      expect(BatchInfoAdapter().typeId, 1);
      expect(PhotoItemAdapter().typeId, 2);
      expect(RecognitionStatusAdapter().typeId, 3);
      expect(RecognitionResultAdapter().typeId, 4);
      expect(TaskModelAdapter().typeId, 5);
      expect(BatchMatchResultAdapter().typeId, 6);
      
      print('✅ 所有TypeId设置正确');
    });
    
    test('验证枚举适配器序列化', () {
      final taskStatusAdapter = TaskStatusAdapter();
      
      // 测试TaskStatus序列化
      expect(taskStatusAdapter.typeId, 0);
      
      // 这里我们无法直接测试read/write方法，因为需要BinaryReader/Writer
      // 但我们可以验证适配器的基本属性
      expect(taskStatusAdapter.hashCode, 0);
      expect(taskStatusAdapter == TaskStatusAdapter(), true);
      
      final recognitionStatusAdapter = RecognitionStatusAdapter();
      expect(recognitionStatusAdapter.typeId, 3);
      expect(recognitionStatusAdapter.hashCode, 3);
      
      print('✅ 枚举适配器验证通过');
    });
    
    test('验证对象适配器基本属性', () {
      final batchInfoAdapter = BatchInfoAdapter();
      expect(batchInfoAdapter.typeId, 1);
      expect(batchInfoAdapter.hashCode, 1);
      expect(batchInfoAdapter == BatchInfoAdapter(), true);
      
      final photoItemAdapter = PhotoItemAdapter();
      expect(photoItemAdapter.typeId, 2);
      expect(photoItemAdapter.hashCode, 2);
      expect(photoItemAdapter == PhotoItemAdapter(), true);
      
      final recognitionResultAdapter = RecognitionResultAdapter();
      expect(recognitionResultAdapter.typeId, 4);
      expect(recognitionResultAdapter.hashCode, 4);
      expect(recognitionResultAdapter == RecognitionResultAdapter(), true);
      
      final taskModelAdapter = TaskModelAdapter();
      expect(taskModelAdapter.typeId, 5);
      expect(taskModelAdapter.hashCode, 5);
      expect(taskModelAdapter == TaskModelAdapter(), true);
      
      final batchMatchResultAdapter = BatchMatchResultAdapter();
      expect(batchMatchResultAdapter.typeId, 6);
      expect(batchMatchResultAdapter.hashCode, 6);
      expect(batchMatchResultAdapter == BatchMatchResultAdapter(), true);
      
      print('✅ 对象适配器基本属性验证通过');
    });
    
    test('验证适配器唯一性', () {
      final adapters = <TypeAdapter<dynamic>>[
        TaskStatusAdapter(),
        BatchInfoAdapter(),
        PhotoItemAdapter(),
        RecognitionStatusAdapter(),
        RecognitionResultAdapter(),
        TaskModelAdapter(),
        BatchMatchResultAdapter(),
      ];
      
      // 验证所有TypeId都是唯一的
      final typeIds = <int>{};
      for (final adapter in adapters) {
        typeIds.add(adapter.typeId);
      }
      expect(typeIds.length, adapters.length);
      
      // 验证TypeId范围正确
      expect(typeIds.every((id) => id >= 0 && id <= 6), true);
      
      print('✅ 适配器唯一性验证通过');
    });
    
    test('验证模型对象创建', () {
      // 验证所有模型对象都可以正常创建
      final taskStatus = TaskStatus.inProgress;
      expect(taskStatus, TaskStatus.inProgress);
      
      final recognitionStatus = RecognitionStatus.pending;
      expect(recognitionStatus, RecognitionStatus.pending);
      
      final batchInfo = BatchInfo(
        productCode: 'PP-1100N',
        batchNumber: '250615F10422',
        plannedQuantity: 10,
      );
      expect(batchInfo.productCode, 'PP-1100N');
      
      final photoItem = PhotoItem(
        label: '测试照片',
        isRequired: true,
        needRecognition: true,
        recognitionFailed: false,
        isRecognitionCompleted: false,
        recognitionStatus: RecognitionStatus.pending,
        manualVerified: false,
        isCustom: false,
      );
      expect(photoItem.label, '测试照片');
      
      final recognitionResult = RecognitionResult(
        isQrOcrConsistent: true,
        matchesPreset: true,
        recognitionTime: DateTime.now(),
      );
      expect(recognitionResult.isQrOcrConsistent, true);
      
      final batchMatchResult = BatchMatchResult(
        batchId: 'batch-1',
        productCode: 'PP-1100N',
        batchNumber: '250615F10422',
        isMatched: true,
        confidence: 0.95,
      );
      expect(batchMatchResult.isMatched, true);
      
      final taskModel = TaskModel(
        template: '平板车',
        batches: [batchInfo],
        photos: [photoItem],
        createTime: DateTime.now(),
        participants: ['user1'],
      );
      expect(taskModel.template, '平板车');
      
      print('✅ 所有模型对象创建成功');
    });
    
    test('验证Hive注解完整性', () {
      // 验证所有需要的类都有Hive注解
      // 这个测试主要是确保我们没有遗漏任何类
      
      final annotatedClasses = [
        'TaskStatus',
        'BatchInfo', 
        'PhotoItem',
        'RecognitionStatus',
        'RecognitionResult',
        'TaskModel',
        'BatchMatchResult',
      ];
      
      final generatedAdapters = [
        'TaskStatusAdapter',
        'BatchInfoAdapter',
        'PhotoItemAdapter', 
        'RecognitionStatusAdapter',
        'RecognitionResultAdapter',
        'TaskModelAdapter',
        'BatchMatchResultAdapter',
      ];
      
      expect(annotatedClasses.length, generatedAdapters.length);
      
      print('✅ Hive注解完整性验证通过');
      print('📊 已注解类数量: ${annotatedClasses.length}');
      print('🔧 已生成适配器数量: ${generatedAdapters.length}');
    });
    
    test('验证适配器注册准备', () {
      // 验证适配器可以被实例化，为注册做准备
      final adapters = <TypeAdapter<dynamic>>[
        TaskStatusAdapter(),
        BatchInfoAdapter(),
        PhotoItemAdapter(),
        RecognitionStatusAdapter(),
        RecognitionResultAdapter(),
        TaskModelAdapter(),
        BatchMatchResultAdapter(),
      ];
      
      expect(adapters.length, 7);
      expect(adapters.every((adapter) => adapter.typeId >= 0), true);
      
      // 验证TypeId没有重复
      final typeIds = <int>[];
      for (final adapter in adapters) {
        typeIds.add(adapter.typeId);
      }
      final uniqueTypeIds = typeIds.toSet();
      expect(typeIds.length, uniqueTypeIds.length);
      
      print('✅ 适配器注册准备完成');
      print('🔧 准备注册的适配器数量: ${adapters.length}');
      print('📋 TypeId范围: ${typeIds.join(', ')}');
    });
  });
}
