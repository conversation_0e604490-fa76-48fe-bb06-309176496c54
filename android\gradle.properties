# Gradle JVM配置
org.gradle.jvmargs=-Xmx2G
# org.gradle.java.home=D:\\Program Files\\Java\\jdk-21  # 注释掉硬编码路径，使用系统JAVA_HOME

# Android配置
android.useAndroidX=true
android.enableJetifier=true

# Gradle性能优化
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true

# 网络配置优化
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
systemProp.https.protocols=TLSv1.2,TLSv1.3

# 代理配置（如果需要，去掉注释并填入代理信息）
# systemProp.http.proxyHost=127.0.0.1
# systemProp.http.proxyPort=7890
# systemProp.https.proxyHost=127.0.0.1
# systemProp.https.proxyPort=7890

# Kotlin编译优化 - 禁用增量编译避免路径冲突
kotlin.code.style=official
kotlin.incremental=false
kotlin.incremental.android=false
