/// 🔒 不可变状态管理器
/// 
/// 提供不可变数据结构的状态管理，确保状态的一致性和可预测性
library immutable_state_manager;

import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 状态变更类型
enum StateChangeType {
  create,
  update,
  delete,
  replace,
}

/// 状态变更记录
class StateChange<T> {
  final StateChangeType type;
  final T? oldValue;
  final T? newValue;
  final DateTime timestamp;
  final String? reason;

  StateChange({
    required this.type,
    this.oldValue,
    this.newValue,
    DateTime? timestamp,
    this.reason,
  }) : timestamp = timestamp ?? DateTime.now();

  @override
  String toString() {
    return 'StateChange(type: $type, reason: $reason, timestamp: $timestamp)';
  }
}

/// 不可变状态管理器
class ImmutableStateManager {
  static const String _tag = 'ImmutableState';
  
  // 状态变更历史
  final List<StateChange> _changeHistory = [];
  static const int _maxHistorySize = 100;

  /// 创建新的任务状态
  TaskModel createTask({
    required String template,
    required List<BatchInfo> batches,
    List<PhotoItem>? photos,
    List<String>? participants,
    String? reason,
  }) {
    try {
      AppLogger.info('🆕 创建新任务状态: $template', tag: _tag);
      
      final task = TaskModel(
        template: template,
        batches: List.unmodifiable(batches.map((b) => _freezeBatchInfo(b))),
        photos: List.unmodifiable(photos?.map((p) => _freezePhotoItem(p)) ?? []),
        participants: List.unmodifiable(participants ?? []),
      );

      _recordChange(StateChange<TaskModel>(
        type: StateChangeType.create,
        newValue: task,
        reason: reason ?? 'Task created',
      ));

      AppLogger.info('✅ 任务状态创建成功: ${task.id}', tag: _tag);
      return task;
    } catch (e) {
      AppLogger.error('❌ 创建任务状态失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 更新任务状态
  TaskModel updateTask(
    TaskModel currentTask, {
    String? template,
    List<BatchInfo>? batches,
    List<PhotoItem>? photos,
    List<String>? participants,
    DateTime? completedAt,
    String? reason,
  }) {
    try {
      AppLogger.info('🔄 更新任务状态: ${currentTask.id}', tag: _tag);
      
      final updatedTask = currentTask.copyWith(
        template: template,
        batches: batches != null 
            ? List.unmodifiable(batches.map((b) => _freezeBatchInfo(b)))
            : null,
        photos: photos != null 
            ? List.unmodifiable(photos.map((p) => _freezePhotoItem(p)))
            : null,
        participants: participants != null 
            ? List.unmodifiable(participants)
            : null,
        completedAt: completedAt,
      );

      _recordChange(StateChange<TaskModel>(
        type: StateChangeType.update,
        oldValue: currentTask,
        newValue: updatedTask,
        reason: reason ?? 'Task updated',
      ));

      AppLogger.info('✅ 任务状态更新成功', tag: _tag);
      return updatedTask;
    } catch (e) {
      AppLogger.error('❌ 更新任务状态失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 更新照片状态
  TaskModel updatePhoto(
    TaskModel currentTask,
    String photoId, {
    String? imagePath,
    RecognitionResult? recognitionResult,
    RecognitionStatus? recognitionStatus,
    bool? isVerified,
    String? reason,
  }) {
    try {
      AppLogger.info('📸 更新照片状态: $photoId', tag: _tag);
      
      final photoIndex = currentTask.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      final updatedPhotos = List<PhotoItem>.from(currentTask.photos);
      updatedPhotos[photoIndex] = _freezePhotoItem(
        updatedPhotos[photoIndex].copyWith(
          imagePath: imagePath,
          recognitionResult: recognitionResult,
          recognitionStatus: recognitionStatus,
          isVerified: isVerified,
        ),
      );

      final updatedTask = currentTask.copyWith(
        photos: List.unmodifiable(updatedPhotos),
      );

      _recordChange(StateChange<TaskModel>(
        type: StateChangeType.update,
        oldValue: currentTask,
        newValue: updatedTask,
        reason: reason ?? 'Photo updated: $photoId',
      ));

      AppLogger.info('✅ 照片状态更新成功', tag: _tag);
      return updatedTask;
    } catch (e) {
      AppLogger.error('❌ 更新照片状态失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 更新批次状态
  TaskModel updateBatch(
    TaskModel currentTask,
    String batchId, {
    int? recognizedQuantity,
    List<String>? recognizedItems,
    String? reason,
  }) {
    try {
      AppLogger.info('📦 更新批次状态: $batchId', tag: _tag);
      
      final batchIndex = currentTask.batches.indexWhere((b) => b.id == batchId);
      if (batchIndex == -1) {
        throw Exception('批次不存在: $batchId');
      }

      final updatedBatches = List<BatchInfo>.from(currentTask.batches);
      updatedBatches[batchIndex] = _freezeBatchInfo(
        updatedBatches[batchIndex].copyWith(
          recognizedQuantity: recognizedQuantity,
          recognizedItems: recognizedItems,
        ),
      );

      final updatedTask = currentTask.copyWith(
        batches: List.unmodifiable(updatedBatches),
      );

      _recordChange(StateChange<TaskModel>(
        type: StateChangeType.update,
        oldValue: currentTask,
        newValue: updatedTask,
        reason: reason ?? 'Batch updated: $batchId',
      ));

      AppLogger.info('✅ 批次状态更新成功', tag: _tag);
      return updatedTask;
    } catch (e) {
      AppLogger.error('❌ 更新批次状态失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 替换任务列表
  List<TaskModel> replaceTasks(
    List<TaskModel> newTasks, {
    String? reason,
  }) {
    try {
      AppLogger.info('🔄 替换任务列表: ${newTasks.length}个任务', tag: _tag);
      
      final frozenTasks = List<TaskModel>.unmodifiable(
        newTasks.map((task) => _freezeTask(task)),
      );

      _recordChange(StateChange<List<TaskModel>>(
        type: StateChangeType.replace,
        newValue: frozenTasks,
        reason: reason ?? 'Tasks replaced',
      ));

      AppLogger.info('✅ 任务列表替换成功', tag: _tag);
      return frozenTasks;
    } catch (e) {
      AppLogger.error('❌ 替换任务列表失败: $e', tag: _tag);
      rethrow;
    }
  }

  /// 验证状态一致性
  bool validateStateConsistency(TaskModel task) {
    try {
      // 验证批次数据一致性
      for (final batch in task.batches) {
        if (batch.recognizedQuantity < 0 || batch.recognizedQuantity > batch.plannedQuantity) {
          AppLogger.warning('⚠️ 批次数据不一致: ${batch.batchNumber}', tag: _tag);
          return false;
        }
      }

      // 验证照片数据一致性
      for (final photo in task.photos) {
        if (photo.isVerified && photo.recognitionResult == null) {
          AppLogger.warning('⚠️ 照片验证状态不一致: ${photo.label}', tag: _tag);
          return false;
        }
      }

      return true;
    } catch (e) {
      AppLogger.error('❌ 状态一致性验证失败: $e', tag: _tag);
      return false;
    }
  }

  /// 获取状态变更历史
  List<StateChange> getChangeHistory({int? limit}) {
    final history = List<StateChange>.from(_changeHistory);
    if (limit != null && limit < history.length) {
      return history.sublist(history.length - limit);
    }
    return history;
  }

  /// 清理变更历史
  void clearHistory() {
    _changeHistory.clear();
    AppLogger.info('🧹 状态变更历史已清理', tag: _tag);
  }

  /// 获取状态统计
  Map<String, dynamic> getStateStatistics() {
    final changeTypes = <StateChangeType, int>{};
    for (final change in _changeHistory) {
      changeTypes[change.type] = (changeTypes[change.type] ?? 0) + 1;
    }

    return {
      'totalChanges': _changeHistory.length,
      'changeTypes': changeTypes.map((k, v) => MapEntry(k.toString(), v)),
      'maxHistorySize': _maxHistorySize,
      'memoryUsage': _changeHistory.length * 100, // 估算内存使用
    };
  }

  /// 私有方法：冻结任务对象
  TaskModel _freezeTask(TaskModel task) {
    return task.copyWith(
      batches: List.unmodifiable(task.batches.map((b) => _freezeBatchInfo(b))),
      photos: List.unmodifiable(task.photos.map((p) => _freezePhotoItem(p))),
      participants: List.unmodifiable(task.participants),
    );
  }

  /// 私有方法：冻结批次信息
  BatchInfo _freezeBatchInfo(BatchInfo batch) {
    return batch.copyWith(
      recognizedItems: List.unmodifiable(batch.recognizedItems),
    );
  }

  /// 私有方法：冻结照片项目
  PhotoItem _freezePhotoItem(PhotoItem photo) {
    // PhotoItem已经是不可变的，直接返回
    return photo;
  }

  /// 私有方法：记录状态变更
  void _recordChange(StateChange change) {
    _changeHistory.add(change);
    
    // 限制历史记录大小
    if (_changeHistory.length > _maxHistorySize) {
      _changeHistory.removeAt(0);
    }
    
    AppLogger.debug('📝 状态变更已记录: ${change.type}', tag: _tag);
  }
}
