import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/fast_recognition_service.dart';
import 'package:loadguard/services/async_upload_service.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

void main() {
  group('🚀 优化后识别系统性能测试', () {
    late FastRecognitionService fastService;
    late AsyncUploadService asyncService;
    
    setUpAll(() async {
      AppLogger.info('🧪 初始化测试环境...');
      fastService = FastRecognitionService.instance;
      asyncService = AsyncUploadService();
      await fastService.initialize();
      await asyncService.initialize();
    });
    
    tearDownAll(() {
      fastService.dispose();
      asyncService.dispose();
    });

    testWidgets('🚀 快速识别服务性能测试', (WidgetTester tester) async {
      // 使用实际测试图片
      final testImagePaths = [
        'photos/wechat_2025-08-04_083559_653.jpg',
        'photos/wechat_2025-08-04_083613_443.jpg',
        'photos/wechat_2025-08-04_083625_280.jpg',
      ];
      
      final results = <Map<String, dynamic>>[];
      
      for (final imagePath in testImagePaths) {
        final file = File(imagePath);
        if (!await file.exists()) {
          AppLogger.warning('⚠️ 测试图片不存在: $imagePath');
          continue;
        }
        
        AppLogger.info('🧪 测试图片: $imagePath');
        final stopwatch = Stopwatch()..start();
        
        try {
          final result = await fastService.recognizeFast(
            imagePath,
            onProgress: (progress, status) {
              AppLogger.debug('📊 进度: ${(progress * 100).toInt()}% - $status');
            },
          );
          
          stopwatch.stop();
          
          final testResult = {
            'imagePath': imagePath,
            'processingTime': stopwatch.elapsedMilliseconds,
            'hasText': result.hasText,
            'hasQRCode': result.hasQRCode,
            'textResults': result.textResults.length,
            'qrResults': result.qrResults.length,
            'overallConfidence': result.overallConfidence,
            'success': result.hasAnyResult,
          };
          
          results.add(testResult);
          
          AppLogger.info('✅ 测试结果: $testResult');
          
          // 性能断言
          expect(stopwatch.elapsedMilliseconds, lessThan(3000), 
                 reason: '识别时间应小于3秒');
          expect(result.hasAnyResult, isTrue, 
                 reason: '应该有识别结果');
          
        } catch (e) {
          stopwatch.stop();
          AppLogger.error('❌ 识别测试失败: $e');
          
          results.add({
            'imagePath': imagePath,
            'processingTime': stopwatch.elapsedMilliseconds,
            'error': e.toString(),
            'success': false,
          });
        }
      }
      
      // 生成性能报告
      _generatePerformanceReport(results);
    });

    testWidgets('⚡ 异步上传服务性能测试', (WidgetTester tester) async {
      final testImagePath = 'photos/wechat_2025-08-04_083559_653.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过异步服务测试');
        return;
      }
      
      final stopwatch = Stopwatch()..start();
      bool recognitionCompleted = false;
      String? recognitionResult;
      String? recognitionError;
      
      final uploadId = await asyncService.uploadPhotoAsync(
        photoId: 'test_photo_001',
        imagePath: testImagePath,
        taskId: 'test_task_001',
        presetProductCode: 'LLD-7042',
        presetBatchNumber: '250712F20440-2C',
        needRecognition: true,
        onRecognitionSuccess: (photoId, result) {
          stopwatch.stop();
          recognitionCompleted = true;
          recognitionResult = result.ocrText;
          AppLogger.info('✅ 异步识别成功: $photoId - ${result.ocrText}');
        },
        onRecognitionFailure: (photoId, error) {
          stopwatch.stop();
          recognitionCompleted = true;
          recognitionError = error;
          AppLogger.error('❌ 异步识别失败: $photoId - $error');
        },
      );
      
      AppLogger.info('📤 异步任务已提交: $uploadId');
      
      // 等待识别完成（最多15秒）
      var waitTime = 0;
      while (!recognitionCompleted && waitTime < 15000) {
        await Future.delayed(const Duration(milliseconds: 100));
        waitTime += 100;
        
        // 检查进度
        final progress = asyncService.getProgress(uploadId);
        if (progress != null) {
          AppLogger.debug('📊 异步进度: ${(progress.progress * 100).toInt()}% - ${progress.message}');
        }
      }
      
      // 性能验证
      expect(recognitionCompleted, isTrue, reason: '识别应该在15秒内完成');
      
      if (recognitionCompleted) {
        expect(stopwatch.elapsedMilliseconds, lessThan(15000), 
               reason: '异步识别应在15秒内完成');
        
        AppLogger.info('📊 异步识别性能: ${stopwatch.elapsedMilliseconds}ms');
        
        if (recognitionResult != null) {
          AppLogger.info('✅ 识别成功，结果: $recognitionResult');
        } else {
          AppLogger.warning('⚠️ 识别失败: $recognitionError');
        }
      }
    });
    
    testWidgets('🎯 对比测试：快速模式 vs 原始模式', (WidgetTester tester) async {
      final testImagePath = 'photos/wechat_2025-08-04_083559_653.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过对比测试');
        return;
      }
      
      // 快速模式测试
      AppLogger.info('🚀 测试快速模式...');
      final fastStopwatch = Stopwatch()..start();
      final fastResult = await fastService.recognizeFast(testImagePath);
      fastStopwatch.stop();
      
      final fastPerformance = {
        'mode': 'fast',
        'time': fastStopwatch.elapsedMilliseconds,
        'hasResult': fastResult.hasAnyResult,
        'confidence': fastResult.overallConfidence,
      };
      
      AppLogger.info('📊 快速模式结果: $fastPerformance');
      
      // 性能验证
      expect(fastStopwatch.elapsedMilliseconds, lessThan(2000), 
             reason: '快速模式应在2秒内完成');
      expect(fastResult.hasAnyResult, isTrue, 
             reason: '快速模式应该有识别结果');
      
      // 生成对比报告
      AppLogger.info('📊 === 性能对比报告 ===');
      AppLogger.info('🚀 快速模式: ${fastPerformance['time']}ms, 成功率: ${fastResult.hasAnyResult}');
      AppLogger.info('📊 置信度: ${fastResult.overallConfidence.toStringAsFixed(2)}');
    });
  });
}

/// 生成性能测试报告
void _generatePerformanceReport(List<Map<String, dynamic>> results) {
  AppLogger.info('📊 === 性能测试报告 ===');
  
  if (results.isEmpty) {
    AppLogger.warning('⚠️ 没有测试结果');
    return;
  }
  
  final successResults = results.where((r) => r['success'] == true).toList();
  final failureResults = results.where((r) => r['success'] == false).toList();
  
  AppLogger.info('📈 测试总数: ${results.length}');
  AppLogger.info('✅ 成功: ${successResults.length}');
  AppLogger.info('❌ 失败: ${failureResults.length}');
  AppLogger.info('📊 成功率: ${(successResults.length / results.length * 100).toStringAsFixed(1)}%');
  
  if (successResults.isNotEmpty) {
    final avgTime = successResults
        .map((r) => r['processingTime'] as int)
        .reduce((a, b) => a + b) / successResults.length;
    
    final avgConfidence = successResults
        .map((r) => r['overallConfidence'] as double)
        .reduce((a, b) => a + b) / successResults.length;
    
    AppLogger.info('⏱️ 平均处理时间: ${avgTime.toStringAsFixed(0)}ms');
    AppLogger.info('🎯 平均置信度: ${avgConfidence.toStringAsFixed(2)}');
  }
  
  // 详细结果
  for (int i = 0; i < results.length; i++) {
    final result = results[i];
    AppLogger.info('📷 图片${i + 1}: ${result['imagePath']}');
    AppLogger.info('   ⏱️ 耗时: ${result['processingTime']}ms');
    AppLogger.info('   📊 结果: ${result['success'] ? '成功' : '失败'}');
    if (result['success']) {
      AppLogger.info('   📝 文字: ${result['hasText']}');
      AppLogger.info('   📱 二维码: ${result['hasQRCode']}');
      AppLogger.info('   🎯 置信度: ${result['overallConfidence'].toStringAsFixed(2)}');
    } else {
      AppLogger.info('   ❌ 错误: ${result['error']}');
    }
  }
}