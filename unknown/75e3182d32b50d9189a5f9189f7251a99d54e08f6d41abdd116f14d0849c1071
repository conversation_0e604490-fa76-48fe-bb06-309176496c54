import 'package:flutter/material.dart';
import 'package:loadguard/services/industrial_label_recognition_service.dart';
import 'package:loadguard/models/task_model.dart';

/// 🏭 【工业标签识别使用示例】
/// 
/// 【功能演示】展示如何使用新的工业标签识别服务
/// 【适用场景】蓝色背景标签、二维码标签、混合标签等实际工业应用
/// 
/// 🎯 【示例内容】：
/// 1. 基本识别调用
/// 2. 蓝色背景标签处理
/// 3. 二维码+文字混合识别
/// 4. 智能策略选择
/// 5. 结果处理和验证
class IndustrialLabelRecognitionExample {
  
  /// 📱 基本使用示例
  static Future<void> basicUsageExample() async {
    print('🏭 === 基本工业标签识别示例 ===');
    
    try {
      // 1. 获取服务实例
      final service = IndustrialLabelRecognitionService.instance;
      
      // 2. 执行识别
      final results = await service.recognizeIndustrialLabel(
        '/path/to/industrial_label.jpg',
        onProgress: (progress, status) {
          print('进度: ${(progress * 100).toInt()}% - $status');
        },
      );
      
      // 3. 处理结果
      print('✅ 识别完成，共识别到 ${results.length} 个元素：');
      for (int i = 0; i < results.length; i++) {
        final result = results[i];
        print('  ${i + 1}. ${result.ocrText} (置信度: ${(result.confidence * 100).toInt()}%)');
        
        // 检查是否为二维码结果
        if (result.metadata?['barcodeFormat'] != null) {
          print('     📱 二维码类型: ${result.metadata!['barcodeType']}');
        }
      }
      
    } catch (e) {
      print('❌ 识别失败: $e');
    }
  }
  
  /// 🔵 蓝色背景标签识别示例
  static Future<void> blueBackgroundExample() async {
    print('\n🔵 === 蓝色背景标签识别示例 ===');
    
    try {
      final service = IndustrialLabelRecognitionService.instance;
      
      // 1. 先检测标签类型
      final labelType = await IndustrialLabelRecognitionService.detectLabelType(
        '/path/to/blue_background_label.jpg'
      );
      
      print('📊 检测到标签类型: ${_getLabelTypeName(labelType)}');
      
      // 2. 根据标签类型选择策略
      final strategy = labelType == IndustrialLabelType.blueBackgroundWithQR
          ? IndustrialRecognitionStrategy.accurate  // 复杂标签用精确模式
          : IndustrialRecognitionStrategy.optimized; // 普通标签用优化模式
      
      // 3. 执行识别
      final results = await service.recognizeIndustrialLabel(
        '/path/to/blue_background_label.jpg',
        strategy: strategy,
        onProgress: (progress, status) {
          print('🔵 蓝色背景处理进度: ${(progress * 100).toInt()}% - $status');
        },
      );
      
      // 4. 分析结果
      _analyzeResults(results);
      
    } catch (e) {
      print('❌ 蓝色背景识别失败: $e');
    }
  }
  
  /// 📱 二维码+文字混合识别示例
  static Future<void> hybridRecognitionExample() async {
    print('\n📱 === 二维码+文字混合识别示例 ===');
    
    try {
      final service = IndustrialLabelRecognitionService.instance;
      
      // 使用精确策略确保最佳识别效果
      final results = await service.recognizeIndustrialLabel(
        '/path/to/label_with_qr_code.jpg',
        strategy: IndustrialRecognitionStrategy.accurate,
        onProgress: (progress, status) {
          print('📱 混合识别进度: ${(progress * 100).toInt()}% - $status');
        },
      );
      
      // 分离文字和二维码结果
      final textResults = results.where((r) => 
          r.metadata?['barcodeFormat'] == null).toList();
      final qrResults = results.where((r) => 
          r.metadata?['barcodeFormat'] != null).toList();
      
      print('✅ 混合识别完成：');
      print('📝 文字识别结果 (${textResults.length}个):');
      for (final result in textResults) {
        print('   - ${result.ocrText} (置信度: ${(result.confidence * 100).toInt()}%)');
        if (result.metadata?['qrValidated'] == true) {
          print('     ✓ 已通过二维码验证');
        }
      }
      
      print('📱 二维码识别结果 (${qrResults.length}个):');
      for (final result in qrResults) {
        print('   - ${result.ocrText}');
        print('     格式: ${result.metadata!['barcodeType']}');
      }
      
    } catch (e) {
      print('❌ 混合识别失败: $e');
    }
  }
  
  /// 🎯 智能策略选择示例
  static Future<void> smartStrategyExample() async {
    print('\n🎯 === 智能策略选择示例 ===');
    
    // 模拟不同的应用场景
    final scenarios = [
      {
        'name': '质量控制场景',
        'hasBlueBackground': true,
        'hasQRCode': true,
        'prioritizeAccuracy': true,
        'prioritizeSpeed': false,
      },
      {
        'name': '批量处理场景',
        'hasBlueBackground': false,
        'hasQRCode': false,
        'prioritizeAccuracy': false,
        'prioritizeSpeed': true,
      },
      {
        'name': '标准识别场景',
        'hasBlueBackground': true,
        'hasQRCode': false,
        'prioritizeAccuracy': false,
        'prioritizeSpeed': false,
      },
    ];
    
    for (final scenario in scenarios) {
      final strategy = IndustrialLabelRecognitionService.recommendStrategy(
        hasBlueBackground: scenario['hasBlueBackground'] as bool,
        hasQRCode: scenario['hasQRCode'] as bool,
        prioritizeAccuracy: scenario['prioritizeAccuracy'] as bool,
        prioritizeSpeed: scenario['prioritizeSpeed'] as bool,
      );
      
      print('📋 ${scenario['name']}: 推荐策略 ${_getStrategyName(strategy)}');
    }
  }
  
  /// 🔄 批量识别示例
  static Future<void> batchRecognitionExample() async {
    print('\n🔄 === 批量识别示例 ===');
    
    final imagePaths = [
      '/path/to/label1.jpg',
      '/path/to/label2.jpg', 
      '/path/to/label3.jpg',
    ];
    
    try {
      final service = IndustrialLabelRecognitionService.instance;
      
      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        print('\n📷 处理图像 ${i + 1}/${imagePaths.length}: ${imagePath.split('/').last}');
        
        // 1. 检测标签类型
        final labelType = await IndustrialLabelRecognitionService.detectLabelType(imagePath);
        print('   标签类型: ${_getLabelTypeName(labelType)}');
        
        // 2. 执行识别
        final results = await service.recognizeIndustrialLabel(
          imagePath,
          strategy: IndustrialRecognitionStrategy.optimized,
          onProgress: (progress, status) {
            if (progress == 1.0) {
              print('   ✅ 识别完成');
            }
          },
        );
        
        // 3. 输出结果摘要
        print('   📊 识别结果: ${results.length}个元素');
        if (results.isNotEmpty) {
          final avgConfidence = results
              .map((r) => r.confidence)
              .reduce((a, b) => a + b) / results.length;
          print('   📈 平均置信度: ${(avgConfidence * 100).toInt()}%');
        }
      }
      
    } catch (e) {
      print('❌ 批量识别失败: $e');
    }
  }
  
  /// 分析识别结果
  static void _analyzeResults(List<RecognitionResult> results) {
    if (results.isEmpty) {
      print('⚠️ 未识别到任何内容');
      return;
    }
    
    final textResults = results.where((r) => 
        r.metadata?['barcodeFormat'] == null).toList();
    final qrResults = results.where((r) => 
        r.metadata?['barcodeFormat'] != null).toList();
    
    print('📊 识别结果分析:');
    print('   📝 文字元素: ${textResults.length}个');
    print('   📱 二维码元素: ${qrResults.length}个');
    
    if (textResults.isNotEmpty) {
      final avgTextConfidence = textResults
          .map((r) => r.confidence)
          .reduce((a, b) => a + b) / textResults.length;
      print('   📈 文字识别平均置信度: ${(avgTextConfidence * 100).toInt()}%');
    }
    
    if (qrResults.isNotEmpty) {
      final avgQRConfidence = qrResults
          .map((r) => r.confidence)
          .reduce((a, b) => a + b) / qrResults.length;
      print('   📈 二维码识别平均置信度: ${(avgQRConfidence * 100).toInt()}%');
    }
    
    // 检查交叉验证结果
    final validatedResults = textResults.where((r) => 
        r.metadata?['qrValidated'] == true).toList();
    if (validatedResults.isNotEmpty) {
      print('   ✅ 通过二维码验证的文字: ${validatedResults.length}个');
    }
  }
  
  /// 获取标签类型名称
  static String _getLabelTypeName(IndustrialLabelType type) {
    switch (type) {
      case IndustrialLabelType.standard:
        return '标准标签';
      case IndustrialLabelType.blueBackground:
        return '蓝色背景标签';
      case IndustrialLabelType.withQRCode:
        return '包含二维码的标签';
      case IndustrialLabelType.blueBackgroundWithQR:
        return '蓝色背景+二维码标签';
      case IndustrialLabelType.unknown:
        return '未知类型';
    }
  }
  
  /// 获取策略名称
  static String _getStrategyName(IndustrialRecognitionStrategy strategy) {
    switch (strategy) {
      case IndustrialRecognitionStrategy.fast:
        return '快速识别';
      case IndustrialRecognitionStrategy.accurate:
        return '精确识别';
      case IndustrialRecognitionStrategy.optimized:
        return '优化识别';
    }
  }
}

/// 🚀 运行所有示例
Future<void> main() async {
  print('🏭 LoadGuard 工业标签识别示例');
  print('=' * 50);
  
  // 运行所有示例
  await IndustrialLabelRecognitionExample.basicUsageExample();
  await IndustrialLabelRecognitionExample.blueBackgroundExample();
  await IndustrialLabelRecognitionExample.hybridRecognitionExample();
  await IndustrialLabelRecognitionExample.smartStrategyExample();
  await IndustrialLabelRecognitionExample.batchRecognitionExample();
  
  print('\n🎉 所有示例运行完成！');
}
