import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/image_preprocessor.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:image/image.dart' as img;
// ✅ 使用统一的算法枚举定义
import 'package:loadguard/models/recognition_algorithm.dart';

/// ML Kit V2 0.15.0 算法优化器
/// 保持核心识别能力不变，优化算法选择策略和性能
class MLKitAlgorithmOptimizer {
  static const String _version = 'ML Kit V2 0.15.0';
  
  /// 12种识别算法策略
  static const List<RecognitionAlgorithm> _algorithms = [
    RecognitionAlgorithm.standardLatin,
    RecognitionAlgorithm.enhancedLatin,
    RecognitionAlgorithm.chineseOptimized,
    RecognitionAlgorithm.mixedLanguage,
    RecognitionAlgorithm.highContrast,
    RecognitionAlgorithm.lowLight,
    RecognitionAlgorithm.blurryText,
    RecognitionAlgorithm.smallText,
    RecognitionAlgorithm.rotatedText,
    RecognitionAlgorithm.perspectiveCorrected,
    RecognitionAlgorithm.noiseReduction,
    RecognitionAlgorithm.adaptiveThreshold,
  ];
  
  /// 评估图像质量并选择最优算法
  static Future<AlgorithmSelection> selectOptimalAlgorithm(String imagePath) async {
    try {
      AppLogger.info('🔍 开始评估图像质量并选择最优算法: $imagePath');
      
      // 1. 图像质量分析
      final qualityMetrics = await _analyzeImageQuality(imagePath);
      
      // 2. 基于质量指标选择算法
      final selectedAlgorithms = _selectAlgorithmsBasedOnQuality(qualityMetrics);
      
      // 3. 生成优化建议
      final optimizationSuggestions = _generateOptimizationSuggestions(qualityMetrics);
      
      AppLogger.info('✅ 算法选择完成: ${selectedAlgorithms.primary.name}');
      
      return AlgorithmSelection(
        primary: selectedAlgorithms.primary,
        fallback: selectedAlgorithms.fallback,
        qualityMetrics: qualityMetrics,
        optimizationSuggestions: optimizationSuggestions,
        confidence: selectedAlgorithms.confidence,
      );
    } catch (e) {
      AppLogger.error('❌ 算法选择失败', error: e);
      // 返回默认算法
      return AlgorithmSelection(
        primary: RecognitionAlgorithm.standardLatin,
        fallback: [RecognitionAlgorithm.enhancedLatin],
        qualityMetrics: ImageQualityMetrics.defaultMetrics(),
        optimizationSuggestions: ['使用默认算法'],
        confidence: 0.5,
      );
    }
  }
  
  /// 执行优化的识别处理
  static Future<OptimizedRecognitionResult> processWithOptimization({
    required String imagePath,
    AlgorithmSelection? algorithmSelection,
    Function(double progress, String status)? onProgress,
    String? presetProductCode,
    String? presetBatchNumber,
  }) async {
    final startTime = DateTime.now();
    
    try {
      onProgress?.call(0.1, '分析图像质量...');
      
      // 1. 选择最优算法（如果未提供）
      algorithmSelection ??= await selectOptimalAlgorithm(imagePath);
      
      onProgress?.call(0.2, '准备识别算法...');
      
      // 2. 应用预处理（如果需要）
      String processedImagePath = imagePath;
      if (algorithmSelection.qualityMetrics.needsPreprocessing) {
        onProgress?.call(0.3, '应用图像预处理...');
        processedImagePath = await _applyPreprocessing(
          imagePath, 
          algorithmSelection.qualityMetrics,
        );
      }
      
      onProgress?.call(0.4, '执行文本识别...');
      
      // 3. 执行主要算法
      var recognitionResult = await _executeAlgorithm(
        algorithmSelection.primary,
        processedImagePath,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
        onProgress: (progress, status) {
          onProgress?.call(0.4 + progress * 0.4, status);
        },
      );
      
      // 4. 如果主要算法失败，尝试备用算法
      if (recognitionResult.status != RecognitionStatus.completed && algorithmSelection.fallback.isNotEmpty) {
        onProgress?.call(0.8, '尝试备用算法...');
        
        for (final fallbackAlgorithm in algorithmSelection.fallback) {
          try {
            recognitionResult = await _executeAlgorithm(
              fallbackAlgorithm,
              processedImagePath,
              presetProductCode: presetProductCode,
              presetBatchNumber: presetBatchNumber,
            );
            
            if (recognitionResult.status == RecognitionStatus.completed) {
              AppLogger.info('✅ 备用算法成功: ${fallbackAlgorithm.name}');
              break;
            }
          } catch (e) {
            AppLogger.warning('⚠️ 备用算法失败: ${fallbackAlgorithm.name} - $e');
            continue;
          }
        }
      }
      
      onProgress?.call(1.0, '识别完成');
      
      final processingTime = DateTime.now().difference(startTime);
      
      return OptimizedRecognitionResult(
        recognitionResult: recognitionResult,
        algorithmUsed: algorithmSelection.primary,
        qualityMetrics: algorithmSelection.qualityMetrics,
        processingTime: processingTime,
        optimizationApplied: algorithmSelection.qualityMetrics.needsPreprocessing,
        fallbackUsed: recognitionResult.status != RecognitionStatus.completed,
      );
      
    } catch (e) {
      AppLogger.error('❌ 优化识别处理失败', error: e);
      rethrow;
    }
  }
  
  /// 分析图像质量
  static Future<ImageQualityMetrics> _analyzeImageQuality(String imagePath) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        throw Exception('无法解码图像');
      }
      
      // 计算各种质量指标
      final brightness = _calculateBrightness(image);
      final contrast = _calculateContrast(image);
      final sharpness = _calculateSharpness(image);
      final noiseLevel = _calculateNoiseLevel(image);
      final textDensity = _estimateTextDensity(image);
      
      // 检测图像特征
      final hasRotation = _detectRotation(image);
      final hasPerspectiveDistortion = _detectPerspectiveDistortion(image);
      final hasBlur = sharpness < 0.3;
      final hasLowContrast = contrast < 0.4;
      
      // 计算综合质量分数
      final overallQuality = _calculateOverallQuality(
        brightness, contrast, sharpness, noiseLevel, textDensity,
      );
      
      return ImageQualityMetrics(
        brightness: brightness,
        contrast: contrast,
        sharpness: sharpness,
        noiseLevel: noiseLevel,
        textDensity: textDensity,
        overallQuality: overallQuality,
        hasRotation: hasRotation,
        hasPerspectiveDistortion: hasPerspectiveDistortion,
        hasBlur: hasBlur,
        hasLowContrast: hasLowContrast,
        imageSize: Size(image.width.toDouble(), image.height.toDouble()),
      );
      
    } catch (e) {
      AppLogger.error('❌ 图像质量分析失败', error: e);
      return ImageQualityMetrics.defaultMetrics();
    }
  }
  
  /// 基于质量指标选择算法
  static AlgorithmSelectionResult _selectAlgorithmsBasedOnQuality(ImageQualityMetrics metrics) {
    RecognitionAlgorithm primary;
    List<RecognitionAlgorithm> fallback = [];
    double confidence = 1.0;
    
    // 基于图像特征选择主要算法
    if (metrics.hasLowContrast) {
      primary = RecognitionAlgorithm.highContrast;
      fallback = [RecognitionAlgorithm.adaptiveThreshold, RecognitionAlgorithm.enhancedLatin];
    } else if (metrics.hasBlur) {
      primary = RecognitionAlgorithm.blurryText;
      fallback = [RecognitionAlgorithm.noiseReduction, RecognitionAlgorithm.enhancedLatin];
    } else if (metrics.brightness < 0.3) {
      primary = RecognitionAlgorithm.lowLight;
      fallback = [RecognitionAlgorithm.highContrast, RecognitionAlgorithm.adaptiveThreshold];
    } else if (metrics.hasRotation) {
      primary = RecognitionAlgorithm.rotatedText;
      fallback = [RecognitionAlgorithm.enhancedLatin, RecognitionAlgorithm.standardLatin];
    } else if (metrics.hasPerspectiveDistortion) {
      primary = RecognitionAlgorithm.perspectiveCorrected;
      fallback = [RecognitionAlgorithm.rotatedText, RecognitionAlgorithm.enhancedLatin];
    } else if (metrics.noiseLevel > 0.6) {
      primary = RecognitionAlgorithm.noiseReduction;
      fallback = [RecognitionAlgorithm.adaptiveThreshold, RecognitionAlgorithm.enhancedLatin];
    } else if (metrics.textDensity > 0.8) {
      primary = RecognitionAlgorithm.smallText;
      fallback = [RecognitionAlgorithm.enhancedLatin, RecognitionAlgorithm.standardLatin];
    } else if (metrics.overallQuality > 0.8) {
      primary = RecognitionAlgorithm.standardLatin;
      fallback = [RecognitionAlgorithm.enhancedLatin];
      confidence = 0.95;
    } else {
      primary = RecognitionAlgorithm.enhancedLatin;
      fallback = [RecognitionAlgorithm.adaptiveThreshold, RecognitionAlgorithm.standardLatin];
      confidence = 0.8;
    }
    
    // 调整置信度
    if (metrics.overallQuality < 0.5) {
      confidence *= 0.7;
    }
    
    return AlgorithmSelectionResult(
      primary: primary,
      fallback: fallback,
      confidence: confidence,
    );
  }
  
  /// 生成优化建议
  static List<String> _generateOptimizationSuggestions(ImageQualityMetrics metrics) {
    final suggestions = <String>[];
    
    if (metrics.brightness < 0.3) {
      suggestions.add('建议增加拍照环境光线');
    }
    if (metrics.contrast < 0.4) {
      suggestions.add('建议调整拍照角度以增加对比度');
    }
    if (metrics.hasBlur) {
      suggestions.add('建议保持设备稳定，避免抖动');
    }
    if (metrics.hasRotation) {
      suggestions.add('建议调整拍照角度，保持文本水平');
    }
    if (metrics.hasPerspectiveDistortion) {
      suggestions.add('建议正对标签拍照，避免倾斜');
    }
    if (metrics.noiseLevel > 0.6) {
      suggestions.add('建议清洁镜头，改善拍照环境');
    }
    
    if (suggestions.isEmpty) {
      suggestions.add('图像质量良好，无需特殊优化');
    }
    
    return suggestions;
  }
  
  /// 应用预处理
  static Future<String> _applyPreprocessing(String imagePath, ImageQualityMetrics metrics) async {
    try {
      final outputPath = '${imagePath}_processed.jpg';

      if (metrics.hasLowContrast || metrics.brightness < 0.3) {
        final processedFile = await ImagePreprocessor.enhanceImageForOCR(File(imagePath), outputPath);
        return processedFile.path;
      } else if (metrics.hasPerspectiveDistortion) {
        final processedFile = await ImagePreprocessor.correctPerspective(File(imagePath), outputPath);
        return processedFile.path;
      } else {
        final processedFile = await ImagePreprocessor.enhanceImageForOCR(File(imagePath), outputPath);
        return processedFile.path;
      }
    } catch (e) {
      AppLogger.warning('⚠️ 预处理失败，使用原图: $e');
      return imagePath;
    }
  }
  
  /// 执行特定算法
  static Future<RecognitionResult> _executeAlgorithm(
    RecognitionAlgorithm algorithm,
    String imagePath, {
    String? presetProductCode,
    String? presetBatchNumber,
    Function(double progress, String status)? onProgress,
  }) async {
    try {
      // 创建对应的TextRecognizer
      final recognizer = _createRecognizerForAlgorithm(algorithm);
      
      // 创建输入图像
      final inputImage = InputImage.fromFilePath(imagePath);
      
      // 执行识别
      final recognizedText = await recognizer.processImage(inputImage);
      
      // 处理结果
      final result = _processRecognitionResult(
        recognizedText,
        algorithm,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
      );
      
      // 清理资源
      await recognizer.close();
      
      return result;
    } catch (e) {
      AppLogger.error('❌ 算法执行失败: ${algorithm.name}', error: e);
      rethrow;
    }
  }
  
  /// 为特定算法创建TextRecognizer
  static TextRecognizer _createRecognizerForAlgorithm(RecognitionAlgorithm algorithm) {
    switch (algorithm) {
      case RecognitionAlgorithm.chineseOptimized:
      case RecognitionAlgorithm.mixedLanguage:
        return TextRecognizer(script: TextRecognitionScript.chinese);
      case RecognitionAlgorithm.standardLatin:
      case RecognitionAlgorithm.enhancedLatin:
      case RecognitionAlgorithm.highContrast:
      case RecognitionAlgorithm.lowLight:
      case RecognitionAlgorithm.blurryText:
      case RecognitionAlgorithm.smallText:
      case RecognitionAlgorithm.rotatedText:
      case RecognitionAlgorithm.perspectiveCorrected:
      case RecognitionAlgorithm.noiseReduction:
      case RecognitionAlgorithm.adaptiveThreshold:
      default:
        return TextRecognizer(script: TextRecognitionScript.latin);
    }
  }
  
  /// 处理识别结果
  static RecognitionResult _processRecognitionResult(
    RecognizedText recognizedText,
    RecognitionAlgorithm algorithm, {
    String? presetProductCode,
    String? presetBatchNumber,
  }) {
    // 这里可以根据不同算法应用不同的后处理逻辑
    final isSuccessful = recognizedText.text.isNotEmpty;
    
    return RecognitionResult(
      ocrText: recognizedText.text,
      isQrOcrConsistent: true,
      matchesPreset: _checkPresetMatch(recognizedText.text, presetProductCode, presetBatchNumber),
      recognitionTime: DateTime.now(),
      status: isSuccessful ? RecognitionStatus.completed : RecognitionStatus.failed,
      metadata: {
        'algorithm': algorithm.name,
        'mlkitVersion': _version,
        'blocksCount': recognizedText.blocks.length,
      },
    );
  }
  
  /// 检查预设匹配
  static bool _checkPresetMatch(String text, String? productCode, String? batchNumber) {
    if (productCode == null && batchNumber == null) return true;
    
    final normalizedText = text.toUpperCase().replaceAll(RegExp(r'\s+'), '');
    
    bool productMatch = productCode == null || normalizedText.contains(productCode.toUpperCase());
    bool batchMatch = batchNumber == null || normalizedText.contains(batchNumber.toUpperCase());
    
    return productMatch && batchMatch;
  }
  
  // 图像质量分析辅助方法
  static double _calculateBrightness(img.Image image) {
    int totalBrightness = 0;
    int pixelCount = 0;
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        totalBrightness += img.getLuminance(pixel).round();
        pixelCount++;
      }
    }
    
    return pixelCount > 0 ? totalBrightness / (pixelCount * 255.0) : 0.0;
  }
  
  static double _calculateContrast(img.Image image) {
    // 简化的对比度计算
    final brightness = _calculateBrightness(image);
    double variance = 0.0;
    int pixelCount = 0;
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final pixelBrightness = img.getLuminance(pixel) / 255.0;
        variance += (pixelBrightness - brightness) * (pixelBrightness - brightness);
        pixelCount++;
      }
    }
    
    return pixelCount > 0 ? variance / pixelCount : 0.0;
  }
  
  static double _calculateSharpness(img.Image image) {
    // 使用Laplacian算子计算锐度
    double sharpness = 0.0;
    int count = 0;
    
    for (int y = 1; y < image.height - 1; y++) {
      for (int x = 1; x < image.width - 1; x++) {
        final center = img.getLuminance(image.getPixel(x, y));
        final top = img.getLuminance(image.getPixel(x, y - 1));
        final bottom = img.getLuminance(image.getPixel(x, y + 1));
        final left = img.getLuminance(image.getPixel(x - 1, y));
        final right = img.getLuminance(image.getPixel(x + 1, y));
        
        final laplacian = (4 * center - top - bottom - left - right).abs();
        sharpness += laplacian;
        count++;
      }
    }
    
    return count > 0 ? (sharpness / count) / 255.0 : 0.0;
  }
  
  static double _calculateNoiseLevel(img.Image image) {
    // 简化的噪声估计
    return 0.3; // 占位实现
  }
  
  static double _estimateTextDensity(img.Image image) {
    // 简化的文本密度估计
    return 0.5; // 占位实现
  }
  
  static bool _detectRotation(img.Image image) {
    // 简化的旋转检测
    return false; // 占位实现
  }
  
  static bool _detectPerspectiveDistortion(img.Image image) {
    // 简化的透视畸变检测
    return false; // 占位实现
  }
  
  static double _calculateOverallQuality(
    double brightness, double contrast, double sharpness, double noiseLevel, double textDensity,
  ) {
    // 加权计算综合质量分数
    return (brightness * 0.2 + contrast * 0.3 + sharpness * 0.3 + (1 - noiseLevel) * 0.2).clamp(0.0, 1.0);
  }
}

// ✅ 算法枚举已移至统一定义文件：lib/models/recognition_algorithm.dart

/// 图像质量指标
class ImageQualityMetrics {
  final double brightness;
  final double contrast;
  final double sharpness;
  final double noiseLevel;
  final double textDensity;
  final double overallQuality;
  final bool hasRotation;
  final bool hasPerspectiveDistortion;
  final bool hasBlur;
  final bool hasLowContrast;
  final Size imageSize;

  const ImageQualityMetrics({
    required this.brightness,
    required this.contrast,
    required this.sharpness,
    required this.noiseLevel,
    required this.textDensity,
    required this.overallQuality,
    required this.hasRotation,
    required this.hasPerspectiveDistortion,
    required this.hasBlur,
    required this.hasLowContrast,
    required this.imageSize,
  });

  bool get needsPreprocessing =>
      hasBlur || hasLowContrast || hasPerspectiveDistortion || brightness < 0.3 || noiseLevel > 0.6;

  String get qualityLevel {
    if (overallQuality >= 0.8) return '优秀';
    if (overallQuality >= 0.6) return '良好';
    if (overallQuality >= 0.4) return '一般';
    return '较差';
  }

  factory ImageQualityMetrics.defaultMetrics() {
    return const ImageQualityMetrics(
      brightness: 0.5,
      contrast: 0.5,
      sharpness: 0.5,
      noiseLevel: 0.3,
      textDensity: 0.5,
      overallQuality: 0.5,
      hasRotation: false,
      hasPerspectiveDistortion: false,
      hasBlur: false,
      hasLowContrast: false,
      imageSize: Size(1000, 1000),
    );
  }
}

/// 算法选择结果
class AlgorithmSelectionResult {
  final RecognitionAlgorithm primary;
  final List<RecognitionAlgorithm> fallback;
  final double confidence;

  const AlgorithmSelectionResult({
    required this.primary,
    required this.fallback,
    required this.confidence,
  });
}

/// 算法选择
class AlgorithmSelection {
  final RecognitionAlgorithm primary;
  final List<RecognitionAlgorithm> fallback;
  final ImageQualityMetrics qualityMetrics;
  final List<String> optimizationSuggestions;
  final double confidence;

  const AlgorithmSelection({
    required this.primary,
    required this.fallback,
    required this.qualityMetrics,
    required this.optimizationSuggestions,
    required this.confidence,
  });
}

/// 优化识别结果
class OptimizedRecognitionResult {
  final RecognitionResult recognitionResult;
  final RecognitionAlgorithm algorithmUsed;
  final ImageQualityMetrics qualityMetrics;
  final Duration processingTime;
  final bool optimizationApplied;
  final bool fallbackUsed;

  const OptimizedRecognitionResult({
    required this.recognitionResult,
    required this.algorithmUsed,
    required this.qualityMetrics,
    required this.processingTime,
    required this.optimizationApplied,
    required this.fallbackUsed,
  });

  bool get isSuccessful => recognitionResult.status == RecognitionStatus.completed;

  Map<String, dynamic> toAnalyticsData() {
    return {
      'algorithm': algorithmUsed.name,
      'qualityLevel': qualityMetrics.qualityLevel,
      'processingTimeMs': processingTime.inMilliseconds,
      'optimizationApplied': optimizationApplied,
      'fallbackUsed': fallbackUsed,
      'successful': isSuccessful,
      'confidence': qualityMetrics.overallQuality,
    };
  }
}
