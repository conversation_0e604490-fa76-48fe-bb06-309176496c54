pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        // 国内镜像源 - 优先使用，增加更多镜像保证可用性
        maven { 
            url 'https://maven.aliyun.com/repository/public'
            // 设置连接和读取超时
            metadataSources {
                mavenPom()
                artifact()
            }
        }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        
        // 华为云镜像源
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        
        // 腾讯云镜像源
        maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
        
        // 清华大学镜像源
        maven { url 'https://mirrors.tuna.tsinghua.edu.cn/maven/' }

        // 官方源作为备用
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.5.0" apply false
    id "org.jetbrains.kotlin.android" version "1.9.24" apply false
}

include ":app"

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)
    repositories {
        // 国内镜像源 - 优先使用，增加更多镜像保证可用性
        maven { 
            url 'https://maven.aliyun.com/repository/public'
            // 设置连接和读取超时
            metadataSources {
                mavenPom()
                artifact()
            }
        }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        
        // 华为云镜像源
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        
        // 腾讯云镜像源
        maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
        
        // 清华大学镜像源
        maven { url 'https://mirrors.tuna.tsinghua.edu.cn/maven/' }

        // 官方源作为备用
        google()
        mavenCentral()
        maven { url 'https://storage.googleapis.com/download.flutter.io' }
    }
}

rootProject.name = "loadguard"
