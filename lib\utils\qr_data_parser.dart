import 'dart:convert';
import 'package:loadguard/utils/app_logger.dart';

/// 🧠 【智能二维码数据解析器】
/// 
/// 【核心功能】：
/// 1. 支持5种数据格式的智能解析
/// 2. 自动格式检测和策略选择
/// 3. 完整的数据验证和错误处理
/// 4. 丰富的产品信息提取
/// 
/// 【支持格式】：
/// - JSON格式：{"code":"LLD-7042","batch":"250712F20440-2C"}
/// - 键值对格式：CODE:LLD-7042;BATCH:250712F20440-2C
/// - 分隔符格式：LLD-7042|250712F20440-2C|2025-07-13
/// - 固定位置格式：LLD7042250712F204402C20250713183320
/// - URL格式：https://example.com?code=LLD-7042&batch=250712F20440-2C

/// 二维码数据格式枚举
enum QRDataFormat {
  json('JSON格式'),
  keyValue('键值对格式'),
  delimited('分隔符格式'),
  fixedPosition('固定位置格式'),
  url('URL格式'),
  raw('原始数据');

  const QRDataFormat(this.description);
  final String description;
}

/// 二维码数据结构
class QRCodeData {
  final QRDataFormat format;
  final String? productCode;
  final String? batchNumber;
  final String? productionDate;
  final String? productionTime;
  final String? productionLine;
  final String? factoryCode;
  final String? qualityStatus;
  final String? operatorId;
  final String? equipmentId;
  final String? url;
  final Map<String, String>? additionalData;
  final String rawData;

  QRCodeData({
    required this.format,
    this.productCode,
    this.batchNumber,
    this.productionDate,
    this.productionTime,
    this.productionLine,
    this.factoryCode,
    this.qualityStatus,
    this.operatorId,
    this.equipmentId,
    this.url,
    this.additionalData,
    required this.rawData,
  });

  /// 创建原始数据对象
  factory QRCodeData.raw(String rawData) {
    return QRCodeData(
      format: QRDataFormat.raw,
      rawData: rawData,
    );
  }

  /// 数据是否有效
  bool get isValid {
    return productCode != null || batchNumber != null || url != null;
  }

  /// 数据摘要
  String get summary {
    final parts = <String>[];
    if (productCode != null) parts.add('产品:$productCode');
    if (batchNumber != null) parts.add('批次:$batchNumber');
    if (productionDate != null) parts.add('日期:$productionDate');
    return parts.join(' | ');
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'format': format.name,
      'productCode': productCode,
      'batchNumber': batchNumber,
      'productionDate': productionDate,
      'productionTime': productionTime,
      'productionLine': productionLine,
      'factoryCode': factoryCode,
      'qualityStatus': qualityStatus,
      'operatorId': operatorId,
      'equipmentId': equipmentId,
      'url': url,
      'additionalData': additionalData,
      'rawData': rawData,
    };
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final List<String> issues;
  final double confidence;

  ValidationResult({
    required this.isValid,
    required this.issues,
    required this.confidence,
  });

  factory ValidationResult.success({double confidence = 1.0}) {
    return ValidationResult(
      isValid: true,
      issues: [],
      confidence: confidence,
    );
  }

  factory ValidationResult.error(String issue) {
    return ValidationResult(
      isValid: false,
      issues: [issue],
      confidence: 0.0,
    );
  }
}

/// 🧠 智能二维码数据解析器
class IntelligentQRDataParser {
  
  /// 主解析方法
  static QRCodeData parseQRData(String rawData) {
    AppLogger.debug('🧠 开始解析二维码数据: ${rawData.substring(0, rawData.length.clamp(0, 50))}...');
    
    // 1. 尝试不同的解析策略
    final strategies = [
      _parseAsJSON,
      _parseAsKeyValue,
      _parseAsDelimited,
      _parseAsURL,
      _parseAsFixedPosition,
    ];
    
    for (final strategy in strategies) {
      try {
        final result = strategy(rawData);
        if (result.isValid) {
          AppLogger.info('✅ 解析成功，格式: ${result.format.description}');
          return result;
        }
      } catch (e) {
        AppLogger.debug('⚠️ 解析策略失败: $e');
        // 继续尝试下一个策略
      }
    }
    
    AppLogger.warning('⚠️ 所有解析策略都失败，返回原始数据');
    // 如果所有策略都失败，返回原始数据
    return QRCodeData.raw(rawData);
  }
  
  /// JSON格式解析
  static QRCodeData _parseAsJSON(String data) {
    final json = jsonDecode(data) as Map<String, dynamic>;
    
    return QRCodeData(
      format: QRDataFormat.json,
      productCode: json['code'] ?? json['product_code'] ?? json['productCode'],
      batchNumber: json['batch'] ?? json['batch_number'] ?? json['batchNumber'],
      productionDate: _parseDate(json['date'] ?? json['production_date'] ?? json['productionDate']),
      productionTime: _parseTime(json['time'] ?? json['production_time'] ?? json['productionTime']),
      productionLine: json['line'] ?? json['production_line'] ?? json['productionLine'],
      factoryCode: json['factory'] ?? json['factory_code'] ?? json['factoryCode'],
      qualityStatus: json['quality'] ?? json['quality_status'] ?? json['qualityStatus'],
      operatorId: json['operator'] ?? json['operator_id'] ?? json['operatorId'],
      equipmentId: json['equipment'] ?? json['equipment_id'] ?? json['equipmentId'],
      additionalData: Map<String, String>.from(json.map((k, v) => MapEntry(k, v.toString()))),
      rawData: data,
    );
  }
  
  /// 键值对格式解析
  static QRCodeData _parseAsKeyValue(String data) {
    final pairs = data.split(RegExp(r'[;&,\n]'));
    final dataMap = <String, String>{};
    
    for (final pair in pairs) {
      final parts = pair.split(RegExp(r'[:=]'));
      if (parts.length == 2) {
        final key = parts[0].trim().toLowerCase();
        final value = parts[1].trim();
        if (key.isNotEmpty && value.isNotEmpty) {
          dataMap[key] = value;
        }
      }
    }
    
    if (dataMap.isEmpty) {
      throw FormatException('无法解析键值对格式');
    }
    
    return QRCodeData(
      format: QRDataFormat.keyValue,
      productCode: dataMap['code'] ?? dataMap['product'] ?? dataMap['productcode'],
      batchNumber: dataMap['batch'] ?? dataMap['lot'] ?? dataMap['batchnumber'],
      productionDate: _parseDate(dataMap['date'] ?? dataMap['productiondate']),
      productionTime: _parseTime(dataMap['time'] ?? dataMap['productiontime']),
      productionLine: dataMap['line'] ?? dataMap['productionline'],
      factoryCode: dataMap['factory'] ?? dataMap['factorycode'],
      qualityStatus: dataMap['quality'] ?? dataMap['qualitystatus'],
      operatorId: dataMap['operator'] ?? dataMap['operatorid'],
      equipmentId: dataMap['equipment'] ?? dataMap['equipmentid'],
      additionalData: dataMap,
      rawData: data,
    );
  }
  
  /// 分隔符格式解析
  static QRCodeData _parseAsDelimited(String data) {
    // 尝试不同的分隔符
    final delimiters = ['|', ',', ';', '\t', ' '];
    
    for (final delimiter in delimiters) {
      if (data.contains(delimiter)) {
        final parts = data.split(delimiter);
        if (parts.length >= 2) {
          return QRCodeData(
            format: QRDataFormat.delimited,
            productCode: parts.isNotEmpty ? parts[0].trim() : null,
            batchNumber: parts.length > 1 ? parts[1].trim() : null,
            productionDate: parts.length > 2 ? _parseDate(parts[2].trim()) : null,
            productionTime: parts.length > 3 ? _parseTime(parts[3].trim()) : null,
            productionLine: parts.length > 4 ? parts[4].trim() : null,
            rawData: data,
          );
        }
      }
    }
    
    throw FormatException('无法识别分隔符格式');
  }
  
  /// URL格式解析（支持多种URL格式）
  static QRCodeData _parseAsURL(String data) {
    String urlToProcess = data.trim();

    // 1. 检查是否为域名格式（如：qiyulongoc.com.cn）
    if (!urlToProcess.startsWith('http') && _isDomainFormat(urlToProcess)) {
      // 为域名格式添加协议前缀
      urlToProcess = 'https://$urlToProcess';
      AppLogger.info('🌐 检测到域名格式，自动添加协议: $urlToProcess');
    }

    // 2. 验证是否为有效URL
    if (!urlToProcess.startsWith('http')) {
      throw FormatException('不是有效的URL格式');
    }

    try {
      final uri = Uri.parse(urlToProcess);
      final params = uri.queryParameters;

      // 3. 特殊处理：如果是纯域名（无查询参数），尝试从路径中提取信息
      Map<String, String> extractedData = {};
      if (params.isEmpty && uri.pathSegments.isNotEmpty) {
        extractedData = _extractFromURLPath(uri);
      } else {
        extractedData = Map<String, String>.from(params);
      }

      return QRCodeData(
        format: QRDataFormat.url,
        productCode: extractedData['code'] ?? extractedData['product'] ?? extractedData['productCode'],
        batchNumber: extractedData['batch'] ?? extractedData['lot'] ?? extractedData['batchNumber'],
        productionDate: _parseDate(extractedData['date'] ?? extractedData['productionDate']),
        productionTime: _parseTime(extractedData['time'] ?? extractedData['productionTime']),
        productionLine: extractedData['line'] ?? extractedData['productionLine'],
        factoryCode: extractedData['factory'] ?? extractedData['factoryCode'],
        url: urlToProcess,
        additionalData: extractedData,
        rawData: data,
      );
    } catch (e) {
      AppLogger.warning('URL解析失败: $e');
      throw FormatException('URL解析失败: $e');
    }
  }

  /// 判断是否为域名格式
  static bool _isDomainFormat(String data) {
    // 检查是否包含域名特征
    final domainPattern = RegExp(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$');
    return domainPattern.hasMatch(data);
  }

  /// 从URL路径中提取信息
  static Map<String, String> _extractFromURLPath(Uri uri) {
    final extractedData = <String, String>{};

    // 尝试从路径段中提取产品信息
    for (final segment in uri.pathSegments) {
      // 检查是否为产品代码格式
      if (RegExp(r'^[A-Z]{2,4}-\d{4}$').hasMatch(segment)) {
        extractedData['productCode'] = segment;
      }
      // 检查是否为批次号格式
      else if (RegExp(r'^\d{6}F\d{5}').hasMatch(segment)) {
        extractedData['batchNumber'] = segment;
      }
      // 检查是否为日期格式
      else if (RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(segment)) {
        extractedData['date'] = segment;
      }
    }

    // 如果没有从路径提取到信息，记录域名用于后续网络验证
    if (extractedData.isEmpty) {
      extractedData['domain'] = uri.host;
      extractedData['needsNetworkValidation'] = 'true';
    }

    return extractedData;
  }
  
  /// 固定位置格式解析
  static QRCodeData _parseAsFixedPosition(String data) {
    // 基于常见的工业标签格式
    if (data.length < 20) {
      throw FormatException('数据长度不符合固定位置格式');
    }
    
    return QRCodeData(
      format: QRDataFormat.fixedPosition,
      productCode: data.substring(0, 7).trim(),
      batchNumber: _formatBatchNumber(data.substring(7, 23)),
      productionDate: _parseDate(data.substring(23, 31)),
      productionTime: _parseTime(data.substring(31, 37)),
      rawData: data,
    );
  }
  
  /// 解析日期
  static String? _parseDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return null;
    
    try {
      // 尝试不同的日期格式
      final formats = [
        RegExp(r'^\d{4}-\d{2}-\d{2}$'),     // 2025-07-13
        RegExp(r'^\d{4}/\d{2}/\d{2}$'),     // 2025/07/13
        RegExp(r'^\d{8}$'),                 // 20250713
        RegExp(r'^\d{2}-\d{2}-\d{4}$'),     // 13-07-2025
      ];
      
      for (final format in formats) {
        if (format.hasMatch(dateStr)) {
          return dateStr;
        }
      }
    } catch (e) {
      AppLogger.warning('日期解析失败: $dateStr');
    }
    
    return null;
  }
  
  /// 解析时间
  static String? _parseTime(String? timeStr) {
    if (timeStr == null || timeStr.isEmpty) return null;
    
    try {
      // 尝试不同的时间格式
      final formats = [
        RegExp(r'^\d{2}:\d{2}:\d{2}$'),     // 18:33:20
        RegExp(r'^\d{2}:\d{2}$'),           // 18:33
        RegExp(r'^\d{6}$'),                 // 183320
      ];
      
      for (final format in formats) {
        if (format.hasMatch(timeStr)) {
          return timeStr;
        }
      }
    } catch (e) {
      AppLogger.warning('时间解析失败: $timeStr');
    }
    
    return null;
  }
  
  /// 格式化批次号
  static String _formatBatchNumber(String batchStr) {
    // 将连续的批次号格式化为标准格式
    if (batchStr.length >= 16) {
      final part1 = batchStr.substring(0, 6);   // 250712
      final part2 = batchStr.substring(6, 7);   // F
      final part3 = batchStr.substring(7, 12);  // 20440
      final part4 = batchStr.substring(12);     // 剩余部分
      return '$part1$part2$part3-$part4';
    }
    return batchStr;
  }
}

/// 数据验证器
class QRDataValidator {
  static ValidationResult validate(QRCodeData data) {
    final issues = <String>[];
    
    // 1. 基础字段验证
    if (data.productCode == null || data.productCode!.isEmpty) {
      issues.add('缺少产品代码');
    } else if (!_isValidProductCode(data.productCode!)) {
      issues.add('产品代码格式无效');
    }
    
    if (data.batchNumber == null || data.batchNumber!.isEmpty) {
      issues.add('缺少批次号');
    } else if (!_isValidBatchNumber(data.batchNumber!)) {
      issues.add('批次号格式无效');
    }
    
    // 2. 日期时间验证
    if (data.productionDate != null && !_isValidDate(data.productionDate!)) {
      issues.add('生产日期格式无效');
    }
    
    if (data.productionTime != null && !_isValidTime(data.productionTime!)) {
      issues.add('生产时间格式无效');
    }
    
    // 3. 业务逻辑验证
    if (data.productionDate != null) {
      try {
        final prodDate = DateTime.parse(data.productionDate!);
        if (prodDate.isAfter(DateTime.now())) {
          issues.add('生产日期不能是未来时间');
        }
      } catch (e) {
        issues.add('生产日期解析失败');
      }
    }
    
    return ValidationResult(
      isValid: issues.isEmpty,
      issues: issues,
      confidence: _calculateConfidence(data, issues),
    );
  }
  
  static bool _isValidProductCode(String code) {
    return RegExp(r'^[A-Z]{2,4}-\d{4}$').hasMatch(code);
  }
  
  static bool _isValidBatchNumber(String batch) {
    return RegExp(r'^\d{6}F\d{5}(-\d+[A-Z])?$').hasMatch(batch);
  }
  
  static bool _isValidDate(String date) {
    return RegExp(r'^\d{4}-\d{2}-\d{2}$').hasMatch(date);
  }
  
  static bool _isValidTime(String time) {
    return RegExp(r'^\d{2}:\d{2}(:\d{2})?$').hasMatch(time);
  }
  
  static double _calculateConfidence(QRCodeData data, List<String> issues) {
    var confidence = 1.0;
    
    // 根据问题数量降低置信度
    confidence -= issues.length * 0.2;
    
    // 根据数据完整性调整置信度
    var completeness = 0.0;
    if (data.productCode != null) completeness += 0.4;
    if (data.batchNumber != null) completeness += 0.4;
    if (data.productionDate != null) completeness += 0.1;
    if (data.productionTime != null) completeness += 0.1;
    
    confidence *= completeness;
    
    return confidence.clamp(0.0, 1.0);
  }
}
