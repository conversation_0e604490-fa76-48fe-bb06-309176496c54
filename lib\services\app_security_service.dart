import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'enterprise_license_service.dart';

/// 🔐 企业级应用安全管理服务
/// 提供安全的本地化管理员激活机制、设备绑定、许可证验证等功能
class AppSecurityService {
  // 🔐 安全配置 - 从环境或配置文件加载
  static String? _masterPassword;
  static String? _secretKey;
  // 移除明文配置密钥，改为动态生成
  static String? _configEncryptionKey;

  // 🔐 移除复杂的激活序列，简化为直接激活码验证

  // 🔐 内置的超级管理员激活密钥（混淆编码，难以逆向）
  static const String _masterKey =
      'TG9hZEd1YXJkX01hc3Rlcl9LZXlfMjAyNF9QUk9fVkVSU0lPTg==';
  static const String _appSignature = 'LoadGuardProfessional2024';

  // 🔑 增强的密钥生成
  static Future<String> _getConfigEncryptionKey() async {
    if (_configEncryptionKey != null) return _configEncryptionKey!;

    final deviceId = await getDeviceId();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final keyData = 'LoadGuard_Config_${deviceId}_$timestamp';
    _configEncryptionKey =
        sha256.convert(utf8.encode(keyData)).toString().substring(0, 32);
    return _configEncryptionKey!;
  }

  // 🔐 简化的设备日期码生成（保留用于设备绑定验证）
  static String _generateDeviceDateCode(String deviceFingerprint) {
    final today = DateTime.now();
    final dateKey = '${today.year}${today.month.toString().padLeft(2, '0')}${today.day.toString().padLeft(2, '0')}';
    final combined = 'LoadGuard_${deviceFingerprint}_$dateKey';
    return sha256
        .convert(utf8.encode(combined))
        .toString()
        .substring(0, 12)
        .toUpperCase();
  }

  static const String _activationCodeKey = 'activation_code';
  static const String _deviceIdKey = 'device_id';
  static const String _firstInstallKey = 'first_install_time';
  static const String _lastValidationKey = 'last_validation_time';
  static const String _licenseTypeKey = 'license_type';
  static const String _activationTimeKey = 'activation_time';
  static const String _masterActivatedKey = 'master_activated';
  static const String _userRoleKey = 'user_role';
  static const String _authorizedDevicesKey = 'authorized_devices';
  static const String _activationHistoryKey = 'activation_history';
  static const String _appUsageCountKey = 'app_usage_count';

  /// 🔐 初始化安全服务 - 优化首次安装流程
  static Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();

    // 记录首次安装时间
    if (!prefs.containsKey(_firstInstallKey)) {
      await prefs.setInt(
          _firstInstallKey, DateTime.now().millisecondsSinceEpoch);

      // 🆕 首次安装自动激活7天试用期
      final trialResult = await activateTrial();
      if (trialResult['success'] == true) {
        print('首次安装：试用期激活成功');
      } else {
        print('首次安装：试用期激活失败 - ${trialResult['message']}');
      }
    }

    // 生成设备ID
    if (!prefs.containsKey(_deviceIdKey)) {
      final deviceId = await _generateDeviceId();
      await prefs.setString(_deviceIdKey, deviceId);
    }

    // 记录启动次数
    final usageCount = prefs.getInt(_appUsageCountKey) ?? 0;
    await prefs.setInt(_appUsageCountKey, usageCount + 1);

    // 🔐 强化许可证检查：确保过期用户无法绕过
    final licenseStatus = await checkLicenseStatus();
    final isValid = licenseStatus['isValid'] as bool;
    final needActivation = licenseStatus['needActivation'] as bool? ?? false;
    final role = licenseStatus['role'] as String;

    // 记录许可证状态用于调试
    print('许可证状态检查: isValid=$isValid, needActivation=$needActivation, role=$role');

    // 超级管理员永不过期
    if (role == 'superAdmin') {
      print('超级管理员账户，跳过过期检查');
      return;
    }

    // 其他用户过期后必须激活
    if (!isValid || needActivation) {
      print('许可证已过期或需要激活，将强制跳转到激活页面');
      // 设置强制激活标记
      await prefs.setBool('force_activation_required', true);
    } else {
      // 许可证有效，清除强制激活标记
      await prefs.remove('force_activation_required');
    }
  }

  /// 🆕 激活试用期（任何人都可以激活）- 重新设计
  static Future<Map<String, dynamic>> activateTrial() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userRole = await getUserRole();

      // 🔧 修复：检查是否已经激活过试用期
      final hasTrialActivated = prefs.getBool('trial_ever_activated') ?? false;
      if (hasTrialActivated) {
        return {
          'success': false,
          'message': '试用期只能激活一次',
          'canRetry': false,
        };
      }

      // 如果已经是激活用户或超级管理员，不能再激活试用
      if (userRole == UserRole.activated || userRole == UserRole.superAdmin) {
        return {
          'success': false,
          'message': '当前账户已激活，无需试用',
          'canRetry': false,
        };
      }

      // 激活7天试用期
      await _saveLicenseInfo(
        LicenseType.trial,
        7, // 7天试用期
        UserRole.trial,
      );

      // 🔧 修复：标记试用期已激活过
      await prefs.setBool('trial_ever_activated', true);
      await prefs.setInt('trial_activated_at', DateTime.now().millisecondsSinceEpoch);

      print('试用期激活成功：7天');
      return {
        'success': true,
        'message': '试用期激活成功，有效期7天',
        'canRetry': false,
        'shouldNavigateHome': true, // 🔧 修复：指示应该跳转到主页
      };
    } catch (e) {
      print('激活试用期失败: $e');
      return {
        'success': false,
        'message': '激活失败: $e',
        'canRetry': true,
      };
    }
  }

  /// 生成设备唯一标识
  static Future<String> _generateDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();
    String identifier = '';

    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        identifier =
            '${androidInfo.id}_${androidInfo.model}_${androidInfo.manufacturer}_${androidInfo.board}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        identifier =
            '${iosInfo.identifierForVendor}_${iosInfo.model}_${iosInfo.systemName}';
      }
    } catch (e) {
      // 如果获取设备信息失败，使用时间戳作为标识
      identifier = 'DEVICE_${DateTime.now().millisecondsSinceEpoch}';
    }

    return sha256
        .convert(utf8.encode((identifier ?? '') + (_secretKey ?? '')))
        .toString()
        .substring(0, 16)
        .toUpperCase();
  }

  /// 🔐 【超级管理员激活】简化验证机制
  static Future<bool> validateSuperAdminAccess(String input) async {
    // 方法1: 主激活码验证（最简单直接的方式）
    if (isMasterActivationCode(input)) {
      await _activateAdmin('master_activation');
      return true;
    }

    // 方法2: 设备绑定验证（防止激活码被盗用）
    final deviceFingerprint = await _getDeviceFingerprint();
    final deviceCode = _generateDeviceDateCode(deviceFingerprint);
    if (input.toUpperCase().replaceAll('-', '') == deviceCode) {
      await _activateAdmin('device_based');
      return true;
    }

    return false;
  }

  /// 🔑 生成设备指纹
  static Future<String> _getDeviceFingerprint() async {
    final deviceInfo = DeviceInfoPlugin();
    String fingerprint = '';

    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        fingerprint =
            '${androidInfo.id}_${androidInfo.model}_${androidInfo.brand}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        fingerprint = '${iosInfo.identifierForVendor}_${iosInfo.model}';
      }
    } catch (e) {
      fingerprint = 'UNKNOWN_DEVICE';
    }

    return sha256.convert(utf8.encode(fingerprint)).toString().substring(0, 16);
  }

  // 🔐 移除重复的方法定义，使用前面已定义的_generateDeviceDateCode

  // 🔐 移除复杂的紧急恢复序列验证，简化为直接验证

  // 🔐 移除复杂的配置文件密码机制，简化为内置验证

  /// 🔑 生成设备特定激活码（新增安全功能）
  static Future<String> _generateDeviceSpecificCode() async {
    final deviceId = await getDeviceId();
    final deviceInfo = DeviceInfoPlugin();
    String deviceSignature = '';

    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceSignature =
            '${androidInfo.id}_${androidInfo.model}_${androidInfo.manufacturer}_${androidInfo.board}_${androidInfo.brand}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceSignature =
            '${iosInfo.identifierForVendor}_${iosInfo.model}_${iosInfo.systemName}_${iosInfo.systemVersion}';
      }
    } catch (e) {
      deviceSignature = deviceId;
    }

    final secretKey = await _getOrGenerateSecretKey();
    final combined = '${deviceSignature}_$secretKey';
    return sha256
        .convert(utf8.encode(combined))
        .toString()
        .substring(0, 16)
        .toUpperCase();
  }

  /// 👑 激活超级管理员
  static Future<void> _activateAdmin(String method) async {
    await _saveLicenseInfo(LicenseType.permanent, -1, UserRole.superAdmin);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_masterActivatedKey, true);
    await prefs.setString('activation_method', method);
    await prefs.setInt(
        'admin_activated_time', DateTime.now().millisecondsSinceEpoch);
  }

  /// 🕐 生成带期限的激活码（仅管理员可用）
  static Future<String> generateTimedActivationCode({
    required int days,
    String? customLabel,
  }) async {
    // 检查是否为超级管理员
    final userRole = await getUserRole();
    if (userRole != UserRole.superAdmin) {
      throw Exception('仅超级管理员可生成激活码');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // 加密期限信息到激活码中
    final expirationData = {
      'days': days,
      'generated': timestamp,
      'label': customLabel ?? '${days}天授权',
    };

    final expirationJson = jsonEncode(expirationData);
    final encryptedExpiration = base64.encode(utf8.encode(expirationJson));

    // 生成激活码：包含期限信息的哈希
    final seed = '${_masterKey}_${timestamp}_${days}_$encryptedExpiration';
    final hash = sha256.convert(utf8.encode(seed)).toString();

    // 格式化激活码：前16位哈希 + 加密的期限信息
    final codePrefix = hash.substring(0, 16).toUpperCase();
    final shortExpiration =
        encryptedExpiration.replaceAll('=', '').substring(0, 8);

    return '$codePrefix-$shortExpiration-${days}D';
  }

  /// 🕐 验证带期限的激活码
  static Future<bool> _validateTimedActivationCode(
      String activationCode) async {
    try {
      // 解析激活码格式：HASH-EXPDATA-DAYSD
      final parts = activationCode.split('-');
      if (parts.length < 3 || !parts.last.endsWith('D')) {
        return false;
      }

      final daysStr = parts.last.replaceAll('D', '');
      final days = int.tryParse(daysStr);
      if (days == null) return false;

      // 激活用户，设置对应期限
      await _saveLicenseInfo(
        days == 30
            ? LicenseType.monthly
            : days == 90
                ? LicenseType.quarterly
                : days == 365
                    ? LicenseType.annual
                    : LicenseType.trial, // 其他天数使用试用类型但设置自定义天数
        days,
        UserRole.activated,
      );

      return true;
    } catch (e) {
      print('验证带期限激活码失败: $e');
      return false;
    }
  }

  /// 【管理员功能】生成设备激活码 (需要超级管理员权限)
  /// [deviceId] 目标设备ID
  /// [licenseType] 许可证类型
  /// [role] 指定用户角色，默认为普通用户
  static Future<String> generateDeviceActivationCode(
      String deviceId, LicenseType licenseType,
      {UserRole role = UserRole.activated}) async {
    // 验证当前设备是否有超级管理员权限
    final currentRole = await getUserRole();
    if (currentRole != UserRole.superAdmin) {
      throw Exception('需要超级管理员权限才能生成激活码');
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final roleStr = role.toString().split('.').last; // 将枚举转换为字符串
    final secretKey = await _getOrGenerateSecretKey();
    final data = '$deviceId:${licenseType.name}:$timestamp:$roleStr:$secretKey';
    final hash = sha256.convert(utf8.encode(data)).toString();

    // 记录授权历史
    _recordActivationGeneration(deviceId, licenseType, role);

    // 格式化激活码：XXXX-XXXX-XXXX-XXXX
    final code = hash.substring(0, 16).toUpperCase();
    return '${code.substring(0, 4)}-${code.substring(4, 8)}-${code.substring(8, 12)}-${code.substring(12, 16)}';
  }

  /// 记录激活码生成历史
  static Future<void> _recordActivationGeneration(
      String deviceId, LicenseType licenseType, UserRole role) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> history = prefs.getStringList(_activationHistoryKey) ?? [];

      final timestamp = DateTime.now().toIso8601String();
      final record =
          '$timestamp|$deviceId|${licenseType.name}|${role.toString().split('.').last}';

      history.add(record);
      // 保留最近100条记录
      if (history.length > 100) {
        history = history.sublist(history.length - 100);
      }

      await prefs.setStringList(_activationHistoryKey, history);
    } catch (e) {
      print('记录激活历史出错: $e');
    }
  }

  /// 【管理员功能】批量生成激活码（用于批量部署）
  static Future<Map<String, String>> generateBatchActivationCodes(
      List<String> deviceIds, LicenseType licenseType,
      {UserRole role = UserRole.activated}) async {
    // 验证当前设备是否有超级管理员权限
    final currentRole = await getUserRole();
    if (currentRole != UserRole.superAdmin) {
      throw Exception('需要超级管理员权限才能批量生成激活码');
    }

    Map<String, String> codes = {};
    for (String deviceId in deviceIds) {
      codes[deviceId] =
          await generateDeviceActivationCode(deviceId, licenseType, role: role);
    }
    return codes;
  }

  /// 🔐 验证激活码 - 修复安全机制
  static Future<bool> validateActivationCode(String activationCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final deviceId = prefs.getString(_deviceIdKey) ?? '';

      // 1. 🔑 检查是否为主激活码（管理员激活）
      if (isMasterActivationCode(activationCode)) {
        // 单机应用：任何设备使用主激活码都可以成为管理员
        final currentDevice = await getDeviceId();
        await prefs.setString('master_device_id', currentDevice);
        await _activateAdmin('master_activation');
        return true;
      }

      // 1.5 🕐 解析带期限的激活码
      if (activationCode.contains('-') && activationCode.endsWith('D')) {
        return await _validateTimedActivationCode(activationCode);
      }

      // 2. 🔐 简化验证：移除复杂的紧急恢复序列

      // 3. 👤 验证管理员生成的用户激活码
      if (await _validateGeneratedActivationCode(activationCode)) {
        // 激活为普通激活用户，根据激活码中的期限设置
        await _activateAsRegularUser(activationCode);
        return true;
      }

      return false;
    } catch (e) {
      throw Exception('激活失败: $e');
    }
  }

  /// 🔐 激活为普通用户
  static Future<void> _activateAsRegularUser(String activationCode) async {
    // 从激活码记录中获取期限信息
    final prefs = await SharedPreferences.getInstance();
    final generatedCodes =
        prefs.getStringList('generated_activation_codes') ?? [];

    final cleanCode =
        activationCode.replaceAll('-', '').replaceAll(' ', '').toUpperCase();
    final coreCode = cleanCode.replaceAll('USER', '');

    for (final record in generatedCodes) {
      final parts = record.split('|');
      if (parts.length >= 6) {
        final recordCode = parts[2];
        final daysValid = int.tryParse(parts[3]) ?? 365;

        if (recordCode.toUpperCase() == coreCode) {
          await _saveLicenseInfo(
              LicenseType.permanent, daysValid, UserRole.activated);
          return;
        }
      }
    }

    // 默认1年期限
    await _saveLicenseInfo(LicenseType.permanent, 365, UserRole.activated);
  }

  /// 验证管理员授权的激活码
  static Future<bool> _validateAuthorizedCode(
      String activationCode, String deviceId) async {
    // 该方法可以被其他管理员设备调用，以验证该管理员授权的激活码
    final prefs = await SharedPreferences.getInstance();
    final authorizedCodes = prefs.getStringList('auth_codes_$deviceId') ?? [];

    // 如果该设备ID有匹配的已授权激活码
    for (final authCode in authorizedCodes) {
      final parts = authCode.split('|');
      if (parts.length >= 4) {
        final code = parts[0];
        final licenseTypeStr = parts[1];
        final expiryStr = parts[2];
        final roleStr = parts.length > 3 ? parts[3] : 'user';

        if (code == activationCode.replaceAll('-', '').toLowerCase()) {
          // 验证过期时间
          final expiry = int.tryParse(expiryStr) ?? 0;
          if (expiry == -1 || DateTime.now().millisecondsSinceEpoch < expiry) {
            // 查找对应的许可证类型
            final licenseType = LicenseType.values.firstWhere(
                (lt) => lt.name == licenseTypeStr,
                orElse: () => LicenseType.monthly);

            // 查找对应的用户角色
            final role = UserRole.values.firstWhere(
                (r) => r.toString().split('.').last == roleStr,
                orElse: () => UserRole.trial);

            // 保存许可证信息
            if (expiry == -1) {
              await _saveLicenseInfo(licenseType, -1, role);
            } else {
              final daysLeft =
                  (expiry - DateTime.now().millisecondsSinceEpoch) ~/
                      (24 * 60 * 60 * 1000);
              await _saveLicenseInfo(
                  licenseType, daysLeft > 0 ? daysLeft : 1, role);
            }
            return true;
          }
        }
      }
    }
    return false;
  }

  /// 🔐 管理员为其他设备生成激活码 - 增强本地化算法
  static Future<String> generateActivationCodeForDevice(
    String deviceLabel, {
    int daysValid = 365, // 默认1年有效期
  }) async {
    try {
      final currentRole = await getUserRole();
      // 检查当前设备是否有超级管理员权限
      if (currentRole != UserRole.superAdmin) {
        throw Exception('需要超级管理员权限才能为其他设备生成激活码');
      }

      // 生成本地化激活码（完全本地算法）
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final expiryTime = timestamp + (daysValid * 24 * 60 * 60 * 1000);
      final deviceId = await getDeviceId();

      // 🔐 增强的本地化激活码生成算法
      final decodedKey = utf8.decode(base64.decode(_masterKey));

      // 多因子组合生成种子
      final deviceFactor = deviceId.substring(0, 8); // 管理员设备因子
      final timeFactor = (timestamp ~/ 1000).toString(); // 时间因子
      final daysFactor = daysValid.toString().padLeft(4, '0'); // 天数因子
      final labelFactor = _generateLabelHash(deviceLabel); // 设备标签因子

      // 组合种子
      final combinedSeed =
          '${decodedKey}_${_appSignature}_${deviceFactor}_${timeFactor}_${daysFactor}_$labelFactor';

      // 生成主哈希
      final primaryHash = sha256.convert(utf8.encode(combinedSeed)).toString();

      // 生成校验码
      final checksum = _generateChecksumForCode(primaryHash, daysValid);

      // 提取激活码核心部分（20位）
      final coreCode = primaryHash.substring(0, 20).toUpperCase();

      // 保存激活码记录（本地存储）
      await _storeActivationRecord(
          timestamp, deviceLabel, coreCode, daysValid, expiryTime);

      // 格式化为便利的激活码: USER-XXXXX-XXXXX-XXXXX-XXXXX
      final formattedCode =
          'USER-${coreCode.substring(0, 5)}-${coreCode.substring(5, 10)}-${coreCode.substring(10, 15)}-${coreCode.substring(15, 20)}';

      return formattedCode;
    } catch (e) {
      throw Exception('生成激活码失败: $e');
    }
  }

  /// 生成设备标签哈希因子
  static String _generateLabelHash(String deviceLabel) {
    final labelBytes =
        utf8.encode(deviceLabel.toLowerCase().replaceAll(' ', ''));
    final labelHash = sha256.convert(labelBytes).toString();
    return labelHash.substring(0, 8);
  }

  /// 为激活码生成校验码
  static String _generateChecksumForCode(String codeHash, int daysValid) {
    final checksumData = '$codeHash:$daysValid:$_appSignature';
    final checksum = sha256.convert(utf8.encode(checksumData)).toString();
    return checksum.substring(0, 4).toUpperCase();
  }

  /// 本地存储激活记录
  static Future<void> _storeActivationRecord(
    int timestamp,
    String deviceLabel,
    String coreCode,
    int daysValid,
    int expiryTime,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> generatedCodes =
        prefs.getStringList('generated_activation_codes') ?? [];

    // 记录格式: 时间戳|设备标签|激活码|有效期天数|过期时间|生成设备
    final currentDeviceId = await getDeviceId();
    final record =
        '$timestamp|$deviceLabel|$coreCode|$daysValid|$expiryTime|${currentDeviceId.substring(0, 8)}';
    generatedCodes.add(record);

    // 保持最近100条记录
    if (generatedCodes.length > 100) {
      generatedCodes = generatedCodes.sublist(generatedCodes.length - 100);
    }

    await prefs.setStringList('generated_activation_codes', generatedCodes);
  }

  /// 🔐 验证管理员生成的激活码 - 增强本地验证算法
  static Future<bool> _validateGeneratedActivationCode(String inputCode) async {
    try {
      // 清理输入的激活码
      final cleanCode =
          inputCode.replaceAll('-', '').replaceAll(' ', '').toUpperCase();

      // 检查是否为USER格式的激活码
      if (!inputCode.toUpperCase().startsWith('USER-')) {
        return false;
      }

      // 提取激活码核心部分（20位）
      final coreCode = cleanCode.replaceAll('USER', '');
      if (coreCode.length != 20) {
        return false;
      }

      // 从本地存储的激活码记录中查找并验证
      final prefs = await SharedPreferences.getInstance();
      final generatedCodes =
          prefs.getStringList('generated_activation_codes') ?? [];

      for (final record in generatedCodes) {
        final parts = record.split('|');
        if (parts.length >= 6) {
          final timestamp = int.tryParse(parts[0]) ?? 0;
          final deviceLabel = parts[1];
          final recordCode = parts[2];
          final daysValid = int.tryParse(parts[3]) ?? 0;
          final expiryTime = int.tryParse(parts[4]) ?? 0;
          final generatorDevice = parts[5];

          // 检查激活码是否匹配
          if (recordCode.toUpperCase() == coreCode) {
            // 检查是否过期
            if (DateTime.now().millisecondsSinceEpoch < expiryTime) {
              // 🔐 额外验证：重新生成激活码验证算法一致性
              if (await _verifyActivationCodeIntegrity(
                  timestamp, deviceLabel, daysValid, recordCode)) {
                return true;
              }
            }
          }
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// 🔐 验证激活码完整性（防篡改）
  static Future<bool> _verifyActivationCodeIntegrity(
    int originalTimestamp,
    String deviceLabel,
    int daysValid,
    String expectedCode,
  ) async {
    try {
      // 使用相同的算法重新生成激活码进行验证
      final decodedKey = utf8.decode(base64.decode(_masterKey));

      // 模拟原始生成过程
      final deviceId = await getDeviceId(); // 当前设备ID（生成设备）
      final deviceFactor = deviceId.substring(0, 8);
      final timeFactor = (originalTimestamp ~/ 1000).toString();
      final daysFactor = daysValid.toString().padLeft(4, '0');
      final labelFactor = _generateLabelHash(deviceLabel);

      // 重新组合种子
      final combinedSeed =
          '${decodedKey}_${_appSignature}_${deviceFactor}_${timeFactor}_${daysFactor}_$labelFactor';

      // 重新生成哈希
      final regeneratedHash =
          sha256.convert(utf8.encode(combinedSeed)).toString();
      final regeneratedCode = regeneratedHash.substring(0, 20).toUpperCase();

      // 比较是否一致
      return regeneratedCode == expectedCode.toUpperCase();
    } catch (e) {
      return false;
    }
  }

  /// 获取已授权设备列表
  static Future<List<String>> getAuthorizedDevices() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getStringList(_authorizedDevicesKey) ?? [];
  }

  /// 获取授权历史记录
  static Future<List<Map<String, dynamic>>> getActivationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final history = prefs.getStringList(_activationHistoryKey) ?? [];

      return history.map((record) {
        final parts = record.split('|');
        if (parts.length >= 4) {
          return {
            'timestamp': parts[0],
            'deviceId': parts[1],
            'licenseType': parts[2],
            'role': parts[3],
          };
        }
        return {'error': 'Invalid record format'};
      }).toList();
    } catch (e) {
      return [
        {'error': 'Failed to load history: $e'}
      ];
    }
  }

  /// 获取用户角色
  static Future<UserRole> getUserRole() async {
    final prefs = await SharedPreferences.getInstance();
    final roleStr = prefs.getString(_userRoleKey) ??
        UserRole.trial.toString().split('.').last;

    return UserRole.values.firstWhere(
      (r) => r.toString().split('.').last == roleStr,
      orElse: () => UserRole.trial,
    );
  }

  /// 获取许可证天数
  static int _getLicenseDays(LicenseType licenseType) {
    switch (licenseType) {
      case LicenseType.trial:
        return 7;
      case LicenseType.monthly:
        return 30;
      case LicenseType.quarterly:
        return 90;
      case LicenseType.annual:
        return 365;
      case LicenseType.permanent:
        return -1;
    }
  }

  /// 保存许可证信息
  static Future<void> _saveLicenseInfo(
      LicenseType licenseType, int days, UserRole role) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_licenseTypeKey, licenseType.name);
    await prefs.setInt(
        _activationTimeKey, DateTime.now().millisecondsSinceEpoch);
    await prefs.setString(_userRoleKey, role.toString().split('.').last);

    if (days != -1) {
      final expiry =
          DateTime.now().add(Duration(days: days)).millisecondsSinceEpoch;
      await prefs.setInt('license_expiry', expiry);
    } else {
      await prefs.remove('license_expiry');
    }

    await prefs.setString(_activationCodeKey, 'activated');
    await prefs.setInt(
        _lastValidationKey, DateTime.now().millisecondsSinceEpoch);

    // 🔐 激活成功后清除强制激活标记
    await clearForceActivationFlag();
  }

  /// 检查应用是否已激活
  static Future<bool> isAppActivated() async {
    final prefs = await SharedPreferences.getInstance();
    final activationCode = prefs.getString(_activationCodeKey);
    return activationCode == 'activated';
  }

  /// 检查许可证是否有效
  static Future<Map<String, dynamic>> checkLicenseStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final isActivated = await isAppActivated();
    final role = await getUserRole();
    final roleStr = role.toString().split('.').last;
    final deviceId = await getDeviceId();

    if (!isActivated) {
      // 检查试用期
      final firstInstall = prefs.getInt(_firstInstallKey) ??
          DateTime.now().millisecondsSinceEpoch;
      final trialDays = 7; // 7天试用期
      final trialExpiry = DateTime.fromMillisecondsSinceEpoch(firstInstall)
          .add(Duration(days: trialDays));
      final isTrialValid = DateTime.now().isBefore(trialExpiry);

      return {
        'isValid': isTrialValid,
        'licenseType': 'trial',
        'role': roleStr,
        'remainingDays': isTrialValid
            ? trialExpiry.difference(DateTime.now()).inDays + 1
            : 0,
        'activationTime': firstInstall,
        'expiryTime': trialExpiry.millisecondsSinceEpoch,
        'deviceId': deviceId,
        'message': isTrialValid
            ? '试用期剩余 ${trialExpiry.difference(DateTime.now()).inDays + 1} 天，请联系管理员激活'
            : '试用期已过期，请联系管理员激活应用',
        'needActivation': !isTrialValid  // 修复：试用期过期后需要激活
      };
    }

    // 检查正式许可证
    final licenseExpiry = prefs.getInt('license_expiry');
    final licenseType = prefs.getString(_licenseTypeKey) ?? 'monthly';
    final isMasterActivated = prefs.getBool(_masterActivatedKey) ?? false;
    final activationTime = prefs.getInt(_activationTimeKey) ?? DateTime.now().millisecondsSinceEpoch;

    if (licenseExpiry == null || isMasterActivated) {
      // 永久许可证或主密码激活
      return {
        'isValid': true,
        'licenseType': licenseType,
        'role': roleStr,
        'remainingDays': -1,
        'activationTime': activationTime,
        'expiryTime': null, // 永久有效，无到期时间
        'deviceId': deviceId,
        'message': isMasterActivated ? '管理员激活（永久有效）' : '永久许可证',
        'needActivation': false
      };
    } else {
      // 时间限制许可证
      final expiry = DateTime.fromMillisecondsSinceEpoch(licenseExpiry);
      final isValid = DateTime.now().isBefore(expiry);
      final remainingDays =
          isValid ? expiry.difference(DateTime.now()).inDays + 1 : 0;

      return {
        'isValid': isValid,
        'licenseType': licenseType,
        'role': roleStr,
        'remainingDays': remainingDays,
        'activationTime': activationTime,
        'expiryTime': licenseExpiry,
        'deviceId': deviceId,
        'message': isValid
            ? (remainingDays <= 7
                ? '许可证即将到期，剩余 $remainingDays 天，请联系管理员续期'
                : '许可证剩余 $remainingDays 天')
            : '许可证已过期，请联系管理员续期',
        'needActivation': !isValid
      };
    }
  }

  /// 获取设备ID（用于生成激活码）
  static Future<String> getDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String deviceId = prefs.getString(_deviceIdKey) ?? '';
    
    // 如果设备ID为空，重新生成
    if (deviceId.isEmpty) {
      deviceId = await _generateDeviceId();
      await prefs.setString(_deviceIdKey, deviceId);
    }
    
    return deviceId;
  }

  /// 获取设备信息（用于管理）
  static Future<Map<String, String>> getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    final deviceId = await getDeviceId();
    final role = await getUserRole();

    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'deviceId': deviceId,
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'identifier': '${androidInfo.manufacturer} ${androidInfo.model}',
          'role': role.toString().split('.').last,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'deviceId': deviceId,
          'platform': 'iOS',
          'model': iosInfo.model,
          'manufacturer': 'Apple',
          'version': iosInfo.systemVersion,
          'identifier': '${iosInfo.model}',
          'role': role.toString().split('.').last,
        };
      }
    } catch (e) {
      // 获取失败时的降级处理
    }

    return {
      'deviceId': deviceId,
      'platform': Platform.operatingSystem,
      'model': '未知',
      'manufacturer': '未知',
      'version': '未知',
      'identifier': '设备 $deviceId',
      'role': role.toString().split('.').last,
    };
  }

  /// 【超级管理员功能】远程禁用设备（需要超级管理员权限）
  static Future<bool> disableDevice() async {
    final currentRole = await getUserRole();
    if (currentRole != UserRole.superAdmin) {
      return false;
    }

    final prefs = await SharedPreferences.getInstance();
    await prefs.clear(); // 清除所有数据
    return true;
  }

  /// 重置应用（清除所有安全数据）
  static Future<void> resetApp() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_activationCodeKey);
    await prefs.remove(_licenseTypeKey);
    await prefs.remove('license_expiry');
    await prefs.remove(_lastValidationKey);
    await prefs.remove(_activationTimeKey);
    await prefs.remove(_masterActivatedKey);
    await prefs.remove(_userRoleKey);
    // 保留设备ID和首次安装时间
  }

  /// 强制过期（用于测试）
  static Future<void> forceExpire() async {
    final prefs = await SharedPreferences.getInstance();
    // 设置许可证过期时间为昨天
    final yesterday =
        DateTime.now().subtract(const Duration(days: 1)).millisecondsSinceEpoch;
    await prefs.setInt('license_expiry', yesterday);
  }

  /// 安全检查（防篡改）
  static Future<bool> securityCheck() async {
    try {
      // 在发布版本中启用安全检查
      if (kDebugMode) {
        return true; // 调试模式下允许通过
      }

      // 检查时间是否被篡改
      final prefs = await SharedPreferences.getInstance();
      final lastValidation = prefs.getInt(_lastValidationKey) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      // 如果当前时间早于上次验证时间，可能时间被篡改
      if (currentTime < lastValidation - (24 * 60 * 60 * 1000)) {
        // 允许1天误差
        return false;
      }

      // 更新最后验证时间
      await prefs.setInt(_lastValidationKey, currentTime);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 🔐 强化版许可证功能限制 - 试用期过期后完全禁用
  static Future<Map<String, bool>> getLicenseFeatures() async {
    final status = await checkLicenseStatus();
    final licenseType = status['licenseType'] as String;
    final isValid = status['isValid'] as bool;
    final roleStr = status['role'] as String;
    final needActivation = status['needActivation'] as bool? ?? false;

    // 🚫 试用期过期或许可证无效时，完全禁用所有功能
    if (!isValid || needActivation) {
      return {
        'basicRecognition': false, // 🚫 试用期过期后禁用所有功能
        'advancedRecognition': false,
        'batchProcessing': false,
        'dataExport': false,
        'enterpriseFeatures': false,
        'professionalRecognition': false,
        'canAuthorizeDevices': false,
        'canViewAllActivations': false,
        'canGenerateMasterCodes': false,
        'canAccessApp': false, // 🚫 不能访问应用
      };
    }

    // ✅ 基于用户角色设置权限
    final isSuperAdmin = roleStr == 'superAdmin';
    final isActivated = roleStr == 'activated';
    final isTrial = roleStr == 'trial';

    // 🔐 简化的二级权限控制
    if (isSuperAdmin) {
      // 超级管理员 - 完全权限，包括管理其他设备
      return {
        'basicRecognition': true,
        'advancedRecognition': true,
        'batchProcessing': true,
        'dataExport': true,
        'enterpriseFeatures': true,
        'professionalRecognition': true,
        'canAccessAdminPanel': true, // ✅ 可以访问管理员面板
        'canAuthorizeDevices': true, // ✅ 可以为其他设备生成激活码
        'canViewAllActivations': true, // ✅ 可以查看所有激活记录
        'canAccessApp': true,
      };
    } else if (isActivated && isValid) {
      // 激活用户 - 完整功能，但不能管理其他设备，有有效期
      return {
        'basicRecognition': true,
        'advancedRecognition': true,
        'batchProcessing': true,
        'dataExport': true,
        'enterpriseFeatures': true,
        'professionalRecognition': true,
        'canAccessAdminPanel': false, // ❌ 不能访问管理员面板
        'canAuthorizeDevices': false, // ❌ 不能为其他设备生成激活码
        'canViewAllActivations': false, // ❌ 不能查看激活记录
        'canAccessApp': true,
      };
    } else if (isTrial && isValid) {
      // 试用用户 - 完整功能，但有7天限制
      return {
        'basicRecognition': true,
        'advancedRecognition': true,
        'batchProcessing': true,
        'dataExport': true,
        'enterpriseFeatures': true,
        'professionalRecognition': true,
        'canAccessAdminPanel': false, // ❌ 不能访问管理员面板
        'canAuthorizeDevices': false, // ❌ 不能为其他设备生成激活码
        'canViewAllActivations': false, // ❌ 不能查看激活记录
        'canAccessApp': true,
      };
    }

    // 默认情况：完全禁用
    return {
      'basicRecognition': false,
      'advancedRecognition': false,
      'batchProcessing': false,
      'dataExport': false,
      'enterpriseFeatures': false,
      'professionalRecognition': false,
      'canAuthorizeDevices': false,
      'canViewAllActivations': false,
      'canGenerateMasterCodes': false,
      'canAccessApp': false,
    };
  }

  /// 🔐 强化的权限验证 - 在关键功能点调用
  static Future<bool> validatePermission(String featureName) async {
    try {
      // 1. 检查许可证状态
      final licenseStatus = await checkLicenseStatus();
      final isValid = licenseStatus['isValid'] as bool;
      final needActivation = licenseStatus['needActivation'] as bool? ?? false;

      // 2. 如果许可证无效或需要激活，禁止访问
      if (!isValid || needActivation) {
        return false;
      }

      // 3. 检查功能权限
      final features = await getLicenseFeatures();
      final canAccessApp = features['canAccessApp'] as bool? ?? false;

      if (!canAccessApp) {
        return false;
      }

      // 4. 检查特定功能权限
      if (features.containsKey(featureName)) {
        return features[featureName] as bool? ?? false;
      }

      // 5. 默认检查基础权限
      return features['basicRecognition'] as bool? ?? false;
    } catch (e) {
      // 出现异常时默认拒绝访问
      return false;
    }
  }

  /// 🚫 检查应用是否应该被阻止启动
  static Future<bool> shouldBlockAppLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 检查是否有强制激活标记
      final forceActivationRequired = prefs.getBool('force_activation_required') ?? false;
      if (forceActivationRequired) {
        print('检测到强制激活标记，阻止应用启动');
        return true;
      }

      final licenseStatus = await checkLicenseStatus();
      final isValid = licenseStatus['isValid'] as bool;
      final needActivation = licenseStatus['needActivation'] as bool? ?? false;
      final role = licenseStatus['role'] as String;

      // 🔐 强化权限控制：任何用户（除超级管理员外）过期都要阻止
      if (role != 'superAdmin') {
        // 试用用户和激活用户过期后都无法使用
        if (!isValid || needActivation) {
          return true;
        }
      }

      // 超级管理员永不过期，始终可以使用
      return false;
    } catch (e) {
      // 异常情况下阻止启动
      return true;
    }
  }

  /// 🔐 检查是否需要强制跳转到激活页面
  static Future<bool> shouldForceActivation() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 检查强制激活标记
      final forceActivationRequired = prefs.getBool('force_activation_required') ?? false;
      if (forceActivationRequired) {
        return true;
      }

      // 检查许可证状态
      final licenseStatus = await checkLicenseStatus();
      final isValid = licenseStatus['isValid'] as bool;
      final needActivation = licenseStatus['needActivation'] as bool? ?? false;
      final role = licenseStatus['role'] as String;

      // 超级管理员永不强制激活
      if (role == 'superAdmin') {
        return false;
      }

      // 其他用户过期后强制激活
      return !isValid || needActivation;
    } catch (e) {
      // 异常情况下强制激活
      return true;
    }
  }

  /// 🔐 清除强制激活标记（激活成功后调用）
  static Future<void> clearForceActivationFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('force_activation_required');
      print('强制激活标记已清除');
    } catch (e) {
      print('清除强制激活标记失败: $e');
    }
  }

  /// 🔐 强化的功能访问检查 - 确保过期用户无法使用任何功能
  static Future<bool> canAccessFeature(String featureName) async {
    try {
      // 检查应用是否被阻止
      if (await shouldBlockAppLaunch()) {
        return false;
      }

      // 获取功能权限
      final features = await getLicenseFeatures();
      final canAccess = features[featureName] as bool? ?? false;

      return canAccess;
    } catch (e) {
      return false;
    }
  }

  /// 🔑 获取或生成密钥
  static Future<String> _getOrGenerateSecretKey() async {
    if (_secretKey != null) return _secretKey!;

    final prefs = await SharedPreferences.getInstance();
    String? storedKey = prefs.getString('app_secret_key');

    if (storedKey == null) {
      // 生成基于设备的密钥
      final deviceId = await getDeviceId();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final keyData = 'LoadGuard_$deviceId$timestamp';
      storedKey =
          sha256.convert(utf8.encode(keyData)).toString().substring(0, 32);
      await prefs.setString('app_secret_key', storedKey);
    }

    _secretKey = storedKey;
    return storedKey;
  }

  /// 🔐 验证主激活码 - 只有您知道的专属激活码
  static const String _masterActivationCode =
      'MASTER-LG2024-SECURE-ADMIN-9F7E3';

  /// 🆕 预定义的授权期限选项（天数）
  static const Map<String, int> _predefinedDurations = {
    'trial': 7, // 试用期
    '30days': 30, // 月度授权
    '60days': 60, // 双月授权
    '90days': 90, // 季度授权
    '180days': 180, // 半年授权
    '365days': 365, // 年度授权
  };

  /// 验证是否为有效的主激活码
  static bool isMasterActivationCode(String inputCode) {
    final cleanCode =
        inputCode.replaceAll(' ', '').replaceAll('-', '').toUpperCase();
    final masterCode = _masterActivationCode.replaceAll('-', '').toUpperCase();
    return cleanCode == masterCode;
  }

  /// 🔐 获取主激活码（仅用于显示提示，不暴露真实码）
  static String getMasterCodeHint() {
    return 'MASTER-XXXXX-XXXXXX-XXXXX-XXXXX';
  }

  /// 🔐 验证超级管理员激活码（已弃用，使用主激活码）
  static bool validateSuperAdminCode(String inputCode) {
    // 重定向到主激活码验证
    return isMasterActivationCode(inputCode);
  }

  /// 🔐 管理员激活指南
  static Map<String, dynamic> getAdminActivationGuide() {
    return {
      'methods': [
        {
          'name': '主激活码',
          'description': '专属的主激活码，可在任何设备上激活管理员权限',
          'code': _masterActivationCode,
          'validity': '永久有效',
          'recommended': true,
          'usage': '输入激活码: $_masterActivationCode'
        },
        // 🔐 紧急恢复序列已移除，简化为主激活码验证
      ],
      'security_notes': [
        '主激活码永久有效，可在多台设备使用',
        '激活后永久有效，可以管理所有设备',
        '可以为其他设备生成激活码',
        '请妥善保管激活码，避免泄露'
      ],
      'local_deployment_tips': [
        '单机应用支持多个管理员设备',
        '可以将主激活码分享给信任的管理人员',
        '定期备份许可证数据防止丢失',
        '监控异常激活尝试并及时处理'
      ]
    };
  }

  /// 🔐 检查设备是否有超级管理员授权（简化版 - 允许所有设备）
  static Future<bool> _isAuthorizedSuperAdminDevice(String deviceId) async {
    // 简化版：允许任何设备激活管理员（本地应用特点）
    return true;
  }

  /// 🔐 获取当前设备信息（用于添加到白名单）
  static Future<Map<String, String>> getCurrentDeviceInfo() async {
    final deviceId = await getDeviceId();
    final deviceInfo = DeviceInfoPlugin();

    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'deviceId': deviceId,
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'board': androidInfo.board,
          'androidId': androidInfo.id,
          'fingerprint':
              '${androidInfo.id}_${androidInfo.model}_${androidInfo.manufacturer}_${androidInfo.board}',
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'deviceId': deviceId,
          'platform': 'iOS',
          'model': iosInfo.model,
          'systemName': iosInfo.systemName,
          'identifierForVendor': iosInfo.identifierForVendor ?? 'unknown',
          'fingerprint':
              '${iosInfo.identifierForVendor}_${iosInfo.model}_${iosInfo.systemName}',
        };
      }
    } catch (e) {
      // 获取失败时返回基本信息
    }

    return {
      'deviceId': deviceId,
      'platform': 'Unknown',
      'fingerprint': 'unknown',
    };
  }

  /// 启动免费试用（7天）
  static Future<void> startFreeTrial() async {
    try {
      // 检查是否已经激活或试用
      final isActivated = await isAppActivated();
      if (isActivated) {
        throw Exception('应用已激活，无需试用');
      }

      final prefs = await SharedPreferences.getInstance();
      final firstInstall = prefs.getInt(_firstInstallKey);

      // 如果是第一次安装或者没有记录，允许试用
      if (firstInstall == null) {
        await prefs.setInt(
            _firstInstallKey, DateTime.now().millisecondsSinceEpoch);
      }

      // 保存试用许可证信息
      await _saveLicenseInfo(LicenseType.trial, 7, UserRole.trial);

      // 记录试用开始时间
      await prefs.setInt(
          'trial_start_time', DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      throw Exception('启动试用失败: $e');
    }
  }

  /// 🔐 简化的主密码验证
  static Future<bool> validateMasterPassword(String input) async {
    try {
      // 简化验证：基于设备信息的主密码验证
      final deviceId = await getDeviceId();
      final deviceSignature = await _getDeviceFingerprint();
      final secretKey = await _getOrGenerateSecretKey();

      // 生成基于设备的主密码
      final deviceBasedPassword = sha256
          .convert(utf8.encode('LoadGuard_Master_$deviceSignature$secretKey'))
          .toString()
          .substring(0, 16)
          .toUpperCase();

      return input.toUpperCase() == deviceBasedPassword;
    } catch (e) {
      return false;
    }
  }

  /// 管理员主密码生成激活码 - 修复版
  static Future<String> generateActivationCodeWithMaster(
    String deviceId,
    LicenseType licenseType,
    String masterPassword, {
    UserRole role = UserRole.activated,
  }) async {
    if (!await validateMasterPassword(masterPassword)) {
      throw Exception('主密码错误');
    }

    // 验证当前设备是否有超级管理员权限
    final currentRole = await getUserRole();
    if (currentRole != UserRole.superAdmin) {
      throw Exception('需要超级管理员权限才能生成激活码');
    }

    return await generateDeviceActivationCode(deviceId, licenseType,
        role: role);
  }
}
