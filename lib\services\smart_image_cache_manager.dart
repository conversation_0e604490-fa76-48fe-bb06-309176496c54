import 'dart:io';
import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:loadguard/utils/app_logger.dart';

/// 🖼️ 【智能图片缓存管理器】
/// 
/// 功能特性：
/// 1. 自动清理过期缓存
/// 2. 内存压力监控
/// 3. 预测性缓存清理
/// 4. 缓存大小限制
class SmartImageCacheManager {
  static SmartImageCacheManager? _instance;
  static SmartImageCacheManager get instance {
    _instance ??= SmartImageCacheManager._();
    return _instance!;
  }
  
  SmartImageCacheManager._();
  
  // 缓存配置
  static const int _maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const int _maxCacheAge = 7 * 24 * 60 * 60 * 1000; // 7天
  static const int _cleanupInterval = 30 * 60 * 1000; // 30分钟
  static const int _maxCacheFiles = 500; // 最大文件数
  
  final Map<String, CacheEntry> _cacheIndex = {};
  final Map<String, Uint8List> _memoryCache = {};
  Timer? _cleanupTimer;
  bool _isInitialized = false;
  
  String? _cacheDirectory;
  
  /// 初始化缓存管理器
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('🖼️ 初始化智能图片缓存管理器...');
    
    try {
      // 创建缓存目录
      final tempDir = Directory.systemTemp;
      _cacheDirectory = path.join(tempDir.path, 'loadguard_image_cache');
      await Directory(_cacheDirectory!).create(recursive: true);
      
      // 加载现有缓存索引
      await _loadCacheIndex();
      
      // 启动定期清理
      _startPeriodicCleanup();
      
      _isInitialized = true;
      AppLogger.info('✅ 图片缓存管理器初始化完成，缓存目录: $_cacheDirectory');
      
      // 执行初始清理
      await _performCleanup();
      
    } catch (e) {
      AppLogger.error('❌ 图片缓存管理器初始化失败: $e');
      rethrow;
    }
  }
  
  /// 获取缓存的图片
  Future<Uint8List?> getCachedImage(String imagePath) async {
    if (!_isInitialized) await initialize();
    
    final cacheKey = _getCacheKey(imagePath);
    
    // 1. 先检查内存缓存
    if (_memoryCache.containsKey(cacheKey)) {
      AppLogger.debug('🎯 内存缓存命中: $imagePath');
      _updateAccessTime(cacheKey);
      return _memoryCache[cacheKey];
    }
    
    // 2. 检查磁盘缓存
    final cacheEntry = _cacheIndex[cacheKey];
    if (cacheEntry != null) {
      final cacheFile = File(cacheEntry.filePath);
      if (await cacheFile.exists()) {
        try {
          final bytes = await cacheFile.readAsBytes();
          
          // 放入内存缓存（如果不太大）
          if (bytes.length < 1024 * 1024) { // 小于1MB
            _memoryCache[cacheKey] = bytes;
            _limitMemoryCache();
          }
          
          _updateAccessTime(cacheKey);
          AppLogger.debug('🎯 磁盘缓存命中: $imagePath');
          return bytes;
        } catch (e) {
          AppLogger.warning('读取缓存文件失败: $e');
          _cacheIndex.remove(cacheKey);
        }
      } else {
        _cacheIndex.remove(cacheKey);
      }
    }
    
    // 3. 缓存未命中，尝试加载原图并缓存
    return await _loadAndCacheImage(imagePath);
  }
  
  /// 加载图片并缓存
  Future<Uint8List?> _loadAndCacheImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        AppLogger.warning('图片文件不存在: $imagePath');
        return null;
      }
      
      final bytes = await file.readAsBytes();
      final cacheKey = _getCacheKey(imagePath);
      
      // 保存到磁盘缓存
      await _saveToDiskCache(cacheKey, bytes);
      
      // 保存到内存缓存（如果不太大）
      if (bytes.length < 1024 * 1024) {
        _memoryCache[cacheKey] = bytes;
        _limitMemoryCache();
      }
      
      AppLogger.debug('🖼️ 图片已缓存: $imagePath');
      return bytes;
      
    } catch (e) {
      AppLogger.error('加载图片失败: $imagePath - $e');
      return null;
    }
  }
  
  /// 保存到磁盘缓存
  Future<void> _saveToDiskCache(String cacheKey, Uint8List bytes) async {
    final fileName = '$cacheKey.cache';
    final filePath = path.join(_cacheDirectory!, fileName);
    final cacheFile = File(filePath);
    
    await cacheFile.writeAsBytes(bytes);
    
    final now = DateTime.now().millisecondsSinceEpoch;
    _cacheIndex[cacheKey] = CacheEntry(
      filePath: filePath,
      size: bytes.length,
      createdTime: now,
      lastAccessTime: now,
    );
  }
  
  /// 限制内存缓存大小
  void _limitMemoryCache() {
    const maxMemoryCacheSize = 20 * 1024 * 1024; // 20MB
    int currentSize = 0;
    final entries = _memoryCache.entries.toList();
    
    // 计算当前大小
    for (final entry in entries) {
      currentSize += entry.value.length;
    }
    
    // 如果超过限制，移除最旧的条目
    if (currentSize > maxMemoryCacheSize) {
      final sortedKeys = _memoryCache.keys.toList()
        ..sort((a, b) {
          final aTime = _cacheIndex[a]?.lastAccessTime ?? 0;
          final bTime = _cacheIndex[b]?.lastAccessTime ?? 0;
          return aTime.compareTo(bTime);
        });
      
      for (final key in sortedKeys) {
        if (currentSize <= maxMemoryCacheSize) break;
        
        final bytes = _memoryCache.remove(key);
        if (bytes != null) {
          currentSize -= bytes.length;
        }
      }
      
      AppLogger.debug('🧹 内存缓存清理完成，当前大小: ${(currentSize / 1024 / 1024).toStringAsFixed(1)}MB');
    }
  }
  
  /// 启动定期清理
  void _startPeriodicCleanup() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(
      Duration(milliseconds: _cleanupInterval),
      (_) => _performCleanup(),
    );
  }
  
  /// 执行清理
  Future<void> _performCleanup() async {
    if (!_isInitialized) return;
    
    AppLogger.info('🧹 开始图片缓存清理...');
    final startTime = DateTime.now();
    
    try {
      int removedFiles = 0;
      int freedSpace = 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final keysToRemove = <String>[];
      
      // 1. 检查过期文件
      for (final entry in _cacheIndex.entries) {
        final cacheEntry = entry.value;
        final age = now - cacheEntry.createdTime;
        
        if (age > _maxCacheAge) {
          keysToRemove.add(entry.key);
          continue;
        }
        
        // 检查文件是否还存在
        if (!await File(cacheEntry.filePath).exists()) {
          keysToRemove.add(entry.key);
        }
      }
      
      // 2. 检查缓存大小限制
      if (_cacheIndex.length > _maxCacheFiles) {
        final sortedEntries = _cacheIndex.entries.toList()
          ..sort((a, b) => a.value.lastAccessTime.compareTo(b.value.lastAccessTime));
        
        final excessCount = _cacheIndex.length - _maxCacheFiles;
        for (int i = 0; i < excessCount; i++) {
          keysToRemove.add(sortedEntries[i].key);
        }
      }
      
      // 3. 检查总缓存大小
      int totalSize = _cacheIndex.values.fold(0, (sum, entry) => sum + entry.size);
      if (totalSize > _maxCacheSize) {
        final sortedEntries = _cacheIndex.entries.toList()
          ..sort((a, b) => a.value.lastAccessTime.compareTo(b.value.lastAccessTime));
        
        for (final entry in sortedEntries) {
          if (totalSize <= _maxCacheSize) break;
          keysToRemove.add(entry.key);
          totalSize -= entry.value.size;
        }
      }
      
      // 4. 执行删除
      for (final key in keysToRemove) {
        final cacheEntry = _cacheIndex.remove(key);
        if (cacheEntry != null) {
          try {
            final file = File(cacheEntry.filePath);
            if (await file.exists()) {
              await file.delete();
              removedFiles++;
              freedSpace += cacheEntry.size;
            }
          } catch (e) {
            AppLogger.warning('删除缓存文件失败: ${cacheEntry.filePath} - $e');
          }
        }
        
        // 同时从内存缓存中移除
        _memoryCache.remove(key);
      }
      
      final duration = DateTime.now().difference(startTime);
      AppLogger.info(
        '✅ 缓存清理完成: 删除${removedFiles}个文件, '
        '释放${(freedSpace / 1024 / 1024).toStringAsFixed(1)}MB空间, '
        '耗时${duration.inMilliseconds}ms'
      );
      
    } catch (e) {
      AppLogger.error('缓存清理失败: $e');
    }
  }
  
  /// 加载缓存索引
  Future<void> _loadCacheIndex() async {
    try {
      final cacheDir = Directory(_cacheDirectory!);
      if (!await cacheDir.exists()) return;
      
      await for (final entity in cacheDir.list()) {
        if (entity is File && entity.path.endsWith('.cache')) {
          final stat = await entity.stat();
          final fileName = path.basenameWithoutExtension(entity.path);
          
          _cacheIndex[fileName] = CacheEntry(
            filePath: entity.path,
            size: stat.size,
            createdTime: stat.modified.millisecondsSinceEpoch,
            lastAccessTime: stat.accessed.millisecondsSinceEpoch,
          );
        }
      }
      
      AppLogger.info('📂 加载缓存索引完成: ${_cacheIndex.length}个文件');
    } catch (e) {
      AppLogger.error('加载缓存索引失败: $e');
    }
  }
  
  /// 更新访问时间
  void _updateAccessTime(String cacheKey) {
    final entry = _cacheIndex[cacheKey];
    if (entry != null) {
      _cacheIndex[cacheKey] = entry.copyWith(
        lastAccessTime: DateTime.now().millisecondsSinceEpoch,
      );
    }
  }
  
  /// 生成缓存键
  String _getCacheKey(String imagePath) {
    final file = File(imagePath);
    final fileName = path.basename(imagePath);
    final size = file.existsSync() ? file.lengthSync() : 0;
    return '${fileName}_${size}_${imagePath.hashCode.abs()}';
  }
  
  /// 获取缓存统计信息
  CacheStats getCacheStats() {
    int totalSize = _cacheIndex.values.fold(0, (sum, entry) => sum + entry.size);
    int memorySize = _memoryCache.values.fold(0, (sum, bytes) => sum + bytes.length);
    
    return CacheStats(
      diskCacheFiles: _cacheIndex.length,
      diskCacheSize: totalSize,
      memoryCacheFiles: _memoryCache.length,
      memoryCacheSize: memorySize,
      cacheDirectory: _cacheDirectory ?? '',
    );
  }
  
  /// 手动清理所有缓存
  Future<void> clearAllCache() async {
    AppLogger.info('🗑️ 手动清理所有缓存...');
    
    try {
      // 清理内存缓存
      _memoryCache.clear();
      
      // 清理磁盘缓存
      for (final entry in _cacheIndex.values) {
        try {
          final file = File(entry.filePath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          AppLogger.warning('删除缓存文件失败: ${entry.filePath} - $e');
        }
      }
      
      _cacheIndex.clear();
      AppLogger.info('✅ 所有缓存已清理');
      
    } catch (e) {
      AppLogger.error('清理缓存失败: $e');
    }
  }
  
  /// 销毁缓存管理器
  void dispose() {
    _cleanupTimer?.cancel();
    _memoryCache.clear();
    _cacheIndex.clear();
    _isInitialized = false;
    AppLogger.info('🧹 图片缓存管理器已销毁');
  }
}

/// 📊 缓存条目
class CacheEntry {
  final String filePath;
  final int size;
  final int createdTime;
  final int lastAccessTime;
  
  const CacheEntry({
    required this.filePath,
    required this.size,
    required this.createdTime,
    required this.lastAccessTime,
  });
  
  CacheEntry copyWith({
    String? filePath,
    int? size,
    int? createdTime,
    int? lastAccessTime,
  }) {
    return CacheEntry(
      filePath: filePath ?? this.filePath,
      size: size ?? this.size,
      createdTime: createdTime ?? this.createdTime,
      lastAccessTime: lastAccessTime ?? this.lastAccessTime,
    );
  }
}

/// 📈 缓存统计信息
class CacheStats {
  final int diskCacheFiles;
  final int diskCacheSize;
  final int memoryCacheFiles;
  final int memoryCacheSize;
  final String cacheDirectory;
  
  const CacheStats({
    required this.diskCacheFiles,
    required this.diskCacheSize,
    required this.memoryCacheFiles,
    required this.memoryCacheSize,
    required this.cacheDirectory,
  });
  
  String get diskCacheSizeFormatted {
    return '${(diskCacheSize / 1024 / 1024).toStringAsFixed(1)}MB';
  }
  
  String get memoryCacheSizeFormatted {
    return '${(memoryCacheSize / 1024 / 1024).toStringAsFixed(1)}MB';
  }
}

/// 🖼️ 智能缓存图片组件
class SmartCachedImage extends StatelessWidget {
  final String imagePath;
  final BoxFit fit;
  final double? width;
  final double? height;
  final Widget? placeholder;
  final Widget? errorWidget;
  
  const SmartCachedImage({
    Key? key,
    required this.imagePath,
    this.fit = BoxFit.cover,
    this.width,
    this.height,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: SmartImageCacheManager.instance.getCachedImage(imagePath),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ?? const Center(child: CircularProgressIndicator());
        }
        
        if (snapshot.hasError || snapshot.data == null) {
          return errorWidget ?? Container(
            color: Colors.grey[300],
            child: const Icon(Icons.error, color: Colors.red),
          );
        }
        
        return Image.memory(
          snapshot.data!,
          fit: fit,
          width: width,
          height: height,
        );
      },
    );
  }
}