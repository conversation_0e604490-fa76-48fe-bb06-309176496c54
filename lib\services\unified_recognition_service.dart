import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/utils/blue_background_processor.dart';
import 'package:loadguard/utils/color_background_processor.dart';
import 'package:loadguard/services/multi_engine_recognition_service.dart';
import 'package:loadguard/services/qr_code_recognition_engine.dart';
import 'package:loadguard/services/advanced_reflection_suppressor.dart';
import 'package:loadguard/utils/qr_data_parser.dart';

/// 🔄 【统一识别服务】
/// 
/// 【设计理念】一套预处理 + 并行识别 + 交叉验证
/// 【技术特色】统一的图像预处理，支持文字OCR和二维码识别
/// 【性能优化】避免重复处理，提高识别速度和准确率
/// 
/// 🎯 【核心流程】：
/// 1. 统一图像预处理（包括蓝色背景处理）
/// 2. 并行执行 ML Kit V2 OCR 和二维码识别
/// 3. 结果交叉验证和置信度融合
/// 4. 网络验证（如果需要）
class UnifiedRecognitionService {
  static UnifiedRecognitionService? _instance;
  static UnifiedRecognitionService get instance {
    _instance ??= UnifiedRecognitionService._();
    return _instance!;
  }
  
  UnifiedRecognitionService._();
  
  bool _isInitialized = false;
  late final MultiEngineRecognitionService _textService;
  late final QRCodeRecognitionEngine _qrCodeEngine;

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;
  
  /// 初始化统一识别服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('🔄 初始化统一识别服务...');
    
    try {
      // 初始化文字识别服务
      _textService = MultiEngineRecognitionService.instance;
      await _textService.initialize();

      // 初始化完整的二维码识别引擎
      _qrCodeEngine = QRCodeRecognitionEngine();
      await _qrCodeEngine.initialize();
      
      _isInitialized = true;
      AppLogger.info('✅ 统一识别服务初始化完成');
    } catch (e) {
      AppLogger.error('❌ 统一识别服务初始化失败: $e');
      rethrow;
    }
  }
  
  /// 🔄 统一识别（文字 + 二维码）
  /// 
  /// 【核心优势】：
  /// 1. 一次预处理，多种识别
  /// 2. 结果交叉验证，提高准确率
  /// 3. 避免重复的图像处理开销
  /// 
  /// 参数说明：
  /// - [imagePath] 原始图像路径
  /// - [enableQRCode] 是否启用二维码识别（默认true）
  /// - [enableNetworkValidation] 是否启用网络验证（默认false）
  /// 
  /// 返回值：统一的识别结果
  Future<UnifiedRecognitionResult> recognizeUnified(
    String imagePath, {
    bool enableQRCode = true,
    bool enableNetworkValidation = false,
    Function(double progress, String status)? onProgress,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final stopwatch = Stopwatch()..start();
    AppLogger.info('🔄 开始统一识别: $imagePath');
    
    try {
      // 1. 统一图像预处理（包括蓝色背景处理）
      onProgress?.call(0.1, '图像预处理...');
      final preprocessedPath = await _unifiedPreprocessing(imagePath);
      
      // 2. 并行执行识别
      onProgress?.call(0.3, '并行识别中...');
      final futures = <Future>[];
      
      // 文字识别
      final textFuture = _textService.recognizeWithMultiEngine(preprocessedPath);
      futures.add(textFuture);
      
      // 二维码识别（如果启用）
      Future<List<RecognitionResult>>? qrFuture;
      if (enableQRCode) {
        qrFuture = _qrCodeEngine.recognize(preprocessedPath);
        futures.add(qrFuture);
      }
      
      // 等待所有识别完成
      final results = await Future.wait(futures);
      final textResults = results[0] as List<RecognitionResult>;
      final qrResults = enableQRCode && results.length > 1
          ? results[1] as List<RecognitionResult>
          : <RecognitionResult>[];
      
      // 3. 结果交叉验证和融合
      onProgress?.call(0.8, '结果验证中...');
      final unifiedResult = await _crossValidateAndFuse(
        textResults, 
        qrResults,
        enableNetworkValidation,
      );
      
      stopwatch.stop();
      onProgress?.call(1.0, '识别完成');
      
      AppLogger.info('✅ 统一识别完成，耗时: ${stopwatch.elapsedMilliseconds}ms');
      
      return unifiedResult.copyWith(
        processingTime: stopwatch.elapsedMilliseconds,
        preprocessedImagePath: preprocessedPath,
      );
      
    } catch (e) {
      stopwatch.stop();
      AppLogger.error('❌ 统一识别失败: $e');
      onProgress?.call(1.0, '识别失败: $e');
      rethrow;
    }
  }
  
  /// 🔧 统一图像预处理 - 增强版
  ///
  /// 【处理流程】：
  /// 1. 检测图像问题（蓝色背景、反光、其他颜色背景等）
  /// 2. 按优先级应用相应的预处理算法
  /// 3. 返回优化后的图像路径
  Future<String> _unifiedPreprocessing(String imagePath) async {
    try {
      String processedPath = imagePath;
      
      // 1. 检测背景颜色类型
      final bgColor = await ColorBackgroundProcessor.detectBackgroundColor(imagePath);

      // 2. 蓝色背景优先处理（最常见问题）
      if (bgColor == BackgroundColor.blue) {
        AppLogger.info('🔵 检测到蓝色背景，应用专用处理');
        processedPath = await BlueBackgroundProcessor.processBlueBackground(processedPath);
      } else if (bgColor != BackgroundColor.neutral) {
        AppLogger.info('🎨 检测到${bgColor.description}背景，应用通用处理');
        processedPath = await ColorBackgroundProcessor.processColorBackground(processedPath);
      }
      
      // 3. 反光检测和处理（作为第二步处理）
      if (await _hasReflectionIssues(processedPath)) {
        AppLogger.info('✨ 检测到反光问题，应用反光抑制算法');
        processedPath = await AdvancedReflectionSuppressor.suppressReflections(processedPath);
      }

      return processedPath;
    } catch (e) {
      AppLogger.warning('⚠️ 图像预处理失败，使用原图: $e');
      return imagePath;
    }
  }
  
  /// 🔍 检测图像是否有反光问题
  Future<bool> _hasReflectionIssues(String imagePath) async {
    try {
      // 简单的反光检测：检查是否有高亮区域
      final bytes = await File(imagePath).readAsBytes();
      final image = img.decodeImage(bytes);
      if (image == null) return false;
      
      int highBrightnessPixels = 0;
      int totalPixels = image.width * image.height;
      
      // 采样检测（提高性能）
      for (int y = 0; y < image.height; y += 5) {
        for (int x = 0; x < image.width; x += 5) {
          final pixel = image.getPixel(x, y);
          final brightness = (pixel.r + pixel.g + pixel.b) / 3;
          if (brightness > 220) { // 很亮的像素
            highBrightnessPixels++;
          }
        }
      }
      
      final reflectionRatio = highBrightnessPixels / (totalPixels / 25); // 采样率调整
      return reflectionRatio > 0.05; // 5%以上高亮像素认为有反光
    } catch (e) {
      AppLogger.warning('反光检测失败: $e');
      return false;
    }
  }
  
  // 二维码识别现在使用完整的QRCodeRecognitionEngine，无需单独实现
  
  /// 🔄 交叉验证和结果融合
  Future<UnifiedRecognitionResult> _crossValidateAndFuse(
    List<RecognitionResult> textResults,
    List<RecognitionResult> qrResults,
    bool enableNetworkValidation,
  ) async {
    final validatedTextResults = <RecognitionResult>[];
    final validatedQRResults = <RecognitionResult>[];
    final crossValidationResults = <CrossValidationResult>[];
    
    // 1. 基础验证：移除明显错误的结果
    for (final textResult in textResults) {
      if (_isValidTextResult(textResult)) {
        validatedTextResults.add(textResult);
      }
    }
    
    for (final qrResult in qrResults) {
      if (_isValidQRResult(qrResult)) {
        validatedQRResults.add(qrResult);
      }
    }
    
    // 2. 交叉验证：文字和二维码结果互相验证
    for (final textResult in validatedTextResults) {
      for (final qrResult in validatedQRResults) {
        final validation = _performCrossValidation(textResult, qrResult);
        if (validation.isMatched) {
          crossValidationResults.add(validation);
        }
      }
    }
    
    // 3. 网络验证（如果启用）
    final networkValidations = <NetworkValidationResult>[];
    if (enableNetworkValidation && validatedQRResults.isNotEmpty) {
      for (final qrResult in validatedQRResults) {
        try {
          final validation = await _performNetworkValidation(qrResult);
          if (validation != null) {
            networkValidations.add(validation);
          }
        } catch (e) {
          AppLogger.warning('⚠️ 网络验证失败: $e');
        }
      }
    }
    
    // 4. 计算综合置信度
    final overallConfidence = _calculateOverallConfidence(
      validatedTextResults,
      validatedQRResults,
      crossValidationResults,
      networkValidations,
    );
    
    return UnifiedRecognitionResult(
      textResults: validatedTextResults,
      qrResults: validatedQRResults,
      crossValidations: crossValidationResults,
      networkValidations: networkValidations,
      overallConfidence: overallConfidence,
      hasQRCode: validatedQRResults.isNotEmpty,
      hasText: validatedTextResults.isNotEmpty,
      isConsistent: crossValidationResults.isNotEmpty,
      isNetworkVerified: networkValidations.any((nv) => nv.isValid),
      processingTime: 0, // 将在调用处设置
      preprocessedImagePath: '', // 将在调用处设置
    );
  }
  
  /// 验证文字识别结果
  bool _isValidTextResult(RecognitionResult result) {
    final text = result.ocrText?.trim() ?? '';
    if (text.isEmpty) return false;
    if (text.length < 2) return false; // 太短的文本可能是噪声
    if ((result.confidence ?? 0.0) < 30.0) return false; // 置信度太低
    return true;
  }
  
  /// 验证二维码识别结果
  bool _isValidQRResult(RecognitionResult result) {
    if (result.qrCode?.trim().isEmpty ?? true) return false;
    if ((result.confidence ?? 0.0) < 80.0) return false; // 二维码识别置信度应该很高
    return true;
  }
  
  /// 执行交叉验证
  CrossValidationResult _performCrossValidation(
    RecognitionResult textResult,
    RecognitionResult qrResult,
  ) {
    final textContent = textResult.ocrText?.toLowerCase() ?? '';
    final qrContent = qrResult.qrCode?.toLowerCase() ?? '';
    
    // 简单的内容匹配检查
    var matchScore = 0.0;
    var matchedElements = <String>[];
    
    // 检查是否包含相同的关键信息
    final textWords = textContent.split(RegExp(r'[\s\-_|;:,]'));
    final qrWords = qrContent.split(RegExp(r'[\s\-_|;:,/]'));
    
    for (final textWord in textWords) {
      if (textWord.length >= 3) { // 只检查有意义的词
        for (final qrWord in qrWords) {
          if (qrWord.contains(textWord) || textWord.contains(qrWord)) {
            matchScore += 1.0;
            matchedElements.add(textWord);
            break;
          }
        }
      }
    }
    
    // 计算匹配度
    final totalElements = textWords.where((w) => w.length >= 3).length;
    final matchRatio = totalElements > 0 ? matchScore / totalElements : 0.0;
    
    return CrossValidationResult(
      textResult: textResult,
      qrResult: qrResult,
      matchScore: matchScore,
      matchRatio: matchRatio,
      isMatched: matchRatio > 0.3, // 30%以上匹配认为是一致的
      matchedElements: matchedElements,
    );
  }
  
  /// 🌐 网络验证（优化支持域名格式）
  Future<NetworkValidationResult?> _performNetworkValidation(RecognitionResult qrResult) async {
    try {
      final content = qrResult.qrCode?.trim() ?? '';

      // 1. 智能URL构建
      String urlToValidate = content;
      if (!content.startsWith('http')) {
        // 检查是否为域名格式（如：qiyulongoc.com.cn）
        if (content.contains('.com') || content.contains('.cn') || content.contains('.org')) {
          urlToValidate = 'https://$content';
          AppLogger.info('🌐 自动构建URL: $urlToValidate');
        } else {
          // 不是有效的URL格式，跳过网络验证
          return NetworkValidationResult(
            qrResult: qrResult,
            url: content,
            isValid: false,
            statusCode: null,
            productInfo: null,
            validationTime: DateTime.now(),
          );
        }
      }

      // 2. 尝试访问URL
      final uri = Uri.tryParse(urlToValidate);
      if (uri != null) {
        try {
          final response = await http.get(uri).timeout(Duration(seconds: 8)); // 增加超时时间

          if (response.statusCode == 200) {
            // 解析响应内容，提取产品信息
            final productInfo = _extractProductInfoFromResponse(response.body);

            AppLogger.info('✅ 网络验证成功: ${uri.host}');
            return NetworkValidationResult(
              qrResult: qrResult,
              url: uri.toString(),
              isValid: true,
              statusCode: response.statusCode,
              productInfo: productInfo,
              validationTime: DateTime.now(),
            );
          } else {
            AppLogger.warning('⚠️ 网络验证失败，状态码: ${response.statusCode}');
          }
        } catch (e) {
          AppLogger.warning('⚠️ 网络请求异常: $e');
        }
      }

      return NetworkValidationResult(
        qrResult: qrResult,
        url: urlToValidate,
        isValid: false,
        statusCode: null,
        productInfo: null,
        validationTime: DateTime.now(),
      );
    } catch (e) {
      AppLogger.warning('网络验证异常: $e');
      return null;
    }
  }

  /// 从网络响应中提取产品信息
  Map<String, String> _extractProductInfoFromResponse(String responseBody) {
    final productInfo = <String, String>{};

    try {
      // 尝试解析JSON响应
      if (responseBody.trim().startsWith('{')) {
        final json = jsonDecode(responseBody);
        if (json is Map<String, dynamic>) {
          json.forEach((key, value) {
            if (value != null) {
              productInfo[key] = value.toString();
            }
          });
        }
      } else {
        // 解析HTML响应，提取关键信息
        final patterns = {
          'productCode': RegExp(r'产品牌号[：:]\s*([A-Z0-9\-]+)', caseSensitive: false),
          'batchNumber': RegExp(r'产品批次号[：:]\s*([A-Z0-9\-]+)', caseSensitive: false),
          'productName': RegExp(r'产品名称[：:]\s*([^<\n]+)', caseSensitive: false),
          'standard': RegExp(r'执行标准[：:]\s*([^<\n]+)', caseSensitive: false),
          'grade': RegExp(r'产品等级[：:]\s*([^<\n]+)', caseSensitive: false),
          'sampleDate': RegExp(r'采样日期时间[：:]\s*([0-9\-\s:]+)', caseSensitive: false),
        };

        patterns.forEach((key, pattern) {
          final match = pattern.firstMatch(responseBody);
          if (match != null && match.group(1) != null) {
            productInfo[key] = match.group(1)!.trim();
          }
        });
      }
    } catch (e) {
      AppLogger.warning('解析网络响应失败: $e');
    }

    return productInfo;
  }

  /// 计算综合置信度
  double _calculateOverallConfidence(
    List<RecognitionResult> textResults,
    List<RecognitionResult> qrResults,
    List<CrossValidationResult> crossValidations,
    List<NetworkValidationResult> networkValidations,
  ) {
    if (textResults.isEmpty && qrResults.isEmpty) return 0.0;
    
    var totalConfidence = 0.0;
    var weightSum = 0.0;
    
    // 文字识别置信度
    for (final result in textResults) {
      final confidence = result.confidence ?? 0.0;
      totalConfidence += confidence * 0.6; // 文字识别权重60%
      weightSum += 0.6;
    }

    // 二维码识别置信度
    for (final result in qrResults) {
      final confidence = result.confidence ?? 0.0;
      totalConfidence += confidence * 0.8; // 二维码识别权重80%
      weightSum += 0.8;
    }
    
    // 交叉验证加成
    if (crossValidations.isNotEmpty) {
      final avgMatchRatio = crossValidations
          .map((cv) => cv.matchRatio)
          .reduce((a, b) => a + b) / crossValidations.length;
      totalConfidence += avgMatchRatio * 20.0; // 交叉验证最多加20分
    }

    // 网络验证加成
    if (networkValidations.isNotEmpty) {
      final validCount = networkValidations.where((nv) => nv.isValid).length;
      final validRatio = validCount / networkValidations.length;
      totalConfidence += validRatio * 15.0; // 网络验证最多加15分
    }

    return weightSum > 0 ? (totalConfidence / weightSum).clamp(0.0, 100.0) : 0.0;
  }
  
  /// 释放资源
  Future<void> dispose() async {
    if (_isInitialized) {
      await _qrCodeEngine.dispose();
      _isInitialized = false;
      AppLogger.info('🔄 统一识别服务已释放资源');
    }
  }
}

// QRCodeResult 类已移除，现在使用 RecognitionResult 统一表示所有识别结果

/// 交叉验证结果
class CrossValidationResult {
  final RecognitionResult textResult;
  final RecognitionResult qrResult;
  final double matchScore;
  final double matchRatio;
  final bool isMatched;
  final List<String> matchedElements;

  CrossValidationResult({
    required this.textResult,
    required this.qrResult,
    required this.matchScore,
    required this.matchRatio,
    required this.isMatched,
    required this.matchedElements,
  });
}

/// 网络验证结果
class NetworkValidationResult {
  final RecognitionResult qrResult;
  final String url;
  final bool isValid;
  final int? statusCode;
  final Map<String, String>? productInfo;
  final DateTime validationTime;

  NetworkValidationResult({
    required this.qrResult,
    required this.url,
    required this.isValid,
    this.statusCode,
    this.productInfo,
    required this.validationTime,
  });
}

/// 统一识别结果
class UnifiedRecognitionResult {
  final List<RecognitionResult> textResults;
  final List<RecognitionResult> qrResults;
  final List<CrossValidationResult> crossValidations;
  final List<NetworkValidationResult> networkValidations;
  final double overallConfidence;
  final bool hasQRCode;
  final bool hasText;
  final bool isConsistent;
  final bool isNetworkVerified;
  final int processingTime;
  final String preprocessedImagePath;

  UnifiedRecognitionResult({
    required this.textResults,
    required this.qrResults,
    required this.crossValidations,
    required this.networkValidations,
    required this.overallConfidence,
    required this.hasQRCode,
    required this.hasText,
    required this.isConsistent,
    required this.isNetworkVerified,
    required this.processingTime,
    required this.preprocessedImagePath,
  });
  
  UnifiedRecognitionResult copyWith({
    List<RecognitionResult>? textResults,
    List<RecognitionResult>? qrResults,
    List<CrossValidationResult>? crossValidations,
    List<NetworkValidationResult>? networkValidations,
    double? overallConfidence,
    bool? hasQRCode,
    bool? hasText,
    bool? isConsistent,
    bool? isNetworkVerified,
    int? processingTime,
    String? preprocessedImagePath,
  }) {
    return UnifiedRecognitionResult(
      textResults: textResults ?? this.textResults,
      qrResults: qrResults ?? this.qrResults,
      crossValidations: crossValidations ?? this.crossValidations,
      networkValidations: networkValidations ?? this.networkValidations,
      overallConfidence: overallConfidence ?? this.overallConfidence,
      hasQRCode: hasQRCode ?? this.hasQRCode,
      hasText: hasText ?? this.hasText,
      isConsistent: isConsistent ?? this.isConsistent,
      isNetworkVerified: isNetworkVerified ?? this.isNetworkVerified,
      processingTime: processingTime ?? this.processingTime,
      preprocessedImagePath: preprocessedImagePath ?? this.preprocessedImagePath,
    );
  }

  /// 获取最佳产品信息
  Map<String, String> getBestProductInfo() {
    final productInfo = <String, String>{};

    // 优先使用网络验证的产品信息
    for (final nv in networkValidations) {
      if (nv.isValid && nv.productInfo != null) {
        productInfo.addAll(nv.productInfo!);
        break;
      }
    }

    // 从文字识别结果中提取信息
    for (final textResult in textResults) {
      if (textResult.extractedProductCode != null) {
        productInfo['productCode'] = textResult.extractedProductCode!;
      }
      if (textResult.extractedBatchNumber != null) {
        productInfo['batchNumber'] = textResult.extractedBatchNumber!;
      }
    }

    // 从二维码内容中提取信息
    for (final qrResult in qrResults) {
      if (qrResult.qrCode != null) {
        final extracted = _extractInfoFromQRContent(qrResult.qrCode!);
        productInfo.addAll(extracted);
      }

      // 也可以从二维码的解析数据中获取信息
      if (qrResult.extractedProductCode != null) {
        productInfo['productCode'] = qrResult.extractedProductCode!;
      }
      if (qrResult.extractedBatchNumber != null) {
        productInfo['batchNumber'] = qrResult.extractedBatchNumber!;
      }
    }

    return productInfo;
  }

  /// 从二维码内容中提取信息（使用专用解析器）
  Map<String, String> _extractInfoFromQRContent(String content) {
    try {
      // 使用专用的智能解析器，避免重复代码
      final parsedData = IntelligentQRDataParser.parseQRData(content);

      final info = <String, String>{};
      if (parsedData.productCode != null) info['productCode'] = parsedData.productCode!;
      if (parsedData.batchNumber != null) info['batchNumber'] = parsedData.batchNumber!;
      if (parsedData.productionDate != null) info['date'] = parsedData.productionDate!;
      if (parsedData.productionTime != null) info['time'] = parsedData.productionTime!;
      if (parsedData.productionLine != null) info['productionLine'] = parsedData.productionLine!;
      if (parsedData.factoryCode != null) info['factoryCode'] = parsedData.factoryCode!;
      if (parsedData.qualityStatus != null) info['qualityStatus'] = parsedData.qualityStatus!;
      if (parsedData.operatorId != null) info['operatorId'] = parsedData.operatorId!;
      if (parsedData.equipmentId != null) info['equipmentId'] = parsedData.equipmentId!;
      if (parsedData.url != null) info['url'] = parsedData.url!;

      // 添加额外数据
      if (parsedData.additionalData != null) {
        info.addAll(parsedData.additionalData!);
      }

      return info;
    } catch (e) {
      AppLogger.warning('二维码内容解析失败: $e');
      return {};
    }
  }
}
