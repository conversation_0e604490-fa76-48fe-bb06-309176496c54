import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/product_database.dart';

void main() {
  group('简单产品数据库测试', () {
    test('验证新增的PP-1100N牌号', () {
      expect(ProductDatabase.hasProduct('PP-1100N'), true);
      final product = ProductDatabase.presetProducts['PP-1100N']!;
      expect(product.code, 'PP-1100N');
      expect(product.name, '聚丙烯注塑料');
      expect(product.category, 'PP');
      expect(product.priority, 7);
    });
    
    test('验证新增的HD-5502S牌号', () {
      expect(ProductDatabase.hasProduct('HD-5502S'), true);
      final product = ProductDatabase.presetProducts['HD-5502S']!;
      expect(product.code, 'HD-5502S');
      expect(product.name, '高密度聚乙烯');
      expect(product.category, 'HDPE');
      expect(product.priority, 7);
    });
    
    test('验证产品总数', () {
      final totalProducts = ProductDatabase.presetProducts.length;
      expect(totalProducts, 37); // 实际产品数量
      print('✅ 总产品数量: $totalProducts');
    });
    
    test('验证热门产品包含新增牌号', () {
      final popularProducts = ProductDatabase.getPopularProducts();
      final popularCodes = popularProducts.map((p) => p.code).toList();
      
      expect(popularCodes, contains('PP-1100N'));
      expect(popularCodes, contains('HD-5502S'));
      
      print('🔥 热门产品数量: ${popularProducts.length}');
    });
  });
}
