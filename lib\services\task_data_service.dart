import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🗄️ 任务数据服务
/// 
/// 专注于数据访问和缓存管理，不包含业务逻辑
/// 提供统一的数据访问接口，隐藏Repository实现细节
class TaskDataService {
  final TaskRepository _repository;
  
  // 内存缓存
  final Map<String, TaskModel> _taskCache = {};
  TaskModel? _currentTaskCache;
  List<TaskModel>? _allTasksCache;
  DateTime? _cacheTimestamp;
  
  // 缓存配置
  static const Duration _cacheValidDuration = Duration(minutes: 5);
  static const int _maxCacheSize = 100;
  
  // 数据变更流
  final StreamController<TaskDataEvent> _dataEventController = StreamController.broadcast();
  Stream<TaskDataEvent> get dataEvents => _dataEventController.stream;

  TaskDataService({TaskRepository? repository})
      : _repository = repository ?? TaskRepositoryImpl();

  /// 初始化数据服务
  Future<void> initialize() async {
    try {
      AppLogger.info('🗄️ 初始化TaskDataService...', tag: 'TaskData');
      await _repository.initialize();
      AppLogger.info('✅ TaskDataService初始化完成', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('❌ TaskDataService初始化失败: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 获取单个任务
  Future<TaskModel?> getTask(String taskId) async {
    try {
      // 先检查缓存
      if (_taskCache.containsKey(taskId)) {
        AppLogger.debug('📋 从缓存获取任务: $taskId', tag: 'TaskData');
        return _taskCache[taskId];
      }

      // 从Repository获取
      AppLogger.debug('📋 从Repository获取任务: $taskId', tag: 'TaskData');
      final task = await _repository.getTaskById(taskId);
      
      if (task != null) {
        // 更新缓存
        _updateTaskCache(task);
      }
      
      return task;
    } catch (e) {
      AppLogger.error('❌ 获取任务失败: $taskId, 错误: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 获取所有任务
  Future<List<TaskModel>> getAllTasks({bool forceRefresh = false}) async {
    try {
      // 检查缓存是否有效
      if (!forceRefresh && _isCacheValid() && _allTasksCache != null) {
        AppLogger.debug('📋 从缓存获取所有任务: ${_allTasksCache!.length}个', tag: 'TaskData');
        return List.from(_allTasksCache!);
      }

      // 从Repository获取
      AppLogger.debug('📋 从Repository获取所有任务', tag: 'TaskData');
      final tasks = await _repository.getAllTasks();
      
      // 更新缓存
      _updateAllTasksCache(tasks);
      
      AppLogger.info('✅ 获取所有任务成功: ${tasks.length}个', tag: 'TaskData');
      return List.from(tasks);
    } catch (e) {
      AppLogger.error('❌ 获取所有任务失败: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 保存任务
  Future<void> saveTask(TaskModel task) async {
    try {
      AppLogger.info('💾 保存任务: ${task.id}', tag: 'TaskData');
      
      // 保存到Repository
      await _repository.saveTask(task);
      
      // 更新缓存
      _updateTaskCache(task);
      _invalidateAllTasksCache();
      
      // 发送数据变更事件
      _dataEventController.add(TaskDataEvent.taskSaved(task));
      
      AppLogger.info('✅ 任务保存成功: ${task.id}', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('❌ 任务保存失败: ${task.id}, 错误: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 更新任务
  Future<void> updateTask(TaskModel task) async {
    try {
      AppLogger.info('🔄 更新任务: ${task.id}', tag: 'TaskData');
      
      // 更新到Repository
      await _repository.saveTask(task);
      
      // 更新缓存
      _updateTaskCache(task);
      _invalidateAllTasksCache();
      
      // 发送数据变更事件
      _dataEventController.add(TaskDataEvent.taskUpdated(task));
      
      AppLogger.info('✅ 任务更新成功: ${task.id}', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('❌ 任务更新失败: ${task.id}, 错误: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      AppLogger.info('🗑️ 删除任务: $taskId', tag: 'TaskData');
      
      // 从Repository删除
      await _repository.deleteTask(taskId);
      
      // 清除缓存
      _taskCache.remove(taskId);
      _invalidateAllTasksCache();
      
      // 发送数据变更事件
      _dataEventController.add(TaskDataEvent.taskDeleted(taskId));
      
      AppLogger.info('✅ 任务删除成功: $taskId', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('❌ 任务删除失败: $taskId, 错误: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 批量保存任务
  Future<void> saveTasks(List<TaskModel> tasks) async {
    try {
      AppLogger.info('💾 批量保存任务: ${tasks.length}个', tag: 'TaskData');
      
      // 批量保存到Repository
      await _repository.saveTasks(tasks);
      
      // 批量更新缓存
      for (final task in tasks) {
        _updateTaskCache(task);
      }
      _invalidateAllTasksCache();
      
      // 发送批量数据变更事件
      _dataEventController.add(TaskDataEvent.tasksBatchSaved(tasks));
      
      AppLogger.info('✅ 批量保存任务成功: ${tasks.length}个', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('❌ 批量保存任务失败: ${tasks.length}个, 错误: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 获取当前任务
  Future<TaskModel?> getCurrentTask() async {
    try {
      // 先检查缓存
      if (_currentTaskCache != null) {
        return _currentTaskCache;
      }

      // 从Repository获取
      final currentTask = await _repository.getCurrentTask();
      _currentTaskCache = currentTask;
      
      return currentTask;
    } catch (e) {
      AppLogger.error('❌ 获取当前任务失败: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task) async {
    try {
      AppLogger.info('🎯 设置当前任务: ${task?.id ?? 'null'}', tag: 'TaskData');
      
      // 保存到Repository
      await _repository.setCurrentTask(task);
      
      // 更新缓存
      _currentTaskCache = task;
      
      // 发送数据变更事件
      _dataEventController.add(TaskDataEvent.currentTaskChanged(task));
      
      AppLogger.info('✅ 当前任务设置成功', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('❌ 设置当前任务失败: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 查询任务
  Future<List<TaskModel>> queryTasks({
    String? template,
    String? productCode,
    TaskStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  }) async {
    try {
      AppLogger.debug('🔍 查询任务: template=$template, status=$status', tag: 'TaskData');
      
      // 使用Repository查询
      final allTasks = await _repository.queryTasks(
        template: template,
        startDate: startDate,
        endDate: endDate,
      );

      // 手动过滤其他条件
      var tasks = allTasks;
      if (productCode != null) {
        tasks = tasks.where((task) => task.productCode == productCode).toList();
      }
      if (status != null) {
        tasks = tasks.where((task) => task.status == status).toList();
      }
      if (limit != null && limit > 0) {
        tasks = tasks.take(limit).toList();
      }
      
      // 更新缓存
      for (final task in tasks) {
        _updateTaskCache(task);
      }
      
      AppLogger.info('✅ 查询任务成功: ${tasks.length}个', tag: 'TaskData');
      return tasks;
    } catch (e) {
      AppLogger.error('❌ 查询任务失败: $e', tag: 'TaskData');
      rethrow;
    }
  }

  /// 清除所有缓存
  void clearCache() {
    AppLogger.info('🧹 清除TaskDataService缓存', tag: 'TaskData');
    _taskCache.clear();
    _allTasksCache = null;
    _currentTaskCache = null;
    _cacheTimestamp = null;
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    return {
      'taskCacheSize': _taskCache.length,
      'allTasksCached': _allTasksCache != null,
      'currentTaskCached': _currentTaskCache != null,
      'cacheTimestamp': _cacheTimestamp?.toIso8601String(),
      'cacheValid': _isCacheValid(),
    };
  }

  /// 释放资源
  /// 获取任务统计信息
  Future<Map<String, dynamic>> getTaskStatistics() async {
    try {
      final tasks = await getAllTasks();
      final completedTasks = tasks.where((t) => t.isCompleted).length;
      final totalTasks = tasks.length;
      final totalPhotos = tasks.fold<int>(0, (sum, task) => sum + task.photos.length);

      return {
        'totalTasks': totalTasks,
        'completedTasks': completedTasks,
        'pendingTasks': totalTasks - completedTasks,
        'totalPhotos': totalPhotos,
        'completionRate': totalTasks > 0 ? completedTasks / totalTasks : 0.0,
      };
    } catch (e) {
      AppLogger.error('获取任务统计失败', error: e, tag: 'TaskData');
      rethrow;
    }
  }

  /// 备份数据
  Future<void> backupData() async {
    try {
      AppLogger.info('开始备份任务数据', tag: 'TaskData');
      // 这里可以实现具体的备份逻辑
      // 例如导出到文件或上传到云端
      AppLogger.info('任务数据备份完成', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('备份任务数据失败', error: e, tag: 'TaskData');
      rethrow;
    }
  }

  /// 恢复数据
  Future<void> restoreData() async {
    try {
      AppLogger.info('开始恢复任务数据', tag: 'TaskData');
      // 这里可以实现具体的恢复逻辑
      // 例如从文件或云端恢复数据
      AppLogger.info('任务数据恢复完成', tag: 'TaskData');
    } catch (e) {
      AppLogger.error('恢复任务数据失败', error: e, tag: 'TaskData');
      rethrow;
    }
  }

  void dispose() {
    AppLogger.info('🔄 释放TaskDataService资源', tag: 'TaskData');
    _dataEventController.close();
    clearCache();
  }

  // 私有方法

  /// 更新任务缓存
  void _updateTaskCache(TaskModel task) {
    _taskCache[task.id] = task;
    
    // 限制缓存大小
    if (_taskCache.length > _maxCacheSize) {
      final oldestKey = _taskCache.keys.first;
      _taskCache.remove(oldestKey);
    }
  }

  /// 更新所有任务缓存
  void _updateAllTasksCache(List<TaskModel> tasks) {
    _allTasksCache = List.from(tasks);
    _cacheTimestamp = DateTime.now();
    
    // 同时更新单个任务缓存
    for (final task in tasks) {
      _updateTaskCache(task);
    }
  }

  /// 使所有任务缓存失效
  void _invalidateAllTasksCache() {
    _allTasksCache = null;
    _cacheTimestamp = null;
  }

  /// 检查缓存是否有效
  bool _isCacheValid() {
    if (_cacheTimestamp == null) return false;
    final now = DateTime.now();
    return now.difference(_cacheTimestamp!) < _cacheValidDuration;
  }
}

/// 任务数据事件
class TaskDataEvent {
  final TaskDataEventType type;
  final TaskModel? task;
  final List<TaskModel>? tasks;
  final String? taskId;
  final DateTime timestamp;

  TaskDataEvent._({
    required this.type,
    this.task,
    this.tasks,
    this.taskId,
  }) : timestamp = DateTime.now();

  factory TaskDataEvent.taskSaved(TaskModel task) => TaskDataEvent._(
    type: TaskDataEventType.taskSaved,
    task: task,
  );

  factory TaskDataEvent.taskUpdated(TaskModel task) => TaskDataEvent._(
    type: TaskDataEventType.taskUpdated,
    task: task,
  );

  factory TaskDataEvent.taskDeleted(String taskId) => TaskDataEvent._(
    type: TaskDataEventType.taskDeleted,
    taskId: taskId,
  );

  factory TaskDataEvent.tasksBatchSaved(List<TaskModel> tasks) => TaskDataEvent._(
    type: TaskDataEventType.tasksBatchSaved,
    tasks: tasks,
  );

  factory TaskDataEvent.currentTaskChanged(TaskModel? task) => TaskDataEvent._(
    type: TaskDataEventType.currentTaskChanged,
    task: task,
  );
}

/// 任务数据事件类型
enum TaskDataEventType {
  taskSaved,
  taskUpdated,
  taskDeleted,
  tasksBatchSaved,
  currentTaskChanged,
}
