import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 🎨 专业级动画主题系统 - 2025版
/// 专为工业标签识别系统设计的现代化主题
class ProfessionalTheme {
  // 🎯 专业级色彩系统
  static const Color primaryDeep = Color(0xFF0A1628); // 深海蓝 - 主色调
  static const Color primaryMain = Color(0xFF1E3A8A); // 专业蓝 - 核心色
  static const Color primaryLight = Color(0xFF3B82F6); // 亮蓝 - 强调色
  static const Color accentGlow = Color(0xFF06B6D4); // 科技青 - 发光色
  static const Color accentWarm = Color(0xFFEF4444); // 警示红 - 状态色
  
  // 🌟 现代化表面色系
  static const Color surfaceDark = Color(0xFF0F172A); // 深色表面
  static const Color surfaceCard = Color(0xFF1E293B); // 卡片表面
  static const Color surfaceElevated = Color(0xFF334155); // 悬浮表面
  static const Color surfaceGlass = Color(0x1AFFFFFF); // 玻璃表面
  
  // 📝 高对比度文本系统
  static const Color textPrimary = Color(0xFFF8FAFC); // 主文本
  static const Color textSecondary = Color(0xFFCBD5E1); // 次要文本
  static const Color textTertiary = Color(0xFF94A3B8); // 辅助文本
  static const Color textOnLight = Color(0xFF1E293B); // 浅色背景文本
  
  // 🎨 功能色彩系统
  static const Color successGreen = Color(0xFF10B981); // 成功绿
  static const Color warningAmber = Color(0xFFF59E0B); // 警告橙
  static const Color errorRed = Color(0xFFEF4444); // 错误红
  static const Color infoBlue = Color(0xFF3B82F6); // 信息蓝
  
  // 🌈 渐变色系统
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryMain, primaryLight],
    stops: [0.0, 1.0],
  );
  
  static const LinearGradient glowGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [accentGlow, primaryLight],
    stops: [0.0, 1.0],
  );
  
  static const LinearGradient glassGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0x20FFFFFF),
      Color(0x10FFFFFF),
    ],
    stops: [0.0, 1.0],
  );
  
  // 🎯 现代化圆角系统
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusXXL = 32.0;
  
  // 💫 专业级阴影系统
  static const List<BoxShadow> shadowSoft = [
    BoxShadow(
      color: Color(0x10000000),
      blurRadius: 8,
      offset: Offset(0, 2),
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> shadowMedium = [
    BoxShadow(
      color: Color(0x15000000),
      blurRadius: 16,
      offset: Offset(0, 4),
      spreadRadius: -2,
    ),
    BoxShadow(
      color: Color(0x08000000),
      blurRadius: 6,
      offset: Offset(0, 2),
      spreadRadius: 0,
    ),
  ];
  
  static const List<BoxShadow> shadowLarge = [
    BoxShadow(
      color: Color(0x20000000),
      blurRadius: 24,
      offset: Offset(0, 8),
      spreadRadius: -4,
    ),
    BoxShadow(
      color: Color(0x10000000),
      blurRadius: 8,
      offset: Offset(0, 4),
      spreadRadius: -2,
    ),
  ];
  
  // ✨ 发光阴影效果
  static const List<BoxShadow> shadowGlow = [
    BoxShadow(
      color: Color(0x4006B6D4),
      blurRadius: 20,
      offset: Offset(0, 0),
      spreadRadius: 0,
    ),
    BoxShadow(
      color: Color(0x2006B6D4),
      blurRadius: 40,
      offset: Offset(0, 0),
      spreadRadius: 4,
    ),
  ];
  
  // 🎨 Material 3 主题配置
  static ThemeData get lightTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryMain,
      brightness: Brightness.light,
      primary: primaryMain,
      secondary: accentGlow,
      tertiary: successGreen,
      surface: Colors.white,
      onSurface: textOnLight,
      error: errorRed,
    );
    
    return _buildTheme(colorScheme, Brightness.light);
  }
  
  static ThemeData get darkTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: primaryMain,
      brightness: Brightness.dark,
      primary: primaryLight,
      secondary: accentGlow,
      tertiary: successGreen,
      surface: surfaceCard,
      onSurface: textPrimary,
      error: errorRed,
    );
    
    return _buildTheme(colorScheme, Brightness.dark);
  }
  
  static ThemeData _buildTheme(ColorScheme colorScheme, Brightness brightness) {
    final isDark = brightness == Brightness.dark;
    
    return ThemeData(
      useMaterial3: true,
      brightness: brightness,
      colorScheme: colorScheme,
      
      // 🎨 应用栏主题
      appBarTheme: AppBarTheme(
        backgroundColor: isDark ? surfaceDark : primaryMain,
        foregroundColor: isDark ? textPrimary : Colors.white,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: isDark ? Brightness.light : Brightness.light,
          systemNavigationBarColor: isDark ? surfaceDark : Colors.white,
          systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        ),
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: isDark ? textPrimary : Colors.white,
        ),
      ),
      
      // 📱 脚手架主题
      scaffoldBackgroundColor: isDark ? surfaceDark : Colors.grey[50],
      
      // 🃏 卡片主题
      cardTheme: CardThemeData(
        color: isDark ? surfaceCard : Colors.white,
        elevation: 0,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusL),
        ),
        margin: const EdgeInsets.all(8),
      ),
      
      // 🔘 按钮主题系统
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryMain,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: primaryMain,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryMain,
          side: BorderSide(color: primaryMain, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryMain,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusM),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          minimumSize: const Size(64, 40),
        ),
      ),
      
      // 📝 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: isDark ? surfaceElevated.withOpacity(0.3) : Colors.grey[50],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusM),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusM),
          borderSide: BorderSide(color: primaryLight, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusM),
          borderSide: BorderSide(
            color: isDark ? surfaceElevated : Colors.grey[300]!,
          ),
        ),
        labelStyle: TextStyle(
          color: isDark ? textSecondary : Colors.grey[700],
        ),
        hintStyle: TextStyle(
          color: isDark ? textTertiary : Colors.grey[500],
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      
      // 📊 进度指示器主题
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: primaryLight,
        linearTrackColor: isDark ? surfaceElevated : Colors.grey[200],
        circularTrackColor: isDark ? surfaceElevated : Colors.grey[200],
      ),
      
      // 🎭 对话框主题
      dialogTheme: DialogThemeData(
        backgroundColor: isDark ? surfaceCard : Colors.white,
        elevation: 24,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusXL),
        ),
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: isDark ? textPrimary : textOnLight,
        ),
        contentTextStyle: TextStyle(
          fontSize: 16,
          color: isDark ? textSecondary : Colors.grey[700],
        ),
      ),
      
      // 🎨 浮动操作按钮主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: accentGlow,
        foregroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusL),
        ),
      ),
      
      // 📱 底部导航栏主题
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: isDark ? surfaceCard : Colors.white,
        selectedItemColor: primaryLight,
        unselectedItemColor: isDark ? textTertiary : Colors.grey[600],
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      
      // 📝 文本主题
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.w800,
          color: isDark ? textPrimary : textOnLight,
          height: 1.1,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w700,
          color: isDark ? textPrimary : textOnLight,
          height: 1.2,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: isDark ? textPrimary : textOnLight,
          height: 1.2,
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: isDark ? textPrimary : textOnLight,
          height: 1.3,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: isDark ? textPrimary : textOnLight,
          height: 1.3,
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: isDark ? textPrimary : textOnLight,
          height: 1.3,
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: isDark ? textPrimary : textOnLight,
          height: 1.4,
        ),
        titleMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: isDark ? textSecondary : Colors.grey[700],
          height: 1.4,
        ),
        titleSmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: isDark ? textSecondary : Colors.grey[700],
          height: 1.4,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: isDark ? textSecondary : Colors.grey[800],
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: isDark ? textSecondary : Colors.grey[700],
          height: 1.5,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: isDark ? textTertiary : Colors.grey[600],
          height: 1.4,
        ),
      ),
      
      // 🎨 图标主题
      iconTheme: IconThemeData(
        color: isDark ? textSecondary : Colors.grey[700],
        size: 24,
      ),
      
      // 📏 分割线主题
      dividerTheme: DividerThemeData(
        color: isDark ? surfaceElevated : Colors.grey[200],
        thickness: 1,
        space: 1,
      ),
    );
  }
}

/// 🎨 专业级文本样式系统
class ProfessionalTextStyles {
  // 🏷️ 标题样式
  static const TextStyle heroTitle = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w800,
    color: ProfessionalTheme.textPrimary,
    height: 1.1,
    letterSpacing: -0.5,
  );
  
  static const TextStyle pageTitle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: ProfessionalTheme.textPrimary,
    height: 1.2,
    letterSpacing: -0.25,
  );
  
  static const TextStyle sectionTitle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: ProfessionalTheme.textPrimary,
    height: 1.3,
  );
  
  static const TextStyle cardTitle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: ProfessionalTheme.textPrimary,
    height: 1.3,
  );
  
  // 📝 正文样式
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: ProfessionalTheme.textSecondary,
    height: 1.5,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: ProfessionalTheme.textSecondary,
    height: 1.5,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: ProfessionalTheme.textTertiary,
    height: 1.4,
  );
  
  // 🔘 按钮样式
  static const TextStyle buttonLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: Colors.white,
    height: 1.2,
    letterSpacing: 0.25,
  );
  
  static const TextStyle buttonMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: Colors.white,
    height: 1.2,
    letterSpacing: 0.25,
  );
  
  // 🏷️ 标签样式
  static const TextStyle label = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: ProfessionalTheme.textPrimary,
    height: 1.3,
    letterSpacing: 0.5,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: 11,
    fontWeight: FontWeight.w500,
    color: ProfessionalTheme.textTertiary,
    height: 1.3,
    letterSpacing: 0.25,
  );
}

/// 🎯 专业级装饰工具类
class ProfessionalDecorations {
  // 🃏 现代化卡片装饰
  static BoxDecoration modernCard({
    bool isElevated = false,
    bool isGlass = false,
    bool hasGlow = false,
    Color? backgroundColor,
  }) {
    List<BoxShadow> shadows = [];
    
    if (hasGlow) {
      shadows.addAll(ProfessionalTheme.shadowGlow);
    } else if (isElevated) {
      shadows.addAll(ProfessionalTheme.shadowLarge);
    } else {
      shadows.addAll(ProfessionalTheme.shadowMedium);
    }
    
    return BoxDecoration(
      color: backgroundColor ?? (isGlass ? null : ProfessionalTheme.surfaceCard),
      gradient: isGlass ? ProfessionalTheme.glassGradient : null,
      borderRadius: BorderRadius.circular(ProfessionalTheme.radiusL),
      border: isGlass ? Border.all(
        color: Colors.white.withOpacity(0.1),
        width: 1,
      ) : null,
      boxShadow: shadows,
    );
  }
  
  // 🔘 现代化按钮装饰
  static BoxDecoration modernButton({
    String type = 'primary',
    bool isPressed = false,
    bool hasGlow = false,
  }) {
    LinearGradient gradient;
    List<BoxShadow> shadows = [];
    
    switch (type) {
      case 'success':
        gradient = const LinearGradient(
          colors: [ProfessionalTheme.successGreen, Color(0xFF059669)],
        );
        break;
      case 'warning':
        gradient = const LinearGradient(
          colors: [ProfessionalTheme.warningAmber, Color(0xFFD97706)],
        );
        break;
      case 'error':
        gradient = const LinearGradient(
          colors: [ProfessionalTheme.errorRed, Color(0xFFDC2626)],
        );
        break;
      case 'glow':
        gradient = ProfessionalTheme.glowGradient;
        if (hasGlow) shadows.addAll(ProfessionalTheme.shadowGlow);
        break;
      default:
        gradient = ProfessionalTheme.primaryGradient;
    }
    
    if (!hasGlow && !isPressed) {
      shadows.addAll(ProfessionalTheme.shadowMedium);
    }
    
    return BoxDecoration(
      gradient: gradient,
      borderRadius: BorderRadius.circular(ProfessionalTheme.radiusM),
      boxShadow: shadows,
    );
  }
  
  // 📝 现代化输入框装饰
  static InputDecoration modernInput({
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isGlass = false,
  }) {
    return InputDecoration(
      hintText: hintText,
      labelText: labelText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: isGlass 
          ? ProfessionalTheme.surfaceGlass 
          : ProfessionalTheme.surfaceElevated.withOpacity(0.3),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ProfessionalTheme.radiusM),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ProfessionalTheme.radiusM),
        borderSide: const BorderSide(
          color: ProfessionalTheme.primaryLight,
          width: 2,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ProfessionalTheme.radiusM),
        borderSide: BorderSide(
          color: isGlass 
              ? Colors.white.withOpacity(0.1)
              : ProfessionalTheme.surfaceElevated,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    );
  }
}