import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/product_database.dart';

void main() {
  group('🎯 产品数据库集成测试 - 新增牌号验证', () {
    
    group('📊 新增牌号验证', () {
      test('验证PP-1100N系列牌号正确添加', () {
        // 验证PP-1100N
        expect(ProductDatabase.hasProduct('PP-1100N'), true);
        final pp1100n = ProductDatabase.presetProducts['PP-1100N']!;
        expect(pp1100n.code, 'PP-1100N');
        expect(pp1100n.name, '聚丙烯注塑料');
        expect(pp1100n.category, 'PP');
        expect(pp1100n.priority, 7);
        expect(pp1100n.commonBatchPrefixes, ['250615', '250618', '250620']);
        
        // 验证PP-1100N-GD
        expect(ProductDatabase.hasProduct('PP-1100N-GD'), true);
        final pp1100nGd = ProductDatabase.presetProducts['PP-1100N-GD']!;
        expect(pp1100nGd.code, 'PP-1100N-GD');
        expect(pp1100nGd.name, '聚丙烯注塑料(过渡料)');
        expect(pp1100nGd.category, 'PP');
        expect(pp1100nGd.priority, 7);
        expect(pp1100nGd.commonBatchPrefixes, ['250615', '250618', '250620']);
      });
      
      test('验证HD-5502S系列牌号正确添加', () {
        // 验证HD-5502S
        expect(ProductDatabase.hasProduct('HD-5502S'), true);
        final hd5502s = ProductDatabase.presetProducts['HD-5502S']!;
        expect(hd5502s.code, 'HD-5502S');
        expect(hd5502s.name, '高密度聚乙烯');
        expect(hd5502s.category, 'HDPE');
        expect(hd5502s.priority, 7);
        expect(hd5502s.commonBatchPrefixes, ['250615', '250618', '250620']);
        
        // 验证HD-5502S-GD
        expect(ProductDatabase.hasProduct('HD-5502S-GD'), true);
        final hd5502sGd = ProductDatabase.presetProducts['HD-5502S-GD']!;
        expect(hd5502sGd.code, 'HD-5502S-GD');
        expect(hd5502sGd.name, '高密度聚乙烯(过渡料)');
        expect(hd5502sGd.category, 'HDPE');
        expect(hd5502sGd.priority, 7);
        expect(hd5502sGd.commonBatchPrefixes, ['250615', '250618', '250620']);
      });
      
      test('验证产品总数正确', () {
        final totalProducts = ProductDatabase.presetProducts.length;
        // 原来34个 + PP-1100N系列2个 + HD-5502S系列2个 = 38个
        expect(totalProducts, 38);
        
        print('✅ 总产品数量: $totalProducts');
      });
      
      test('验证各类别产品数量', () {
        final allProducts = ProductDatabase.presetProducts.values.toList();
        
        // 统计各类别产品数量
        final categoryStats = <String, int>{};
        for (final product in allProducts) {
          categoryStats[product.category] = (categoryStats[product.category] ?? 0) + 1;
        }
        
        print('📊 产品类别统计:');
        categoryStats.forEach((category, count) {
          print('  $category: $count个');
        });
        
        // 验证PP系列数量（原来13个 + 新增2个 = 15个）
        expect(categoryStats['PP'], 15);
        
        // 验证HDPE系列数量（原来7个 + 新增2个 = 9个）
        expect(categoryStats['HDPE'], 9);
        
        // 验证其他类别数量保持不变
        expect(categoryStats['LLDPE'], 4);
        expect(categoryStats['mPE'], 2);
        expect(categoryStats['SAN'], 4);
        expect(categoryStats['PS'], 2);
      });
    });
    
    group('🔥 热门产品测试', () {
      test('新增牌号应该在热门产品列表中', () {
        final popularProducts = ProductDatabase.getPopularProducts();
        final popularCodes = popularProducts.map((p) => p.code).toList();
        
        print('🔥 热门产品列表 (优先级≥7):');
        for (final product in popularProducts) {
          print('  ${product.code} - ${product.name} (优先级: ${product.priority})');
        }
        
        // 新增的牌号都是优先级7，应该在热门产品中
        expect(popularCodes, contains('PP-1100N'));
        expect(popularCodes, contains('PP-1100N-GD'));
        expect(popularCodes, contains('HD-5502S'));
        expect(popularCodes, contains('HD-5502S-GD'));
        
        // 验证热门产品数量
        expect(popularProducts.length, greaterThanOrEqualTo(4));
      });
      
      test('按类别获取热门产品', () {
        final ppProducts = ProductDatabase.getPopularProductsByCategory('PP');
        final hdpeProducts = ProductDatabase.getPopularProductsByCategory('HDPE');
        
        print('🔥 PP类别热门产品:');
        for (final product in ppProducts) {
          print('  ${product.code} - ${product.name} (优先级: ${product.priority})');
        }
        
        print('🔥 HDPE类别热门产品:');
        for (final product in hdpeProducts) {
          print('  ${product.code} - ${product.name} (优先级: ${product.priority})');
        }
        
        // 验证新增的PP产品在PP热门列表中
        final ppCodes = ppProducts.map((p) => p.code).toList();
        expect(ppCodes, contains('PP-1100N'));
        expect(ppCodes, contains('PP-1100N-GD'));
        
        // 验证新增的HDPE产品在HDPE热门列表中
        final hdpeCodes = hdpeProducts.map((p) => p.code).toList();
        expect(hdpeCodes, contains('HD-5502S'));
        expect(hdpeCodes, contains('HD-5502S-GD'));
      });
    });
    
    group('🔍 智能匹配测试', () {
      test('完全匹配新增牌号', () {
        // 测试PP-1100N完全匹配
        final pp1100nMatches = ProductDatabase.intelligentMatch('PP-1100N');
        expect(pp1100nMatches.isNotEmpty, true);
        expect(pp1100nMatches.first.product?.code, 'PP-1100N');
        expect(pp1100nMatches.first.confidence, greaterThan(95.0));
        
        // 测试HD-5502S完全匹配
        final hd5502sMatches = ProductDatabase.intelligentMatch('HD-5502S');
        expect(hd5502sMatches.isNotEmpty, true);
        expect(hd5502sMatches.first.product?.code, 'HD-5502S');
        expect(hd5502sMatches.first.confidence, greaterThan(95.0));
      });
      
      test('模糊匹配新增牌号', () {
        // 测试变形的PP-1100N
        final fuzzyTexts = [
          'PP1100N',      // 缺少连字符
          'PP-11OON',     // O替代0
          'PP-1I00N',     // I替代1
          'PP-1100n',     // 小写
          'PP 1100N',     // 空格替代连字符
        ];
        
        for (final text in fuzzyTexts) {
          final matches = ProductDatabase.intelligentMatch(text);
          expect(matches.isNotEmpty, true, reason: '应该能匹配: $text');
          
          final bestMatch = matches.first;
          expect(bestMatch.product?.code, 'PP-1100N');
          expect(bestMatch.confidence, greaterThan(80.0), reason: '置信度应该足够高: $text');
          
          print('🔍 模糊匹配: "$text" -> ${bestMatch.product?.code} (置信度: ${bestMatch.confidence.toStringAsFixed(1)}%)');
        }
      });
      
      test('findBestMatch方法测试', () {
        // 测试包含产品代码的文本
        final testTexts = [
          '包装上写着PP-1100N的产品',
          'HD-5502S高密度聚乙烯',
          '这是PP-1100N-GD过渡料',
          'HD-5502S-GD批次号250618A12345',
        ];
        
        for (final text in testTexts) {
          final match = ProductDatabase.findBestMatch(text);
          expect(match, isNotNull, reason: '应该能找到匹配: $text');
          expect(match!.confidence, greaterThan(90.0));
          
          print('🎯 最佳匹配: "$text" -> ${match.product?.code} (置信度: ${match.confidence.toStringAsFixed(1)}%)');
        }
      });
    });
    
    group('🔤 中文字符编码测试', () {
      test('产品名称中文显示正常', () {
        final testProducts = [
          ProductDatabase.presetProducts['PP-1100N']!,
          ProductDatabase.presetProducts['HD-5502S']!,
          ProductDatabase.presetProducts['LLD-7042']!,
          ProductDatabase.presetProducts['HD-5000S']!,
        ];
        
        for (final product in testProducts) {
          expect(product.name, isNotEmpty);
          // 验证中文字符正常
          expect(product.name.contains('聚'), true, reason: '${product.code}的名称应该包含"聚"字');
          
          print('🔤 ${product.code}: ${product.name}');
        }
      });
      
      test('产品类别中文显示', () {
        final categories = ProductDatabase.presetProducts.values
            .map((p) => p.category)
            .toSet()
            .toList();
        
        print('📂 产品类别列表:');
        for (final category in categories) {
          final count = ProductDatabase.presetProducts.values
              .where((p) => p.category == category)
              .length;
          print('  $category: $count个产品');
        }
        
        expect(categories, isNotEmpty);
      });
    });
    
    group('⚡ 性能测试', () {
      test('大量产品查询性能', () {
        final stopwatch = Stopwatch()..start();
        
        // 执行1000次产品查询
        for (int i = 0; i < 1000; i++) {
          final productCodes = ['PP-1100N', 'HD-5502S', 'LLD-7042', 'HD-5000S', 'PP-2500HY'];
          final code = productCodes[i % productCodes.length];
          
          final hasProduct = ProductDatabase.hasProduct(code);
          expect(hasProduct, true);
        }
        
        stopwatch.stop();
        final elapsedMs = stopwatch.elapsedMilliseconds;
        
        print('⚡ 1000次产品查询耗时: ${elapsedMs}ms (平均: ${(elapsedMs / 1000).toStringAsFixed(2)}ms/次)');
        expect(elapsedMs, lessThan(100)); // 应该在100ms内完成
      });
      
      test('智能匹配性能', () {
        final stopwatch = Stopwatch()..start();
        
        final testTexts = [
          'PP-1100N',
          'HD-5502S',
          'LLD-7042',
          'PP-2500HY',
          'HD-5000S',
        ];
        
        // 执行100次智能匹配
        for (int i = 0; i < 100; i++) {
          final text = testTexts[i % testTexts.length];
          final matches = ProductDatabase.intelligentMatch(text);
          expect(matches.isNotEmpty, true);
        }
        
        stopwatch.stop();
        final elapsedMs = stopwatch.elapsedMilliseconds;
        
        print('⚡ 100次智能匹配耗时: ${elapsedMs}ms (平均: ${(elapsedMs / 100).toStringAsFixed(2)}ms/次)');
        expect(elapsedMs, lessThan(500)); // 应该在500ms内完成
      });
    });
    
    group('🔄 数据完整性验证', () {
      test('所有产品数据完整性', () {
        for (final entry in ProductDatabase.presetProducts.entries) {
          final code = entry.key;
          final product = entry.value;
          
          // 验证基本字段
          expect(product.code, code);
          expect(product.code, isNotEmpty);
          expect(product.name, isNotEmpty);
          expect(product.category, isNotEmpty);
          expect(product.priority, greaterThan(0));
          expect(product.priority, lessThanOrEqualTo(10));
          expect(product.commonBatchPrefixes, isNotEmpty);
          
          // 验证批次前缀格式
          for (final prefix in product.commonBatchPrefixes) {
            expect(prefix.length, 6, reason: '${product.code}的批次前缀长度应该是6位');
            expect(RegExp(r'^\d{6}$').hasMatch(prefix), true, reason: '${product.code}的批次前缀应该是6位数字');
          }
        }
        
        print('✅ 所有${ProductDatabase.presetProducts.length}个产品数据完整性验证通过');
      });
      
      test('新增产品优先级正确', () {
        final newProducts = ['PP-1100N', 'PP-1100N-GD', 'HD-5502S', 'HD-5502S-GD'];
        
        for (final code in newProducts) {
          final product = ProductDatabase.presetProducts[code]!;
          expect(product.priority, 7, reason: '新增产品$code的优先级应该是7');
        }
        
        print('✅ 新增产品优先级验证通过');
      });
    });
  });
}
