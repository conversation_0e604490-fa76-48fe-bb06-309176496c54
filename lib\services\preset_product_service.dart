import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/product_database.dart';
import 'logging_service.dart';

/// 预设产品服务
class PresetProductService {
  static final PresetProductService _instance = PresetProductService._internal();
  static PresetProductService get instance => _instance;
  
  PresetProductService._internal();
  
  final List<ProductInfo> _presetProducts = [];
  bool _isInitialized = false;
  int _usageCount = 0;
  DateTime? _lastUsed;
  
  /// 初始化服务
  Future<void> initialize() async {
    try {
      AppLogger.info('初始化预设产品服务', tag: 'PresetProduct');
      
      // 加载预设产品数据
      await _loadPresetProducts();
      
      _isInitialized = true;
      AppLogger.info('预设产品服务初始化完成，加载了${_presetProducts.length}个产品', tag: 'PresetProduct');
    } catch (e) {
      AppLogger.error('预设产品服务初始化失败', tag: 'PresetProduct', error: e);
      rethrow;
    }
  }
  
  /// 加载预设产品
  Future<void> _loadPresetProducts() async {
    // 添加一些默认的预设产品
    _presetProducts.addAll([
      const ProductInfo(
        code: 'STD001',
        name: '标准货物',
        category: '通用',
        commonBatchPrefixes: ['STD'],
        priority: 5,
      ),
      const ProductInfo(
        code: 'FRG001',
        name: '易碎品',
        category: '特殊',
        commonBatchPrefixes: ['FRG'],
        priority: 8,
      ),
      const ProductInfo(
        code: 'HAZ001',
        name: '危险品',
        category: '危险',
        commonBatchPrefixes: ['HAZ'],
        priority: 10,
      ),
    ]);
  }
  
  /// 获取所有预设产品
  List<ProductInfo> getAllProducts() {
    _recordUsage();
    return List.unmodifiable(_presetProducts);
  }
  
  /// 根据代码获取产品
  ProductInfo? getProductByCode(String code) {
    _recordUsage();
    try {
      return _presetProducts.firstWhere((p) => p.code == code);
    } catch (e) {
      return null;
    }
  }
  
  /// 搜索产品
  List<ProductInfo> searchProducts(String query) {
    _recordUsage();
    if (query.isEmpty) return getAllProducts();
    
    final lowerQuery = query.toLowerCase();
    return _presetProducts.where((product) {
      return product.name.toLowerCase().contains(lowerQuery) ||
             product.code.toLowerCase().contains(lowerQuery) ||
             product.category.toLowerCase().contains(lowerQuery);
    }).toList();
  }
  
  /// 按分类获取产品
  List<ProductInfo> getProductsByCategory(String category) {
    _recordUsage();
    return _presetProducts.where((p) => p.category == category).toList();
  }
  
  /// 获取所有分类
  List<String> getAllCategories() {
    _recordUsage();
    return _presetProducts.map((p) => p.category).toSet().toList();
  }
  
  /// 添加预设产品
  void addPresetProduct(ProductInfo product) {
    if (!_presetProducts.any((p) => p.code == product.code)) {
      _presetProducts.add(product);
      AppLogger.info('添加预设产品: ${product.name}', tag: 'PresetProduct');
    }
  }

  /// 移除预设产品
  void removePresetProduct(String code) {
    _presetProducts.removeWhere((p) => p.code == code);
    AppLogger.info('移除预设产品: $code', tag: 'PresetProduct');
  }
  
  /// 记录使用情况
  void _recordUsage() {
    _usageCount++;
    _lastUsed = DateTime.now();
  }
  
  /// 获取使用统计
  Map<String, dynamic> getUsageStatistics() {
    return {
      'usageCount': _usageCount,
      'lastUsed': _lastUsed?.toIso8601String(),
      'productCount': _presetProducts.length,
      'categories': getAllCategories(),
      'isInitialized': _isInitialized,
    };
  }
  
  /// 重置统计
  void resetStatistics() {
    _usageCount = 0;
    _lastUsed = null;
    AppLogger.info('重置预设产品使用统计', tag: 'PresetProduct');
  }
  
  /// 检查服务状态
  bool get isInitialized => _isInitialized;
  
  /// 获取产品数量
  int get productCount => _presetProducts.length;
}
