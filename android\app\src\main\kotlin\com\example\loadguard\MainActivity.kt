package com.example.loadguard

import android.os.Handler
import android.os.Looper
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.*

/**
 * 🚀 LoadGuard MainActivity - ML Kit Text Recognition v2集成
 * 提供原生ML Kit v2性能，支持本地模型
 */
class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.loadguard/text_recognition"
    private var textRecognizerHelper: TextRecognizerHelper? = null
    private val mainHandler = Handler(Looper.getMainLooper())

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "initializeTextRecognizer" -> {
                    try {
                        textRecognizerHelper = TextRecognizerHelper(
                            context = applicationContext,
                            modelPath = "" // v2使用自动模型管理
                        )
                        result.success(true)
                    } catch (e: Exception) {
                        result.error("INITIALIZATION_ERROR", "ML Kit v2初始化失败: ${e.message ?: "未知错误"}", null)
                    }
                }

                "recognizeText" -> {
                    val imagePath = call.argument<String>("imagePath")
                    if (imagePath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "图片路径不能为空", null)
                        return@setMethodCallHandler
                    }

                    val helper = textRecognizerHelper
                    if (helper == null) {
                        result.error("NOT_INITIALIZED", "ML Kit v2未初始化", null)
                        return@setMethodCallHandler
                    }

                    // 使用协程处理异步识别，增强异常处理
                    CoroutineScope(Dispatchers.Main).launch {
                        try {
                            val recognitionResult = helper.recognizeText(imagePath)
                            result.success(recognitionResult)
                        } catch (e: SecurityException) {
                            result.error("SECURITY_ERROR", "权限不足: ${e.message}", null)
                        } catch (e: java.io.FileNotFoundException) {
                            result.error("FILE_NOT_FOUND", "图片文件不存在: $imagePath", null)
                        } catch (e: OutOfMemoryError) {
                            result.error("MEMORY_ERROR", "内存不足，请尝试压缩图片", null)
                        } catch (e: Exception) {
                            result.error("RECOGNITION_ERROR", "识别失败: ${e.message ?: "未知错误"}", null)
                        }
                    }
                }

                "getServiceInfo" -> {
                    try {
                        val serviceInfo = textRecognizerHelper?.getServiceInfo() ?: mapOf<String, Any>(
                            "isInitialized" to false,
                            "error" to "服务未初始化"
                        )
                        result.success(serviceInfo)
                    } catch (e: Exception) {
                        result.error("SERVICE_ERROR", e.message ?: "未知错误", null)
                    }
                }

                "healthCheck" -> {
                    try {
                        val isReady = textRecognizerHelper?.isReady() ?: false
                        result.success(isReady)
                    } catch (e: Exception) {
                        result.success(false)
                    }
                }

                "disposeTextRecognizer" -> {
                    try {
                        textRecognizerHelper?.close()
                        textRecognizerHelper = null
                        result.success(true)
                    } catch (e: Exception) {
                        result.error("DISPOSE_ERROR", e.message ?: "未知错误", null)
                    }
                }

                else -> result.notImplemented()
            }
        }
    }

    override fun onDestroy() {
        textRecognizerHelper?.close()
        super.onDestroy()
    }
}
