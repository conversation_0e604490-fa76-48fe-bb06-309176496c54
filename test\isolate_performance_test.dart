import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/super_fast_recognition_service.dart';
import 'package:loadguard/services/isolate_image_processor.dart';
import 'package:loadguard/utils/app_logger.dart';

void main() {
  group('🚀 Isolate并行处理性能测试', () {
    late SuperFastRecognitionService superService;
    late IsolateImageProcessor isolateProcessor;
    
    setUpAll(() async {
      AppLogger.info('🧪 初始化Isolate测试环境...');
      superService = SuperFastRecognitionService.instance;
      isolateProcessor = IsolateImageProcessor.instance;
      
      // 注意：在测试环境中，MLKit和Isolate可能有限制
      try {
        await superService.initialize();
        await isolateProcessor.initialize();
        AppLogger.info('✅ Isolate测试环境初始化完成');
      } catch (e) {
        AppLogger.warning('⚠️ 测试环境初始化警告: $e');
      }
    });
    
    tearDownAll(() {
      superService.dispose();
      isolateProcessor.dispose();
      AppLogger.info('🧹 Isolate测试环境已清理');
    });

    testWidgets('🚀 Isolate图像处理器基础功能测试', (WidgetTester tester) async {
      expect(isolateProcessor, isNotNull);
      expect(isolateProcessor.isInitialized, isTrue);
      
      AppLogger.info('✅ IsolateImageProcessor 基础结构测试通过');
    });

    testWidgets('⚡ 超快速模式 vs 智能模式性能对比', (WidgetTester tester) async {
      // 模拟测试图片路径
      final testImagePaths = [
        'photos/wechat_2025-08-04_083559_653.jpg',
        'photos/wechat_2025-08-04_083613_443.jpg',
      ];
      
      final performanceResults = <Map<String, dynamic>>[];
      
      for (final imagePath in testImagePaths) {
        final file = File(imagePath);
        if (!await file.exists()) {
          AppLogger.warning('⚠️ 测试图片不存在: $imagePath');
          continue;
        }
        
        AppLogger.info('🧪 测试图片: $imagePath');
        
        // 测试超快速模式
        final ultraFastStopwatch = Stopwatch()..start();
        try {
          final ultraFastResult = await superService.recognizeSuper(
            imagePath,
            strategy: RecognitionStrategy.ultraFast,
            onProgress: (progress, status) {
              AppLogger.debug('超快速模式进度: ${(progress * 100).toInt()}% - $status');
            },
          );
          ultraFastStopwatch.stop();
          
          // 测试智能模式
          final smartStopwatch = Stopwatch()..start();
          final smartResult = await superService.recognizeSuper(
            imagePath,
            strategy: RecognitionStrategy.smart,
            onProgress: (progress, status) {
              AppLogger.debug('智能模式进度: ${(progress * 100).toInt()}% - $status');
            },
          );
          smartStopwatch.stop();
          
          final result = {
            'imagePath': imagePath,
            'ultraFast': {
              'time': ultraFastStopwatch.elapsedMilliseconds,
              'success': ultraFastResult.success,
              'hasResult': ultraFastResult.hasAnyResult,
              'confidence': ultraFastResult.overallConfidence,
              'attempts': ultraFastResult.totalAttempts,
            },
            'smart': {
              'time': smartStopwatch.elapsedMilliseconds,
              'success': smartResult.success,
              'hasResult': smartResult.hasAnyResult,
              'confidence': smartResult.overallConfidence,
              'attempts': smartResult.totalAttempts,
              'usedIsolate': smartResult.preprocessedPath != null,
            },
          };
          
          performanceResults.add(result);
          
          AppLogger.info('📊 性能对比结果: $result');
          
          // 性能验证
          expect(ultraFastResult.success, isTrue, reason: '超快速模式应该成功');
          expect(smartResult.success, isTrue, reason: '智能模式应该成功');
          
          // 智能模式应该有更好的置信度或相同的速度
          if (smartResult.overallConfidence > ultraFastResult.overallConfidence) {
            AppLogger.info('✅ 智能模式置信度更高: ${smartResult.overallConfidence} vs ${ultraFastResult.overallConfidence}');
          } else if (smartStopwatch.elapsedMilliseconds <= ultraFastStopwatch.elapsedMilliseconds * 1.5) {
            AppLogger.info('✅ 智能模式时间可接受: ${smartStopwatch.elapsedMilliseconds}ms vs ${ultraFastStopwatch.elapsedMilliseconds}ms');
          }
          
        } catch (e) {
          AppLogger.error('❌ 性能测试失败: $e');
          performanceResults.add({
            'imagePath': imagePath,
            'error': e.toString(),
          });
        }
      }
      
      // 生成性能报告
      _generateIsolatePerformanceReport(performanceResults);
    });

    testWidgets('🔵 蓝光处理Isolate性能测试', (WidgetTester tester) async {
      final testImagePath = 'photos/wechat_2025-08-04_083559_653.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过蓝光处理测试');
        return;
      }
      
      AppLogger.info('🔵 开始蓝光处理Isolate测试...');
      final stopwatch = Stopwatch()..start();
      
      try {
        final processedPath = await isolateProcessor.processBlueBackgroundInIsolate(testImagePath);
        stopwatch.stop();
        
        // 验证处理结果
        final processedFile = File(processedPath);
        expect(processedFile.existsSync(), isTrue, reason: '处理后的文件应该存在');
        
        AppLogger.info('✅ 蓝光处理Isolate测试成功');
        AppLogger.info('📊 处理时间: ${stopwatch.elapsedMilliseconds}ms');
        AppLogger.info('📄 输出文件: $processedPath');
        
        // 性能验证
        expect(stopwatch.elapsedMilliseconds, lessThan(10000), 
               reason: '蓝光处理应在10秒内完成');
        
        // 清理测试文件
        try {
          processedFile.deleteSync();
          AppLogger.info('🧹 测试文件已清理');
        } catch (e) {
          AppLogger.warning('⚠️ 清理测试文件失败: $e');
        }
        
      } catch (e) {
        stopwatch.stop();
        AppLogger.error('❌ 蓝光处理Isolate测试失败: $e');
        fail('蓝光处理Isolate应该成功: $e');
      }
    });

    testWidgets('✨ 反光抑制Isolate性能测试', (WidgetTester tester) async {
      final testImagePath = 'photos/wechat_2025-08-04_083613_443.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过反光抑制测试');
        return;
      }
      
      AppLogger.info('✨ 开始反光抑制Isolate测试...');
      final stopwatch = Stopwatch()..start();
      
      try {
        final processedPath = await isolateProcessor.suppressReflectionInIsolate(testImagePath);
        stopwatch.stop();
        
        // 验证处理结果
        final processedFile = File(processedPath);
        expect(processedFile.existsSync(), isTrue, reason: '处理后的文件应该存在');
        
        AppLogger.info('✅ 反光抑制Isolate测试成功');
        AppLogger.info('📊 处理时间: ${stopwatch.elapsedMilliseconds}ms');
        AppLogger.info('📄 输出文件: $processedPath');
        
        // 性能验证
        expect(stopwatch.elapsedMilliseconds, lessThan(8000), 
               reason: '反光抑制应在8秒内完成');
        
        // 清理测试文件
        try {
          processedFile.deleteSync();
          AppLogger.info('🧹 测试文件已清理');
        } catch (e) {
          AppLogger.warning('⚠️ 清理测试文件失败: $e');
        }
        
      } catch (e) {
        stopwatch.stop();
        AppLogger.error('❌ 反光抑制Isolate测试失败: $e');
        fail('反光抑制Isolate应该成功: $e');
      }
    });

    testWidgets('🧠 智能预处理Isolate综合测试', (WidgetTester tester) async {
      final testImagePath = 'photos/wechat_2025-08-04_083625_280.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过智能预处理测试');
        return;
      }
      
      AppLogger.info('🧠 开始智能预处理Isolate测试...');
      final stopwatch = Stopwatch()..start();
      
      try {
        final processedPath = await isolateProcessor.smartPreprocessInIsolate(
          testImagePath,
          enableBlueBackground: true,
          enableReflectionSuppression: true,
          enableAutoEnhancement: true,
        );
        stopwatch.stop();
        
        // 验证处理结果
        final processedFile = File(processedPath);
        expect(processedFile.existsSync(), isTrue, reason: '处理后的文件应该存在');
        
        AppLogger.info('✅ 智能预处理Isolate测试成功');
        AppLogger.info('📊 处理时间: ${stopwatch.elapsedMilliseconds}ms');
        AppLogger.info('📄 输出文件: $processedPath');
        
        // 性能验证
        expect(stopwatch.elapsedMilliseconds, lessThan(15000), 
               reason: '智能预处理应在15秒内完成');
        
        // 清理测试文件
        try {
          processedFile.deleteSync();
          AppLogger.info('🧹 测试文件已清理');
        } catch (e) {
          AppLogger.warning('⚠️ 清理测试文件失败: $e');
        }
        
      } catch (e) {
        stopwatch.stop();
        AppLogger.error('❌ 智能预处理Isolate测试失败: $e');
        fail('智能预处理Isolate应该成功: $e');
      }
    });
  });
}

/// 生成Isolate性能测试报告
void _generateIsolatePerformanceReport(List<Map<String, dynamic>> results) {
  AppLogger.info('📊 === Isolate并行处理性能报告 ===');
  
  if (results.isEmpty) {
    AppLogger.warning('⚠️ 没有性能测试结果');
    return;
  }
  
  final successResults = results.where((r) => !r.containsKey('error')).toList();
  final failureResults = results.where((r) => r.containsKey('error')).toList();
  
  AppLogger.info('📈 测试总数: ${results.length}');
  AppLogger.info('✅ 成功: ${successResults.length}');
  AppLogger.info('❌ 失败: ${failureResults.length}');
  
  if (successResults.isNotEmpty) {
    // 计算平均性能
    double avgUltraFastTime = 0;
    double avgSmartTime = 0;
    double avgUltraFastConfidence = 0;
    double avgSmartConfidence = 0;
    int isolateUsageCount = 0;
    
    for (final result in successResults) {
      final ultraFast = result['ultraFast'] as Map<String, dynamic>;
      final smart = result['smart'] as Map<String, dynamic>;
      
      avgUltraFastTime += ultraFast['time'] as int;
      avgSmartTime += smart['time'] as int;
      avgUltraFastConfidence += ultraFast['confidence'] as double;
      avgSmartConfidence += smart['confidence'] as double;
      
      if (smart['usedIsolate'] == true) {
        isolateUsageCount++;
      }
    }
    
    avgUltraFastTime /= successResults.length;
    avgSmartTime /= successResults.length;
    avgUltraFastConfidence /= successResults.length;
    avgSmartConfidence /= successResults.length;
    
    AppLogger.info('⚡ 超快速模式平均时间: ${avgUltraFastTime.toStringAsFixed(0)}ms');
    AppLogger.info('🧠 智能模式平均时间: ${avgSmartTime.toStringAsFixed(0)}ms');
    AppLogger.info('⚡ 超快速模式平均置信度: ${avgUltraFastConfidence.toStringAsFixed(2)}');
    AppLogger.info('🧠 智能模式平均置信度: ${avgSmartConfidence.toStringAsFixed(2)}');
    AppLogger.info('🔄 Isolate处理使用率: ${isolateUsageCount}/${successResults.length} (${(isolateUsageCount / successResults.length * 100).toStringAsFixed(1)}%)');
    
    // 性能提升计算
    final timeRatio = avgSmartTime / avgUltraFastTime;
    final confidenceImprovement = avgSmartConfidence - avgUltraFastConfidence;
    
    AppLogger.info('📊 === 性能分析 ===');
    AppLogger.info('⏱️ 时间比率: ${timeRatio.toStringAsFixed(2)}x (智能模式 vs 超快速模式)');
    AppLogger.info('🎯 置信度提升: ${confidenceImprovement.toStringAsFixed(3)}');
    
    if (timeRatio < 2.0 && confidenceImprovement > 0.05) {
      AppLogger.info('✅ 智能模式表现优秀：在合理的时间内显著提升了准确率');
    } else if (timeRatio < 1.5) {
      AppLogger.info('✅ 智能模式速度优秀：时间开销很小');
    } else {
      AppLogger.info('⚠️ 智能模式需要优化：时间开销较大');
    }
  }
  
  // 详细结果
  AppLogger.info('📊 === 详细测试结果 ===');
  for (int i = 0; i < results.length; i++) {
    final result = results[i];
    AppLogger.info('📷 图片${i + 1}: ${result['imagePath']}');
    
    if (result.containsKey('error')) {
      AppLogger.info('   ❌ 错误: ${result['error']}');
    } else {
      final ultraFast = result['ultraFast'] as Map<String, dynamic>;
      final smart = result['smart'] as Map<String, dynamic>;
      
      AppLogger.info('   ⚡ 超快速: ${ultraFast['time']}ms, 置信度: ${ultraFast['confidence'].toStringAsFixed(2)}');
      AppLogger.info('   🧠 智能模式: ${smart['time']}ms, 置信度: ${smart['confidence'].toStringAsFixed(2)}, Isolate: ${smart['usedIsolate']}');
    }
  }
  
  AppLogger.info('📊 === Isolate并行处理报告结束 ===');
}