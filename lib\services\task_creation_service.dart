/// 🆕 任务创建服务
///
/// 专门负责任务的创建逻辑，包括：
/// - 单批次任务创建
/// - 多批次任务创建
/// - 混合任务创建
/// - 任务初始化配置
/// 从TaskService中提取，减少其复杂度
library task_creation_service;

import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/template_config.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/services/workload_assignment_service.dart';
import 'package:loadguard/services/personal_workload_history_service.dart';

/// 任务创建服务
class TaskCreationService {
  late final WorkloadAssignmentService _workloadAssignmentService;
  late final PersonalWorkloadHistoryService _workloadHistoryService;

  TaskCreationService() {
    _workloadAssignmentService = WorkloadAssignmentService();
    _workloadHistoryService = PersonalWorkloadHistoryService();
  }

  /// 创建单批次任务
  Future<TaskModel> createSingleBatchTask({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    List<String>? participants,
  }) async {
    try {
      AppLogger.info('🆕 开始创建单批次任务: $productCode-$batchNumber', tag: 'TaskCreation');
      
      // 创建批次信息
      final batchInfo = BatchInfo(
        productCode: productCode,
        batchNumber: batchNumber,
        plannedQuantity: quantity,
      );

      // 创建任务
      final task = TaskModel(
        template: template,
        productCode: productCode,
        batchNumber: batchNumber,
        quantity: quantity,
        batches: [batchInfo],
        participants: participants ?? [],
      );

      // 初始化任务配置
      await _initializeTaskConfiguration(task);

      AppLogger.info('✅ 单批次任务创建成功: ${task.id}', tag: 'TaskCreation');
      return task;
    } catch (e) {
      AppLogger.error('❌ 创建单批次任务失败: $e', tag: 'TaskCreation');
      rethrow;
    }
  }

  /// 创建多批次任务
  Future<TaskModel> createMultiBatchTask({
    required String template,
    required Map<String, int> batches,
    List<String>? participants,
  }) async {
    try {
      AppLogger.info('🆕 开始创建多批次任务: ${batches.length}个批次', tag: 'TaskCreation');
      
      // 创建批次信息列表
      final batchInfoList = <BatchInfo>[];
      for (final entry in batches.entries) {
        final parts = entry.key.split('-');
        final productCode = parts.isNotEmpty ? parts[0] : entry.key;
        final batchNumber = parts.length > 1 ? parts[1] : entry.key;
        
        batchInfoList.add(BatchInfo(
          productCode: productCode,
          batchNumber: batchNumber,
          plannedQuantity: entry.value,
        ));
      }

      // 创建任务
      final task = TaskModel(
        template: template,
        batches: batchInfoList,
        participants: participants ?? [],
      );

      // 初始化任务配置
      await _initializeTaskConfiguration(task);

      AppLogger.info('✅ 多批次任务创建成功: ${task.id}', tag: 'TaskCreation');
      return task;
    } catch (e) {
      AppLogger.error('❌ 创建多批次任务失败: $e', tag: 'TaskCreation');
      rethrow;
    }
  }

  /// 创建混合任务
  Future<TaskModel> createMixedTask({
    required String template,
    required List<BatchInfo> batches,
    List<String>? participants,
  }) async {
    try {
      AppLogger.info('🆕 开始创建混合任务: ${batches.length}个批次', tag: 'TaskCreation');
      
      // 创建任务
      final task = TaskModel(
        template: template,
        batches: batches,
        participants: participants ?? [],
      );

      // 初始化任务配置
      await _initializeTaskConfiguration(task);

      AppLogger.info('✅ 混合任务创建成功: ${task.id}', tag: 'TaskCreation');
      return task;
    } catch (e) {
      AppLogger.error('❌ 创建混合任务失败: $e', tag: 'TaskCreation');
      rethrow;
    }
  }

  /// 初始化任务配置
  Future<void> _initializeTaskConfiguration(TaskModel task) async {
    try {
      AppLogger.info('🔧 初始化任务配置: ${task.id}', tag: 'TaskCreation');
      
      // 初始化照片配置
      await _initializePhotoConfiguration(task);
      
      // 初始化工作量分配
      await _initializeWorkloadAssignment(task);
      
      // 初始化其他配置
      await _initializeOtherConfigurations(task);
      
      AppLogger.info('✅ 任务配置初始化完成', tag: 'TaskCreation');
    } catch (e) {
      AppLogger.error('❌ 任务配置初始化失败: $e', tag: 'TaskCreation');
      rethrow;
    }
  }

  /// 初始化照片配置
  Future<void> _initializePhotoConfiguration(TaskModel task) async {
    try {
      // 根据模板配置照片要求
      final photoConfigs = _getTemplateConfig(task.template);

      // 为每个批次创建照片配置
      for (final batch in task.batches) {
        final batchPhotos = _createPhotoConfigsForBatch(batch, photoConfigs);
        task.photos.addAll(batchPhotos);
      }

      AppLogger.info('📸 照片配置初始化完成: ${task.photos.length}张照片', tag: 'TaskCreation');
    } catch (e) {
      AppLogger.error('❌ 照片配置初始化失败: $e', tag: 'TaskCreation');
      rethrow;
    }
  }

  /// 初始化工作量分配
  Future<void> _initializeWorkloadAssignment(TaskModel task) async {
    try {
      if (task.participants.isNotEmpty) {
        // 使用工作量分配服务计算分配
        // TODO: 实现工作量分配逻辑
        AppLogger.info('👥 工作量分配准备: ${task.participants.length}人', tag: 'TaskCreation');

        AppLogger.info('👥 工作量分配完成: ${task.participants.length}人', tag: 'TaskCreation');
      }
    } catch (e) {
      AppLogger.error('❌ 工作量分配失败: $e', tag: 'TaskCreation');
      rethrow;
    }
  }

  /// 初始化其他配置
  Future<void> _initializeOtherConfigurations(TaskModel task) async {
    try {
      // 设置任务状态
      // TODO: 根据实际TaskModel属性设置状态
      AppLogger.info('⚙️ 任务状态初始化: ${task.id}');

      AppLogger.info('⚙️ 其他配置初始化完成', tag: 'TaskCreation');
    } catch (e) {
      AppLogger.error('❌ 其他配置初始化失败: $e', tag: 'TaskCreation');
      rethrow;
    }
  }

  /// 获取模板配置
  List<PhotoConfig> _getTemplateConfig(String template) {
    // 使用TemplateConfig的静态方法获取配置
    return TemplateConfig.getPhotoConfigs(template);
  }

  /// 为批次创建照片配置
  List<PhotoItem> _createPhotoConfigsForBatch(BatchInfo batch, List<PhotoConfig> configs) {
    final photos = <PhotoItem>[];

    // 根据PhotoConfig创建PhotoItem
    for (final config in configs) {
      photos.add(PhotoItem(
        label: config.label,
        configId: config.id,
        isRequired: config.isRequired,
        needRecognition: config.needRecognition,
      ));
    }

    return photos;
  }

  /// 验证任务创建参数
  void validateCreationParameters({
    required String template,
    String? productCode,
    String? batchNumber,
    int? quantity,
    Map<String, int>? batches,
    List<BatchInfo>? batchInfos,
  }) {
    if (template.isEmpty) {
      throw ArgumentError('模板不能为空');
    }
    
    if (productCode != null && productCode.isEmpty) {
      throw ArgumentError('产品代码不能为空');
    }
    
    if (batchNumber != null && batchNumber.isEmpty) {
      throw ArgumentError('批次号不能为空');
    }
    
    if (quantity != null && quantity <= 0) {
      throw ArgumentError('数量必须大于0');
    }
    
    if (batches != null && batches.isEmpty) {
      throw ArgumentError('批次信息不能为空');
    }
    
    if (batchInfos != null && batchInfos.isEmpty) {
      throw ArgumentError('批次信息不能为空');
    }
  }

  /// 获取创建统计信息
  Map<String, dynamic> getCreationStatistics() {
    return {
      'serviceInitialized': true,
      'supportedTemplates': ['装车任务', '卸车任务', '混合任务'],
      'maxBatchesPerTask': 50,
      'maxParticipantsPerTask': 20,
    };
  }
}
