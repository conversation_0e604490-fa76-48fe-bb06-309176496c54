import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'responsive_helper.dart';

/// 🎭 模态框助手类
/// 提供统一的底部弹出面板和对话框配置
class ModalHelper {
  /// 标准的底部弹出面板配置
  static Future<T?> showStandardBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool isScrollControlled = true,
    bool enableDrag = true,
    bool isDismissible = true,
    double? heightFactor,
    EdgeInsets? padding,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final deviceType = ResponsiveHelper.getDeviceType(context);

    // 根据设备类型调整高度因子
    final defaultHeightFactor = _getDefaultHeightFactor(deviceType);
    final effectiveHeightFactor = heightFactor ?? defaultHeightFactor;

    // 根据设备类型调整形状
    final effectiveShape = shape ?? _getDefaultShape(isMobile);

    // 根据设备类型调整背景色
    final effectiveBackgroundColor = backgroundColor ?? Colors.transparent;

    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: isScrollControlled,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      elevation: elevation ?? 0,
      backgroundColor: effectiveBackgroundColor,
      shape: effectiveShape,
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * effectiveHeightFactor,
      ),
      builder: (context) => Container(
        padding: padding ?? _getDefaultPadding(context),
        child: child,
      ),
    );
  }

  /// 显示全屏底部面板（适用于复杂内容）
  static Future<T?> showFullScreenBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool enableDrag = true,
    bool isDismissible = true,
    EdgeInsets? padding,
    Color? backgroundColor,
  }) {
    return showStandardBottomSheet<T>(
      context: context,
      child: child,
      heightFactor: 0.95,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      padding: padding,
      backgroundColor: backgroundColor,
    );
  }

  /// 显示紧凑型底部面板（适用于简单选择）
  static Future<T?> showCompactBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool enableDrag = true,
    bool isDismissible = true,
    EdgeInsets? padding,
    Color? backgroundColor,
  }) {
    return showStandardBottomSheet<T>(
      context: context,
      child: child,
      heightFactor: 0.4,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      padding: padding,
      backgroundColor: backgroundColor,
    );
  }

  /// 显示自适应对话框
  static Future<T?> showAdaptiveDialog<T>({
    required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    EdgeInsets? padding,
  }) {
    final isMobile = ResponsiveHelper.isMobile(context);

    if (isMobile) {
      // 移动设备使用底部弹出面板
      return showStandardBottomSheet<T>(
        context: context,
        child: child,
        isDismissible: barrierDismissible,
        padding: padding,
      );
    } else {
      // 桌面设备使用传统对话框
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        barrierColor: barrierColor,
        barrierLabel: barrierLabel,
        builder: (context) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: padding ?? const EdgeInsets.all(24),
            constraints: const BoxConstraints(
              maxWidth: 500,
              maxHeight: 600,
            ),
            child: child,
          ),
        ),
      );
    }
  }

  /// 显示确认对话框
  static Future<bool?> showConfirmDialog({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
    bool isDestructive = false,
  }) {
    return showAdaptiveDialog<bool>(
      context: context,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(cancelText),
              ),
              const SizedBox(width: 8),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: FilledButton.styleFrom(
                  backgroundColor: isDestructive ? Colors.red : null,
                ),
                child: Text(confirmText),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 显示加载对话框
  static void showLoadingDialog({
    required BuildContext context,
    String message = '加载中...',
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        onPopInvoked: (didPop) {}, // 不允许弹出
        child: Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 16),
                Text(message),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 隐藏当前对话框
  static void hideDialog(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
  }

  /// 创建带手势处理的对话框包装器
  static Widget createGestureWrapper({
    required Widget child,
    required BuildContext context,
    bool enableSwipeToClose = true,
    bool enableTapToClose = false,
    bool enableEscapeToClose = true,
    VoidCallback? onClose,
  }) {
    return GestureDetector(
      onTap: enableTapToClose
          ? () {
              onClose?.call();
              hideDialog(context);
            }
          : null,
      child: KeyboardListener(
        focusNode: FocusNode(),
        autofocus: enableEscapeToClose,
        onKeyEvent: enableEscapeToClose
            ? (KeyEvent event) {
                if (event is KeyDownEvent &&
                    event.logicalKey == LogicalKeyboardKey.escape) {
                  onClose?.call();
                  hideDialog(context);
                }
              }
            : null,
        child: enableSwipeToClose
            ? Dismissible(
                key: const Key('modal_dismissible'),
                direction: DismissDirection.down,
                onDismissed: (_) {
                  onClose?.call();
                },
                child: child,
              )
            : child,
      ),
    );
  }

  /// 显示带统一手势的底部面板
  static Future<T?> showGestureBottomSheet<T>({
    required BuildContext context,
    required Widget child,
    bool enableSwipeToClose = true,
    bool enableTapBackdropToClose = true,
    bool enableEscapeToClose = true,
    VoidCallback? onClose,
    double? heightFactor,
    EdgeInsets? padding,
  }) {
    return showStandardBottomSheet<T>(
      context: context,
      heightFactor: heightFactor,
      padding: padding,
      isDismissible: enableTapBackdropToClose,
      child: createGestureWrapper(
        child: child,
        context: context,
        enableSwipeToClose: enableSwipeToClose,
        enableTapToClose: false, // 避免与背景点击冲突
        enableEscapeToClose: enableEscapeToClose,
        onClose: onClose,
      ),
    );
  }

  /// 显示带统一手势的对话框
  static Future<T?> showGestureDialog<T>({
    required BuildContext context,
    required Widget child,
    bool enableTapBackdropToClose = true,
    bool enableEscapeToClose = true,
    VoidCallback? onClose,
    EdgeInsets? padding,
  }) {
    return showAdaptiveDialog<T>(
      context: context,
      barrierDismissible: enableTapBackdropToClose,
      padding: padding,
      child: createGestureWrapper(
        child: child,
        context: context,
        enableSwipeToClose: ResponsiveHelper.isMobile(context),
        enableTapToClose: false,
        enableEscapeToClose: enableEscapeToClose,
        onClose: onClose,
      ),
    );
  }

  /// 获取默认高度因子
  static double _getDefaultHeightFactor(DeviceType deviceType) {
    switch (deviceType) {
      case DeviceType.mobile:
        return 0.7;
      case DeviceType.mobileLarge:
        return 0.75;
      case DeviceType.tablet:
        return 0.6;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return 0.5;
    }
  }

  /// 获取默认形状
  static ShapeBorder _getDefaultShape(bool isMobile) {
    return RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        top: Radius.circular(isMobile ? 20 : 16),
      ),
    );
  }

  /// 获取默认内边距
  static EdgeInsets _getDefaultPadding(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);
    return EdgeInsets.all(isMobile ? 16 : 24);
  }
}

/// 统一的底部操作面板
class StandardBottomSheet extends StatelessWidget {
  final String? title;
  final Widget child;
  final List<Widget>? actions;
  final bool showHandle;
  final EdgeInsets? padding;

  const StandardBottomSheet({
    Key? key,
    this.title,
    required this.child,
    this.actions,
    this.showHandle = true,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final effectivePadding = padding ?? EdgeInsets.all(isMobile ? 16 : 24);

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showHandle)
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          if (title != null) ...[
            Padding(
              padding: effectivePadding.copyWith(bottom: 0),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title!,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            const Divider(),
          ],
          Flexible(
            child: Padding(
              padding: effectivePadding,
              child: child,
            ),
          ),
          if (actions != null) ...[
            const Divider(),
            Padding(
              padding: effectivePadding.copyWith(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: actions!,
              ),
            ),
          ],
          // 底部安全区域
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }
}
