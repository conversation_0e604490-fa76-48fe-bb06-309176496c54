import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/task_model.dart';

void main() {
  group('🗄️ Hive适配器生成测试', () {
    test('验证Hive注解是否正确添加', () {
      // 这个测试主要是验证注解是否正确添加
      // 实际的适配器生成需要build_runner
      
      // 创建测试对象验证构造函数
      final taskStatus = TaskStatus.inProgress;
      expect(taskStatus, TaskStatus.inProgress);
      
      final batchInfo = BatchInfo(
        productCode: 'PP-1100N',
        batchNumber: '250615F10422',
        plannedQuantity: 10,
      );
      expect(batchInfo.productCode, 'PP-1100N');
      expect(batchInfo.batchNumber, '250615F10422');
      expect(batchInfo.plannedQuantity, 10);
      
      final photoItem = PhotoItem(
        label: '测试照片',
        isRequired: true,
        needRecognition: true,
        recognitionFailed: false,
        isRecognitionCompleted: false,
        recognitionStatus: RecognitionStatus.pending,
        manualVerified: false,
        isCustom: false,
      );
      expect(photoItem.label, '测试照片');
      expect(photoItem.isRequired, true);
      expect(photoItem.recognitionStatus, RecognitionStatus.pending);
      
      final recognitionResult = RecognitionResult(
        isQrOcrConsistent: true,
        matchesPreset: true,
        recognitionTime: DateTime.now(),
      );
      expect(recognitionResult.isQrOcrConsistent, true);
      expect(recognitionResult.matchesPreset, true);
      
      final batchMatchResult = BatchMatchResult(
        batchId: 'batch-1',
        productCode: 'PP-1100N',
        batchNumber: '250615F10422',
        isMatched: true,
        confidence: 0.95,
      );
      expect(batchMatchResult.batchId, 'batch-1');
      expect(batchMatchResult.isMatched, true);
      expect(batchMatchResult.confidence, 0.95);
      
      final taskModel = TaskModel(
        template: '平板车',
        batches: [batchInfo],
        photos: [photoItem],
        createTime: DateTime.now(),
        participants: ['user1', 'user2'],
      );
      expect(taskModel.template, '平板车');
      expect(taskModel.batches.length, 1);
      expect(taskModel.photos.length, 1);
      expect(taskModel.participants.length, 2);
      
      print('✅ 所有Hive注解的类都可以正常创建');
    });
    
    test('验证枚举值', () {
      // 验证TaskStatus枚举
      expect(TaskStatus.values.length, 3);
      expect(TaskStatus.values, contains(TaskStatus.inProgress));
      expect(TaskStatus.values, contains(TaskStatus.completed));
      expect(TaskStatus.values, contains(TaskStatus.failed));
      
      // 验证RecognitionStatus枚举
      expect(RecognitionStatus.values.length, 5);
      expect(RecognitionStatus.values, contains(RecognitionStatus.pending));
      expect(RecognitionStatus.values, contains(RecognitionStatus.processing));
      expect(RecognitionStatus.values, contains(RecognitionStatus.completed));
      expect(RecognitionStatus.values, contains(RecognitionStatus.failed));
      expect(RecognitionStatus.values, contains(RecognitionStatus.cancelled));
      
      print('✅ 枚举值验证通过');
    });
    
    test('验证JSON序列化', () {
      // 验证对象可以序列化为JSON
      final batchInfo = BatchInfo(
        productCode: 'PP-1100N',
        batchNumber: '250615F10422',
        plannedQuantity: 10,
        recognizedQuantity: 5,
      );
      
      final json = batchInfo.toJson();
      expect(json['productCode'], 'PP-1100N');
      expect(json['batchNumber'], '250615F10422');
      expect(json['plannedQuantity'], 10);
      expect(json['recognizedQuantity'], 5);
      
      // 验证可以从JSON反序列化
      final restored = BatchInfo.fromJson(json);
      expect(restored.productCode, batchInfo.productCode);
      expect(restored.batchNumber, batchInfo.batchNumber);
      expect(restored.plannedQuantity, batchInfo.plannedQuantity);
      expect(restored.recognizedQuantity, batchInfo.recognizedQuantity);
      
      print('✅ JSON序列化验证通过');
    });
    
    test('验证TaskModel完整功能', () {
      final now = DateTime.now();
      
      final batchInfo = BatchInfo(
        productCode: 'HD-5502S',
        batchNumber: '250618F20533',
        plannedQuantity: 15,
        recognizedQuantity: 10,
      );
      
      final photoItem = PhotoItem(
        label: '车头车牌',
        isRequired: true,
        imagePath: '/path/to/image.jpg',
        needRecognition: true,
        recognitionFailed: false,
        isRecognitionCompleted: true,
        recognitionStatus: RecognitionStatus.completed,
        manualVerified: true,
        isCustom: false,
      );
      
      final taskModel = TaskModel(
        template: '集装箱',
        batches: [batchInfo],
        photos: [photoItem],
        createTime: now,
        participants: ['worker1', 'supervisor1'],
      );
      
      // 验证计算属性
      expect(taskModel.productCode, 'HD-5502S');
      expect(taskModel.batchNumber, '250618F20533');
      expect(taskModel.quantity, 15);
      expect(taskModel.status, TaskStatus.completed); // 因为必需照片已完成
      expect(taskModel.createdAt, now);
      
      // 验证JSON序列化
      final json = taskModel.toJson();
      expect(json['template'], '集装箱');
      expect(json['batches'], isA<List>());
      expect(json['photos'], isA<List>());
      expect(json['participants'], isA<List>());
      
      // 验证从JSON反序列化
      final restored = TaskModel.fromJson(json);
      expect(restored.template, taskModel.template);
      expect(restored.batches.length, taskModel.batches.length);
      expect(restored.photos.length, taskModel.photos.length);
      expect(restored.participants.length, taskModel.participants.length);
      
      print('✅ TaskModel完整功能验证通过');
    });
    
    test('验证Hive TypeId唯一性', () {
      // 验证所有TypeId都是唯一的
      final typeIds = <int>{};
      
      // 这里我们手动列出所有的TypeId
      // 在实际的适配器生成后，可以通过反射或其他方式自动检查
      final usedTypeIds = [
        0, // TaskStatus
        1, // BatchInfo
        2, // PhotoItem
        3, // RecognitionStatus
        4, // RecognitionResult
        5, // TaskModel
        6, // BatchMatchResult
      ];
      
      for (final typeId in usedTypeIds) {
        expect(typeIds.contains(typeId), false, 
               reason: 'TypeId $typeId 重复使用');
        typeIds.add(typeId);
      }
      
      expect(typeIds.length, usedTypeIds.length);
      
      print('✅ Hive TypeId唯一性验证通过');
    });
  });
}
