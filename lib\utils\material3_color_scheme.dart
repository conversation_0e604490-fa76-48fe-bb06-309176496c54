import 'package:flutter/material.dart';
import 'theme_colors.dart';

/// 🎨 Material 3 色彩方案扩展
/// 基于现有ThemeColors创建符合Material 3规范的色彩方案

class Material3ColorScheme {
  /// 🌙 深色主题色彩方案
  static ColorScheme get darkColorScheme {
    return ColorScheme.fromSeed(
      seedColor: ThemeColors.primary,
      brightness: Brightness.dark,
      // 主色调
      primary: ThemeColors.primary,
      onPrimary: ThemeColors.textOnDark,
      primaryContainer: ThemeColors.primaryLight,
      onPrimaryContainer: ThemeColors.textOnDark,

      // 辅助色
      secondary: ThemeColors.secondary,
      onSecondary: ThemeColors.textOnDark,
      secondaryContainer: ThemeColors.secondary.withValues(alpha: 0.2),
      onSecondaryContainer: ThemeColors.textOnDark,

      // 第三色
      tertiary: ThemeColors.accent,
      onTertiary: ThemeColors.textOnDark,
      tertiaryContainer: ThemeColors.accent.withValues(alpha: 0.2),
      onTertiaryContainer: ThemeColors.textOnDark,

      // 表面色
      surface: ThemeColors.cardBackgroundColor,
      onSurface: ThemeColors.textDark,
      surfaceContainerHighest: ThemeColors.cardBackgroundColor.withValues(alpha: 0.8),
      onSurfaceVariant: ThemeColors.textMedium,

      // 背景色
      background: ThemeColors.background,
      onBackground: ThemeColors.textDark,

      // 错误色
      error: ThemeColors.error,
      onError: ThemeColors.textOnDark,
      errorContainer: ThemeColors.error.withValues(alpha: 0.2),
      onErrorContainer: ThemeColors.textOnDark,

      // 轮廓色
      outline: ThemeColors.border,
      outlineVariant: ThemeColors.border.withValues(alpha: 0.5),

      // 阴影色
      shadow: Colors.black.withValues(alpha: 0.3),
      scrim: Colors.black.withValues(alpha: 0.5),

      // 反色
      inverseSurface: ThemeColors.textDark,
      onInverseSurface: ThemeColors.cardBackgroundColor,
      inversePrimary: ThemeColors.accent,
    );
  }

  /// ☀️ 浅色主题色彩方案
  static ColorScheme get lightColorScheme {
    return ColorScheme.fromSeed(
      seedColor: ThemeColors.primary,
      brightness: Brightness.light,
      // 主色调
      primary: ThemeColors.primary,
      onPrimary: ThemeColors.textOnDark,
      primaryContainer: ThemeColors.primaryLight,
      onPrimaryContainer: ThemeColors.textOnDark,

      // 辅助色
      secondary: ThemeColors.secondary,
      onSecondary: ThemeColors.textOnDark,
      secondaryContainer: ThemeColors.secondary.withValues(alpha: 0.1),
      onSecondaryContainer: ThemeColors.textDark,

      // 第三色
      tertiary: ThemeColors.accent,
      onTertiary: ThemeColors.textOnDark,
      tertiaryContainer: ThemeColors.accent.withValues(alpha: 0.1),
      onTertiaryContainer: ThemeColors.textDark,

      // 表面色
      surface: ThemeColors.cardBackgroundColor,
      onSurface: ThemeColors.textDark,
      surfaceContainerHighest: ThemeColors.cardBackgroundColor.withValues(alpha: 0.8),
      onSurfaceVariant: ThemeColors.textMedium,

      // 背景色
      background: ThemeColors.background,
      onBackground: ThemeColors.textDark,

      // 错误色
      error: ThemeColors.error,
      onError: ThemeColors.textOnDark,
      errorContainer: ThemeColors.error.withValues(alpha: 0.1),
      onErrorContainer: ThemeColors.textDark,

      // 轮廓色
      outline: ThemeColors.border,
      outlineVariant: ThemeColors.border.withValues(alpha: 0.5),

      // 阴影色
      shadow: Colors.black.withValues(alpha: 0.1),
      scrim: Colors.black.withValues(alpha: 0.3),

      // 反色
      inverseSurface: ThemeColors.textDark,
      onInverseSurface: ThemeColors.cardBackgroundColor,
      inversePrimary: ThemeColors.accent,
    );
  }

  /// 🎯 获取动态色彩方案
  static ColorScheme getColorScheme(Brightness brightness) {
    return brightness == Brightness.dark ? darkColorScheme : lightColorScheme;
  }

  /// 🌈 获取基于种子色的色彩方案
  static ColorScheme fromSeedColor(Color seedColor, Brightness brightness) {
    return ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: brightness,
    );
  }

  /// 🎨 获取功能色彩
  static Map<String, Color> getFunctionalColors(Brightness brightness) {
    final isDark = brightness == Brightness.dark;
    final opacity = isDark ? 0.8 : 1.0;

    return {
      'success': ThemeColors.success.withValues(alpha: opacity),
      'warning': ThemeColors.warning.withValues(alpha: opacity),
      'danger': ThemeColors.danger.withValues(alpha: opacity),
      'info': ThemeColors.info.withValues(alpha: opacity),
      'yellow': ThemeColors.yellow.withValues(alpha: opacity),
    };
  }

  /// 🎨 获取渐变色彩
  static List<LinearGradient> getGradients(Brightness brightness) {
    return [
      ThemeColors.primaryGradient,
      ThemeColors.blueGradient,
      ThemeColors.greenGradient,
      ThemeColors.yellowGradient,
      ThemeColors.orangeGradient,
      ThemeColors.purpleGradient,
      ThemeColors.tealGradient,
    ];
  }
}
