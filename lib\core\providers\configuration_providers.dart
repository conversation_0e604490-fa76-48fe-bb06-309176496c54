import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:riverpod_annotation/riverpod_annotation.dart';
// import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../../utils/app_logger.dart';
import '../../services/enterprise_license_service.dart';

// 临时禁用代码生成，避免编译错误
// part 'configuration_providers.g.dart';
// part 'configuration_providers.freezed.dart';

/// 关于页面状态模型 - 简化版本
class AboutPageState {
  final String appName;
  final String version;
  final String buildNumber;
  final String packageName;
  final Map<String, dynamic> systemInfo;
  final Map<String, dynamic> licenseInfo;
  final List<Map<String, dynamic>> dependencies;
  final bool isLoading;
  final String errorMessage;
  final bool showDebugInfo;

  const AboutPageState({
    this.appName = '',
    this.version = '',
    this.buildNumber = '',
    this.packageName = '',
    this.systemInfo = const {},
    this.licenseInfo = const {},
    this.dependencies = const [],
    this.isLoading = false,
    this.errorMessage = '',
    this.showDebugInfo = false,
  });

  AboutPageState copyWith({
    String? appName,
    String? version,
    String? buildNumber,
    String? packageName,
    Map<String, dynamic>? systemInfo,
    Map<String, dynamic>? licenseInfo,
    List<Map<String, dynamic>>? dependencies,
    bool? isLoading,
    String? errorMessage,
    bool? showDebugInfo,
  }) {
    return AboutPageState(
      appName: appName ?? this.appName,
      version: version ?? this.version,
      buildNumber: buildNumber ?? this.buildNumber,
      packageName: packageName ?? this.packageName,
      systemInfo: systemInfo ?? this.systemInfo,
      licenseInfo: licenseInfo ?? this.licenseInfo,
      dependencies: dependencies ?? this.dependencies,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      showDebugInfo: showDebugInfo ?? this.showDebugInfo,
    );
  }
}

/// 关于页面Provider - 简化版本
class AboutPageNotifier extends StateNotifier<AboutPageState> {
  AboutPageNotifier() : super(const AboutPageState()) {
    _loadAppInfo();
  }

  /// 加载应用信息
  Future<void> _loadAppInfo() async {
    state = state.copyWith(isLoading: true);

    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final systemInfo = await _getSystemInfo();
      final licenseInfo = await _getLicenseInfo();
      final dependencies = await _getDependencies();

      state = state.copyWith(
        appName: packageInfo.appName,
        version: packageInfo.version,
        buildNumber: packageInfo.buildNumber,
        packageName: packageInfo.packageName,
        systemInfo: systemInfo,
        licenseInfo: licenseInfo,
        dependencies: dependencies,
        isLoading: false,
      );

      AppLogger.info('应用信息加载完成', tag: 'AboutPage');
    } catch (e) {
      AppLogger.error('应用信息加载失败', error: e, tag: 'AboutPage');
      state = state.copyWith(
        isLoading: false,
        errorMessage: '应用信息加载失败: $e',
      );
    }
  }

  Future<Map<String, dynamic>> _getSystemInfo() async {
    return {
      'platform': 'Android/iOS',
      'dartVersion': '3.0.0',
      'flutterVersion': '3.10.0',
      'mlKitVersion': '0.15.0',
    };
  }

  Future<Map<String, dynamic>> _getLicenseInfo() async {
    try {
      final licenseService = EnterpriseLicenseService();
      final status = await licenseService.getLicenseStatus();
      return {
        'type': status['userRole']?.toString() ?? '企业版授权',
        'isValid': status['isValid'] ?? false,
        'remainingDays': status['remainingDays'] ?? 0,
      };
    } catch (e) {
      return {'error': '许可证信息获取失败'};
    }
  }

  Future<List<Map<String, dynamic>>> _getDependencies() async {
    return [
      {'name': 'flutter', 'version': '3.10.0', 'license': 'BSD-3-Clause'},
      {'name': 'google_mlkit_text_recognition', 'version': '0.15.0', 'license': 'Apache-2.0'},
      {'name': 'riverpod', 'version': '2.3.0', 'license': 'MIT'},
      {'name': 'hive', 'version': '2.2.3', 'license': 'Apache-2.0'},
      {'name': 'go_router', 'version': '7.1.1', 'license': 'BSD-3-Clause'},
    ];
  }

  void toggleDebugInfo() {
    state = state.copyWith(showDebugInfo: !state.showDebugInfo);
    AppLogger.info('调试信息显示切换: ${state.showDebugInfo}', tag: 'AboutPage');
  }

  void clearError() {
    state = state.copyWith(errorMessage: '');
  }

  Future<void> refresh() async {
    await _loadAppInfo();
  }
}

// Provider定义
final aboutPageProvider = StateNotifierProvider<AboutPageNotifier, AboutPageState>((ref) {
  return AboutPageNotifier();
});

/// 性能统计状态模型 - 简化版本
class PerformanceStatsState {
  final Map<String, dynamic> performanceMetrics;
  final List<Map<String, dynamic>> memoryUsage;
  final List<Map<String, dynamic>> cpuUsage;
  final List<Map<String, dynamic>> networkStats;
  final List<Map<String, dynamic>> mlKitPerformance;
  final bool isLoading;
  final String errorMessage;
  final bool isMonitoring;
  final String monitoringMode;

  const PerformanceStatsState({
    this.performanceMetrics = const {},
    this.memoryUsage = const [],
    this.cpuUsage = const [],
    this.networkStats = const [],
    this.mlKitPerformance = const [],
    this.isLoading = false,
    this.errorMessage = '',
    this.isMonitoring = false,
    this.monitoringMode = 'realtime',
  });

  PerformanceStatsState copyWith({
    Map<String, dynamic>? performanceMetrics,
    List<Map<String, dynamic>>? memoryUsage,
    List<Map<String, dynamic>>? cpuUsage,
    List<Map<String, dynamic>>? networkStats,
    List<Map<String, dynamic>>? mlKitPerformance,
    bool? isLoading,
    String? errorMessage,
    bool? isMonitoring,
    String? monitoringMode,
  }) {
    return PerformanceStatsState(
      performanceMetrics: performanceMetrics ?? this.performanceMetrics,
      memoryUsage: memoryUsage ?? this.memoryUsage,
      cpuUsage: cpuUsage ?? this.cpuUsage,
      networkStats: networkStats ?? this.networkStats,
      mlKitPerformance: mlKitPerformance ?? this.mlKitPerformance,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isMonitoring: isMonitoring ?? this.isMonitoring,
      monitoringMode: monitoringMode ?? this.monitoringMode,
    );
  }
}

/// 性能统计Provider - 简化版本
class PerformanceStatsNotifier extends StateNotifier<PerformanceStatsState> {
  PerformanceStatsNotifier() : super(const PerformanceStatsState()) {
    _loadPerformanceData();
  }

  Future<void> _loadPerformanceData() async {
    state = state.copyWith(isLoading: true);

    try {
      final performanceMetrics = {
        'cpuUsage': 45.2,
        'memoryUsage': 128.5,
        'recognitionSpeed': 150.0,
        'accuracy': 98.5,
      };

      final memoryUsage = <Map<String, dynamic>>[
        {'timestamp': DateTime.now().millisecondsSinceEpoch, 'usage': 128.5},
      ];

      final cpuUsage = <Map<String, dynamic>>[
        {'timestamp': DateTime.now().millisecondsSinceEpoch, 'usage': 45.2},
      ];

      final networkStats = <Map<String, dynamic>>[
        {'requests': 150, 'responses': 148, 'errors': 2},
      ];

      final mlKitPerformance = <Map<String, dynamic>>[
        {'recognitions': 1250, 'averageTime': 150.0, 'accuracy': 98.5},
      ];

      state = state.copyWith(
        performanceMetrics: performanceMetrics,
        memoryUsage: memoryUsage,
        cpuUsage: cpuUsage,
        networkStats: networkStats,
        mlKitPerformance: mlKitPerformance,
        isLoading: false,
      );

      AppLogger.info('性能数据加载完成', tag: 'PerformanceStats');
    } catch (e) {
      AppLogger.error('性能数据加载失败', error: e, tag: 'PerformanceStats');
      state = state.copyWith(
        isLoading: false,
        errorMessage: '性能数据加载失败: $e',
      );
    }
  }

  Future<void> startMonitoring() async {
    try {
      state = state.copyWith(isMonitoring: true);
      AppLogger.info('性能监控已启动', tag: 'PerformanceStats');
    } catch (e) {
      AppLogger.error('启动性能监控失败', error: e, tag: 'PerformanceStats');
      state = state.copyWith(errorMessage: '启动监控失败: $e');
    }
  }

  Future<void> stopMonitoring() async {
    try {
      state = state.copyWith(isMonitoring: false);
      AppLogger.info('性能监控已停止', tag: 'PerformanceStats');
    } catch (e) {
      AppLogger.error('停止性能监控失败', error: e, tag: 'PerformanceStats');
      state = state.copyWith(errorMessage: '停止监控失败: $e');
    }
  }

  void updateMonitoringMode(String mode) {
    state = state.copyWith(monitoringMode: mode);
    AppLogger.info('监控模式更新: $mode', tag: 'PerformanceStats');
  }

  Future<String?> exportPerformanceReport() async {
    try {
      final reportPath = '/reports/performance_${DateTime.now().millisecondsSinceEpoch}.json';
      AppLogger.info('性能报告导出成功: $reportPath', tag: 'PerformanceStats');
      return reportPath;
    } catch (e) {
      AppLogger.error('性能报告导出失败', error: e, tag: 'PerformanceStats');
      state = state.copyWith(errorMessage: '报告导出失败: $e');
      return null;
    }
  }

  void clearError() {
    state = state.copyWith(errorMessage: '');
  }

  Future<void> refresh() async {
    await _loadPerformanceData();
  }
}

// Provider定义
final performanceStatsProvider = StateNotifierProvider<PerformanceStatsNotifier, PerformanceStatsState>((ref) {
  return PerformanceStatsNotifier();
});