import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/repositories/config_repository.dart';
import 'package:loadguard/repositories/config_repository_impl.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';
import 'package:loadguard/models/config_models.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 配置数据迁移服务
/// 负责将硬编码数据迁移到动态存储，并管理数据版本
class ConfigMigrationService {
  final ConfigRepository _configRepository;
  
  ConfigMigrationService(this._configRepository);
  
  /// 检查并执行数据迁移
  Future<MigrationResult> checkAndMigrate() async {
    try {
      AppLogger.info('🔍 开始检查配置数据迁移状态...');
      
      final migrationStatus = await _checkMigrationStatus();
      
      if (migrationStatus.needsMigration) {
        AppLogger.info('📦 检测到需要迁移的数据，开始迁移过程...');
        return await _performMigration(migrationStatus);
      } else {
        AppLogger.info('✅ 配置数据已是最新版本，无需迁移');
        return MigrationResult.success(
          message: '配置数据已是最新版本',
          migratedItems: 0,
        );
      }
    } catch (e) {
      AppLogger.error('❌ 配置数据迁移检查失败', error: e);
      return MigrationResult.failure(
        message: '迁移检查失败: $e',
        error: e,
      );
    }
  }
  
  /// 强制重新迁移所有数据
  Future<MigrationResult> forceMigration() async {
    try {
      AppLogger.info('🔄 开始强制重新迁移所有配置数据...');
      
      // 清除现有配置数据
      await _clearExistingConfigs();
      
      // 执行完整迁移
      await _configRepository.migrateFromLegacyData();
      
      // 设置迁移版本标记
      await _setMigrationVersion();
      
      AppLogger.info('✅ 强制迁移完成');
      return MigrationResult.success(
        message: '强制迁移完成',
        migratedItems: 88, // 88名工作人员 + 其他配置
      );
    } catch (e) {
      AppLogger.error('❌ 强制迁移失败', error: e);
      return MigrationResult.failure(
        message: '强制迁移失败: $e',
        error: e,
      );
    }
  }
  
  /// 检查迁移状态
  Future<MigrationStatus> _checkMigrationStatus() async {
    try {
      // 检查工作人员数据
      final workers = await _configRepository.getAllWorkers();
      final hasWorkers = workers.isNotEmpty;
      
      // 检查仓库数据
      final warehouses = await _configRepository.getAllWarehouses();
      final hasWarehouses = warehouses.isNotEmpty;
      
      // 检查模板数据
      final templates = await _configRepository.getAllTemplates();
      final hasTemplates = templates.isNotEmpty;
      
      // 检查迁移版本标记
      final migrationVersion = await _getMigrationVersion();
      final isLatestVersion = migrationVersion == _currentMigrationVersion;
      
      final needsMigration = !hasWorkers || !hasWarehouses || !hasTemplates || !isLatestVersion;
      
      return MigrationStatus(
        needsMigration: needsMigration,
        hasWorkers: hasWorkers,
        hasWarehouses: hasWarehouses,
        hasTemplates: hasTemplates,
        currentVersion: migrationVersion,
        targetVersion: _currentMigrationVersion,
        workerCount: workers.length,
        warehouseCount: warehouses.length,
        templateCount: templates.length,
      );
    } catch (e) {
      AppLogger.error('❌ 检查迁移状态失败', error: e);
      rethrow;
    }
  }
  
  /// 执行迁移
  Future<MigrationResult> _performMigration(MigrationStatus status) async {
    try {
      int migratedItems = 0;
      final migrationSteps = <String>[];
      
      // 执行数据迁移
      await _configRepository.migrateFromLegacyData();
      
      // 统计迁移的数据
      if (!status.hasWorkers) {
        final workers = await _configRepository.getAllWorkers();
        migratedItems += workers.length;
        migrationSteps.add('迁移工作人员: ${workers.length}人');
      }
      
      if (!status.hasWarehouses) {
        final warehouses = await _configRepository.getAllWarehouses();
        migratedItems += warehouses.length;
        migrationSteps.add('迁移仓库: ${warehouses.length}个');
      }
      
      if (!status.hasTemplates) {
        final templates = await _configRepository.getAllTemplates();
        migratedItems += templates.length;
        migrationSteps.add('迁移模板: ${templates.length}个');
      }
      
      // 设置迁移版本标记
      await _setMigrationVersion();
      
      AppLogger.info('✅ 数据迁移完成: $migratedItems项');
      return MigrationResult.success(
        message: '数据迁移完成',
        migratedItems: migratedItems,
        migrationSteps: migrationSteps,
      );
    } catch (e) {
      AppLogger.error('❌ 数据迁移失败', error: e);
      return MigrationResult.failure(
        message: '数据迁移失败: $e',
        error: e,
      );
    }
  }
  
  /// 清除现有配置数据
  Future<void> _clearExistingConfigs() async {
    try {
      // 这里可以实现清除逻辑，但要小心不要删除用户自定义的配置
      AppLogger.info('🧹 清除现有配置数据...');
      // 实际实现中可能需要更精细的清除策略
    } catch (e) {
      AppLogger.error('❌ 清除配置数据失败', error: e);
      rethrow;
    }
  }
  
  /// 获取迁移版本
  Future<String> _getMigrationVersion() async {
    try {
      final versionConfig = await _configRepository.getSystemConfigByKey('migration_version');
      return versionConfig?.value.toString() ?? '0.0.0';
    } catch (e) {
      return '0.0.0'; // 默认版本
    }
  }
  
  /// 设置迁移版本
  Future<void> _setMigrationVersion() async {
    try {
      final versionConfig = SystemConfig(
        key: 'migration_version',
        value: _currentMigrationVersion,
        description: '配置数据迁移版本标记',
        category: 'migration',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      await _configRepository.saveSystemConfig(versionConfig);
    } catch (e) {
      AppLogger.error('❌ 设置迁移版本失败', error: e);
      rethrow;
    }
  }
  
  /// 当前迁移版本
  static const String _currentMigrationVersion = '1.0.0';
}

/// 迁移状态
class MigrationStatus {
  final bool needsMigration;
  final bool hasWorkers;
  final bool hasWarehouses;
  final bool hasTemplates;
  final String currentVersion;
  final String targetVersion;
  final int workerCount;
  final int warehouseCount;
  final int templateCount;
  
  const MigrationStatus({
    required this.needsMigration,
    required this.hasWorkers,
    required this.hasWarehouses,
    required this.hasTemplates,
    required this.currentVersion,
    required this.targetVersion,
    required this.workerCount,
    required this.warehouseCount,
    required this.templateCount,
  });
  
  @override
  String toString() {
    return 'MigrationStatus(needsMigration: $needsMigration, workers: $workerCount, warehouses: $warehouseCount, templates: $templateCount, version: $currentVersion -> $targetVersion)';
  }
}

/// 迁移结果
class MigrationResult {
  final bool success;
  final String message;
  final int migratedItems;
  final List<String> migrationSteps;
  final Object? error;
  
  const MigrationResult._({
    required this.success,
    required this.message,
    required this.migratedItems,
    required this.migrationSteps,
    this.error,
  });
  
  factory MigrationResult.success({
    required String message,
    required int migratedItems,
    List<String> migrationSteps = const [],
  }) {
    return MigrationResult._(
      success: true,
      message: message,
      migratedItems: migratedItems,
      migrationSteps: migrationSteps,
    );
  }
  
  factory MigrationResult.failure({
    required String message,
    required Object error,
  }) {
    return MigrationResult._(
      success: false,
      message: message,
      migratedItems: 0,
      migrationSteps: const [],
      error: error,
    );
  }
  
  @override
  String toString() {
    return 'MigrationResult(success: $success, message: $message, migratedItems: $migratedItems)';
  }
}

/// ConfigMigrationService Provider
final configMigrationServiceProvider = Provider<ConfigMigrationService>((ref) {
  final configRepository = ConfigRepositoryImpl(dataSource: HiveConfigDataSource());
  return ConfigMigrationService(configRepository);
});
