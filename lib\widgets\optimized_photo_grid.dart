import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/models/task_model.dart';
import 'dart:io';

/// 📸 【性能优化版】照片网格组件
/// 
/// 优化特性：
/// 1. 减少Widget重建 - 使用const构造函数和memorization
/// 2. 图片懒加载 - 只加载可见区域的图片
/// 3. 缓存机制 - 避免重复计算状态
class OptimizedTaskPhotoGrid extends ConsumerStatefulWidget {
  final TaskModel task;
  final Function(String) onTakePicture;
  final bool showStatusBadge;
  final void Function(PhotoItem)? onShowRecognitionDetail;
  final VoidCallback? onShowAllResults;
  final void Function(PhotoItem)? onRetryRecognition;
  final void Function(String)? onDeleteCustomPhoto;

  const OptimizedTaskPhotoGrid({
    Key? key,
    required this.task,
    required this.onTakePicture,
    this.showStatusBadge = false,
    this.onShowRecognitionDetail,
    this.onShowAllResults,
    this.onRetryRecognition,
    this.onDeleteCustomPhoto,
  }) : super(key: key);

  @override
  ConsumerState<OptimizedTaskPhotoGrid> createState() => _OptimizedTaskPhotoGridState();
}

class _OptimizedTaskPhotoGridState extends ConsumerState<OptimizedTaskPhotoGrid> {
  final Map<String, PhotoCardState> _photoStateCache = {}; // 状态缓存
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        // 🚀 性能优化：添加缓存扩展，减少Widget销毁重建
        cacheExtent: 200.0,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
        ),
        itemCount: widget.task.photos.length,
        itemBuilder: (context, index) {
          final photo = widget.task.photos[index];
          
          // 🚀 性能优化：使用状态缓存，避免重复计算
          final cachedState = _getOrUpdatePhotoState(photo);
          
          return OptimizedPhotoCard(
            key: ValueKey(photo.id), // 使用稳定的key
            photo: photo,
            photoState: cachedState,
            onTap: () => _handlePhotoTap(photo),
            onLongPress: () => _handlePhotoLongPress(photo),
            onRetryRecognition: widget.onRetryRecognition,
            onDeleteCustomPhoto: widget.onDeleteCustomPhoto,
            showStatusBadge: widget.showStatusBadge,
          );
        },
      ),
    );
  }
  
  /// 🚀 获取或更新照片状态缓存
  PhotoCardState _getOrUpdatePhotoState(PhotoItem photo) {
    final cacheKey = '${photo.id}_${photo.imagePath}_${photo.recognitionStatus}_${photo.isVerified}';
    
    if (!_photoStateCache.containsKey(cacheKey)) {
      _photoStateCache[cacheKey] = PhotoCardState.fromPhoto(photo);
    }
    
    return _photoStateCache[cacheKey]!;
  }
  
  void _handlePhotoTap(PhotoItem photo) {
    if (photo.imagePath != null) {
      _showPhotoPreview(photo);
    } else {
      widget.onTakePicture(photo.id);
    }
  }
  
  void _handlePhotoLongPress(PhotoItem photo) {
    if (photo.imagePath != null) {
      _showPhotoOptions(photo);
    }
  }
  
  void _showPhotoPreview(PhotoItem photo) {
    // 现有的照片预览逻辑
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PhotoPreviewDialog(photo: photo),
      ),
    );
  }
  
  void _showPhotoOptions(PhotoItem photo) {
    showModalBottomSheet(
      context: context,
      builder: (context) => PhotoOptionsSheet(
        photo: photo,
        onRetryRecognition: widget.onRetryRecognition,
        onDeleteCustomPhoto: widget.onDeleteCustomPhoto,
      ),
    );
  }
}

/// 🚀 性能优化的照片卡片组件
class OptimizedPhotoCard extends StatelessWidget {
  final PhotoItem photo;
  final PhotoCardState photoState;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final void Function(PhotoItem)? onRetryRecognition;
  final void Function(String)? onDeleteCustomPhoto;
  final bool showStatusBadge;
  
  const OptimizedPhotoCard({
    Key? key,
    required this.photo,
    required this.photoState,
    required this.onTap,
    required this.onLongPress,
    this.onRetryRecognition,
    this.onDeleteCustomPhoto,
    required this.showStatusBadge,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: photoState.hasImage ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: photoState.borderColor,
          width: photoState.isHighlighted ? 3 : 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // 主要内容
            _buildMainContent(),
            // 状态指示器
            if (showStatusBadge) _buildStatusBadge(),
            // 加载动画
            if (photoState.isLoading) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMainContent() {
    if (photoState.hasImage) {
      return _buildImageContent();
    } else {
      return _buildPlaceholderContent();
    }
  }
  
  Widget _buildImageContent() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 🚀 性能优化：使用Image.file的缓存机制
          Image.file(
            File(photo.imagePath!),
            fit: BoxFit.cover,
            cacheWidth: 200, // 限制缓存大小
            cacheHeight: 200,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey[300],
                child: const Icon(Icons.error, color: Colors.red),
              );
            },
          ),
          // 遮罩层
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.3),
                ],
              ),
            ),
          ),
          // 状态文本
          Positioned(
            bottom: 8,
            left: 8,
            right: 8,
            child: Text(
              photoState.statusText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPlaceholderContent() {
    return Container(
      decoration: BoxDecoration(
        color: photoState.isRequired ? Colors.orange[50] : Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: photoState.isRequired ? Colors.orange : Colors.blue,
          width: 2,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.camera_alt,
            size: 48,
            color: photoState.isRequired ? Colors.orange : Colors.blue,
          ),
          const SizedBox(height: 8),
          Text(
            photo.description ?? '照片${photo.id}',
            style: TextStyle(
              color: photoState.isRequired ? Colors.orange[800] : Colors.blue[800],
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          if (photoState.isRequired)
            const Text(
              '必拍',
              style: TextStyle(
                color: Colors.orange,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildStatusBadge() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: photoState.statusColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(
          photoState.statusBadgeText,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
  
  Widget _buildLoadingOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      ),
    );
  }
}

/// 📊 照片卡片状态类 - 用于缓存计算结果
class PhotoCardState {
  final bool hasImage;
  final bool isRequired;
  final bool isLoading;
  final bool isHighlighted;
  final Color borderColor;
  final Color statusColor;
  final String statusText;
  final String statusBadgeText;
  
  const PhotoCardState({
    required this.hasImage,
    required this.isRequired,
    required this.isLoading,
    required this.isHighlighted,
    required this.borderColor,
    required this.statusColor,
    required this.statusText,
    required this.statusBadgeText,
  });
  
  factory PhotoCardState.fromPhoto(PhotoItem photo) {
    final hasImage = photo.imagePath != null;
    final isRequired = photo.isRequired;
    final needRecognition = photo.needRecognition;
    final recognitionStatus = photo.recognitionStatus;
    final isVerified = photo.isVerified;
    
    // 状态计算逻辑
    bool isLoading = false;
    bool isHighlighted = false;
    Color borderColor = Colors.grey[300]!;
    Color statusColor = Colors.grey;
    String statusText = '';
    String statusBadgeText = '';
    
    if (hasImage && needRecognition) {
      switch (recognitionStatus) {
        case RecognitionStatus.processing:
          isLoading = true;
          isHighlighted = true;
          borderColor = Colors.blue;
          statusColor = Colors.blue;
          statusText = 'AI识别中...';
          statusBadgeText = '识别中';
          break;
        case RecognitionStatus.completed:
          isHighlighted = isVerified;
          borderColor = isVerified ? Colors.green : Colors.orange;
          statusColor = isVerified ? Colors.green : Colors.orange;
          statusText = isVerified ? '识别完成' : '待确认';
          statusBadgeText = isVerified ? '已完成' : '待确认';
          break;
        case RecognitionStatus.failed:
          isHighlighted = true;
          borderColor = Colors.red;
          statusColor = Colors.red;
          statusText = '识别失败';
          statusBadgeText = '失败';
          break;
        default:
          borderColor = Colors.grey;
          statusColor = Colors.grey;
          statusText = '等待识别';
          statusBadgeText = '等待';
      }
    } else if (hasImage) {
      borderColor = Colors.green;
      statusColor = Colors.green;
      statusText = '已拍照';
      statusBadgeText = '完成';
    } else if (isRequired) {
      borderColor = Colors.orange;
      statusColor = Colors.orange;
      statusText = '必拍照片';
      statusBadgeText = '必拍';
    }
    
    return PhotoCardState(
      hasImage: hasImage,
      isRequired: isRequired,
      isLoading: isLoading,
      isHighlighted: isHighlighted,
      borderColor: borderColor,
      statusColor: statusColor,
      statusText: statusText,
      statusBadgeText: statusBadgeText,
    );
  }
}

/// 📷 照片预览对话框
class PhotoPreviewDialog extends StatelessWidget {
  final PhotoItem photo;
  
  const PhotoPreviewDialog({Key? key, required this.photo}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          photo.description ?? '照片${photo.id}',
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: Center(
        child: InteractiveViewer(
          child: Image.file(
            File(photo.imagePath!),
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}

/// 📋 照片操作选项表
class PhotoOptionsSheet extends StatelessWidget {
  final PhotoItem photo;
  final void Function(PhotoItem)? onRetryRecognition;
  final void Function(String)? onDeleteCustomPhoto;
  
  const PhotoOptionsSheet({
    Key? key,
    required this.photo,
    this.onRetryRecognition,
    this.onDeleteCustomPhoto,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.refresh),
            title: const Text('重新识别'),
            onTap: () {
              Navigator.of(context).pop();
              onRetryRecognition?.call(photo);
            },
          ),
          if (photo.isCustom)
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('删除照片', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.of(context).pop();
                onDeleteCustomPhoto?.call(photo.id);
              },
            ),
          ListTile(
            leading: const Icon(Icons.close),
            title: const Text('取消'),
            onTap: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}