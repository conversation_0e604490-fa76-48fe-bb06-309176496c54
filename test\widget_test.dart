// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:loadguard/main.dart';

void main() {
  testWidgets('LoadGuard app basic smoke test', (WidgetTester tester) async {
    // Create a simple router for testing
    final router = GoRouter(
      initialLocation: '/',
      routes: [
        GoRoute(
          path: '/',
          builder: (context, state) => const Scaffold(
            body: Center(child: Text('装运卫士')),
          ),
        ),
      ],
    );

    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ProviderScope(
        child: LoadGuard<PERSON><PERSON>(router: router),
      ),
    );

    // Verify that the app loads and displays the main title
    expect(find.text('装运卫士'), findsOneWidget);
  });
}
