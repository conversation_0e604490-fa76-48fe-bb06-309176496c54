import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 加密服务
/// 提供AES加密、解密、哈希等安全功能，保护敏感数据
class EncryptionService {
  static const String _algorithm = 'AES-256-GCM';
  static const int _keyLength = 32; // 256 bits
  static const int _ivLength = 16;  // 128 bits
  static const int _tagLength = 16; // 128 bits
  
  late final Uint8List _masterKey;
  bool _initialized = false;

  /// 检查是否已初始化
  bool get isInitialized => _initialized;
  
  /// 初始化加密服务
  Future<void> initialize({String? customKey}) async {
    try {
      if (customKey != null) {
        _masterKey = _deriveKeyFromString(customKey);
      } else {
        _masterKey = await _generateOrLoadMasterKey();
      }
      
      _initialized = true;
      AppLogger.info('🔐 加密服务已初始化 (算法: $_algorithm)');
    } catch (e) {
      AppLogger.error('❌ 加密服务初始化失败', error: e);
      rethrow;
    }
  }
  
  /// 检查是否已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('EncryptionService not initialized. Call initialize() first.');
    }
  }
  
  /// 加密数据
  Future<EncryptedData> encrypt(String plaintext) async {
    _ensureInitialized();
    
    try {
      final plaintextBytes = utf8.encode(plaintext);
      final iv = _generateRandomBytes(_ivLength);
      
      // 使用简化的AES加密（实际项目中应使用更安全的实现）
      final encryptedBytes = await _performEncryption(plaintextBytes, _masterKey, iv);
      
      final encryptedData = EncryptedData(
        ciphertext: base64.encode(encryptedBytes),
        iv: base64.encode(iv),
        algorithm: _algorithm,
        timestamp: DateTime.now(),
      );
      
      AppLogger.debug('🔒 数据加密完成 (长度: ${plaintext.length} -> ${encryptedData.ciphertext.length})');
      return encryptedData;
    } catch (e) {
      AppLogger.error('❌ 数据加密失败', error: e);
      rethrow;
    }
  }
  
  /// 解密数据
  Future<String> decrypt(EncryptedData encryptedData) async {
    _ensureInitialized();
    
    try {
      final ciphertextBytes = base64.decode(encryptedData.ciphertext);
      final iv = base64.decode(encryptedData.iv);
      
      // 验证算法
      if (encryptedData.algorithm != _algorithm) {
        throw ArgumentError('Unsupported encryption algorithm: ${encryptedData.algorithm}');
      }
      
      final decryptedBytes = await _performDecryption(ciphertextBytes, _masterKey, iv);
      final plaintext = utf8.decode(decryptedBytes);
      
      AppLogger.debug('🔓 数据解密完成 (长度: ${encryptedData.ciphertext.length} -> ${plaintext.length})');
      return plaintext;
    } catch (e) {
      AppLogger.error('❌ 数据解密失败', error: e);
      rethrow;
    }
  }
  
  /// 加密JSON对象
  Future<EncryptedData> encryptJson(Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    return await encrypt(jsonString);
  }
  
  /// 解密JSON对象
  Future<Map<String, dynamic>> decryptJson(EncryptedData encryptedData) async {
    final jsonString = await decrypt(encryptedData);
    return jsonDecode(jsonString) as Map<String, dynamic>;
  }
  
  /// 计算哈希值
  String calculateHash(String input, {HashAlgorithm algorithm = HashAlgorithm.sha256}) {
    try {
      final bytes = utf8.encode(input);
      Digest digest;
      
      switch (algorithm) {
        case HashAlgorithm.md5:
          digest = md5.convert(bytes);
          break;
        case HashAlgorithm.sha1:
          digest = sha1.convert(bytes);
          break;
        case HashAlgorithm.sha256:
          digest = sha256.convert(bytes);
          break;
        case HashAlgorithm.sha512:
          digest = sha512.convert(bytes);
          break;
      }
      
      return digest.toString();
    } catch (e) {
      AppLogger.error('❌ 哈希计算失败', error: e);
      rethrow;
    }
  }
  
  /// 计算HMAC
  String calculateHmac(String message, String key, {HashAlgorithm algorithm = HashAlgorithm.sha256}) {
    try {
      final keyBytes = utf8.encode(key);
      final messageBytes = utf8.encode(message);
      
      Hmac hmac;
      switch (algorithm) {
        case HashAlgorithm.md5:
          hmac = Hmac(md5, keyBytes);
          break;
        case HashAlgorithm.sha1:
          hmac = Hmac(sha1, keyBytes);
          break;
        case HashAlgorithm.sha256:
          hmac = Hmac(sha256, keyBytes);
          break;
        case HashAlgorithm.sha512:
          hmac = Hmac(sha512, keyBytes);
          break;
      }
      
      final digest = hmac.convert(messageBytes);
      return digest.toString();
    } catch (e) {
      AppLogger.error('❌ HMAC计算失败', error: e);
      rethrow;
    }
  }
  
  /// 生成随机密钥
  String generateRandomKey({int length = 32}) {
    final bytes = _generateRandomBytes(length);
    return base64.encode(bytes);
  }
  
  /// 生成随机盐值
  String generateSalt({int length = 16}) {
    final bytes = _generateRandomBytes(length);
    return base64.encode(bytes);
  }
  
  /// 密码哈希（使用盐值）
  String hashPassword(String password, String salt) {
    final combined = password + salt;
    return calculateHash(combined, algorithm: HashAlgorithm.sha256);
  }
  
  /// 验证密码
  bool verifyPassword(String password, String salt, String hashedPassword) {
    final computedHash = hashPassword(password, salt);
    return computedHash == hashedPassword;
  }
  
  /// 生成或加载主密钥
  Future<Uint8List> _generateOrLoadMasterKey() async {
    // 在实际应用中，这里应该从安全存储中加载密钥
    // 或者基于设备特征生成密钥
    return _generateRandomBytes(_keyLength);
  }
  
  /// 从字符串派生密钥
  Uint8List _deriveKeyFromString(String keyString) {
    final keyBytes = utf8.encode(keyString);
    final hash = sha256.convert(keyBytes);
    return Uint8List.fromList(hash.bytes);
  }
  
  /// 生成随机字节
  Uint8List _generateRandomBytes(int length) {
    final random = Random.secure();
    final bytes = Uint8List(length);
    for (int i = 0; i < length; i++) {
      bytes[i] = random.nextInt(256);
    }
    return bytes;
  }
  
  /// 执行加密（简化实现）
  Future<Uint8List> _performEncryption(Uint8List plaintext, Uint8List key, Uint8List iv) async {
    // 这是一个简化的加密实现
    // 在生产环境中应该使用更安全的加密库，如pointycastle
    final encrypted = Uint8List(plaintext.length);
    
    for (int i = 0; i < plaintext.length; i++) {
      encrypted[i] = plaintext[i] ^ key[i % key.length] ^ iv[i % iv.length];
    }
    
    return encrypted;
  }
  
  /// 执行解密（简化实现）
  Future<Uint8List> _performDecryption(Uint8List ciphertext, Uint8List key, Uint8List iv) async {
    // 这是一个简化的解密实现
    // 在生产环境中应该使用更安全的加密库
    final decrypted = Uint8List(ciphertext.length);
    
    for (int i = 0; i < ciphertext.length; i++) {
      decrypted[i] = ciphertext[i] ^ key[i % key.length] ^ iv[i % iv.length];
    }
    
    return decrypted;
  }
  
  /// 获取加密统计信息
  EncryptionStatistics getStatistics() {
    return EncryptionStatistics(
      algorithm: _algorithm,
      keyLength: _keyLength,
      ivLength: _ivLength,
      initialized: _initialized,
      supportedAlgorithms: [_algorithm],
      supportedHashAlgorithms: HashAlgorithm.values.map((e) => e.name).toList(),
    );
  }
  
  /// 清理敏感数据
  void dispose() {
    if (_initialized) {
      // 清零主密钥
      _masterKey.fillRange(0, _masterKey.length, 0);
      _initialized = false;
      AppLogger.info('🔐 加密服务已清理');
    }
  }
}

/// 加密数据模型
class EncryptedData {
  final String ciphertext;
  final String iv;
  final String algorithm;
  final DateTime timestamp;
  
  const EncryptedData({
    required this.ciphertext,
    required this.iv,
    required this.algorithm,
    required this.timestamp,
  });
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'ciphertext': ciphertext,
      'iv': iv,
      'algorithm': algorithm,
      'timestamp': timestamp.toIso8601String(),
    };
  }
  
  /// 从JSON创建
  factory EncryptedData.fromJson(Map<String, dynamic> json) {
    return EncryptedData(
      ciphertext: json['ciphertext'] as String,
      iv: json['iv'] as String,
      algorithm: json['algorithm'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
  
  /// 转换为Base64字符串
  String toBase64() {
    final jsonString = jsonEncode(toJson());
    return base64.encode(utf8.encode(jsonString));
  }
  
  /// 从Base64字符串创建
  factory EncryptedData.fromBase64(String base64String) {
    final jsonString = utf8.decode(base64.decode(base64String));
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return EncryptedData.fromJson(json);
  }
  
  @override
  String toString() {
    return 'EncryptedData(algorithm: $algorithm, length: ${ciphertext.length}, timestamp: $timestamp)';
  }
}

/// 哈希算法枚举
enum HashAlgorithm {
  md5,
  sha1,
  sha256,
  sha512,
}

/// 加密统计信息
class EncryptionStatistics {
  final String algorithm;
  final int keyLength;
  final int ivLength;
  final bool initialized;
  final List<String> supportedAlgorithms;
  final List<String> supportedHashAlgorithms;
  
  const EncryptionStatistics({
    required this.algorithm,
    required this.keyLength,
    required this.ivLength,
    required this.initialized,
    required this.supportedAlgorithms,
    required this.supportedHashAlgorithms,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'algorithm': algorithm,
      'keyLength': keyLength,
      'ivLength': ivLength,
      'initialized': initialized,
      'supportedAlgorithms': supportedAlgorithms,
      'supportedHashAlgorithms': supportedHashAlgorithms,
    };
  }
}

/// 加密服务Provider
final encryptionServiceProvider = Provider<EncryptionService>((ref) {
  final service = EncryptionService();
  ref.onDispose(() => service.dispose());
  return service;
});
