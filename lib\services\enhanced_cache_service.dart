import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'logging_service.dart';

/// 增强缓存服务
class EnhancedCacheService {
  static final EnhancedCacheService _instance = EnhancedCacheService._internal();
  static EnhancedCacheService get instance => _instance;
  
  EnhancedCacheService._internal();
  
  final Map<String, _CacheEntry> _cache = {};
  final Map<String, Timer> _timers = {};
  int _maxSize = 1000;
  Duration _defaultTtl = const Duration(hours: 1);
  
  /// 设置缓存项
  void set<T>(String key, T value, {Duration? ttl}) {
    try {
      final expiry = DateTime.now().add(ttl ?? _defaultTtl);
      
      // 如果缓存已满，清理过期项
      if (_cache.length >= _maxSize) {
        _cleanupExpired();
      }
      
      // 如果还是满的，移除最旧的项
      if (_cache.length >= _maxSize) {
        _removeOldest();
      }
      
      // 取消现有的定时器
      _timers[key]?.cancel();
      
      // 设置新的缓存项
      _cache[key] = _CacheEntry(value, expiry);
      
      // 设置过期定时器
      final duration = expiry.difference(DateTime.now());
      if (duration.inMilliseconds > 0) {
        _timers[key] = Timer(duration, () {
          remove(key);
        });
      }
      
      AppLogger.debug('缓存设置: $key', tag: 'Cache');
    } catch (e) {
      AppLogger.error('缓存设置失败: $key', tag: 'Cache', error: e);
    }
  }
  
  /// 获取缓存项
  T? get<T>(String key) {
    try {
      final entry = _cache[key];
      if (entry == null) {
        return null;
      }
      
      // 检查是否过期
      if (entry.isExpired) {
        remove(key);
        return null;
      }
      
      // 更新访问时间
      entry.lastAccessed = DateTime.now();
      
      AppLogger.debug('缓存命中: $key', tag: 'Cache');
      return entry.value as T?;
    } catch (e) {
      AppLogger.error('缓存获取失败: $key', tag: 'Cache', error: e);
      return null;
    }
  }
  
  /// 移除缓存项
  void remove(String key) {
    try {
      _cache.remove(key);
      _timers[key]?.cancel();
      _timers.remove(key);
      
      AppLogger.debug('缓存移除: $key', tag: 'Cache');
    } catch (e) {
      AppLogger.error('缓存移除失败: $key', tag: 'Cache', error: e);
    }
  }
  
  /// 检查缓存是否存在
  bool contains(String key) {
    final entry = _cache[key];
    if (entry == null || entry.isExpired) {
      if (entry?.isExpired == true) {
        remove(key);
      }
      return false;
    }
    return true;
  }
  
  /// 清理过期项
  void _cleanupExpired() {
    final now = DateTime.now();
    final expiredKeys = _cache.entries
        .where((entry) => entry.value.expiry.isBefore(now))
        .map((entry) => entry.key)
        .toList();
    
    for (final key in expiredKeys) {
      remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      AppLogger.debug('清理过期缓存: ${expiredKeys.length}项', tag: 'Cache');
    }
  }
  
  /// 移除最旧的项
  void _removeOldest() {
    if (_cache.isEmpty) return;
    
    String? oldestKey;
    DateTime? oldestTime;
    
    for (final entry in _cache.entries) {
      final accessTime = entry.value.lastAccessed;
      if (oldestTime == null || accessTime.isBefore(oldestTime)) {
        oldestTime = accessTime;
        oldestKey = entry.key;
      }
    }
    
    if (oldestKey != null) {
      remove(oldestKey);
      AppLogger.debug('移除最旧缓存: $oldestKey', tag: 'Cache');
    }
  }
  
  /// 清空所有缓存
  void clear() {
    try {
      for (final timer in _timers.values) {
        timer.cancel();
      }
      _cache.clear();
      _timers.clear();
      
      AppLogger.info('清空所有缓存', tag: 'Cache');
    } catch (e) {
      AppLogger.error('清空缓存失败', tag: 'Cache', error: e);
    }
  }
  
  /// 获取缓存统计信息
  Map<String, dynamic> getStats() {
    _cleanupExpired();
    
    return {
      'size': _cache.length,
      'maxSize': _maxSize,
      'hitRate': _calculateHitRate(),
      'memoryUsage': _estimateMemoryUsage(),
    };
  }
  
  /// 计算命中率（简化实现）
  double _calculateHitRate() {
    // 这里可以实现更复杂的命中率计算
    return _cache.isNotEmpty ? 0.8 : 0.0;
  }
  
  /// 估算内存使用（简化实现）
  int _estimateMemoryUsage() {
    // 简单估算，实际实现可能需要更精确的计算
    return _cache.length * 1024; // 假设每项1KB
  }
  
  /// 设置最大缓存大小
  void setMaxSize(int size) {
    _maxSize = size;
    if (_cache.length > _maxSize) {
      _cleanupExpired();
      while (_cache.length > _maxSize) {
        _removeOldest();
      }
    }
  }
  
  /// 设置默认TTL
  void setDefaultTtl(Duration ttl) {
    _defaultTtl = ttl;
  }
  
  /// 获取缓存大小
  int get size => _cache.length;
  
  /// 获取最大大小
  int get maxSize => _maxSize;

  /// 初始化缓存服务
  Future<void> initialize() async {
    try {
      AppLogger.info('初始化增强缓存服务', tag: 'Cache');
      // 清理过期项
      _cleanupExpired();
    } catch (e) {
      AppLogger.error('缓存服务初始化失败', tag: 'Cache', error: e);
      rethrow;
    }
  }

  /// 获取缓存的识别结果
  Future<dynamic> getCachedRecognitionResult(String key) async {
    return get(key);
  }

  /// 缓存处理后的图像
  Future<void> cacheProcessedImage(String key, dynamic imageData) async {
    set(key, imageData);
  }

  /// 获取缓存统计信息（别名）
  Map<String, dynamic> getCacheStats() {
    return getStats();
  }

  /// 智能清理
  Future<void> smartCleanup() async {
    try {
      _cleanupExpired();

      // 如果缓存使用率超过80%，清理最旧的项
      if (_cache.length > _maxSize * 0.8) {
        final removeCount = (_cache.length * 0.2).round();
        for (int i = 0; i < removeCount; i++) {
          _removeOldest();
        }
      }

      AppLogger.debug('智能清理完成', tag: 'Cache');
    } catch (e) {
      AppLogger.error('智能清理失败', tag: 'Cache', error: e);
    }
  }

  /// 释放资源
  void dispose() {
    try {
      clear();
      AppLogger.info('缓存服务已释放', tag: 'Cache');
    } catch (e) {
      AppLogger.error('缓存服务释放失败', tag: 'Cache', error: e);
    }
  }
}

/// 缓存条目
class _CacheEntry {
  final dynamic value;
  final DateTime expiry;
  DateTime lastAccessed;
  
  _CacheEntry(this.value, this.expiry) : lastAccessed = DateTime.now();
  
  bool get isExpired => DateTime.now().isAfter(expiry);
}

/// 性能缓存服务（兼容性别名）
class PerformanceCacheService extends EnhancedCacheService {
  static final PerformanceCacheService _instance = PerformanceCacheService._internal();
  static PerformanceCacheService get instance => _instance;

  PerformanceCacheService._internal() : super._internal();

  /// 获取性能统计信息
  Map<String, dynamic> getPerformanceStats() {
    return {
      'cacheHitRate': _calculateHitRate(),
      'cacheSize': size,
      'maxCacheSize': maxSize,
      'memoryUsage': _estimateMemoryUsage(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// 内存优化服务（简化实现）
class MemoryOptimizationService {
  static final MemoryOptimizationService _instance = MemoryOptimizationService._internal();
  static MemoryOptimizationService get instance => _instance;
  
  MemoryOptimizationService._internal();
  
  /// 获取内存使用情况
  Map<String, dynamic> getMemoryUsage() {
    return {
      'cacheSize': EnhancedCacheService.instance.size,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 获取内存统计信息
  Future<Map<String, dynamic>> getMemoryStats() async {
    return {
      'totalMemory': 1024 * 1024 * 512, // 模拟512MB
      'usedMemory': EnhancedCacheService.instance.size * 1024,
      'freeMemory': 1024 * 1024 * 512 - (EnhancedCacheService.instance.size * 1024),
      'cacheSize': EnhancedCacheService.instance.size,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// 清理内存
  void cleanup() {
    EnhancedCacheService.instance.clear();
    AppLogger.info('执行内存清理', tag: 'MemoryOptimization');
  }
}

// ✅ PerformanceOptimizer已移动到performance_manager.dart
// 避免重复定义冲突
