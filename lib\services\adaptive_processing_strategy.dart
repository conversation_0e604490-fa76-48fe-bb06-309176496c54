import 'dart:io';
import 'package:loadguard/services/smart_image_quality_assessor.dart';
import 'package:loadguard/services/high_performance_image_processor.dart';
import 'package:loadguard/services/logging_service.dart';
import 'package:image/image.dart' as img;

/// 自适应处理策略服务
/// 根据图像质量智能选择最优的预处理算法组合
class AdaptiveProcessingStrategy {
  static final AdaptiveProcessingStrategy _instance =
      AdaptiveProcessingStrategy._internal();
  factory AdaptiveProcessingStrategy() => _instance;
  AdaptiveProcessingStrategy._internal();

  final _imageProcessor = HighPerformanceImageProcessor();
  final _qualityAssessor = SmartImageQualityAssessor();

  /// 智能预处理入口点
  Future<String> processImageIntelligently(String imagePath) async {
    try {
      final startTime = DateTime.now();

      // 1. 快速质量评估
      final qualityProfile =
          await SmartImageQualityAssessor.assessImageQuality(imagePath);

      Log.i(
          '图像质量评估: ${qualityProfile.qualityLevel}(${qualityProfile.overallScore.toStringAsFixed(1)}), 策略: ${qualityProfile.processingStrategy}',
          tag: 'AdaptiveProcessing');

      // 2. 根据策略选择处理方案
      String processedPath;

      switch (qualityProfile.processingStrategy) {
        case ProcessingStrategy.minimal:
          processedPath = await _minimalProcessing(imagePath, qualityProfile);
          break;
        case ProcessingStrategy.selective:
          processedPath = await _selectiveProcessing(imagePath, qualityProfile);
          break;
        case ProcessingStrategy.standard:
          processedPath = await _standardProcessing(imagePath, qualityProfile);
          break;
        case ProcessingStrategy.aggressive:
          processedPath =
              await _aggressiveProcessing(imagePath, qualityProfile);
          break;
      }

      final processingTime =
          DateTime.now().difference(startTime).inMilliseconds;
      Log.i(
          '智能处理完成: ${qualityProfile.processingStrategy}, 耗时: ${processingTime}ms',
          tag: 'AdaptiveProcessing');

      return processedPath;
    } catch (e) {
      Log.e('智能处理失败: $e', tag: 'AdaptiveProcessing');
      return imagePath; // 返回原图
    }
  }

  /// 最小处理策略：高质量图像，跳过预处理
  Future<String> _minimalProcessing(
      String imagePath, ImageQualityProfile profile) async {
    Log.i('应用最小处理策略', tag: 'AdaptiveProcessing');

    // 高质量图像，只做轻微调整
    if (profile.features['needsExposureAdjustment'] == true) {
      return await _imageProcessor.processImage(
          imagePath, ProcessingType.adaptiveEnhancement);
    }

    return imagePath; // 直接返回原图
  }

  /// 选择性处理策略：中等质量图像，选择性应用算法
  Future<String> _selectiveProcessing(
      String imagePath, ImageQualityProfile profile) async {
    Log.i('应用选择性处理策略', tag: 'AdaptiveProcessing');

    String currentPath = imagePath;

    // 根据具体问题选择性应用算法
    if (profile.features['isDark'] == true) {
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.aiLowLight);
    }

    if (profile.features['hasReflections'] == true) {
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.deepReflectionSuppression);
    }

    if (profile.sharpnessScore < 60) {
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.ultraSharp);
    }

    return currentPath;
  }

  /// 标准处理策略：低质量图像，应用常规算法组合
  Future<String> _standardProcessing(
      String imagePath, ImageQualityProfile profile) async {
    Log.i('应用标准处理策略', tag: 'AdaptiveProcessing');

    String currentPath = imagePath;

    // 应用标准算法组合
    if (profile.features['isDark'] == true || profile.exposureScore < 50) {
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.aiLowLight);
    }

    if (profile.contrastScore < 50) {
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.adaptiveEnhancement);
    }

    if (profile.sharpnessScore < 70) {
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.ultraSharp);
    }

    if (profile.features['hasReflections'] == true) {
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.deepReflectionSuppression);
    }

    return currentPath;
  }

  /// 激进处理策略：极低质量图像，应用全套算法
  Future<String> _aggressiveProcessing(
      String imagePath, ImageQualityProfile profile) async {
    Log.i('应用激进处理策略', tag: 'AdaptiveProcessing');

    String currentPath = imagePath;

    // 应用全套12种专业算法
    try {
      // 1. AI增强弱光
      if (profile.features['isDark'] == true || profile.exposureScore < 60) {
        currentPath = await _imageProcessor.processImage(
            currentPath, ProcessingType.aiLowLight);
      }

      // 2. 深度反光抑制
      if (profile.features['hasReflections'] == true ||
          profile.reflectionScore < 70) {
        currentPath = await _imageProcessor.processImage(
            currentPath, ProcessingType.deepReflectionSuppression);
      }

      // 3. 超级锐化
      if (profile.sharpnessScore < 80) {
        currentPath = await _imageProcessor.processImage(
            currentPath, ProcessingType.ultraSharp);
      }

      // 4. 自适应增强
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.adaptiveEnhancement);

      // 5. 边缘保护
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.edgePreserving);

      // 6. 多尺度融合
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.multiScaleFusion);

      // 7. 智能图像增强
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.intelligentEnhancement);

      // 8. 深度学习增强
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.deepLearning);

      // 🔧 新增：补充缺失的4种算法

      // 9. 透视校正（针对倾斜的标签）
      if (profile.features['isSkewed'] == true || profile.overallScore < 40) {
        currentPath = await _imageProcessor.processImage(
            currentPath, ProcessingType.perspectiveCorrection);
      }

      // 10. 智能降噪（保持细节的降噪）
      if (profile.noiseScore < 60) {
        currentPath = await _imageProcessor.processImage(
            currentPath, ProcessingType.noiseReduction);
      }

      // 11. 对比度优化（自适应对比度增强）
      if (profile.contrastScore < 70) {
        currentPath = await _imageProcessor.processImage(
            currentPath, ProcessingType.contrastOptimization);
      }

      // 12. 文字增强（专门针对文字识别优化）
      currentPath = await _imageProcessor.processImage(
          currentPath, ProcessingType.textEnhancement);

      Log.i('激进处理完成: 应用了完整的12种专业算法', tag: 'AdaptiveProcessing');
    } catch (e) {
      Log.e('激进处理部分失败: $e', tag: 'AdaptiveProcessing');
      // 即使部分失败，也返回已处理的结果
    }

    return currentPath;
  }

  /// 获取处理策略描述
  String getStrategyDescription(ProcessingStrategy strategy) {
    switch (strategy) {
      case ProcessingStrategy.minimal:
        return '最小处理 - 高质量图像快速通道';
      case ProcessingStrategy.selective:
        return '选择性处理 - 针对性算法应用';
      case ProcessingStrategy.standard:
        return '标准处理 - 常规算法组合';
      case ProcessingStrategy.aggressive:
        return '激进处理 - 12种专业算法全套应用';
    }
  }

  /// 获取算法组合信息
  List<String> getAlgorithmCombination(ProcessingStrategy strategy) {
    switch (strategy) {
      case ProcessingStrategy.minimal:
        return ['自适应曝光补偿'];
      case ProcessingStrategy.selective:
        return ['AI增强弱光', '深度反光抑制', '超级锐化'];
      case ProcessingStrategy.standard:
        return ['AI增强弱光', '自适应增强', '超级锐化', '深度反光抑制'];
      case ProcessingStrategy.aggressive:
        return [
          'AI增强弱光',
          '深度反光抑制',
          '超级锐化',
          '自适应增强',
          '边缘保护',
          '多尺度融合',
          '智能图像增强',
          '深度学习增强'
        ];
    }
  }
}

/// 快速通道处理器（用于存证照片）
class FastTrackProcessor {
  static final FastTrackProcessor _instance = FastTrackProcessor._internal();
  factory FastTrackProcessor() => _instance;
  FastTrackProcessor._internal();

  /// 快速处理存证照片
  Future<String> processFastTrack(String imagePath) async {
    try {
      final startTime = DateTime.now();

      // 存证照片只做最基本的处理
      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('文件不存在: $imagePath');
      }

      // 简单的文件验证和优化
      final fileSize = await file.length();
      if (fileSize > 10 * 1024 * 1024) {
        // 10MB以上进行压缩
        final compressedPath = await _compressImage(imagePath);
        final processingTime =
            DateTime.now().difference(startTime).inMilliseconds;
        Log.i('快速通道处理完成: 压缩处理, 耗时: ${processingTime}ms', tag: 'FastTrack');
        return compressedPath;
      }

      final processingTime =
          DateTime.now().difference(startTime).inMilliseconds;
      Log.i('快速通道处理完成: 直接通过, 耗时: ${processingTime}ms', tag: 'FastTrack');
      return imagePath;
    } catch (e) {
      Log.e('快速通道处理失败: $e', tag: 'FastTrack');
      return imagePath;
    }
  }

  /// 压缩图像
  Future<String> _compressImage(String imagePath) async {
    try {
      final bytes = await File(imagePath).readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return imagePath;
      }

      // 压缩图像
      final compressedBytes = img.encodeJpg(image, quality: 85);
      final compressedPath = imagePath.replaceAll('.jpg', '_compressed.jpg');
      await File(compressedPath).writeAsBytes(compressedBytes);

      return compressedPath;
    } catch (e) {
      Log.e('图像压缩失败: $e', tag: 'FastTrack');
      return imagePath;
    }
  }
}

/// 并行处理器（用于批量处理）
class ParallelProcessor {
  static final ParallelProcessor _instance = ParallelProcessor._internal();
  factory ParallelProcessor() => _instance;
  ParallelProcessor._internal();

  final _adaptiveStrategy = AdaptiveProcessingStrategy();
  final _fastTrackProcessor = FastTrackProcessor();

  /// 并行处理多张图像
  Future<List<String>> processImagesInParallel(
      List<String> imagePaths, List<bool> needRecognition) async {
    if (imagePaths.length != needRecognition.length) {
      throw ArgumentError('imagePaths和needRecognition数组长度不匹配');
    }

    final futures = <Future<String>>[];

    for (int i = 0; i < imagePaths.length; i++) {
      final imagePath = imagePaths[i];
      final needsRecognition = needRecognition[i];

      if (needsRecognition) {
        // 识别照片使用智能处理
        futures.add(_adaptiveStrategy.processImageIntelligently(imagePath));
      } else {
        // 存证照片使用快速通道
        futures.add(_fastTrackProcessor.processFastTrack(imagePath));
      }
    }

    // 并行执行所有处理任务
    return await Future.wait(futures);
  }
}
