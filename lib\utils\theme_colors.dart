import 'package:flutter/material.dart';

/// 工业级应用主题色彩系统 - 2025版
/// 为裕龙石化等高端企业客户定制
class ThemeColors {
  // 基础色彩
  static const Color primary = Color(0xFF0D324D); // 深空蓝 - 主色调
  static const Color secondary = Color(0xFF3F4C55); // 石墨灰 - 辅助色
  static const Color accent = Color(0xFF0088CC); // 流程蓝 - 强调色
  static const Color background = Color(0xFFF7F9FC); // 背景色
  static const Color primaryLight = Color(0xFF1A425E); // 浅主色
  static const Color yellow = Color(0xFFF5A623); // 黄色
  static const Color error = Color(0xFFDC3545); // 错误色

  // 功能色彩
  static const Color success = Color(0xFF28A745); // 成功/安全绿
  static const Color warning = Color(0xFFF5A623); // 警告/注意橙
  static const Color danger = Color(0xFFDC3545); // 危险/错误红
  static const Color info = Color(0xFF0088CC); // 信息蓝

  // 文本色彩 - 增强对比度版本
  static const Color textDark = Color(0xFF222831); // 主文本色
  static const Color textMedium = Color(0xFF596068); // 次要文本
  static const Color textLight = Color(0xFF8D99A6); // 辅助文本
  static const Color textOnGradient = Color(0xFFFFFFFF); // 渐变背景上的文本 - 纯白高对比度
  static const Color textOnDark = Color(0xFFFFFFFF); // 深色背景上的文本 - 纯白
  static const Color textOnDarkSecondary =
      Color(0xFFE1E5EB); // 深色背景上的次要文本 - 高对比度浅色
  static const Color textHighContrast = Color(0xFFFFFFFF); // 高对比度文本（用于重要信息）

  // 技术指标卡文字颜色
  static const Color textPrimaryColor = Color(0xFF222831);
  static const Color textSecondaryColor = Color(0xFF596068);
  static const Color textPrimaryLightColor = Color(0xFFF7F9FC);
  static const Color textSecondaryLightColor = Color(0xFFE1E5EB);
  static const Color iconColor = Color(0xFF3F4C55);
  static const Color iconLightColor = Color(0xFFCBD5E0);

  // 卡片颜色
  static const Color cardBackgroundColor = Color(0xFFFFFFFF);
  static const Color cardBackground = Color(0xFFFFFFFF); // 卡片背景
  static const Color premiumCardBackgroundColor = Color(0xFF1A425E);
  static const Color enterpriseCardBackgroundColor = Color(0xFF0D324D);
  static const Color cardBorderColor = Color(0xFFCBD5E0);

  // 边框和分割线
  static const Color border = Color(0xFFE1E5EB); // 边框色
  static const Color divider = Color(0xFFE1E5EB); // 分割线

  // 图标色彩
  static const Color iconDark = Color(0xFF3F4C55); // 深色图标
  static const Color iconLight = Color(0xFFCBD5E0); // 浅色图标
  static const Color iconSecondary = Color(0xFF8D99A6); // 次要图标色

  // 交互状态
  static const Color primaryHover = Color(0xFF11405F); // 主色悬停
  static const Color primaryPressed = Color(0xFF0A2538); // 主色按下

  // 边框圆角
  static const double radiusSmall = 4.0; // 小圆角
  static const double radiusMedium = 8.0; // 中圆角
  static const double radiusLarge = 12.0; // 大圆角
  static const double radiusXLarge = 20.0; // 特大圆角
  static const double radiusRound = 24.0; // 圆形圆角
  static const double buttonRadius = 8.0; // 按钮圆角
  static const double cardRadius = 12.0; // 卡片圆角

  // 2025现代化圆角系统 - 基于现有系统扩展
  static const double modernRadiusSmall = 8.0; // 现代化小圆角
  static const double modernRadiusMedium = 16.0; // 现代化中圆角
  static const double modernRadiusLarge = 24.0; // 现代化大圆角
  static const double modernRadiusXLarge = 32.0; // 现代化特大圆角

  // 阴影效果
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x0F000000),
      blurRadius: 10.0,
      offset: Offset(0, 4),
    ),
  ];

  static const List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 8.0,
      offset: Offset(0, 2),
    ),
  ];

  static const List<BoxShadow> floatingButtonShadow = [
    BoxShadow(
      color: Color(0x1A000000),
      blurRadius: 12.0,
      offset: Offset(0, 4),
    ),
  ];

  static const List<BoxShadow> innerShadow = [
    BoxShadow(
      color: Color(0x20000000),
      blurRadius: 8,
      offset: Offset(2, 2),
      spreadRadius: -2,
    )
  ];

  // 2025现代化阴影系统 - 基于现有深色背景的新拟态效果
  static const Color neumorphicBase = Color(0xFF0A192F); // 使用现有深色背景
  static const Color neumorphicLight = Color(0xFF1A2B3D); // 基于背景色的浅色突起
  static const Color neumorphicShadow = Color(0xFF05101A); // 基于背景色的深色凹陷

  // 新拟态阴影组合
  static const List<BoxShadow> neumorphicCardShadow = [
    BoxShadow(
      color: neumorphicLight,
      offset: Offset(-8, -8),
      blurRadius: 16,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: neumorphicShadow,
      offset: Offset(8, 8),
      blurRadius: 16,
      spreadRadius: 0,
    ),
  ];

  // 新拟态按钮阴影
  static const List<BoxShadow> neumorphicButtonShadow = [
    BoxShadow(
      color: neumorphicLight,
      offset: Offset(-4, -4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: neumorphicShadow,
      offset: Offset(4, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  // 新拟态内凹阴影（输入框）
  static const List<BoxShadow> neumorphicInsetShadow = [
    BoxShadow(
      color: neumorphicShadow,
      offset: Offset(-2, -2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: neumorphicLight,
      offset: Offset(2, 2),
      blurRadius: 6,
      spreadRadius: 0,
    ),
  ];

  // 悬浮态新拟态阴影
  static const List<BoxShadow> neumorphicHoverShadow = [
    BoxShadow(
      color: neumorphicLight,
      offset: Offset(-6, -6),
      blurRadius: 12,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: neumorphicShadow,
      offset: Offset(6, 6),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];

  // 现代化卡片阴影 - 增强版
  static const List<BoxShadow> modernCardShadow = [
    BoxShadow(
      color: Color(0x15000000),
      blurRadius: 20.0,
      offset: Offset(0, 8),
      spreadRadius: -4,
    ),
    BoxShadow(
      color: Color(0x08000000),
      blurRadius: 6.0,
      offset: Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  // 悬浮状态阴影
  static const List<BoxShadow> hoverShadow = [
    BoxShadow(
      color: Color(0x20000000),
      blurRadius: 24.0,
      offset: Offset(0, 12),
      spreadRadius: -6,
    ),
  ];

  // 玻璃效果背景
  static const Color glassBackground = Color(0x0AFFFFFF); // 玻璃态背景
  static const Color glassBorder = Color(0x30FFFFFF); // 玻璃态边框
  static const Color enterpriseGlassBackground = Color(0x20FFFFFF); // 企业级玻璃态背景
  static const Color enterpriseGlassBorder = Color(0x40CBD5E0); // 企业级玻璃态边框
  static const Color flatbedGlassBackground = Color(0x18FFFFFF); // 平板车玻璃态背景
  static const Color flatbedGlassBorder = Color(0x30FFFFFF); // 平板车玻璃态边框
  static const Color containerGlassBackground = Color(0x15FFFFFF); // 集装箱玻璃态背景
  static const Color containerGlassBorder = Color(0x25FFFFFF); // 集装箱玻璃态边框

  // 现代化玻璃态效果 - 增强版
  static const Color modernGlassBackground = Color(0x15FFFFFF); // 现代化玻璃背景
  static const Color modernGlassBorder = Color(0x25FFFFFF); // 现代化玻璃边框
  static const Color modernGlassHighlight = Color(0x40FFFFFF); // 现代化玻璃高亮
  static const Color modernGlassShadow = Color(0x08000000); // 现代化玻璃阴影

  // 专业渐变色彩
  static const LinearGradient softGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF0D324D), // 深空蓝
      Color(0xFF2B4357), // 过渡蓝灰
    ],
    stops: [0.0, 1.0],
  );

  // 主渐变 - 深空专业渐变
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF0D324D), // 深空蓝
      Color(0xFF1A425E), // 中间过渡蓝
      Color(0xFF2B4357), // 深石墨蓝
      Color(0xFF3F4C55), // 石墨灰
      Color(0xFF465158), // 深石墨灰
    ],
    stops: [0.0, 0.25, 0.5, 0.75, 1.0],
  );

  // 平板车模块渐变
  static const LinearGradient flatbedGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF3F4C55), // 石墨灰
      Color(0xFF566067), // 浅石墨
    ],
    stops: [0.0, 1.0],
  );

  // 集装箱模块渐变
  static const LinearGradient containerGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1A425E), // 集装箱蓝
      Color(0xFF2D5475), // 浅集装箱蓝
    ],
    stops: [0.0, 1.0],
  );

  // 功能渐变色
  static const LinearGradient blueGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF0088CC), Color(0xFF0070A8)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient greenGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF28A745), Color(0xFF218838)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient yellowGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFF5A623), Color(0xFFE09612)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient orangeGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFFF5A623), Color(0xFFE09612)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient purpleGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF7B68EE), Color(0xFF6A5ACD)],
    stops: [0.0, 1.0],
  );

  static const LinearGradient tealGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF20B2AA), Color(0xFF008B8B)],
    stops: [0.0, 1.0],
  );

  static const Gradient redGradient = LinearGradient(
    colors: [Color(0xFFE53935), Color(0xFFFF7043)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // 按钮渐变色
  static const LinearGradient primaryButtonGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      Color(0xFF0D324D), // 深空蓝
      Color(0xFF0088CC), // 流程蓝
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient successButtonGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      Color(0xFF218838),
      Color(0xFF28A745),
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient warningButtonGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      Color(0xFFE09612),
      Color(0xFFF5A623),
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient dangerButtonGradient = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      Color(0xFFB92D3B),
      Color(0xFFDC3545),
    ],
    stops: [0.0, 1.0],
  );

  // 高级卡片渐变 - 半透明玻璃态效果
  static const LinearGradient premiumCardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0x30FFFFFF),
      Color(0x15FFFFFF),
    ],
    stops: [0.0, 1.0],
  );

  // 企业专属卡片渐变
  static const LinearGradient enterpriseCardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0x25FFFFFF),
      Color(0x10FFFFFF),
    ],
    stops: [0.0, 1.0],
  );

  // 2025现代化渐变系列 - 基于现有品牌色增强
  static const LinearGradient modernPrimaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF0D324D), // 深空蓝
      Color(0xFF1A425E), // 中间过渡
      Color(0xFF0088CC), // 流程蓝
    ],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient modernGlassGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0x20FFFFFF), // 玻璃高亮
      Color(0x08FFFFFF), // 玻璃透明
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient modernSuccessGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF28A745), // 成功绿
      Color(0xFF20C997), // 现代绿
    ],
    stops: [0.0, 1.0],
  );

  static const LinearGradient modernWarningGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFF5A623), // 警告橙
      Color(0xFFFFB347), // 现代橙
    ],
    stops: [0.0, 1.0],
  );

  // 状态渐变获取方法
  static LinearGradient getStatusGradient(String status) {
    switch (status) {
      case 'success':
        return greenGradient;
      case 'warning':
        return yellowGradient;
      case 'danger':
        return dangerButtonGradient;
      default:
        return blueGradient;
    }
  }

  // 现代化状态渐变获取方法
  static LinearGradient getModernStatusGradient(String status) {
    switch (status) {
      case 'success':
        return modernSuccessGradient;
      case 'warning':
        return modernWarningGradient;
      case 'danger':
        return dangerButtonGradient;
      case 'primary':
        return modernPrimaryGradient;
      case 'glass':
        return modernGlassGradient;
      default:
        return modernPrimaryGradient;
    }
  }

  // 统一字体大小系统
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  static const double fontSizeTitle = 22.0;
  static const double fontSizeHeader = 24.0;

  // 统一间距系统
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;

  // 统一卡片内边距
  static const EdgeInsets cardPaddingSmall = EdgeInsets.all(12.0);
  static const EdgeInsets cardPaddingMedium = EdgeInsets.all(16.0);
  static const EdgeInsets cardPaddingLarge = EdgeInsets.all(20.0);

  // 统一按钮高度
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightMedium = 44.0;
  static const double buttonHeightLarge = 52.0;

  static const Color surfaceContainerHighest = Color(0xFFF2F4F8); // Material3最高层容器色
  static const Color outline = Color(0xFFB0BEC5); // Material3轮廓色
}

/// 标准化文字样式 - 解决对比度和权重问题
class TextStyles {
  // 标题样式 - 用于页面主标题
  static const TextStyle title = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700, // 加粗
    color: ThemeColors.textOnDark,
    height: 1.2,
  );

  // 副标题样式 - 用于页面副标题
  static const TextStyle subtitle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600, // 半粗体
    color: ThemeColors.textOnDark,
    height: 1.3,
  );

  // 卡片标题样式
  static const TextStyle cardTitle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w700, // 加粗
    color: ThemeColors.textOnDark,
    height: 1.2,
  );

  // 卡片副标题样式
  static const TextStyle cardSubtitle = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500, // 中等权重
    color: ThemeColors.textOnDarkSecondary,
    height: 1.4,
  );

  // 正文样式 - 高对比度
  static const TextStyle body = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500, // 中等权重
    color: ThemeColors.textOnDark,
    height: 1.5,
  );

  // 说明文字样式 - 高对比度
  static const TextStyle description = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500, // 中等权重
    color: ThemeColors.textOnDarkSecondary,
    height: 1.5,
  );

  // 按钮文字样式
  static const TextStyle button = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600, // 半粗体
    color: ThemeColors.textOnDark,
    height: 1.2,
  );

  // 弹窗标题样式
  static const TextStyle dialogTitle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w700, // 加粗
    color: ThemeColors.textDark, // 弹窗通常是浅色背景
    height: 1.2,
  );

  // 弹窗内容样式
  static const TextStyle dialogContent = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500, // 中等权重
    color: ThemeColors.textMedium,
    height: 1.5,
  );

  // 小文字样式（提示等）
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500, // 中等权重
    color: ThemeColors.textOnDarkSecondary,
    height: 1.3,
  );

  // 2025现代化文本样式

  // 现代化标题 - 加强版
  static const TextStyle modernTitle = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.w800, // 超粗体
    color: ThemeColors.textOnDark,
    height: 1.1,
    letterSpacing: -0.5, // 现代化字间距
  );

  // 现代化副标题 - 增强可读性
  static const TextStyle modernSubtitle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: ThemeColors.textOnDark,
    height: 1.25,
    letterSpacing: -0.25,
  );

  // 现代化按钮文本 - 微交互增强
  static const TextStyle modernButton = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w700,
    color: ThemeColors.textOnDark,
    height: 1.2,
    letterSpacing: 0.25, // 按钮文字展开
  );

  // 现代化描述文本 - 优化可读性
  static const TextStyle modernDescription = TextStyle(
    fontSize: 15,
    fontWeight: FontWeight.w500,
    color: ThemeColors.textOnDarkSecondary,
    height: 1.4,
    letterSpacing: 0.1,
  );

  // 现代化标签文本
  static const TextStyle modernLabel = TextStyle(
    fontSize: 13,
    fontWeight: FontWeight.w600,
    color: ThemeColors.textOnDark,
    height: 1.3,
    letterSpacing: 0.5, // 标签文字展开
  );
}

/// 2025现代化UI组件工具类
class ModernUIHelper {
  // 现代化卡片装饰
  static BoxDecoration modernCardDecoration({
    bool isNeumorphic = false,
    bool isGlass = false,
    bool isHovered = false,
    Color? backgroundColor,
  }) {
    if (isNeumorphic) {
      return BoxDecoration(
        color: backgroundColor ?? ThemeColors.neumorphicBase,
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        boxShadow: isHovered
            ? ThemeColors.neumorphicHoverShadow
            : ThemeColors.neumorphicCardShadow,
      );
    } else if (isGlass) {
      return BoxDecoration(
        gradient: ThemeColors.modernGlassGradient,
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        border: Border.all(
          color: ThemeColors.modernGlassBorder,
          width: 1,
        ),
        boxShadow:
            isHovered ? ThemeColors.hoverShadow : ThemeColors.modernCardShadow,
      );
    } else {
      return BoxDecoration(
        color: backgroundColor ?? ThemeColors.cardBackground,
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        boxShadow:
            isHovered ? ThemeColors.hoverShadow : ThemeColors.modernCardShadow,
      );
    }
  }

  // 现代化按钮装饰
  static BoxDecoration modernButtonDecoration({
    String type = 'primary',
    bool isPressed = false,
    bool isNeumorphic = false,
  }) {
    if (isNeumorphic) {
      return BoxDecoration(
        color: ThemeColors.neumorphicBase,
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
        boxShadow: isPressed
            ? ThemeColors.neumorphicInsetShadow
            : ThemeColors.neumorphicButtonShadow,
      );
    } else {
      return BoxDecoration(
        gradient: ThemeColors.getModernStatusGradient(type),
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
        boxShadow: isPressed ? [] : ThemeColors.buttonShadow,
      );
    }
  }

  // 现代化输入框装饰
  static InputDecoration modernInputDecoration({
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isNeumorphic = false,
  }) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: TextStyles.modernDescription,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: isNeumorphic
          ? ThemeColors.neumorphicBase
          : ThemeColors.modernGlassBackground,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
        borderSide: BorderSide(
          color:
              isNeumorphic ? Colors.transparent : ThemeColors.modernGlassBorder,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
        borderSide: BorderSide(
          color:
              isNeumorphic ? Colors.transparent : ThemeColors.modernGlassBorder,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
        borderSide: const BorderSide(
          color: ThemeColors.primary,
          width: 2,
        ),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    );
  }

  // 现代化标签装饰
  static BoxDecoration modernTagDecoration(Color categoryColor) {
    return BoxDecoration(
      color: categoryColor,
      borderRadius: BorderRadius.circular(ThemeColors.modernRadiusSmall),
      boxShadow: [
        BoxShadow(
          color: categoryColor.withValues(alpha: 0.3),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  // 预定义的类别颜色映射（避免循环依赖）
  static const Map<String, Color> categoryColors = {
    'LLDPE': Color(0xFF4CAF50), // 绿色
    'HDPE': Color(0xFF2196F3), // 蓝色
    'PP': Color(0xFFFF9800), // 橙色
    'mPE': Color(0xFF9C27B0), // 紫色
    'SAN': Color(0xFFE91E63), // 粉红色
    'PS': Color(0xFF795548), // 棕色
  };

  // 获取类别颜色的便捷方法
  static Color getCategoryColor(String category) {
    return categoryColors[category] ?? ThemeColors.primary;
  }
}

/// 玻璃态+渐变背景主题
final ThemeData glassGradientTheme = ThemeData(
  brightness: Brightness.light,
  primaryColor: ThemeColors.primary,
  scaffoldBackgroundColor: Colors.transparent,
  appBarTheme: const AppBarTheme(
    backgroundColor: ThemeColors.primary,
    elevation: 0,
    centerTitle: true,
    iconTheme: IconThemeData(color: Colors.white),
    titleTextStyle: TextStyle(
      color: Colors.white,
      fontSize: 22,
      fontWeight: FontWeight.bold,
    ),
  ),
  cardTheme: CardThemeData(
    color: Colors.white.withValues(alpha: 0.18),
    elevation: 8,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(20)),
    ),
  ),
  dialogTheme: DialogThemeData(
    backgroundColor: Colors.white.withValues(alpha: 0.22),
  ),
  textTheme: const TextTheme(
    bodyLarge: TextStyle(color: Colors.black87),
    bodyMedium: TextStyle(color: Colors.black87),
    titleLarge: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
  ),
  // 渐变背景可通过BoxDecoration统一引用ThemeColors.softGradient
);
