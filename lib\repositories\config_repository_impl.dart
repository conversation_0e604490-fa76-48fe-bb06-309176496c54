import 'package:loadguard/models/config_models.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/models/template_config.dart' as template_config;
import 'package:loadguard/repositories/config_repository.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';
import 'package:loadguard/utils/app_logger.dart';

/// ConfigRepository的具体实现
/// 使用Hive作为主要存储，支持数据迁移和版本管理
class ConfigRepositoryImpl implements ConfigRepository {
  final HiveConfigDataSource _dataSource;
  
  // 配置键前缀
  static const String _workerPrefix = 'worker_';
  static const String _warehousePrefix = 'warehouse_';
  static const String _groupPrefix = 'group_';
  static const String _templatePrefix = 'template_';
  static const String _photoConfigPrefix = 'photo_config_';
  static const String _photoGroupPrefix = 'photo_group_';
  static const String _rolePrefix = 'role_';
  static const String _systemPrefix = 'system_';
  // static const String _versionPrefix = 'version_'; // 暂时未使用
  
  ConfigRepositoryImpl({HiveConfigDataSource? dataSource})
      : _dataSource = dataSource ?? HiveConfigDataSource();

  /// 转换RecognitionType枚举
  RecognitionType _convertRecognitionType(template_config.RecognitionType type) {
    switch (type) {
      case template_config.RecognitionType.none:
        return RecognitionType.none;
      case template_config.RecognitionType.qrCode:
        return RecognitionType.qrCode;
      case template_config.RecognitionType.barcode:
        return RecognitionType.barcode;
      case template_config.RecognitionType.text:
        return RecognitionType.text;
      case template_config.RecognitionType.mixed:
        return RecognitionType.mixed;
    }
  }

  @override
  Future<void> initialize() async {
    try {
      await _dataSource.initialize();
      
      // 检查是否需要数据迁移
      await _checkAndMigrate();
      
      AppLogger.info('✅ ConfigRepository 初始化成功');
    } catch (e) {
      AppLogger.error('❌ ConfigRepository 初始化失败: $e');
      throw ConfigRepositoryException('ConfigRepository初始化失败', originalError: e);
    }
  }

  // ==================== 工作人员配置 ====================

  @override
  Future<List<WorkerConfig>> getAllWorkers() async {
    try {
      final keys = await _dataSource.getAllConfigKeys();
      final workerKeys = keys.where((key) => key.startsWith(_workerPrefix));
      
      final workers = <WorkerConfig>[];
      for (final key in workerKeys) {
        final data = await _dataSource.getConfig(key);
        if (data != null) {
          try {
            workers.add(WorkerConfig.fromJson(data));
          } catch (e) {
            AppLogger.warning('解析工作人员配置失败: $key - $e');
          }
        }
      }
      
      // 按ID排序
      workers.sort((a, b) => a.id.compareTo(b.id));
      return workers;
    } catch (e) {
      AppLogger.error('❌ 获取所有工作人员失败: $e');
      throw ConfigRepositoryException('获取工作人员列表失败', originalError: e);
    }
  }

  @override
  Future<WorkerConfig?> getWorkerById(String id) async {
    try {
      final data = await _dataSource.getConfig('$_workerPrefix$id');
      return data != null ? WorkerConfig.fromJson(data) : null;
    } catch (e) {
      AppLogger.error('❌ 根据ID获取工作人员失败: $id - $e');
      return null;
    }
  }

  @override
  Future<void> saveWorker(WorkerConfig worker) async {
    try {
      final updatedWorker = worker.copyWith(updatedAt: DateTime.now());
      await _dataSource.saveConfig('$_workerPrefix${worker.id}', updatedWorker.toJson());
      AppLogger.info('💾 保存工作人员成功: ${worker.name}');
    } catch (e) {
      AppLogger.error('❌ 保存工作人员失败: ${worker.name} - $e');
      throw ConfigRepositoryException('保存工作人员失败', originalError: e);
    }
  }

  @override
  Future<void> saveWorkers(List<WorkerConfig> workers) async {
    try {
      final configs = <String, Map<String, dynamic>>{};
      for (final worker in workers) {
        final updatedWorker = worker.copyWith(updatedAt: DateTime.now());
        configs['$_workerPrefix${worker.id}'] = updatedWorker.toJson();
      }
      
      await _dataSource.saveConfigs(configs);
      AppLogger.info('💾 批量保存工作人员成功: ${workers.length}人');
    } catch (e) {
      AppLogger.error('❌ 批量保存工作人员失败: $e');
      throw ConfigRepositoryException('批量保存工作人员失败', originalError: e);
    }
  }

  @override
  Future<void> deleteWorker(String id) async {
    try {
      await _dataSource.deleteConfig('$_workerPrefix$id');
      AppLogger.info('🗑️ 删除工作人员成功: $id');
    } catch (e) {
      AppLogger.error('❌ 删除工作人员失败: $id - $e');
      throw ConfigRepositoryException('删除工作人员失败', originalError: e);
    }
  }

  @override
  Future<List<WorkerConfig>> queryWorkers({
    String? warehouse,
    String? group,
    String? role,
    bool? isActive,
  }) async {
    try {
      final allWorkers = await getAllWorkers();
      
      return allWorkers.where((worker) {
        if (warehouse != null && worker.warehouse != warehouse) return false;
        if (group != null && worker.group != group) return false;
        if (role != null && worker.role != role) return false;
        if (isActive != null && worker.isActive != isActive) return false;
        return true;
      }).toList();
    } catch (e) {
      AppLogger.error('❌ 查询工作人员失败: $e');
      throw ConfigRepositoryException('查询工作人员失败', originalError: e);
    }
  }

  // ==================== 仓库配置 ====================

  @override
  Future<List<WarehouseConfig>> getAllWarehouses() async {
    try {
      final keys = await _dataSource.getAllConfigKeys();
      final warehouseKeys = keys.where((key) => key.startsWith(_warehousePrefix));
      
      final warehouses = <WarehouseConfig>[];
      for (final key in warehouseKeys) {
        final data = await _dataSource.getConfig(key);
        if (data != null) {
          try {
            warehouses.add(WarehouseConfig.fromJson(data));
          } catch (e) {
            AppLogger.warning('解析仓库配置失败: $key - $e');
          }
        }
      }
      
      return warehouses;
    } catch (e) {
      AppLogger.error('❌ 获取所有仓库失败: $e');
      throw ConfigRepositoryException('获取仓库列表失败', originalError: e);
    }
  }

  @override
  Future<WarehouseConfig?> getWarehouseById(String id) async {
    try {
      final data = await _dataSource.getConfig('$_warehousePrefix$id');
      return data != null ? WarehouseConfig.fromJson(data) : null;
    } catch (e) {
      AppLogger.error('❌ 根据ID获取仓库失败: $id - $e');
      return null;
    }
  }

  @override
  Future<void> saveWarehouse(WarehouseConfig warehouse) async {
    try {
      final updatedWarehouse = warehouse.copyWith(updatedAt: DateTime.now());
      await _dataSource.saveConfig('$_warehousePrefix${warehouse.id}', updatedWarehouse.toJson());
      AppLogger.info('💾 保存仓库成功: ${warehouse.name}');
    } catch (e) {
      AppLogger.error('❌ 保存仓库失败: ${warehouse.name} - $e');
      throw ConfigRepositoryException('保存仓库失败', originalError: e);
    }
  }

  @override
  Future<void> deleteWarehouse(String id) async {
    try {
      await _dataSource.deleteConfig('$_warehousePrefix$id');
      AppLogger.info('🗑️ 删除仓库成功: $id');
    } catch (e) {
      AppLogger.error('❌ 删除仓库失败: $id - $e');
      throw ConfigRepositoryException('删除仓库失败', originalError: e);
    }
  }

  // ==================== 工作组配置 ====================

  @override
  Future<List<GroupConfig>> getAllGroups() async {
    try {
      final keys = await _dataSource.getAllConfigKeys();
      final groupKeys = keys.where((key) => key.startsWith(_groupPrefix));
      
      final groups = <GroupConfig>[];
      for (final key in groupKeys) {
        final data = await _dataSource.getConfig(key);
        if (data != null) {
          try {
            groups.add(GroupConfig.fromJson(data));
          } catch (e) {
            AppLogger.warning('解析工作组配置失败: $key - $e');
          }
        }
      }
      
      return groups;
    } catch (e) {
      AppLogger.error('❌ 获取所有工作组失败: $e');
      throw ConfigRepositoryException('获取工作组列表失败', originalError: e);
    }
  }

  @override
  Future<GroupConfig?> getGroupById(String id) async {
    try {
      final data = await _dataSource.getConfig('$_groupPrefix$id');
      return data != null ? GroupConfig.fromJson(data) : null;
    } catch (e) {
      AppLogger.error('❌ 根据ID获取工作组失败: $id - $e');
      return null;
    }
  }

  @override
  Future<void> saveGroup(GroupConfig group) async {
    try {
      final updatedGroup = group.copyWith(updatedAt: DateTime.now());
      await _dataSource.saveConfig('$_groupPrefix${group.id}', updatedGroup.toJson());
      AppLogger.info('💾 保存工作组成功: ${group.name}');
    } catch (e) {
      AppLogger.error('❌ 保存工作组失败: ${group.name} - $e');
      throw ConfigRepositoryException('保存工作组失败', originalError: e);
    }
  }

  @override
  Future<void> deleteGroup(String id) async {
    try {
      await _dataSource.deleteConfig('$_groupPrefix$id');
      AppLogger.info('🗑️ 删除工作组成功: $id');
    } catch (e) {
      AppLogger.error('❌ 删除工作组失败: $id - $e');
      throw ConfigRepositoryException('删除工作组失败', originalError: e);
    }
  }

  @override
  Future<List<GroupConfig>> getGroupsByWarehouse(String warehouseId) async {
    try {
      final allGroups = await getAllGroups();
      return allGroups.where((group) => group.warehouseId == warehouseId).toList();
    } catch (e) {
      AppLogger.error('❌ 根据仓库获取工作组失败: $warehouseId - $e');
      throw ConfigRepositoryException('获取仓库工作组失败', originalError: e);
    }
  }

  // ==================== 数据迁移 ====================

  @override
  Future<void> migrateFromLegacyData() async {
    try {
      AppLogger.info('🔄 开始从硬编码数据迁移...');
      
      // 迁移工作人员数据
      await _migrateWorkerData();
      
      // 迁移仓库数据
      await _migrateWarehouseData();
      
      // 迁移工作组数据
      await _migrateGroupData();
      
      // 迁移模板数据
      await _migrateTemplateData();
      
      AppLogger.info('✅ 硬编码数据迁移完成');
    } catch (e) {
      AppLogger.error('❌ 数据迁移失败: $e');
      throw ConfigMigrationException('数据迁移失败', originalError: e);
    }
  }

  /// 检查并执行数据迁移
  Future<void> _checkAndMigrate() async {
    try {
      // 检查是否已有工作人员数据
      final workers = await getAllWorkers();
      if (workers.isEmpty) {
        AppLogger.info('🔍 检测到空的工作人员数据，开始迁移...');
        await migrateFromLegacyData();
      }
    } catch (e) {
      AppLogger.warning('检查数据迁移失败: $e');
    }
  }

  /// 迁移工作人员数据
  Future<void> _migrateWorkerData() async {
    try {
      final workerConfigs = allWorkers.map((worker) => WorkerConfig(
        id: worker.id,
        name: worker.name,
        role: worker.role,
        warehouse: worker.warehouse,
        group: worker.group,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      )).toList();
      
      await saveWorkers(workerConfigs);
      AppLogger.info('✅ 工作人员数据迁移完成: ${workerConfigs.length}人');
    } catch (e) {
      AppLogger.error('❌ 工作人员数据迁移失败: $e');
      rethrow;
    }
  }

  /// 迁移仓库数据
  Future<void> _migrateWarehouseData() async {
    try {
      // 从工作人员数据中提取仓库信息
      final warehouseNames = allWorkers.map((w) => w.warehouse).toSet().toList();
      
      final warehouseConfigs = warehouseNames.asMap().entries.map((entry) {
        return WarehouseConfig(
          id: (entry.key + 1).toString(),
          name: entry.value,
          location: '${entry.value}位置',
          isActive: true,
          description: '从工作人员数据迁移的仓库配置',
          supportedTemplates: ['平板车', '集装箱'],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }).toList();
      
      for (final warehouse in warehouseConfigs) {
        await saveWarehouse(warehouse);
      }
      
      AppLogger.info('✅ 仓库数据迁移完成: ${warehouseConfigs.length}个');
    } catch (e) {
      AppLogger.error('❌ 仓库数据迁移失败: $e');
      rethrow;
    }
  }

  /// 迁移工作组数据
  Future<void> _migrateGroupData() async {
    try {
      // 从工作人员数据中提取工作组信息
      final groupMap = <String, List<WorkerInfo>>{};
      for (final worker in allWorkers) {
        final key = '${worker.warehouse}_${worker.group}';
        groupMap[key] = (groupMap[key] ?? [])..add(worker);
      }
      
      int groupId = 1;
      for (final entry in groupMap.entries) {
        final parts = entry.key.split('_');
        final warehouse = parts[0];
        final groupName = parts[1];
        
        final groupConfig = GroupConfig(
          id: groupId.toString(),
          name: groupName,
          warehouseId: _getWarehouseIdByName(warehouse),
          isActive: true,
          description: '从工作人员数据迁移的工作组配置',
          memberIds: entry.value.map((w) => w.id).toList(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await saveGroup(groupConfig);
        groupId++;
      }
      
      AppLogger.info('✅ 工作组数据迁移完成: ${groupMap.length}个');
    } catch (e) {
      AppLogger.error('❌ 工作组数据迁移失败: $e');
      rethrow;
    }
  }

  /// 迁移模板数据
  Future<void> _migrateTemplateData() async {
    try {
      // 迁移平板车模板
      await _migrateTemplateConfig('平板车', '1');
      
      // 迁移集装箱模板
      await _migrateTemplateConfig('集装箱', '2');
      
      AppLogger.info('✅ 模板数据迁移完成');
    } catch (e) {
      AppLogger.error('❌ 模板数据迁移失败: $e');
      rethrow;
    }
  }

  /// 迁移单个模板配置
  Future<void> _migrateTemplateConfig(String templateName, String templateId) async {
    try {
      // 获取原始照片配置
      final photoConfigs = template_config.TemplateConfig.getPhotoConfigs(templateName);
      final photoGroups = template_config.TemplateConfig.getPhotoGroups(templateName);

      // 转换为新的配置模型
      final newPhotoGroups = photoGroups.map((group) => PhotoGroupModel(
        id: group.id,
        name: group.name,
        description: group.description,
        icon: group.icon,
        color: group.color,
        isRequired: group.isRequired,
        includeInStatistics: group.includeInStatistics,
        stage: group.stage,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      )).toList();

      final newPhotoConfigs = photoConfigs.map((config) => PhotoConfigModel(
        id: config.id,
        label: config.label,
        groupId: config.groupId,
        description: config.description,
        isRequired: config.isRequired,
        needRecognition: config.needRecognition,
        recognitionType: _convertRecognitionType(config.recognitionType),
        stage: config.stage,
        order: config.order,
        tips: config.tips,
        retryPrompt: config.retryPrompt,
        isCustom: config.isCustom,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      )).toList();
      
      // 创建模板配置
      final templateConfig = TemplateConfigModel(
        id: templateId,
        name: templateName,
        type: templateName, // 使用模板名称作为类型
        description: '从硬编码数据迁移的$templateName模板配置',
        photoGroups: newPhotoGroups,
        photoConfigs: newPhotoConfigs,
        totalPhotos: newPhotoConfigs.length,
        requiredPhotos: newPhotoConfigs.where((c) => c.isRequired).length,
        recognitionPhotos: newPhotoConfigs.where((c) => c.needRecognition).length,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await saveTemplate(templateConfig);
      
      // 保存照片组和照片配置
      for (final group in newPhotoGroups) {
        await savePhotoGroup(group);
      }
      
      for (final config in newPhotoConfigs) {
        await savePhotoConfig(config);
      }
      
      AppLogger.info('✅ $templateName 模板迁移完成');
    } catch (e) {
      AppLogger.error('❌ $templateName 模板迁移失败: $e');
      rethrow;
    }
  }

  /// 根据仓库名称获取仓库ID
  String _getWarehouseIdByName(String warehouseName) {
    // 简单的映射逻辑，实际应用中可能需要更复杂的逻辑
    switch (warehouseName) {
      case '1号库':
        return '1';
      case '2号库':
        return '2';
      case '3号库':
        return '3';
      default:
        return '1'; // 默认返回1号库
    }
  }

  // ==================== 其他方法的占位符 ====================
  // 由于文件长度限制，其他方法将在后续添加

  @override
  Future<List<TemplateConfigModel>> getAllTemplates() async {
    try {
      final templates = <TemplateConfigModel>[];
      final keys = await _dataSource.getAllConfigKeys();
      for (final key in keys) {
        if (key.startsWith(_templatePrefix)) {
          final data = await _dataSource.getConfig(key);
          if (data != null) {
            try {
              templates.add(TemplateConfigModel.fromJson(data));
            } catch (e) {
              AppLogger.warning('解析模板配置失败: $key - $e');
            }
          }
        }
      }
      return templates;
    } catch (e) {
      AppLogger.error('获取所有模板配置失败', error: e);
      return [];
    }
  }

  @override
  Future<TemplateConfigModel?> getTemplateById(String id) async {
    try {
      final key = '$_templatePrefix$id';
      final data = await _dataSource.getConfig(key);
      return data != null ? TemplateConfigModel.fromJson(data) : null;
    } catch (e) {
      AppLogger.error('根据ID获取模板配置失败', error: e);
      return null;
    }
  }

  @override
  Future<void> saveTemplate(TemplateConfigModel template) async {
    try {
      final key = '$_templatePrefix${template.id}';
      await _dataSource.saveConfig(key, template.toJson());
    } catch (e) {
      AppLogger.error('保存模板配置失败', error: e);
      rethrow;
    }
  }

  @override
  Future<void> deleteTemplate(String id) async {
    try {
      final key = '$_templatePrefix$id';
      await _dataSource.deleteConfig(key);
    } catch (e) {
      AppLogger.error('删除模板配置失败', error: e);
      rethrow;
    }
  }

  @override
  Future<List<PhotoConfigModel>> getPhotoConfigsByTemplate(String templateId) async {
    try {
      final configs = <PhotoConfigModel>[];
      final keys = await _dataSource.getAllConfigKeys();
      for (final key in keys) {
        if (key.startsWith(_photoConfigPrefix)) {
          final data = await _dataSource.getConfig(key);
          if (data != null && data['templateId'] == templateId) {
            try {
              configs.add(PhotoConfigModel.fromJson(data));
            } catch (e) {
              AppLogger.warning('解析照片配置失败: $key - $e');
            }
          }
        }
      }
      return configs;
    } catch (e) {
      AppLogger.error('获取模板照片配置失败', error: e);
      return [];
    }
  }

  @override
  Future<List<PhotoGroupModel>> getPhotoGroupsByTemplate(String templateId) async {
    try {
      final groups = <PhotoGroupModel>[];
      final keys = await _dataSource.getAllConfigKeys();
      for (final key in keys) {
        if (key.startsWith(_photoGroupPrefix)) {
          final data = await _dataSource.getConfig(key);
          if (data != null && data['templateId'] == templateId) {
            try {
              groups.add(PhotoGroupModel.fromJson(data));
            } catch (e) {
              AppLogger.warning('解析照片组配置失败: $key - $e');
            }
          }
        }
      }
      return groups;
    } catch (e) {
      AppLogger.error('获取模板照片组配置失败', error: e);
      return [];
    }
  }

  @override
  Future<void> savePhotoConfig(PhotoConfigModel photoConfig) async {
    try {
      final key = '$_photoConfigPrefix${photoConfig.id}';
      await _dataSource.saveConfig(key, photoConfig.toJson());
    } catch (e) {
      AppLogger.error('保存照片配置失败', error: e);
      rethrow;
    }
  }

  @override
  Future<void> savePhotoGroup(PhotoGroupModel photoGroup) async {
    try {
      final key = '$_photoGroupPrefix${photoGroup.id}';
      await _dataSource.saveConfig(key, photoGroup.toJson());
    } catch (e) {
      AppLogger.error('保存照片组配置失败', error: e);
      rethrow;
    }
  }

  @override
  Future<List<RoleConfig>> getAllRoles() async {
    try {
      final roles = <RoleConfig>[];
      final keys = await _dataSource.getAllConfigKeys();
      for (final key in keys) {
        if (key.startsWith(_rolePrefix)) {
          final data = await _dataSource.getConfig(key);
          if (data != null) {
            try {
              roles.add(RoleConfig.fromJson(data));
            } catch (e) {
              AppLogger.warning('解析角色配置失败: $key - $e');
            }
          }
        }
      }
      return roles;
    } catch (e) {
      AppLogger.error('获取所有角色配置失败', error: e);
      return [];
    }
  }

  @override
  Future<RoleConfig?> getRoleById(String id) async {
    try {
      final key = '$_rolePrefix$id';
      final data = await _dataSource.getConfig(key);
      return data != null ? RoleConfig.fromJson(data) : null;
    } catch (e) {
      AppLogger.error('根据ID获取角色配置失败', error: e);
      return null;
    }
  }

  @override
  Future<void> saveRole(RoleConfig role) async {
    try {
      final key = '$_rolePrefix${role.id}';
      await _dataSource.saveConfig(key, role.toJson());
    } catch (e) {
      AppLogger.error('保存角色配置失败', error: e);
      rethrow;
    }
  }

  @override
  Future<void> deleteRole(String id) async {
    try {
      final key = '$_rolePrefix$id';
      await _dataSource.deleteConfig(key);
    } catch (e) {
      AppLogger.error('删除角色配置失败', error: e);
      rethrow;
    }
  }

  @override
  Future<List<SystemConfig>> getAllSystemConfigs() async {
    try {
      final configs = <SystemConfig>[];
      final keys = await _dataSource.getAllConfigKeys();
      for (final key in keys) {
        if (key.startsWith(_systemPrefix)) {
          final data = await _dataSource.getConfig(key);
          if (data != null) {
            try {
              configs.add(SystemConfig.fromJson(data));
            } catch (e) {
              AppLogger.warning('解析系统配置失败: $key - $e');
            }
          }
        }
      }
      return configs;
    } catch (e) {
      AppLogger.error('获取所有系统配置失败', error: e);
      return [];
    }
  }

  @override
  Future<SystemConfig?> getSystemConfigByKey(String key) async {
    try {
      final configKey = '$_systemPrefix$key';
      final data = await _dataSource.getConfig(configKey);
      return data != null ? SystemConfig.fromJson(data) : null;
    } catch (e) {
      AppLogger.error('根据键获取系统配置失败', error: e);
      return null;
    }
  }

  @override
  Future<void> saveSystemConfig(SystemConfig config) async {
    try {
      final key = '$_systemPrefix${config.key}';
      await _dataSource.saveConfig(key, config.toJson());
    } catch (e) {
      AppLogger.error('保存系统配置失败', error: e);
      rethrow;
    }
  }

  @override
  Future<List<SystemConfig>> getSystemConfigsByCategory(String category) async {
    try {
      final configs = <SystemConfig>[];
      final allConfigs = await getAllSystemConfigs();
      for (final config in allConfigs) {
        if (config.category == category) {
          configs.add(config);
        }
      }
      return configs;
    } catch (e) {
      AppLogger.error('根据分类获取系统配置失败', error: e);
      return [];
    }
  }

  @override
  Future<Map<String, dynamic>> exportConfigs({
    bool includeWorkers = true,
    bool includeWarehouses = true,
    bool includeGroups = true,
    bool includeTemplates = true,
    bool includeRoles = true,
    bool includeSystemConfigs = true,
  }) async {
    try {
      final export = <String, dynamic>{
        'version': '1.0.0',
        'exportTime': DateTime.now().toIso8601String(),
      };

      if (includeWorkers) {
        final workers = await getAllWorkers();
        export['workers'] = workers.map((w) => w.toJson()).toList();
      }
      if (includeWarehouses) {
        final warehouses = await getAllWarehouses();
        export['warehouses'] = warehouses.map((w) => w.toJson()).toList();
      }
      if (includeGroups) {
        final groups = await getAllGroups();
        export['groups'] = groups.map((g) => g.toJson()).toList();
      }
      if (includeTemplates) {
        final templates = await getAllTemplates();
        export['templates'] = templates.map((t) => t.toJson()).toList();
      }
      if (includeRoles) {
        final roles = await getAllRoles();
        export['roles'] = roles.map((r) => r.toJson()).toList();
      }
      if (includeSystemConfigs) {
        final configs = await getAllSystemConfigs();
        export['systemConfigs'] = configs.map((c) => c.toJson()).toList();
      }

      return export;
    } catch (e) {
      AppLogger.error('导出配置数据失败', error: e);
      rethrow;
    }
  }

  @override
  Future<void> importConfigs(Map<String, dynamic> configExport, {
    bool overwriteExisting = false,
  }) async {
    try {
      if (configExport['workers'] != null) {
        final workers = (configExport['workers'] as List)
            .map((json) => WorkerConfig.fromJson(json))
            .toList();
        for (final worker in workers) {
          await saveWorker(worker);
        }
      }

      if (configExport['warehouses'] != null) {
        final warehouses = (configExport['warehouses'] as List)
            .map((json) => WarehouseConfig.fromJson(json))
            .toList();
        for (final warehouse in warehouses) {
          await saveWarehouse(warehouse);
        }
      }

      if (configExport['groups'] != null) {
        final groups = (configExport['groups'] as List)
            .map((json) => GroupConfig.fromJson(json))
            .toList();
        for (final group in groups) {
          await saveGroup(group);
        }
      }

      if (configExport['templates'] != null) {
        final templates = (configExport['templates'] as List)
            .map((json) => TemplateConfigModel.fromJson(json))
            .toList();
        for (final template in templates) {
          await saveTemplate(template);
        }
      }

      if (configExport['roles'] != null) {
        final roles = (configExport['roles'] as List)
            .map((json) => RoleConfig.fromJson(json))
            .toList();
        for (final role in roles) {
          await saveRole(role);
        }
      }

      if (configExport['systemConfigs'] != null) {
        final configs = (configExport['systemConfigs'] as List)
            .map((json) => SystemConfig.fromJson(json))
            .toList();
        for (final config in configs) {
          await saveSystemConfig(config);
        }
      }
    } catch (e) {
      AppLogger.error('导入配置数据失败', error: e);
      rethrow;
    }
  }

  @override
  Future<void> backupConfigs() async {
    // TODO: 实现备份配置数据
  }

  @override
  Future<void> restoreConfigs(String backupId) async {
    // TODO: 实现恢复配置数据
  }

  @override
  Future<void> saveConfigVersion(Map<String, dynamic> version) async {
    // TODO: 实现保存配置版本
  }

  @override
  Future<List<Map<String, dynamic>>> getConfigVersionHistory(String configType) async {
    // TODO: 实现获取配置版本历史
    return [];
  }

  @override
  Future<void> rollbackToVersion(String configType, int version) async {
    // TODO: 实现回滚到指定版本
  }

  @override
  Future<Map<String, dynamic>> getConfigStatistics() async {
    // TODO: 实现获取配置统计信息
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> validateConfigIntegrity() async {
    // TODO: 实现验证配置数据完整性
    return {};
  }

  @override
  Future<void> cleanupInvalidConfigs() async {
    // TODO: 实现清理无效配置
  }

  @override
  Future<void> close() async {
    await _dataSource.close();
    AppLogger.info('🔒 ConfigRepository 已关闭');
  }
}
