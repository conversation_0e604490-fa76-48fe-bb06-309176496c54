import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../services/logging_service.dart';

/// 统一手势导航系统
/// 参照微信、抖音等主流应用的手势导航体验
/// 在确保安全管理程序不受影响的前提下提供流畅的导航体验
class GestureNavigation {
  static const double _swipeThreshold = 100.0;
  static const double _velocityThreshold = 300.0;
  static const Duration _animationDuration = Duration(milliseconds: 300);

  /// 安全页面列表 - 这些页面禁用手势导航以确保安全
  static const Set<String> _securePages = {
    '/activation',
    '/enterprise-activation',
    '/admin-management',
    '/security-management',
    '/launcher',
  };

  /// 检查当前页面是否允许手势导航
  static bool _isGestureAllowed(BuildContext context) {
    final currentRoute = GoRouterState.of(context).uri.path;
    return !_securePages.contains(currentRoute);
  }

  /// 创建带手势导航的页面包装器
  static Widget wrapWithGesture({
    required Widget child,
    required BuildContext context,
    bool enableBackGesture = true,
    bool enableEdgeSwipe = true,
    VoidCallback? onBackGesture,
  }) {
    // 安全页面不启用手势导航
    if (!_isGestureAllowed(context)) {
      return child;
    }

    return GestureDetector(
      onPanStart: (details) => _onPanStart(details, context),
      onPanUpdate: (details) => _onPanUpdate(details, context),
      onPanEnd: (details) => _onPanEnd(details, context, onBackGesture),
      child: child,
    );
  }

  static void _onPanStart(DragStartDetails details, BuildContext context) {
    // 记录手势开始位置
    AppLogger.debug('手势导航开始: ${details.globalPosition}', tag: 'GestureNav');
  }

  static void _onPanUpdate(DragUpdateDetails details, BuildContext context) {
    // 实时更新手势状态
    final deltaX = details.delta.dx;
    
    // 右滑返回手势检测
    if (deltaX > 0 && details.globalPosition.dx < 50) {
      // 边缘右滑手势
      _handleEdgeSwipe(details, context);
    }
  }

  static void _onPanEnd(
    DragEndDetails details,
    BuildContext context,
    VoidCallback? onBackGesture,
  ) {
    final velocity = details.velocity.pixelsPerSecond;
    
    // 检测快速右滑返回
    if (velocity.dx > _velocityThreshold && velocity.dx.abs() > velocity.dy.abs()) {
      _performBackNavigation(context, onBackGesture);
    }
  }

  static void _handleEdgeSwipe(DragUpdateDetails details, BuildContext context) {
    // 边缘滑动视觉反馈
    HapticFeedback.selectionClick();
  }

  static void _performBackNavigation(BuildContext context, VoidCallback? onBackGesture) {
    try {
      if (onBackGesture != null) {
        onBackGesture();
      } else if (context.canPop()) {
        context.pop();
      }
      
      // 触觉反馈
      HapticFeedback.lightImpact();
      AppLogger.info('手势返回导航执行', tag: 'GestureNav');
    } catch (e) {
      AppLogger.error('手势导航失败', error: e, tag: 'GestureNav');
    }
  }

  /// 创建类似微信的页面切换动画
  static PageRouteBuilder<T> createSlideRoute<T>({
    required Widget page,
    required RouteSettings settings,
    bool isSecurePage = false,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: isSecurePage 
          ? Duration.zero  // 安全页面无动画
          : _animationDuration,
      reverseTransitionDuration: isSecurePage 
          ? Duration.zero 
          : _animationDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        if (isSecurePage) {
          return child; // 安全页面直接显示
        }

        // 类似微信的滑动动画
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// 创建类似抖音的底部弹出动画
  static PageRouteBuilder<T> createBottomSlideRoute<T>({
    required Widget page,
    required RouteSettings settings,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: _animationDuration,
      reverseTransitionDuration: _animationDuration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
    );
  }

  /// 创建淡入淡出动画（用于安全页面切换）
  static PageRouteBuilder<T> createFadeRoute<T>({
    required Widget page,
    required RouteSettings settings,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: const Duration(milliseconds: 200),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }

  /// 全局手势配置
  static void configureGlobalGestures(BuildContext context) {
    // 配置系统级手势
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: [SystemUiOverlay.top],
    );
  }

  /// 禁用手势导航（用于安全页面）
  static Widget disableGestures({required Widget child}) {
    return PopScope(
      canPop: false,
      child: child,
    );
  }

  /// 创建自定义返回按钮
  static Widget createBackButton({
    required BuildContext context,
    VoidCallback? onPressed,
    Color? color,
  }) {
    return IconButton(
      icon: Icon(
        Icons.arrow_back_ios,
        color: color ?? Theme.of(context).iconTheme.color,
      ),
      onPressed: onPressed ?? () {
        if (context.canPop()) {
          context.pop();
        }
      },
    );
  }

  /// 创建手势指示器
  static Widget createGestureIndicator({
    required BuildContext context,
    bool showBackHint = true,
  }) {
    if (!_isGestureAllowed(context) || !showBackHint) {
      return const SizedBox.shrink();
    }

    return Positioned(
      left: 0,
      top: 0,
      bottom: 0,
      width: 20,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Colors.white.withValues(alpha: 0.1),
              Colors.transparent,
            ],
          ),
        ),
      ),
    );
  }

  /// 检查是否支持手势导航
  static bool isGestureSupported(BuildContext context) {
    return _isGestureAllowed(context);
  }

  /// 获取当前页面的手势配置
  static Map<String, dynamic> getGestureConfig(BuildContext context) {
    final isAllowed = _isGestureAllowed(context);
    return {
      'gestureEnabled': isAllowed,
      'backGestureEnabled': isAllowed,
      'edgeSwipeEnabled': isAllowed,
      'animationEnabled': isAllowed,
    };
  }
}

/// 手势导航混入类
mixin GestureNavigationMixin<T extends StatefulWidget> on State<T> {
  bool get enableGestureNavigation => true;
  bool get enableBackGesture => true;
  bool get enableEdgeSwipe => true;

  @override
  Widget build(BuildContext context) {
    final child = buildContent(context);
    
    if (!enableGestureNavigation) {
      return child;
    }

    return GestureNavigation.wrapWithGesture(
      context: context,
      enableBackGesture: enableBackGesture,
      enableEdgeSwipe: enableEdgeSwipe,
      onBackGesture: onBackGesture,
      child: child,
    );
  }

  /// 子类需要实现的内容构建方法
  Widget buildContent(BuildContext context);

  /// 自定义返回手势处理
  void onBackGesture() {
    if (context.canPop()) {
      context.pop();
    }
  }
}
