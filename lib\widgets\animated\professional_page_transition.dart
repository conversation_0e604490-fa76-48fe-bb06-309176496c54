import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../theme/material3_theme.dart';

/// 🎯 Material 3 专业级页面转换动画组件
/// 完全符合 Material Design 3 规范的页面转换系统
class ProfessionalPageTransition<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Material3TransitionType type;
  final Duration duration;
  final Duration reverseDuration;
  final Curve curve;
  final Curve reverseCurve;
  final Alignment? alignment;
  final Offset? offset;

  ProfessionalPageTransition({
    required this.child,
    this.type = Material3TransitionType.sharedAxis,
    this.duration = const Duration(milliseconds: 300),
    this.reverseDuration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOutCubic,
    this.reverseCurve = Curves.easeInOutCubic,
    this.alignment,
    this.offset,
    RouteSettings? settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionDuration: duration,
          reverseTransitionDuration: reverseDuration,
          settings: settings,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildTransition(
              context,
              animation,
              secondaryAnimation,
              child,
              type,
              curve,
              reverseCurve,
              alignment,
              offset,
            );
          },
        );

  /// 创建共享轴转换
  ProfessionalPageTransition.sharedAxis({
    required Widget child,
    Material3SharedAxisType axisType = Material3SharedAxisType.horizontal,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 300),
    RouteSettings? settings,
  }) : this(
          child: child,
          type: Material3TransitionType.sharedAxis,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: Material3Theme.emphasizedCurve,
          reverseCurve: Material3Theme.emphasizedCurve,
          offset: axisType == Material3SharedAxisType.horizontal
              ? const Offset(1.0, 0.0)
              : axisType == Material3SharedAxisType.vertical
                  ? const Offset(0.0, 1.0)
                  : const Offset(0.0, 0.0),
          settings: settings,
        );

  /// 创建淡入淡出转换
  ProfessionalPageTransition.fadeThrough({
    required Widget child,
    Duration duration = const Duration(milliseconds: 210),
    Duration reverseDuration = const Duration(milliseconds: 210),
    RouteSettings? settings,
  }) : this(
          child: child,
          type: Material3TransitionType.fadeThrough,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: Material3Theme.standardCurve,
          reverseCurve: Material3Theme.standardCurve,
          settings: settings,
        );

  /// 创建容器变换转换
  ProfessionalPageTransition.containerTransform({
    required Widget child,
    Duration duration = const Duration(milliseconds: 300),
    Duration reverseDuration = const Duration(milliseconds: 300),
    RouteSettings? settings,
  }) : this(
          child: child,
          type: Material3TransitionType.containerTransform,
          duration: duration,
          reverseDuration: reverseDuration,
          curve: Material3Theme.emphasizedCurve,
          reverseCurve: Material3Theme.emphasizedCurve,
          settings: settings,
        );

  static Widget _buildTransition(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
    Material3TransitionType type,
    Curve curve,
    Curve reverseCurve,
    Alignment? alignment,
    Offset? offset,
  ) {
    switch (type) {
      case Material3TransitionType.sharedAxis:
        return _SharedAxisTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          offset: offset ?? const Offset(1.0, 0.0),
          child: child,
        );

      case Material3TransitionType.fadeThrough:
        return _FadeThroughTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
        );

      case Material3TransitionType.containerTransform:
        return _ContainerTransformTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
        );

      case Material3TransitionType.slide:
        return _SlideTransition(
          animation: animation,
          offset: offset ?? const Offset(1.0, 0.0),
          child: child,
        );

      case Material3TransitionType.scale:
        return _ScaleTransition(
          animation: animation,
          alignment: alignment ?? Alignment.center,
          child: child,
        );

      case Material3TransitionType.rotation:
        return _RotationTransition(
          animation: animation,
          alignment: alignment ?? Alignment.center,
          child: child,
        );
    }
  }
}

/// Material 3 转换类型枚举
enum Material3TransitionType {
  sharedAxis,        // 共享轴转换
  fadeThrough,       // 淡入淡出转换
  containerTransform, // 容器变换转换
  slide,             // 滑动转换
  scale,             // 缩放转换
  rotation,          // 旋转转换
}

/// Material 3 共享轴类型枚举
enum Material3SharedAxisType {
  horizontal, // 水平轴
  vertical,   // 垂直轴
  scaled,     // 缩放轴
}

/// 共享轴转换组件
class _SharedAxisTransition extends StatelessWidget {
  final Animation<double> animation;
  final Animation<double> secondaryAnimation;
  final Offset offset;
  final Widget child;

  const _SharedAxisTransition({
    required this.animation,
    required this.secondaryAnimation,
    required this.offset,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: offset,
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Material3Theme.emphasizedCurve,
      )),
      child: SlideTransition(
        position: Tween<Offset>(
          begin: Offset.zero,
          end: Offset(-offset.dx, -offset.dy),
        ).animate(CurvedAnimation(
          parent: secondaryAnimation,
          curve: Material3Theme.emphasizedCurve,
        )),
        child: FadeTransition(
          opacity: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
          )),
          child: FadeTransition(
            opacity: Tween<double>(
              begin: 1.0,
              end: 0.0,
            ).animate(CurvedAnimation(
              parent: secondaryAnimation,
              curve: const Interval(0.0, 0.3, curve: Curves.easeOut),
            )),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// 淡入淡出转换组件
class _FadeThroughTransition extends StatelessWidget {
  final Animation<double> animation;
  final Animation<double> secondaryAnimation;
  final Widget child;

  const _FadeThroughTransition({
    required this.animation,
    required this.secondaryAnimation,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: const Interval(0.35, 1.0, curve: Curves.easeOut),
      )),
      child: FadeTransition(
        opacity: Tween<double>(
          begin: 1.0,
          end: 0.0,
        ).animate(CurvedAnimation(
          parent: secondaryAnimation,
          curve: const Interval(0.0, 0.35, curve: Curves.easeOut),
        )),
        child: ScaleTransition(
          scale: Tween<double>(
            begin: 0.92,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Material3Theme.standardCurve,
          )),
          child: child,
        ),
      ),
    );
  }
}

/// 容器变换转换组件
class _ContainerTransformTransition extends StatelessWidget {
  final Animation<double> animation;
  final Animation<double> secondaryAnimation;
  final Widget child;

  const _ContainerTransformTransition({
    required this.animation,
    required this.secondaryAnimation,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: CurvedAnimation(
        parent: animation,
        curve: const Interval(0.0, 1.0, curve: Curves.easeInOut),
      ),
      child: ScaleTransition(
        scale: Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Material3Theme.emphasizedCurve,
        )),
        child: child,
      ),
    );
  }
}

/// 滑动转换组件
class _SlideTransition extends StatelessWidget {
  final Animation<double> animation;
  final Offset offset;
  final Widget child;

  const _SlideTransition({
    required this.animation,
    required this.offset,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: offset,
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Material3Theme.emphasizedCurve,
      )),
      child: child,
    );
  }
}

/// 缩放转换组件
class _ScaleTransition extends StatelessWidget {
  final Animation<double> animation;
  final Alignment alignment;
  final Widget child;

  const _ScaleTransition({
    required this.animation,
    required this.alignment,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Material3Theme.emphasizedCurve,
      )),
      alignment: alignment,
      child: child,
    );
  }
}

/// 旋转转换组件
class _RotationTransition extends StatelessWidget {
  final Animation<double> animation;
  final Alignment alignment;
  final Widget child;

  const _RotationTransition({
    required this.animation,
    required this.alignment,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Material3Theme.emphasizedCurve,
      )),
      alignment: alignment,
      child: child,
    );
  }
}

/// 🎯 Material 3 页面转换工具类
/// 提供便捷的页面转换方法
class Material3PageTransitions {
  Material3PageTransitions._();

  /// 推入新页面（共享轴转换）
  static Future<T?> pushSharedAxis<T extends Object?>(
    BuildContext context,
    Widget page, {
    Material3SharedAxisType axisType = Material3SharedAxisType.horizontal,
    Duration duration = const Duration(milliseconds: 300),
    RouteSettings? settings,
  }) {
    return Navigator.of(context).push<T>(
      ProfessionalPageTransition<T>.sharedAxis(
        child: page,
        axisType: axisType,
        duration: duration,
        settings: settings,
      ),
    );
  }

  /// 推入新页面（淡入淡出转换）
  static Future<T?> pushFadeThrough<T extends Object?>(
    BuildContext context,
    Widget page, {
    Duration duration = const Duration(milliseconds: 210),
    RouteSettings? settings,
  }) {
    return Navigator.of(context).push<T>(
      ProfessionalPageTransition<T>.fadeThrough(
        child: page,
        duration: duration,
        settings: settings,
      ),
    );
  }

  /// 推入新页面（容器变换转换）
  static Future<T?> pushContainerTransform<T extends Object?>(
    BuildContext context,
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    RouteSettings? settings,
  }) {
    return Navigator.of(context).push<T>(
      ProfessionalPageTransition<T>.containerTransform(
        child: page,
        duration: duration,
        settings: settings,
      ),
    );
  }

  /// 替换当前页面（共享轴转换）
  static Future<T?> replaceSharedAxis<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget page, {
    Material3SharedAxisType axisType = Material3SharedAxisType.horizontal,
    Duration duration = const Duration(milliseconds: 300),
    TO? result,
    RouteSettings? settings,
  }) {
    return Navigator.of(context).pushReplacement<T, TO>(
      ProfessionalPageTransition<T>.sharedAxis(
        child: page,
        axisType: axisType,
        duration: duration,
        settings: settings,
      ),
      result: result,
    );
  }

  /// 替换当前页面（淡入淡出转换）
  static Future<T?> replaceFadeThrough<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget page, {
    Duration duration = const Duration(milliseconds: 210),
    TO? result,
    RouteSettings? settings,
  }) {
    return Navigator.of(context).pushReplacement<T, TO>(
      ProfessionalPageTransition<T>.fadeThrough(
        child: page,
        duration: duration,
        settings: settings,
      ),
      result: result,
    );
  }
}

/// 🎯 Material 3 Hero 动画组件
/// 增强的 Hero 动画，支持 Material 3 规范
class ProfessionalHero extends StatelessWidget {
  final Object tag;
  final Widget child;
  final CreateRectTween? createRectTween;
  final HeroFlightShuttleBuilder? flightShuttleBuilder;
  final HeroPlaceholderBuilder? placeholderBuilder;
  final bool transitionOnUserGestures;

  const ProfessionalHero({
    Key? key,
    required this.tag,
    required this.child,
    this.createRectTween,
    this.flightShuttleBuilder,
    this.placeholderBuilder,
    this.transitionOnUserGestures = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: tag,
      createRectTween: createRectTween ?? _createMaterial3RectTween,
      flightShuttleBuilder: flightShuttleBuilder ?? _material3FlightShuttleBuilder,
      placeholderBuilder: placeholderBuilder,
      transitionOnUserGestures: transitionOnUserGestures,
      child: child,
    );
  }

  /// Material 3 矩形补间创建器
  static RectTween _createMaterial3RectTween(Rect? begin, Rect? end) {
    return MaterialRectArcTween(begin: begin, end: end);
  }

  /// Material 3 飞行穿梭构建器
  static Widget _material3FlightShuttleBuilder(
    BuildContext flightContext,
    Animation<double> animation,
    HeroFlightDirection flightDirection,
    BuildContext fromHeroContext,
    BuildContext toHeroContext,
  ) {
    final Hero toHero = toHeroContext.widget as Hero;
    final Hero fromHero = fromHeroContext.widget as Hero;

    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Material(
          type: MaterialType.transparency,
          child: FadeTransition(
            opacity: CurvedAnimation(
              parent: animation,
              curve: Material3Theme.emphasizedCurve,
            ),
            child: ScaleTransition(
              scale: Tween<double>(
                begin: 0.8,
                end: 1.0,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Material3Theme.emphasizedCurve,
              )),
              child: flightDirection == HeroFlightDirection.push
                  ? toHero.child
                  : fromHero.child,
            ),
          ),
        );
      },
    );
  }
}

/// 🎯 Material 3 页面视图转换组件
/// 用于 PageView 的页面转换效果
class ProfessionalPageView extends StatelessWidget {
  final PageController? controller;
  final List<Widget> children;
  final Material3TransitionType transitionType;
  final Duration transitionDuration;
  final Curve curve;
  final ValueChanged<int>? onPageChanged;
  final bool reverse;

  const ProfessionalPageView({
    Key? key,
    this.controller,
    required this.children,
    this.transitionType = Material3TransitionType.sharedAxis,
    this.transitionDuration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOutCubic,
    this.onPageChanged,
    this.reverse = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      controller: controller,
      onPageChanged: onPageChanged,
      reverse: reverse,
      itemCount: children.length,
      itemBuilder: (context, index) {
        return AnimatedBuilder(
          animation: controller ?? PageController(),
          builder: (context, child) {
            double value = 0.0;
            if (controller?.hasClients == true) {
              value = controller!.page! - index;
            }

            return _buildTransitionPage(
              child: children[index],
              value: value,
            );
          },
        );
      },
    );
  }

  Widget _buildTransitionPage({
    required Widget child,
    required double value,
  }) {
    switch (transitionType) {
      case Material3TransitionType.sharedAxis:
        return Transform.translate(
          offset: Offset(value * 300, 0),
          child: Opacity(
            opacity: (1 - value.abs()).clamp(0.0, 1.0),
            child: child,
          ),
        );

      case Material3TransitionType.fadeThrough:
        return Opacity(
          opacity: (1 - value.abs()).clamp(0.0, 1.0),
          child: Transform.scale(
            scale: (1 - value.abs() * 0.1).clamp(0.9, 1.0),
            child: child,
          ),
        );

      case Material3TransitionType.scale:
        return Transform.scale(
          scale: (1 - value.abs() * 0.3).clamp(0.7, 1.0),
          child: child,
        );

      case Material3TransitionType.rotation:
        return Transform.rotate(
          angle: value * math.pi * 0.1,
          child: child,
        );

      default:
        return child;
    }
  }
}

/// 🎯 Material 3 底部表单转换组件
/// 用于底部表单的出现和消失动画
class ProfessionalBottomSheetTransition extends StatelessWidget {
  final Animation<double> animation;
  final Widget child;

  const ProfessionalBottomSheetTransition({
    Key? key,
    required this.animation,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0.0, 1.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Material3Theme.emphasizedCurve,
      )),
      child: FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
        ),
        child: child,
      ),
    );
  }
}

/// 🎯 Material 3 对话框转换组件
/// 用于对话框的出现和消失动画
class ProfessionalDialogTransition extends StatelessWidget {
  final Animation<double> animation;
  final Widget child;

  const ProfessionalDialogTransition({
    Key? key,
    required this.animation,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: Material3Theme.emphasizedCurve,
      )),
      child: FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: Material3Theme.standardCurve,
        ),
        child: child,
      ),
    );
  }
}