import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/performance_manager.dart';
import 'package:loadguard/services/resource_manager.dart';
import 'package:loadguard/services/local_data_manager.dart';
import 'package:loadguard/services/encryption_service.dart';
import 'package:loadguard/services/secure_storage_service.dart';
import 'package:loadguard/services/data_security_manager.dart';
import 'package:loadguard/services/global_error_handler.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/repositories/hive_task_data_source.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/config_models.dart';

void main() {
  group('System Integration Tests', () {
    late ProviderContainer container;
    
    setUp(() {
      container = ProviderContainer();
    });
    
    tearDown(() {
      container.dispose();
    });
    
    group('Core Services Integration', () {
      test('should initialize all core services successfully', () async {
        // 测试所有核心服务的初始化
        final taskService = TaskService();
        final mlkitService = MLKitTextRecognitionService();
        final performanceManager = PerformanceManager.instance;
        
        // 初始化服务
        await taskService.initialize();
        performanceManager.initialize();
        
        // 验证服务状态
        expect(taskService.isInitialized, true);
        expect(performanceManager, isNotNull);
        expect(mlkitService, isNotNull);
        
        // 清理
        await taskService.dispose();
        await performanceManager.dispose();
        mlkitService.dispose();
      });
      
      test('should handle task lifecycle correctly', () async {
        final taskService = TaskService();
        await taskService.initialize();
        
        // 创建测试任务
        final task = TaskModel(
          id: 'test_task_001',
          templateId: 'template_001',
          workerId: 'worker_001',
          warehouseId: 'warehouse_001',
          status: TaskStatus.pending,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          photos: [],
          recognitionResults: [],
          workloadData: {},
        );
        
        // 添加任务
        await taskService.addTask(task);
        expect(taskService.tasks.length, 1);
        expect(taskService.tasks.first.id, 'test_task_001');
        
        // 更新任务状态
        final updatedTask = task.copyWith(status: TaskStatus.inProgress);
        await taskService.updateTask(updatedTask);
        
        final retrievedTask = taskService.getTaskById('test_task_001');
        expect(retrievedTask?.status, TaskStatus.inProgress);
        
        // 删除任务
        await taskService.deleteTask('test_task_001');
        expect(taskService.tasks.length, 0);
        
        await taskService.dispose();
      });
    });
    
    group('Data Security Integration', () {
      test('should encrypt and decrypt sensitive data correctly', () async {
        final encryptionService = EncryptionService();
        final secureStorage = SecureStorageService();
        final errorHandler = GlobalErrorHandler();
        
        await encryptionService.initialize(customKey: 'test_key_for_integration');
        await secureStorage.initialize(encryptionService);
        
        final securityManager = DataSecurityManager(encryptionService, secureStorage);
        await securityManager.initialize();
        
        // 测试敏感数据保护
        const testData = {'activationCode': 'TEST-ACTIVATION-CODE-123'};
        await securityManager.protectSensitiveData('activation_code', testData);
        
        // 测试数据访问
        final retrievedData = await securityManager.accessSensitiveData<Map<String, dynamic>>(
          'activation_code',
          reason: 'Integration test verification',
        );
        
        expect(retrievedData, isNotNull);
        expect(retrievedData!['activationCode'], 'TEST-ACTIVATION-CODE-123');
        
        // 测试安全审计
        final auditReport = await securityManager.performSecurityAudit();
        expect(auditReport.securityScore, greaterThan(0));
        expect(auditReport.recommendations, isNotEmpty);
        
        // 清理
        await securityManager.dispose();
        await secureStorage.dispose();
        encryptionService.dispose();
      });
    });
    
    group('Performance Management Integration', () {
      test('should monitor and optimize performance correctly', () async {
        final performanceManager = PerformanceManager.instance;
        final resourceManager = ResourceManager(performanceManager);
        
        performanceManager.initialize();
        
        // 创建资源组
        final testGroup = resourceManager.createResourceGroup('integration_test');
        
        // 添加一些资源
        final timer = testGroup.createTimer(const Duration(milliseconds: 100), () {});
        final controller = testGroup.createStreamController<String>();
        
        expect(testGroup.resourceCount, 2);
        
        // 记录性能指标
        performanceManager.recordMetric('test_metric', 100.0, metadata: {
          'test': 'integration',
          'group': 'performance',
        });
        
        // 测量执行时间
        final result = await performanceManager.measureExecutionTime(
          'integration_test_operation',
          () async {
            await Future.delayed(const Duration(milliseconds: 50));
            return 'test_result';
          },
        );
        
        expect(result, 'test_result');
        
        // 获取性能统计
        final stats = performanceManager.getPerformanceStatistics();
        expect(stats.totalMetrics, greaterThan(0));
        expect(stats.metricStats.containsKey('test_metric'), true);
        expect(stats.metricStats.containsKey('execution_time_integration_test_operation'), true);
        
        // 优化性能
        await performanceManager.optimizePerformance();
        
        // 清理资源
        await resourceManager.dispose();
        await performanceManager.dispose();
      });
    });
    
    group('Local Data Management Integration', () {
      test('should manage local data lifecycle correctly', () async {
        final encryptionService = EncryptionService();
        final secureStorage = SecureStorageService();
        final errorHandler = GlobalErrorHandler();
        
        await encryptionService.initialize(customKey: 'test_key_local_data');
        await secureStorage.initialize(encryptionService);
        
        final localDataManager = LocalDataManager(secureStorage, errorHandler);
        await localDataManager.initialize();
        
        // 测试任务数据保存和加载
        final testTasks = [
          TaskModel(
            id: 'local_test_001',
            templateId: 'template_001',
            workerId: 'worker_001',
            warehouseId: 'warehouse_001',
            status: TaskStatus.completed,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            photos: [],
            recognitionResults: [],
            workloadData: {},
          ),
        ];
        
        await localDataManager.saveTaskData(testTasks);
        final loadedTasks = await localDataManager.loadTaskData();
        
        expect(loadedTasks.length, 1);
        expect(loadedTasks.first.id, 'local_test_001');
        expect(loadedTasks.first.status, TaskStatus.completed);
        
        // 测试配置数据
        final testConfig = {
          'appVersion': '1.0.0',
          'enableDebug': true,
          'maxRetries': 3,
        };
        
        await localDataManager.saveConfigData(testConfig);
        final loadedConfig = await localDataManager.loadConfigData();
        
        expect(loadedConfig['appVersion'], '1.0.0');
        expect(loadedConfig['enableDebug'], true);
        expect(loadedConfig['maxRetries'], 3);
        
        // 测试数据导出
        final exportPath = await localDataManager.exportData();
        expect(exportPath, isNotEmpty);
        expect(exportPath, contains('loadguard_export'));
        
        // 测试数据统计
        final stats = await localDataManager.getDataStatistics();
        expect(stats.taskCount, 1);
        expect(stats.configItemCount, 3);
        expect(stats.dataSize, greaterThan(0));
        expect(stats.isLocalApp, true);
        
        // 清理
        await localDataManager.dispose();
        await secureStorage.dispose();
        encryptionService.dispose();
      });
    });
    
    group('Error Handling Integration', () {
      test('should handle errors gracefully across services', () async {
        final errorHandler = GlobalErrorHandler();
        
        // 测试网络错误处理
        final networkError = Exception('Network connection failed');
        await errorHandler.handleException(
          networkError,
          operation: 'integration_test_network',
        );
        
        // 测试存储错误处理
        final storageError = Exception('Storage access denied');
        await errorHandler.handleStorageError(
          storageError,
          operation: 'integration_test_storage',
        );
        
        // 测试识别错误处理
        final recognitionError = Exception('Recognition algorithm failed');
        await errorHandler.handleRecognitionError(
          recognitionError,
          operation: 'integration_test_recognition',
        );
        
        // 获取错误统计
        final stats = errorHandler.getErrorStatistics();
        expect(stats.totalErrors, 3);
        expect(stats.errorsByCategory.length, greaterThan(0));
        
        // 获取优化建议
        final recommendations = errorHandler.getOptimizationRecommendations();
        expect(recommendations, isNotEmpty);
        
        // 清理
        await errorHandler.dispose();
      });
    });
    
    group('End-to-End Workflow Integration', () {
      test('should complete full task workflow successfully', () async {
        // 初始化所有必要的服务
        final taskService = TaskService();
        final performanceManager = PerformanceManager.instance;
        final encryptionService = EncryptionService();
        final secureStorage = SecureStorageService();
        final errorHandler = GlobalErrorHandler();
        
        await taskService.initialize();
        performanceManager.initialize();
        await encryptionService.initialize(customKey: 'e2e_test_key');
        await secureStorage.initialize(encryptionService);
        
        final securityManager = DataSecurityManager(encryptionService, secureStorage);
        await securityManager.initialize();
        
        // 1. 创建任务
        final task = TaskModel(
          id: 'e2e_test_001',
          templateId: 'template_001',
          workerId: 'worker_001',
          warehouseId: 'warehouse_001',
          status: TaskStatus.pending,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          photos: [],
          recognitionResults: [],
          workloadData: {},
        );
        
        await taskService.addTask(task);
        
        // 2. 记录性能指标
        performanceManager.recordMetric('e2e_task_created', 1.0);
        
        // 3. 保护敏感数据
        await securityManager.protectSensitiveData('e2e_task_data', {
          'taskId': task.id,
          'sensitiveInfo': 'confidential_data',
        });
        
        // 4. 更新任务状态
        final updatedTask = task.copyWith(status: TaskStatus.inProgress);
        await taskService.updateTask(updatedTask);
        
        // 5. 模拟任务完成
        final completedTask = updatedTask.copyWith(
          status: TaskStatus.completed,
          updatedAt: DateTime.now(),
        );
        await taskService.updateTask(completedTask);
        
        // 6. 验证最终状态
        final finalTask = taskService.getTaskById('e2e_test_001');
        expect(finalTask?.status, TaskStatus.completed);
        
        // 7. 获取性能统计
        final perfStats = performanceManager.getPerformanceStatistics();
        expect(perfStats.totalMetrics, greaterThan(0));
        
        // 8. 执行安全审计
        final auditReport = await securityManager.performSecurityAudit();
        expect(auditReport.securityScore, greaterThan(0));
        
        // 9. 清理所有资源
        await taskService.dispose();
        await securityManager.dispose();
        await secureStorage.dispose();
        encryptionService.dispose();
        await performanceManager.dispose();
      });
    });
  });
}
