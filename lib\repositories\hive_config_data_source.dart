import 'dart:convert';
import 'package:hive/hive.dart';
import 'package:loadguard/repositories/config_repository.dart';
import 'package:loadguard/utils/app_logger.dart';

/// Hive配置数据源实现
/// 使用Hive作为配置数据的本地存储
class HiveConfigDataSource implements ConfigDataSource {
  static const String _configBoxName = 'config_box';
  Box<String>? _configBox;
  bool _isInitialized = false;

  @override
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;
      
      // 确保Hive已初始化
      if (!Hive.isBoxOpen(_configBoxName)) {
        _configBox = await Hive.openBox<String>(_configBoxName);
      } else {
        _configBox = Hive.box<String>(_configBoxName);
      }
      
      _isInitialized = true;
      AppLogger.info('✅ HiveConfigDataSource 初始化成功');
    } catch (e) {
      AppLogger.error('❌ HiveConfigDataSource 初始化失败: $e');
      throw ConfigRepositoryException('Hive配置数据源初始化失败', originalError: e);
    }
  }

  @override
  Future<void> saveConfig(String key, Map<String, dynamic> data) async {
    await _ensureInitialized();
    
    try {
      final jsonString = jsonEncode(data);
      await _configBox!.put(key, jsonString);
      AppLogger.info('💾 配置保存成功: $key');
    } catch (e) {
      AppLogger.error('❌ 配置保存失败: $key - $e');
      throw ConfigRepositoryException('保存配置失败', originalError: e);
    }
  }

  @override
  Future<Map<String, dynamic>?> getConfig(String key) async {
    await _ensureInitialized();
    
    try {
      final jsonString = _configBox!.get(key);
      if (jsonString == null) return null;
      
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      AppLogger.error('❌ 配置获取失败: $key - $e');
      return null;
    }
  }

  @override
  Future<void> deleteConfig(String key) async {
    await _ensureInitialized();
    
    try {
      await _configBox!.delete(key);
      AppLogger.info('🗑️ 配置删除成功: $key');
    } catch (e) {
      AppLogger.error('❌ 配置删除失败: $key - $e');
      throw ConfigRepositoryException('删除配置失败', originalError: e);
    }
  }

  @override
  Future<List<String>> getAllConfigKeys() async {
    await _ensureInitialized();
    
    try {
      return _configBox!.keys.cast<String>().toList();
    } catch (e) {
      AppLogger.error('❌ 获取配置键列表失败: $e');
      return [];
    }
  }

  @override
  Future<void> saveConfigs(Map<String, Map<String, dynamic>> configs) async {
    await _ensureInitialized();
    
    try {
      final Map<String, String> jsonConfigs = {};
      for (final entry in configs.entries) {
        jsonConfigs[entry.key] = jsonEncode(entry.value);
      }
      
      await _configBox!.putAll(jsonConfigs);
      AppLogger.info('💾 批量配置保存成功: ${configs.length}个');
    } catch (e) {
      AppLogger.error('❌ 批量配置保存失败: $e');
      throw ConfigRepositoryException('批量保存配置失败', originalError: e);
    }
  }

  @override
  Future<void> clearAllConfigs() async {
    await _ensureInitialized();
    
    try {
      await _configBox!.clear();
      AppLogger.info('🧹 清空所有配置成功');
    } catch (e) {
      AppLogger.error('❌ 清空配置失败: $e');
      throw ConfigRepositoryException('清空配置失败', originalError: e);
    }
  }

  @override
  Future<bool> isAvailable() async {
    try {
      await _ensureInitialized();
      return _configBox != null && _configBox!.isOpen;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> close() async {
    try {
      if (_configBox != null && _configBox!.isOpen) {
        await _configBox!.close();
      }
      _isInitialized = false;
      AppLogger.info('🔒 HiveConfigDataSource 已关闭');
    } catch (e) {
      AppLogger.error('❌ 关闭HiveConfigDataSource失败: $e');
    }
  }

  /// 确保数据源已初始化
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// 获取配置数据的统计信息
  Future<Map<String, dynamic>> getConfigStatistics() async {
    await _ensureInitialized();
    
    try {
      final keys = await getAllConfigKeys();
      final stats = <String, int>{};
      
      for (final key in keys) {
        final prefix = key.split('_').first;
        stats[prefix] = (stats[prefix] ?? 0) + 1;
      }
      
      return {
        'totalConfigs': keys.length,
        'configTypes': stats,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('❌ 获取配置统计失败: $e');
      return {};
    }
  }

  /// 备份配置数据到JSON
  Future<Map<String, dynamic>> backupToJson() async {
    await _ensureInitialized();
    
    try {
      final keys = await getAllConfigKeys();
      final backup = <String, dynamic>{};
      
      for (final key in keys) {
        final config = await getConfig(key);
        if (config != null) {
          backup[key] = config;
        }
      }
      
      return {
        'version': '1.0',
        'timestamp': DateTime.now().toIso8601String(),
        'configs': backup,
      };
    } catch (e) {
      AppLogger.error('❌ 备份配置到JSON失败: $e');
      rethrow;
    }
  }

  /// 从JSON恢复配置数据
  Future<void> restoreFromJson(Map<String, dynamic> backup) async {
    await _ensureInitialized();
    
    try {
      final configs = backup['configs'] as Map<String, dynamic>?;
      if (configs == null) {
        throw ConfigRepositoryException('备份数据格式错误：缺少configs字段');
      }
      
      // 清空现有配置
      await clearAllConfigs();
      
      // 恢复配置
      final configsToSave = <String, Map<String, dynamic>>{};
      for (final entry in configs.entries) {
        if (entry.value is Map<String, dynamic>) {
          configsToSave[entry.key] = entry.value as Map<String, dynamic>;
        }
      }
      
      await saveConfigs(configsToSave);
      AppLogger.info('🔄 从JSON恢复配置成功: ${configsToSave.length}个');
    } catch (e) {
      AppLogger.error('❌ 从JSON恢复配置失败: $e');
      rethrow;
    }
  }

  /// 压缩配置数据（删除过期版本等）
  Future<void> compactConfigs() async {
    await _ensureInitialized();
    
    try {
      await _configBox!.compact();
      AppLogger.info('🗜️ 配置数据压缩完成');
    } catch (e) {
      AppLogger.error('❌ 配置数据压缩失败: $e');
    }
  }

  /// 获取配置数据大小（字节）
  Future<int> getConfigDataSize() async {
    await _ensureInitialized();
    
    try {
      int totalSize = 0;
      final keys = await getAllConfigKeys();
      
      for (final key in keys) {
        final jsonString = _configBox!.get(key);
        if (jsonString != null) {
          totalSize += jsonString.length;
        }
      }
      
      return totalSize;
    } catch (e) {
      AppLogger.error('❌ 计算配置数据大小失败: $e');
      return 0;
    }
  }
}
