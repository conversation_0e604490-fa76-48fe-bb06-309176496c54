import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/data/datasources/task_data_source.dart';
import 'package:loadguard/models/task_model.dart';

/// 模拟任务数据源，用于测试接口
class MockTaskDataSource implements TaskDataSource {
  final Map<String, TaskModel> _tasks = {};
  final Map<String, dynamic> _stats = {};
  final List<Map<String, dynamic>> _history = [];

  @override
  Future<List<TaskModel>> getAllTasks() async {
    return _tasks.values.toList();
  }

  @override
  Future<bool> saveAllTasks(List<TaskModel> tasks) async {
    _tasks.clear();
    for (final task in tasks) {
      _tasks[task.id] = task;
    }
    return true;
  }

  @override
  Future<TaskModel?> getTask(String taskId) async {
    return _tasks[taskId];
  }

  @override
  Future<bool> saveTask(TaskModel task) async {
    _tasks[task.id] = task;
    return true;
  }

  @override
  Future<bool> deleteTask(String taskId) async {
    return _tasks.remove(taskId) != null;
  }

  @override
  Future<Map<String, dynamic>> getTaskStats() async {
    return Map.from(_stats);
  }

  @override
  Future<bool> saveTaskStats(Map<String, dynamic> stats) async {
    _stats.clear();
    _stats.addAll(stats);
    return true;
  }

  @override
  Future<List<Map<String, dynamic>>> getTaskHistory() async {
    return List.from(_history);
  }

  @override
  Future<bool> addTaskHistory(Map<String, dynamic> historyItem) async {
    _history.add(historyItem);
    return true;
  }

  @override
  Future<bool> cleanupExpiredData({int daysToKeep = 30}) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
    
    // 清理过期任务
    _tasks.removeWhere((key, task) {
      if (task.status != TaskStatus.completed) return false;
      final lastUpdate = task.completedAt ?? task.createdAt;
      return lastUpdate.isBefore(cutoffDate);
    });
    
    // 清理过期历史
    _history.removeWhere((item) {
      final timestamp = item['timestamp'] as String?;
      if (timestamp == null) return true;
      try {
        final date = DateTime.parse(timestamp);
        return date.isBefore(cutoffDate);
      } catch (e) {
        return true;
      }
    });
    
    return true;
  }

  @override
  Future<Map<String, dynamic>> getStorageStats() async {
    return {
      'tasksCount': _tasks.length,
      'historyCount': _history.length,
      'statsKeys': _stats.keys.length,
      'lastUpdate': DateTime.now().toIso8601String(),
      'storageType': 'Mock',
    };
  }
}

void main() {
  group('🗄️ 任务数据源接口测试', () {
    late TaskDataSource dataSource;
    
    setUp(() {
      dataSource = MockTaskDataSource();
    });
    
    test('应该能够保存和读取任务', () async {
      // 创建测试任务
      final testTask = TaskModel(
        id: 'test-task-1',
        template: '平板车',
        createTime: DateTime.now(),
      );
      
      // 保存任务
      final saveResult = await dataSource.saveTask(testTask);
      expect(saveResult, true);
      
      // 读取任务
      final retrievedTask = await dataSource.getTask('test-task-1');
      expect(retrievedTask, isNotNull);
      expect(retrievedTask!.id, testTask.id);
      expect(retrievedTask.template, testTask.template);
      
      print('✅ 任务保存和读取测试通过');
    });
    
    test('应该能够获取所有任务', () async {
      // 创建多个测试任务
      final tasks = List.generate(5, (index) => TaskModel(
        id: 'test-task-$index',
        template: index % 2 == 0 ? '平板车' : '集装箱',
        createTime: DateTime.now(),
      ));
      
      // 批量保存
      await dataSource.saveAllTasks(tasks);
      
      // 获取所有任务
      final allTasks = await dataSource.getAllTasks();
      expect(allTasks.length, 5);
      
      print('✅ 获取所有任务测试通过 (${allTasks.length}个任务)');
    });
    
    test('应该能够删除任务', () async {
      // 先保存一个任务
      final testTask = TaskModel(
        id: 'test-task-delete',
        template: '平板车',
        createTime: DateTime.now(),
      );
      
      await dataSource.saveTask(testTask);
      
      // 确认任务存在
      final savedTask = await dataSource.getTask('test-task-delete');
      expect(savedTask, isNotNull);
      
      // 删除任务
      final deleteResult = await dataSource.deleteTask('test-task-delete');
      expect(deleteResult, true);
      
      // 确认任务已删除
      final deletedTask = await dataSource.getTask('test-task-delete');
      expect(deletedTask, isNull);
      
      print('✅ 删除任务测试通过');
    });
    
    test('应该能够管理任务统计', () async {
      final testStats = {
        'totalTasks': 10,
        'completedTasks': 5,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
      
      // 保存统计
      final saveResult = await dataSource.saveTaskStats(testStats);
      expect(saveResult, true);
      
      // 读取统计
      final retrievedStats = await dataSource.getTaskStats();
      expect(retrievedStats['totalTasks'], testStats['totalTasks']);
      expect(retrievedStats['completedTasks'], testStats['completedTasks']);
      
      print('✅ 任务统计测试通过');
    });
    
    test('应该能够管理任务历史', () async {
      final testHistory = {
        'action': 'task_completed',
        'taskId': 'test-task-1',
        'timestamp': DateTime.now().toIso8601String(),
        'details': '任务完成',
      };
      
      // 添加历史记录
      final addResult = await dataSource.addTaskHistory(testHistory);
      expect(addResult, true);
      
      // 读取历史记录
      final history = await dataSource.getTaskHistory();
      expect(history.length, 1);
      expect(history.first['action'], testHistory['action']);
      
      print('✅ 任务历史测试通过');
    });
    
    test('应该能够清理过期数据', () async {
      // 创建一些任务，包括已完成的旧任务
      final oldTask = TaskModel(
        id: 'old-task',
        template: '平板车',
        createTime: DateTime.now().subtract(Duration(days: 60)),
      );
      oldTask.completedAt = DateTime.now().subtract(Duration(days: 50));
      
      final newTask = TaskModel(
        id: 'new-task',
        template: '集装箱',
        createTime: DateTime.now(),
      );
      
      await dataSource.saveTask(oldTask);
      await dataSource.saveTask(newTask);
      
      // 添加一些历史记录
      await dataSource.addTaskHistory({
        'action': 'old_action',
        'timestamp': DateTime.now().subtract(Duration(days: 60)).toIso8601String(),
      });
      
      await dataSource.addTaskHistory({
        'action': 'new_action',
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // 清理过期数据（保留30天）
      final cleanupResult = await dataSource.cleanupExpiredData(daysToKeep: 30);
      expect(cleanupResult, true);
      
      // 验证清理结果
      final remainingTasks = await dataSource.getAllTasks();
      expect(remainingTasks.length, 1);
      expect(remainingTasks.first.id, 'new-task');
      
      final remainingHistory = await dataSource.getTaskHistory();
      expect(remainingHistory.length, 1);
      expect(remainingHistory.first['action'], 'new_action');
      
      print('✅ 数据清理测试通过');
    });
    
    test('应该能够获取存储统计', () async {
      // 添加一些数据
      await dataSource.saveTask(TaskModel(
        id: 'stats-test',
        template: '平板车',
        createTime: DateTime.now(),
      ));
      
      await dataSource.saveTaskStats({'test': 'value'});
      await dataSource.addTaskHistory({'action': 'test'});
      
      // 获取统计
      final stats = await dataSource.getStorageStats();
      expect(stats['tasksCount'], 1);
      expect(stats['historyCount'], 1);
      expect(stats['statsKeys'], 1);
      expect(stats['storageType'], 'Mock');
      
      print('✅ 存储统计测试通过: $stats');
    });
    
    test('性能测试', () async {
      final stopwatch = Stopwatch()..start();
      
      // 创建100个测试任务
      final tasks = List.generate(100, (index) => TaskModel(
        id: 'perf-test-$index',
        template: index % 2 == 0 ? '平板车' : '集装箱',
        createTime: DateTime.now(),
      ));
      
      // 批量保存
      await dataSource.saveAllTasks(tasks);
      
      stopwatch.stop();
      final saveTime = stopwatch.elapsedMilliseconds;
      
      // 读取性能测试
      stopwatch.reset();
      stopwatch.start();
      
      final retrievedTasks = await dataSource.getAllTasks();
      
      stopwatch.stop();
      final loadTime = stopwatch.elapsedMilliseconds;
      
      expect(retrievedTasks.length, 100);
      expect(saveTime, lessThan(100)); // 保存应该在100ms内完成
      expect(loadTime, lessThan(50)); // 读取应该在50ms内完成
      
      print('✅ 性能测试通过: 保存${saveTime}ms, 读取${loadTime}ms');
    });
  });
}
