import 'package:flutter/material.dart';
import '../utils/responsive_helper.dart';
import '../utils/theme_colors.dart';

/// 📱 响应式脚手架组件
/// 提供自适应布局和导航支持
class ResponsiveScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? bottomNavigationBar;
  final Widget? bottomSheet;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final bool extendBody;
  final bool extendBodyBehindAppBar;
  final String? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool enableSafeArea;
  final EdgeInsets? padding;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final BoxDecoration? decoration;
  final bool useGradientBackground;

  const ResponsiveScaffold({
    Key? key,
    required this.body,
    this.appBar,
    this.drawer,
    this.endDrawer,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.bottomNavigationBar,
    this.bottomSheet,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.enableSafeArea = true,
    this.padding,
    this.showBackButton = true,
    this.onBackPressed,
    this.decoration,
    this.useGradientBackground = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveHelper.isDesktop(context);
    final isMobile = ResponsiveHelper.isMobile(context);
    final maxWidth = ResponsiveHelper.getResponsiveMaxWidth(
      context,
      mobileMaxWidth: double.infinity,
      tabletMaxWidth: 800,
      desktopMaxWidth: 1200,
    );

    Widget scaffoldBody = body;

    // 为大屏设备添加最大宽度限制和居中
    if (!isMobile && maxWidth != double.infinity) {
      scaffoldBody = Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: maxWidth),
          child: body,
        ),
      );
    }

    // 为桌面设备添加额外的边距
    if (isDesktop) {
      scaffoldBody = Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: scaffoldBody,
      );
    }

    // 应用响应式边距
    if (padding != null) {
      scaffoldBody = Padding(
        padding: ResponsiveHelper.getResponsivePadding(context, padding!),
        child: scaffoldBody,
      );
    }

    // 安全区域包装
    if (enableSafeArea) {
      scaffoldBody = SafeArea(child: scaffoldBody);
    }

    // 构建AppBar
    PreferredSizeWidget? finalAppBar = appBar;
    if (finalAppBar == null && title != null) {
      finalAppBar = _buildResponsiveAppBar(context);
    }

    Widget scaffold = Scaffold(
      appBar: finalAppBar,
      drawer: drawer,
      endDrawer: endDrawer,
      body: scaffoldBody,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      bottomNavigationBar: bottomNavigationBar,
      bottomSheet: bottomSheet,
      backgroundColor: backgroundColor ?? Colors.transparent,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      extendBody: extendBody,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
    );

    // 添加背景装饰
    if (useGradientBackground || decoration != null) {
      scaffold = Container(
        decoration: decoration ??
            const BoxDecoration(
              gradient: ThemeColors.primaryGradient,
            ),
        child: scaffold,
      );
    }

    return scaffold;
  }

  /// 构建响应式AppBar
  PreferredSizeWidget _buildResponsiveAppBar(BuildContext context) {
    final isDesktop = ResponsiveHelper.isDesktop(context);
    final responsiveFontSize =
        ResponsiveHelper.getResponsiveFontSize(context, 20);

    return AppBar(
      title: title != null
          ? Text(
              title!,
              style: TextStyle(
                fontSize: responsiveFontSize,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            )
          : null,
      leading: leading ??
          (showBackButton && Navigator.canPop(context)
              ? IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    size: ResponsiveHelper.getResponsiveFontSize(context, 24),
                  ),
                  onPressed: onBackPressed ?? () => Navigator.pop(context),
                )
              : null),
      actions: actions,
      backgroundColor: Theme.of(context).colorScheme.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      elevation: 0,
      automaticallyImplyLeading: automaticallyImplyLeading,
      centerTitle: !isDesktop, // 桌面端左对齐，移动端居中
      toolbarHeight: isDesktop ? 64 : 56, // 桌面端更高的工具栏
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shadowColor: Theme.of(context).colorScheme.shadow,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
    );
  }
}

/// 响应式容器组件
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? width;
  final double? height;
  final BoxDecoration? decoration;
  final AlignmentGeometry? alignment;
  final bool centerOnDesktop;
  final double? maxWidth;
  final double? maxHeight;

  const ResponsiveContainer({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.decoration,
    this.alignment,
    this.centerOnDesktop = true,
    this.maxWidth,
    this.maxHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveHelper.isDesktop(context);

    EdgeInsets? finalPadding = padding;
    EdgeInsets? finalMargin = margin;

    if (padding != null) {
      finalPadding = ResponsiveHelper.getResponsivePadding(context, padding!);
    }

    if (margin != null) {
      finalMargin = ResponsiveHelper.getResponsivePadding(context, margin!);
    }

    Widget container = Container(
      width: width,
      height: height,
      padding: finalPadding,
      margin: finalMargin,
      decoration: decoration,
      alignment: alignment,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? double.infinity,
        maxHeight: maxHeight ?? double.infinity,
      ),
      child: child,
    );

    // 桌面端居中显示
    if (isDesktop && centerOnDesktop && maxWidth != null) {
      container = Center(child: container);
    }

    return container;
  }
}

/// 响应式列组件
class ResponsiveColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final TextDirection? textDirection;
  final VerticalDirection verticalDirection;
  final TextBaseline? textBaseline;
  final EdgeInsets? padding;
  final double? spacing;

  const ResponsiveColumn({
    Key? key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.textDirection,
    this.verticalDirection = VerticalDirection.down,
    this.textBaseline,
    this.padding,
    this.spacing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<Widget> finalChildren = children;

    // 添加响应式间距
    if (spacing != null) {
      final responsiveSpacing =
          ResponsiveHelper.getResponsiveSpacing(context, spacing!);
      finalChildren = [];
      for (int i = 0; i < children.length; i++) {
        finalChildren.add(children[i]);
        if (i < children.length - 1) {
          finalChildren.add(SizedBox(height: responsiveSpacing));
        }
      }
    }

    Widget column = Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      textDirection: textDirection,
      verticalDirection: verticalDirection,
      textBaseline: textBaseline,
      children: finalChildren,
    );

    if (padding != null) {
      column = Padding(
        padding: ResponsiveHelper.getResponsivePadding(context, padding!),
        child: column,
      );
    }

    return column;
  }
}

/// 响应式行组件
class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final TextDirection? textDirection;
  final VerticalDirection verticalDirection;
  final TextBaseline? textBaseline;
  final EdgeInsets? padding;
  final double? spacing;
  final bool wrapOnMobile;

  const ResponsiveRow({
    Key? key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.textDirection,
    this.verticalDirection = VerticalDirection.down,
    this.textBaseline,
    this.padding,
    this.spacing,
    this.wrapOnMobile = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);

    List<Widget> finalChildren = children;

    // 添加响应式间距
    if (spacing != null) {
      final responsiveSpacing =
          ResponsiveHelper.getResponsiveSpacing(context, spacing!);
      finalChildren = [];
      for (int i = 0; i < children.length; i++) {
        finalChildren.add(children[i]);
        if (i < children.length - 1) {
          finalChildren.add(SizedBox(width: responsiveSpacing));
        }
      }
    }

    Widget rowWidget;

    // 移动端换行显示
    if (wrapOnMobile && isMobile) {
      rowWidget = Wrap(
        direction: Axis.horizontal,
        crossAxisAlignment: WrapCrossAlignment.center,
        spacing: spacing != null
            ? ResponsiveHelper.getResponsiveSpacing(context, spacing!)
            : 0,
        runSpacing: spacing != null
            ? ResponsiveHelper.getResponsiveSpacing(context, spacing!)
            : 0,
        children: children,
      );
    } else {
      rowWidget = Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        textDirection: textDirection,
        verticalDirection: verticalDirection,
        textBaseline: textBaseline,
        children: finalChildren,
      );
    }

    if (padding != null) {
      rowWidget = Padding(
        padding: ResponsiveHelper.getResponsivePadding(context, padding!),
        child: rowWidget,
      );
    }

    return rowWidget;
  }
}

/// 响应式网格组件
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? spacing;
  final double? runSpacing;
  final EdgeInsets? padding;
  final WrapAlignment alignment;
  final WrapCrossAlignment crossAxisAlignment;

  const ResponsiveGrid({
    Key? key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing,
    this.runSpacing,
    this.padding,
    this.alignment = WrapAlignment.start,
    this.crossAxisAlignment = WrapCrossAlignment.start,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveHelper.getResponsiveColumns(
      context,
      mobileColumns: mobileColumns ?? 1,
      tabletColumns: tabletColumns ?? 2,
      desktopColumns: desktopColumns ?? 3,
    );

    final finalSpacing = spacing != null
        ? ResponsiveHelper.getResponsiveSpacing(context, spacing!)
        : 8.0;

    final finalRunSpacing = runSpacing != null
        ? ResponsiveHelper.getResponsiveSpacing(context, runSpacing!)
        : 8.0;

    // 计算每个子项的宽度
    final screenWidth = MediaQuery.of(context).size.width;
    final effectivePadding = padding != null
        ? ResponsiveHelper.getResponsivePadding(context, padding!)
        : EdgeInsets.zero;

    final availableWidth = screenWidth -
        effectivePadding.left -
        effectivePadding.right -
        (finalSpacing * (columns - 1));

    final itemWidth = availableWidth / columns;

    List<Widget> wrappedChildren = children.map((child) {
      return SizedBox(
        width: itemWidth,
        child: child,
      );
    }).toList();

    Widget grid = Wrap(
      spacing: finalSpacing,
      runSpacing: finalRunSpacing,
      alignment: alignment,
      crossAxisAlignment: crossAxisAlignment,
      children: wrappedChildren,
    );

    if (padding != null) {
      grid = Padding(
        padding: ResponsiveHelper.getResponsivePadding(context, padding!),
        child: grid,
      );
    }

    return grid;
  }
}

/// 响应式文本组件
class ResponsiveText extends StatelessWidget {
  final String text;
  final double baseFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextStyle? style;
  final double? height;

  const ResponsiveText(
    this.text, {
    Key? key,
    this.baseFontSize = 16,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.style,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final responsiveFontSize =
        ResponsiveHelper.getResponsiveFontSize(context, baseFontSize);

    return Text(
      text,
      style: TextStyle(
        fontSize: responsiveFontSize,
        fontWeight: fontWeight,
        color: color,
        height: height,
      ).merge(style),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}
