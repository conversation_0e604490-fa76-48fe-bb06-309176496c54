import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/exceptions/app_exceptions.dart';
import 'package:loadguard/services/global_error_handler.dart';

/// 错误边界Widget
/// 捕获子Widget树中的错误并显示友好的错误界面
class ErrorBoundary extends ConsumerStatefulWidget {
  const ErrorBoundary({
    super.key,
    required this.child,
    this.onError,
    this.fallbackBuilder,
  });

  final Widget child;
  final void Function(Object error, StackTrace stackTrace)? onError;
  final Widget Function(Object error, StackTrace stackTrace)? fallbackBuilder;

  @override
  ConsumerState<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends ConsumerState<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.fallbackBuilder?.call(_error!, _stackTrace!) ??
          ErrorFallbackWidget(
            error: _error!,
            stackTrace: _stackTrace!,
            onRetry: () {
              setState(() {
                _error = null;
                _stackTrace = null;
              });
            },
          );
    }

    return ErrorCatcher(
      onError: (error, stackTrace) {
        setState(() {
          _error = error;
          _stackTrace = stackTrace;
        });
        widget.onError?.call(error, stackTrace);
        
        // 通过全局错误处理器处理
        ref.read(globalErrorHandlerProvider).handleException(
          error,
          stackTrace: stackTrace,
          context: context,
          operation: 'ErrorBoundary',
          showUserMessage: false, // 由ErrorBoundary自己显示UI
        );
      },
      child: widget.child,
    );
  }
}

/// 错误捕获器
class ErrorCatcher extends StatefulWidget {
  const ErrorCatcher({
    super.key,
    required this.child,
    required this.onError,
  });

  final Widget child;
  final void Function(Object error, StackTrace stackTrace) onError;

  @override
  State<ErrorCatcher> createState() => _ErrorCatcherState();
}

class _ErrorCatcherState extends State<ErrorCatcher> {
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 这里可以添加错误捕获逻辑
  }
}

/// 错误回退Widget
class ErrorFallbackWidget extends StatelessWidget {
  const ErrorFallbackWidget({
    super.key,
    required this.error,
    required this.stackTrace,
    this.onRetry,
    this.showDetails = false,
  });

  final Object error;
  final StackTrace stackTrace;
  final VoidCallback? onRetry;
  final bool showDetails;

  @override
  Widget build(BuildContext context) {
    final appException = error is AppException ? error as AppException : null;
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getErrorIcon(appException?.severity ?? ErrorSeverity.medium),
            size: 64,
            color: _getErrorColor(appException?.severity ?? ErrorSeverity.medium),
          ),
          const SizedBox(height: 16),
          
          Text(
            _getErrorTitle(appException?.severity ?? ErrorSeverity.medium),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: _getErrorColor(appException?.severity ?? ErrorSeverity.medium),
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          
          Text(
            appException?.displayMessage ?? '发生了未知错误',
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (onRetry != null && (appException?.retryable ?? true))
                ElevatedButton.icon(
                  onPressed: onRetry,
                  icon: const Icon(Icons.refresh),
                  label: const Text('重试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              
              if (onRetry != null && (appException?.retryable ?? true))
                const SizedBox(width: 16),
              
              OutlinedButton.icon(
                onPressed: () => _showErrorDetails(context),
                icon: const Icon(Icons.info_outline),
                label: const Text('详情'),
              ),
            ],
          ),
          
          if (showDetails) ...[
            const SizedBox(height: 24),
            _buildErrorDetails(context),
          ],
        ],
      ),
    );
  }

  IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.critical:
        return Icons.error;
      case ErrorSeverity.high:
        return Icons.warning;
      case ErrorSeverity.medium:
        return Icons.info;
      case ErrorSeverity.low:
        return Icons.help_outline;
    }
  }

  Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.critical:
        return Colors.red[700]!;
      case ErrorSeverity.high:
        return Colors.orange[700]!;
      case ErrorSeverity.medium:
        return Colors.blue[700]!;
      case ErrorSeverity.low:
        return Colors.grey[600]!;
    }
  }

  String _getErrorTitle(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.critical:
        return '严重错误';
      case ErrorSeverity.high:
        return '错误';
      case ErrorSeverity.medium:
        return '警告';
      case ErrorSeverity.low:
        return '提示';
    }
  }

  void _showErrorDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('错误详情'),
        content: SingleChildScrollView(
          child: _buildErrorDetails(context),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorDetails(BuildContext context) {
    final appException = error is AppException ? error as AppException : null;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (appException != null) ...[
          _buildDetailItem('错误类型', appException.runtimeType.toString()),
          _buildDetailItem('错误代码', appException.code),
          _buildDetailItem('错误消息', appException.message),
          _buildDetailItem('严重程度', appException.severity.name),
          _buildDetailItem('错误类别', appException.category.name),
          _buildDetailItem('可重试', appException.retryable ? '是' : '否'),
          _buildDetailItem('可操作', appException.actionable ? '是' : '否'),
          if (appException.details != null)
            _buildDetailItem('详细信息', appException.details.toString()),
        ] else ...[
          _buildDetailItem('错误类型', error.runtimeType.toString()),
          _buildDetailItem('错误消息', error.toString()),
        ],
        
        const SizedBox(height: 16),
        ExpansionTile(
          title: const Text('堆栈跟踪'),
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                stackTrace.toString(),
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}

/// 错误提示条
class ErrorSnackBar extends SnackBar {
  ErrorSnackBar({
    super.key,
    required AppException exception,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) : super(
          content: Row(
            children: [
              Icon(
                _getErrorIcon(exception.severity),
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  exception.displayMessage,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: _getErrorColor(exception.severity),
          action: exception.retryable && onRetry != null
              ? SnackBarAction(
                  label: '重试',
                  textColor: Colors.white,
                  onPressed: onRetry,
                )
              : null,
          duration: Duration(
            seconds: exception.severity == ErrorSeverity.critical ? 10 : 4,
          ),
        );

  static IconData _getErrorIcon(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.critical:
        return Icons.error;
      case ErrorSeverity.high:
        return Icons.warning;
      case ErrorSeverity.medium:
        return Icons.info;
      case ErrorSeverity.low:
        return Icons.help_outline;
    }
  }

  static Color _getErrorColor(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.critical:
        return Colors.red[700]!;
      case ErrorSeverity.high:
        return Colors.orange[700]!;
      case ErrorSeverity.medium:
        return Colors.blue[700]!;
      case ErrorSeverity.low:
        return Colors.grey[600]!;
    }
  }
}

/// 错误处理扩展
extension ErrorHandlingExtension on BuildContext {
  /// 显示错误SnackBar
  void showErrorSnackBar(
    AppException exception, {
    VoidCallback? onRetry,
  }) {
    ScaffoldMessenger.of(this).showSnackBar(
      ErrorSnackBar(
        exception: exception,
        onRetry: onRetry,
      ),
    );
  }

  /// 处理异步操作错误
  Future<T?> handleAsyncError<T>(
    Future<T> future, {
    String? operation,
    VoidCallback? onRetry,
    bool showSnackBar = true,
  }) async {
    try {
      return await future;
    } catch (error, stackTrace) {
      final errorHandler = ProviderScope.containerOf(this).read(globalErrorHandlerProvider);
      await errorHandler.handleException(
        error,
        stackTrace: stackTrace,
        context: this,
        operation: operation,
        showUserMessage: !showSnackBar,
        onRetry: onRetry,
      );
      
      if (showSnackBar && error is AppException) {
        showErrorSnackBar(error, onRetry: onRetry);
      }
      
      return null;
    }
  }
}
