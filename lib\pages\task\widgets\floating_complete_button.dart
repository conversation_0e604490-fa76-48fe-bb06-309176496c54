import 'package:flutter/material.dart';
import 'package:loadguard/models/task_model.dart';

/// 📋 悬浮完成按钮组件
class FloatingCompleteButton extends StatelessWidget {
  final TaskModel task;
  final VoidCallback onComplete;

  const FloatingCompleteButton({
    Key? key,
    required this.task,
    required this.onComplete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final requiredPhotos = task.photos.where((p) => p.isRequired).length;
    final completedRequired =
        task.photos.where((p) => p.isRequired && p.imagePath != null).length;

    final canComplete = completedRequired >= requiredPhotos;

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: FloatingActionButton.extended(
        onPressed: canComplete ? onComplete : null,
        backgroundColor:
            canComplete ? const Color(0xFF4CAF50) : Colors.grey[400],
        foregroundColor: Colors.white,
        elevation: 8,
        icon: Icon(
          canComplete ? Icons.check_circle : Icons.schedule,
          size: 24,
        ),
        label: Text(
          canComplete ? '完成' : '必拍 $completedRequired/$requiredPhotos',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.3,
          ),
        ),
        extendedIconLabelSpacing: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}
