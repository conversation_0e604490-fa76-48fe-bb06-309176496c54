import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:loadguard/services/secure_storage_service.dart';
import 'package:loadguard/services/global_error_handler.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/config_models.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 本地数据管理器
/// 专为本地应用设计，同时预留服务器同步接口
/// 特点：
/// 1. 本地优先 - 所有数据本地存储，离线可用
/// 2. 数据导出 - 支持数据导出，便于备份和迁移
/// 3. 同步预留 - 预留服务器同步接口，便于未来扩展
/// 4. 兼容性 - 支持数据格式升级和版本兼容
class LocalDataManager {
  final SecureStorageService _secureStorage;
  final GlobalErrorHandler _errorHandler;
  
  // 本地数据存储路径
  late final Directory _dataDirectory;
  late final Directory _backupDirectory;
  late final Directory _exportDirectory;
  
  // 数据文件
  static const String _tasksFileName = 'tasks.json';
  static const String _configFileName = 'config.json';
  static const String _statisticsFileName = 'statistics.json';
  static const String _metadataFileName = 'metadata.json';
  
  // 数据版本管理
  static const String _currentDataVersion = '1.0.0';
  
  bool _initialized = false;
  
  LocalDataManager(this._secureStorage, this._errorHandler);
  
  /// 初始化本地数据管理器
  Future<void> initialize() async {
    try {
      AppLogger.info('📁 初始化本地数据管理器...');
      
      // 创建数据目录
      await _createDataDirectories();
      
      // 检查数据版本和迁移
      await _checkAndMigrateData();
      
      // 验证数据完整性
      await _validateDataIntegrity();
      
      _initialized = true;
      AppLogger.info('✅ 本地数据管理器初始化完成');
    } catch (e) {
      AppLogger.error('❌ 本地数据管理器初始化失败', error: e);
      await _errorHandler.handleException(e, operation: 'LocalDataManager.initialize');
      rethrow;
    }
  }
  
  /// 创建数据目录结构
  Future<void> _createDataDirectories() async {
    final appDir = await getApplicationDocumentsDirectory();
    
    _dataDirectory = Directory('${appDir.path}/loadguard_data');
    _backupDirectory = Directory('${appDir.path}/loadguard_backup');
    _exportDirectory = Directory('${appDir.path}/loadguard_export');
    
    await _dataDirectory.create(recursive: true);
    await _backupDirectory.create(recursive: true);
    await _exportDirectory.create(recursive: true);
    
    AppLogger.debug('📁 数据目录已创建: ${_dataDirectory.path}');
  }
  
  /// 检查和迁移数据
  Future<void> _checkAndMigrateData() async {
    final metadataFile = File('${_dataDirectory.path}/$_metadataFileName');
    
    if (!await metadataFile.exists()) {
      // 首次运行，创建元数据
      await _createInitialMetadata();
      return;
    }
    
    final metadata = await _loadMetadata();
    final currentVersion = metadata['dataVersion'] as String?;
    
    if (currentVersion != _currentDataVersion) {
      AppLogger.info('🔄 检测到数据版本变化: $currentVersion -> $_currentDataVersion');
      await _migrateData(currentVersion, _currentDataVersion);
    }
  }
  
  /// 创建初始元数据
  Future<void> _createInitialMetadata() async {
    final metadata = {
      'dataVersion': _currentDataVersion,
      'createdAt': DateTime.now().toIso8601String(),
      'lastModified': DateTime.now().toIso8601String(),
      'deviceId': await _getDeviceId(),
      'appVersion': '1.0.0', // 从package_info获取
      'isLocalApp': true,
      'syncEnabled': false, // 本地应用默认不启用同步
    };
    
    await _saveMetadata(metadata);
    AppLogger.info('📝 初始元数据已创建');
  }
  
  /// 获取设备ID（本地应用标识）
  Future<String> _getDeviceId() async {
    try {
      // 尝试从安全存储获取
      String? deviceId = await _secureStorage.getSensitiveData<String>('device_id');
      
      if (deviceId == null) {
        // 生成新的设备ID
        deviceId = 'local_${DateTime.now().millisecondsSinceEpoch}_${Platform.operatingSystem}';
        await _secureStorage.storeSensitiveData('device_id', deviceId);
      }
      
      return deviceId;
    } catch (e) {
      // 如果安全存储失败，使用临时ID
      return 'temp_${DateTime.now().millisecondsSinceEpoch}';
    }
  }
  
  /// 保存任务数据（本地优先）
  Future<void> saveTaskData(List<TaskModel> tasks) async {
    _ensureInitialized();
    
    try {
      final tasksFile = File('${_dataDirectory.path}/$_tasksFileName');
      final tasksJson = tasks.map((task) => task.toJson()).toList();
      
      await tasksFile.writeAsString(jsonEncode({
        'version': _currentDataVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'count': tasks.length,
        'tasks': tasksJson,
      }));
      
      await _updateMetadata();
      AppLogger.debug('💾 任务数据已保存: ${tasks.length}个任务');
    } catch (e) {
      await _errorHandler.handleStorageError(e, operation: 'saveTaskData');
      rethrow;
    }
  }
  
  /// 加载任务数据
  Future<List<TaskModel>> loadTaskData() async {
    _ensureInitialized();
    
    try {
      final tasksFile = File('${_dataDirectory.path}/$_tasksFileName');
      
      if (!await tasksFile.exists()) {
        return [];
      }
      
      final content = await tasksFile.readAsString();
      final data = jsonDecode(content) as Map<String, dynamic>;
      final tasksJson = data['tasks'] as List;
      
      final tasks = tasksJson.map((json) => TaskModel.fromJson(json)).toList();
      
      AppLogger.debug('📖 任务数据已加载: ${tasks.length}个任务');
      return tasks;
    } catch (e) {
      await _errorHandler.handleStorageError(e, operation: 'loadTaskData');
      return [];
    }
  }
  
  /// 保存配置数据
  Future<void> saveConfigData(Map<String, dynamic> config) async {
    _ensureInitialized();
    
    try {
      final configFile = File('${_dataDirectory.path}/$_configFileName');
      
      await configFile.writeAsString(jsonEncode({
        'version': _currentDataVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'config': config,
      }));
      
      await _updateMetadata();
      AppLogger.debug('⚙️ 配置数据已保存');
    } catch (e) {
      await _errorHandler.handleStorageError(e, operation: 'saveConfigData');
      rethrow;
    }
  }
  
  /// 加载配置数据
  Future<Map<String, dynamic>> loadConfigData() async {
    _ensureInitialized();
    
    try {
      final configFile = File('${_dataDirectory.path}/$_configFileName');
      
      if (!await configFile.exists()) {
        return {};
      }
      
      final content = await configFile.readAsString();
      final data = jsonDecode(content) as Map<String, dynamic>;
      
      return data['config'] as Map<String, dynamic>;
    } catch (e) {
      await _errorHandler.handleStorageError(e, operation: 'loadConfigData');
      return {};
    }
  }
  
  /// 导出数据（便于备份和迁移）
  Future<String> exportData({
    bool includeTasks = true,
    bool includeConfig = true,
    bool includeStatistics = true,
    String? customPath,
  }) async {
    _ensureInitialized();
    
    try {
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final exportFileName = 'loadguard_export_$timestamp.json';
      final exportPath = customPath ?? '${_exportDirectory.path}/$exportFileName';
      
      final exportData = <String, dynamic>{
        'exportInfo': {
          'version': _currentDataVersion,
          'timestamp': DateTime.now().toIso8601String(),
          'deviceId': await _getDeviceId(),
          'appType': 'local',
        },
      };
      
      if (includeTasks) {
        final tasks = await loadTaskData();
        exportData['tasks'] = tasks.map((task) => task.toJson()).toList();
      }
      
      if (includeConfig) {
        exportData['config'] = await loadConfigData();
      }
      
      if (includeStatistics) {
        exportData['statistics'] = await _loadStatistics();
      }
      
      // 添加元数据
      exportData['metadata'] = await _loadMetadata();
      
      final exportFile = File(exportPath);
      await exportFile.writeAsString(jsonEncode(exportData));
      
      AppLogger.info('📤 数据导出完成: $exportPath');
      return exportPath;
    } catch (e) {
      await _errorHandler.handleStorageError(e, operation: 'exportData');
      rethrow;
    }
  }
  
  /// 导入数据
  Future<void> importData(String filePath, {bool overwrite = false}) async {
    _ensureInitialized();
    
    try {
      final importFile = File(filePath);
      if (!await importFile.exists()) {
        throw FileSystemException('Import file not found', filePath);
      }
      
      final content = await importFile.readAsString();
      final importData = jsonDecode(content) as Map<String, dynamic>;
      
      // 验证导入数据
      await _validateImportData(importData);
      
      // 创建备份
      if (!overwrite) {
        await _createBackup();
      }
      
      // 导入任务数据
      if (importData.containsKey('tasks')) {
        final tasksJson = importData['tasks'] as List;
        final tasks = tasksJson.map((json) => TaskModel.fromJson(json)).toList();
        await saveTaskData(tasks);
      }
      
      // 导入配置数据
      if (importData.containsKey('config')) {
        await saveConfigData(importData['config'] as Map<String, dynamic>);
      }
      
      AppLogger.info('📥 数据导入完成: $filePath');
    } catch (e) {
      await _errorHandler.handleStorageError(e, operation: 'importData');
      rethrow;
    }
  }
  
  /// 创建数据备份
  Future<String> createBackup() async {
    _ensureInitialized();
    return await _createBackup();
  }
  
  /// 内部备份方法
  Future<String> _createBackup() async {
    final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
    final backupFileName = 'backup_$timestamp.json';
    final backupPath = '${_backupDirectory.path}/$backupFileName';
    
    // 导出所有数据作为备份
    final exportPath = await exportData(customPath: backupPath);
    
    // 清理旧备份（保留最近10个）
    await _cleanupOldBackups();
    
    AppLogger.info('💾 数据备份完成: $exportPath');
    return exportPath;
  }
  
  /// 清理旧备份
  Future<void> _cleanupOldBackups() async {
    try {
      final backupFiles = await _backupDirectory.list().toList();
      final jsonFiles = backupFiles
          .where((file) => file.path.endsWith('.json'))
          .cast<File>()
          .toList();
      
      if (jsonFiles.length > 10) {
        // 按修改时间排序
        jsonFiles.sort((a, b) => a.lastModifiedSync().compareTo(b.lastModifiedSync()));
        
        // 删除最旧的文件
        for (int i = 0; i < jsonFiles.length - 10; i++) {
          await jsonFiles[i].delete();
        }
        
        AppLogger.debug('🧹 已清理 ${jsonFiles.length - 10} 个旧备份文件');
      }
    } catch (e) {
      AppLogger.warning('⚠️ 清理旧备份失败: $e');
    }
  }
  
  /// 获取数据统计信息
  Future<LocalDataStatistics> getDataStatistics() async {
    _ensureInitialized();
    
    try {
      final tasks = await loadTaskData();
      final config = await loadConfigData();
      final metadata = await _loadMetadata();
      
      // 计算文件大小
      final dataSize = await _calculateDataSize();
      
      return LocalDataStatistics(
        taskCount: tasks.length,
        configItemCount: config.length,
        dataSize: dataSize,
        lastModified: DateTime.parse(metadata['lastModified'] as String),
        dataVersion: metadata['dataVersion'] as String,
        deviceId: metadata['deviceId'] as String,
        isLocalApp: metadata['isLocalApp'] as bool? ?? true,
        syncEnabled: metadata['syncEnabled'] as bool? ?? false,
      );
    } catch (e) {
      await _errorHandler.handleException(e, operation: 'getDataStatistics');
      rethrow;
    }
  }
  
  /// 计算数据大小
  Future<int> _calculateDataSize() async {
    int totalSize = 0;
    
    try {
      final files = await _dataDirectory.list().toList();
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }
    } catch (e) {
      AppLogger.warning('⚠️ 计算数据大小失败: $e');
    }
    
    return totalSize;
  }
  
  /// 验证数据完整性
  Future<void> _validateDataIntegrity() async {
    try {
      // 检查关键文件是否存在且可读
      final metadataFile = File('${_dataDirectory.path}/$_metadataFileName');
      if (await metadataFile.exists()) {
        await _loadMetadata(); // 尝试读取元数据
      }
      
      AppLogger.debug('✅ 数据完整性验证通过');
    } catch (e) {
      AppLogger.warning('⚠️ 数据完整性验证失败: $e');
    }
  }
  
  /// 验证导入数据
  Future<void> _validateImportData(Map<String, dynamic> importData) async {
    if (!importData.containsKey('exportInfo')) {
      throw FormatException('Invalid import data: missing exportInfo');
    }
    
    final exportInfo = importData['exportInfo'] as Map<String, dynamic>;
    final version = exportInfo['version'] as String?;
    
    if (version == null) {
      throw FormatException('Invalid import data: missing version');
    }
    
    // 这里可以添加版本兼容性检查
    AppLogger.debug('✅ 导入数据验证通过 (版本: $version)');
  }
  
  /// 数据迁移
  Future<void> _migrateData(String? fromVersion, String toVersion) async {
    AppLogger.info('🔄 开始数据迁移: $fromVersion -> $toVersion');
    
    // 创建迁移前备份
    await _createBackup();
    
    // 这里可以添加具体的迁移逻辑
    // 例如：字段重命名、数据格式转换等
    
    // 更新版本信息
    final metadata = await _loadMetadata();
    metadata['dataVersion'] = toVersion;
    metadata['lastMigration'] = DateTime.now().toIso8601String();
    await _saveMetadata(metadata);
    
    AppLogger.info('✅ 数据迁移完成');
  }
  
  /// 加载元数据
  Future<Map<String, dynamic>> _loadMetadata() async {
    final metadataFile = File('${_dataDirectory.path}/$_metadataFileName');
    final content = await metadataFile.readAsString();
    return jsonDecode(content) as Map<String, dynamic>;
  }
  
  /// 保存元数据
  Future<void> _saveMetadata(Map<String, dynamic> metadata) async {
    final metadataFile = File('${_dataDirectory.path}/$_metadataFileName');
    await metadataFile.writeAsString(jsonEncode(metadata));
  }
  
  /// 更新元数据
  Future<void> _updateMetadata() async {
    try {
      final metadata = await _loadMetadata();
      metadata['lastModified'] = DateTime.now().toIso8601String();
      await _saveMetadata(metadata);
    } catch (e) {
      AppLogger.warning('⚠️ 更新元数据失败: $e');
    }
  }
  
  /// 加载统计数据
  Future<Map<String, dynamic>> _loadStatistics() async {
    try {
      final statsFile = File('${_dataDirectory.path}/$_statisticsFileName');
      if (!await statsFile.exists()) {
        return {};
      }
      
      final content = await statsFile.readAsString();
      final data = jsonDecode(content) as Map<String, dynamic>;
      return data['statistics'] as Map<String, dynamic>? ?? {};
    } catch (e) {
      return {};
    }
  }
  
  /// 检查是否已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('LocalDataManager not initialized');
    }
  }
  
  /// 释放资源
  Future<void> dispose() async {
    _initialized = false;
    AppLogger.info('📁 本地数据管理器已释放');
  }
}

/// 本地数据统计信息
class LocalDataStatistics {
  final int taskCount;
  final int configItemCount;
  final int dataSize; // 字节
  final DateTime lastModified;
  final String dataVersion;
  final String deviceId;
  final bool isLocalApp;
  final bool syncEnabled;

  const LocalDataStatistics({
    required this.taskCount,
    required this.configItemCount,
    required this.dataSize,
    required this.lastModified,
    required this.dataVersion,
    required this.deviceId,
    required this.isLocalApp,
    required this.syncEnabled,
  });

  /// 格式化数据大小
  String get formattedDataSize {
    if (dataSize < 1024) {
      return '${dataSize}B';
    } else if (dataSize < 1024 * 1024) {
      return '${(dataSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(dataSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'taskCount': taskCount,
      'configItemCount': configItemCount,
      'dataSize': dataSize,
      'formattedDataSize': formattedDataSize,
      'lastModified': lastModified.toIso8601String(),
      'dataVersion': dataVersion,
      'deviceId': deviceId,
      'isLocalApp': isLocalApp,
      'syncEnabled': syncEnabled,
    };
  }

  @override
  String toString() {
    return 'LocalDataStatistics(tasks: $taskCount, config: $configItemCount, size: $formattedDataSize)';
  }
}

/// 本地数据管理器Provider
final localDataManagerProvider = Provider<LocalDataManager>((ref) {
  final secureStorage = ref.read(secureStorageServiceProvider);
  final errorHandler = ref.read(globalErrorHandlerProvider);
  final manager = LocalDataManager(secureStorage, errorHandler);
  ref.onDispose(() => manager.dispose());
  return manager;
});
