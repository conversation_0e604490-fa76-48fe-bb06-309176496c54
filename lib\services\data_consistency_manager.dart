import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/providers/task_notifier.dart';
import 'package:loadguard/providers/workload_notifier.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/core/providers/app_providers.dart';
import 'package:loadguard/models/task_model.dart';

/// 🔧 数据一致性管理器
/// 
/// 负责确保多数据源之间的数据一致性，解决状态同步问题
class DataConsistencyManager {
  static final DataConsistencyManager _instance = DataConsistencyManager._internal();
  factory DataConsistencyManager() => _instance;
  DataConsistencyManager._internal();

  /// 数据同步锁，防止并发操作冲突
  final Map<String, Completer<void>> _syncLocks = {};
  
  /// 数据版本管理
  final Map<String, int> _dataVersions = {};
  
  /// 待同步操作队列
  final List<DataOperation> _pendingOperations = [];
  
  /// 是否正在执行批量同步
  bool _isBatchSyncing = false;

  /// 🔄 统一同步所有数据源
  /// 
  /// 确保TaskService、TaskNotifier、Repository之间的数据一致性
  static Future<void> syncAllDataSources(WidgetRef ref) async {
    final manager = DataConsistencyManager();
    await manager._performFullSync(ref);
  }

  /// 🔒 执行事务性操作
  /// 
  /// 确保多个操作要么全部成功，要么全部回滚
  static Future<T> executeTransaction<T>(
    WidgetRef ref,
    Future<T> Function() operation, {
    String? operationId,
  }) async {
    final manager = DataConsistencyManager();
    return await manager._executeTransactionInternal(ref, operation, operationId);
  }

  /// 📊 验证数据一致性
  /// 
  /// 检查各数据源之间是否存在不一致
  static Future<DataConsistencyReport> validateConsistency(WidgetRef ref) async {
    final manager = DataConsistencyManager();
    return await manager._validateDataConsistency(ref);
  }

  /// 🔄 同步单个任务
  /// 
  /// 确保特定任务在所有数据源中保持一致
  static Future<void> syncTask(WidgetRef ref, String taskId) async {
    final manager = DataConsistencyManager();
    await manager._syncSingleTask(ref, taskId);
  }

  /// 执行完整数据同步
  Future<void> _performFullSync(WidgetRef ref) async {
    if (_isBatchSyncing) {
      AppLogger.info('⚠️ 批量同步正在进行中，跳过本次同步', tag: 'DataConsistency');
      return;
    }

    try {
      _isBatchSyncing = true;
      AppLogger.info('🔄 开始执行完整数据同步...', tag: 'DataConsistency');

      // 1. 获取所有数据源的数据
      final taskService = ref.read(taskServiceProvider);
      final taskRepository = ref.read(taskRepositoryProvider);
      final taskNotifier = ref.read(taskNotifierProvider.notifier);

      // 2. 从Repository获取权威数据
      final repositoryTasks = await taskRepository.getAllTasks();
      AppLogger.info('📊 Repository中的任务数量: ${repositoryTasks.length}', tag: 'DataConsistency');

      // 3. 同步到TaskService
      await _syncTaskService(taskService, repositoryTasks);

      // 4. 同步到TaskNotifier
      await _syncTaskNotifier(taskNotifier, repositoryTasks);

      // 5. 处理待同步操作
      await _processPendingOperations(ref);

      AppLogger.info('✅ 完整数据同步完成', tag: 'DataConsistency');
    } catch (e) {
      AppLogger.error('❌ 完整数据同步失败: $e', tag: 'DataConsistency');
      rethrow;
    } finally {
      _isBatchSyncing = false;
    }
  }

  /// 同步TaskService数据
  Future<void> _syncTaskService(TaskService taskService, List<TaskModel> authoritativeTasks) async {
    try {
      // 清空TaskService的内存数据
      taskService.tasks.clear();
      
      // 重新加载权威数据
      for (final task in authoritativeTasks) {
        taskService.tasks.add(task);
      }
      
      AppLogger.info('✅ TaskService数据同步完成: ${authoritativeTasks.length}个任务', tag: 'DataConsistency');
    } catch (e) {
      AppLogger.error('❌ TaskService数据同步失败: $e', tag: 'DataConsistency');
      rethrow;
    }
  }

  /// 同步TaskNotifier数据
  Future<void> _syncTaskNotifier(TaskNotifier taskNotifier, List<TaskModel> authoritativeTasks) async {
    try {
      // 通过TaskNotifier的refreshTasks方法重新加载数据
      await taskNotifier.refreshTasks();
      
      AppLogger.info('✅ TaskNotifier数据同步完成', tag: 'DataConsistency');
    } catch (e) {
      AppLogger.error('❌ TaskNotifier数据同步失败: $e', tag: 'DataConsistency');
      rethrow;
    }
  }

  /// 执行事务性操作
  Future<T> _executeTransactionInternal<T>(
    WidgetRef ref,
    Future<T> Function() operation,
    String? operationId,
  ) async {
    final lockId = operationId ?? 'transaction_${DateTime.now().millisecondsSinceEpoch}';
    
    // 检查是否已有相同操作在执行
    if (_syncLocks.containsKey(lockId)) {
      AppLogger.info('⚠️ 操作已在执行中，等待完成: $lockId', tag: 'DataConsistency');
      await _syncLocks[lockId]!.future;
    }

    final completer = Completer<void>();
    _syncLocks[lockId] = completer;

    try {
      AppLogger.info('🔒 开始执行事务操作: $lockId', tag: 'DataConsistency');
      
      // 创建数据快照
      final snapshot = await _createDataSnapshot(ref);
      
      try {
        // 执行操作
        final result = await operation();
        
        // 操作成功，同步所有数据源
        await syncAllDataSources(ref);
        
        AppLogger.info('✅ 事务操作成功完成: $lockId', tag: 'DataConsistency');
        return result;
      } catch (e) {
        // 操作失败，恢复数据快照
        AppLogger.error('❌ 事务操作失败，正在回滚: $lockId, 错误: $e', tag: 'DataConsistency');
        await _restoreDataSnapshot(ref, snapshot);
        rethrow;
      }
    } finally {
      completer.complete();
      _syncLocks.remove(lockId);
    }
  }

  /// 验证数据一致性
  Future<DataConsistencyReport> _validateDataConsistency(WidgetRef ref) async {
    try {
      AppLogger.info('🔍 开始验证数据一致性...', tag: 'DataConsistency');

      final taskService = ref.read(taskServiceProvider);
      final taskRepository = ref.read(taskRepositoryProvider);
      final taskStateAsync = ref.read(taskNotifierProvider);

      // 获取各数据源的任务数据
      final serviceTasks = taskService.tasks;
      final repositoryTasks = await taskRepository.getAllTasks();
      final notifierTasks = taskStateAsync.when(
        data: (taskState) => taskState.tasks,
        loading: () => <TaskModel>[],
        error: (_, __) => <TaskModel>[],
      );

      // 比较数据一致性
      final report = DataConsistencyReport(
        serviceTaskCount: serviceTasks.length,
        repositoryTaskCount: repositoryTasks.length,
        notifierTaskCount: notifierTasks.length,
        isConsistent: serviceTasks.length == repositoryTasks.length && 
                     repositoryTasks.length == notifierTasks.length,
        inconsistencies: _findInconsistencies(serviceTasks, repositoryTasks, notifierTasks),
        timestamp: DateTime.now(),
      );

      AppLogger.info('📊 数据一致性验证完成: ${report.isConsistent ? "一致" : "不一致"}', tag: 'DataConsistency');
      return report;
    } catch (e) {
      AppLogger.error('❌ 数据一致性验证失败: $e', tag: 'DataConsistency');
      rethrow;
    }
  }

  /// 同步单个任务
  Future<void> _syncSingleTask(WidgetRef ref, String taskId) async {
    try {
      AppLogger.info('🔄 开始同步单个任务: $taskId', tag: 'DataConsistency');

      final taskRepository = ref.read(taskRepositoryProvider);
      final authoritativeTask = await taskRepository.getTaskById(taskId);

      if (authoritativeTask == null) {
        AppLogger.warning('⚠️ 任务不存在于Repository中: $taskId', tag: 'DataConsistency');
        return;
      }

      // 同步到TaskService
      final taskService = ref.read(taskServiceProvider);
      final serviceTaskIndex = taskService.tasks.indexWhere((t) => t.id == taskId);
      if (serviceTaskIndex >= 0) {
        taskService.tasks[serviceTaskIndex] = authoritativeTask;
      } else {
        taskService.tasks.add(authoritativeTask);
      }

      // 同步到TaskNotifier
      await ref.read(taskNotifierProvider.notifier).updateTask(authoritativeTask);

      AppLogger.info('✅ 单个任务同步完成: $taskId', tag: 'DataConsistency');
    } catch (e) {
      AppLogger.error('❌ 单个任务同步失败: $taskId, 错误: $e', tag: 'DataConsistency');
      rethrow;
    }
  }

  /// 处理待同步操作
  Future<void> _processPendingOperations(WidgetRef ref) async {
    if (_pendingOperations.isEmpty) return;

    AppLogger.info('🔄 处理${_pendingOperations.length}个待同步操作...', tag: 'DataConsistency');

    final operations = List<DataOperation>.from(_pendingOperations);
    _pendingOperations.clear();

    for (final operation in operations) {
      try {
        await operation.execute(ref);
        AppLogger.info('✅ 待同步操作执行成功: ${operation.type}', tag: 'DataConsistency');
      } catch (e) {
        AppLogger.error('❌ 待同步操作执行失败: ${operation.type}, 错误: $e', tag: 'DataConsistency');
        // 失败的操作重新加入队列
        _pendingOperations.add(operation);
      }
    }
  }

  /// 创建数据快照
  Future<DataSnapshot> _createDataSnapshot(WidgetRef ref) async {
    final taskService = ref.read(taskServiceProvider);
    final taskRepository = ref.read(taskRepositoryProvider);

    return DataSnapshot(
      serviceTasks: List<TaskModel>.from(taskService.tasks),
      repositoryTasks: await taskRepository.getAllTasks(),
      timestamp: DateTime.now(),
    );
  }

  /// 恢复数据快照
  Future<void> _restoreDataSnapshot(WidgetRef ref, DataSnapshot snapshot) async {
    try {
      AppLogger.info('🔄 正在恢复数据快照...', tag: 'DataConsistency');

      final taskService = ref.read(taskServiceProvider);
      final taskRepository = ref.read(taskRepositoryProvider);

      // 恢复TaskService数据
      taskService.tasks.clear();
      taskService.tasks.addAll(snapshot.serviceTasks);

      // 恢复Repository数据
      for (final task in snapshot.repositoryTasks) {
        await taskRepository.saveTask(task);
      }

      // 刷新TaskNotifier
      await ref.read(taskNotifierProvider.notifier).refreshTasks();

      AppLogger.info('✅ 数据快照恢复完成', tag: 'DataConsistency');
    } catch (e) {
      AppLogger.error('❌ 数据快照恢复失败: $e', tag: 'DataConsistency');
      rethrow;
    }
  }

  /// 查找数据不一致项
  List<String> _findInconsistencies(
    List<TaskModel> serviceTasks,
    List<TaskModel> repositoryTasks,
    List<TaskModel> notifierTasks,
  ) {
    final inconsistencies = <String>[];

    // 检查任务数量不一致
    if (serviceTasks.length != repositoryTasks.length) {
      inconsistencies.add('TaskService和Repository任务数量不一致: ${serviceTasks.length} vs ${repositoryTasks.length}');
    }

    if (repositoryTasks.length != notifierTasks.length) {
      inconsistencies.add('Repository和TaskNotifier任务数量不一致: ${repositoryTasks.length} vs ${notifierTasks.length}');
    }

    // 检查具体任务不一致
    final serviceIds = serviceTasks.map((t) => t.id).toSet();
    final repositoryIds = repositoryTasks.map((t) => t.id).toSet();
    final notifierIds = notifierTasks.map((t) => t.id).toSet();

    final missingInService = repositoryIds.difference(serviceIds);
    final missingInRepository = serviceIds.difference(repositoryIds);
    final missingInNotifier = repositoryIds.difference(notifierIds);

    if (missingInService.isNotEmpty) {
      inconsistencies.add('TaskService中缺失的任务: ${missingInService.join(", ")}');
    }

    if (missingInRepository.isNotEmpty) {
      inconsistencies.add('Repository中缺失的任务: ${missingInRepository.join(", ")}');
    }

    if (missingInNotifier.isNotEmpty) {
      inconsistencies.add('TaskNotifier中缺失的任务: ${missingInNotifier.join(", ")}');
    }

    return inconsistencies;
  }
}

/// 数据操作抽象类
abstract class DataOperation {
  final String type;
  final DateTime timestamp;

  DataOperation(this.type) : timestamp = DateTime.now();

  Future<void> execute(WidgetRef ref);
}

/// 数据一致性报告
class DataConsistencyReport {
  final int serviceTaskCount;
  final int repositoryTaskCount;
  final int notifierTaskCount;
  final bool isConsistent;
  final List<String> inconsistencies;
  final DateTime timestamp;

  DataConsistencyReport({
    required this.serviceTaskCount,
    required this.repositoryTaskCount,
    required this.notifierTaskCount,
    required this.isConsistent,
    required this.inconsistencies,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'DataConsistencyReport{\n'
        '  serviceTaskCount: $serviceTaskCount,\n'
        '  repositoryTaskCount: $repositoryTaskCount,\n'
        '  notifierTaskCount: $notifierTaskCount,\n'
        '  isConsistent: $isConsistent,\n'
        '  inconsistencies: $inconsistencies,\n'
        '  timestamp: $timestamp\n'
        '}';
  }
}

/// 数据快照
class DataSnapshot {
  final List<TaskModel> serviceTasks;
  final List<TaskModel> repositoryTasks;
  final DateTime timestamp;

  DataSnapshot({
    required this.serviceTasks,
    required this.repositoryTasks,
    required this.timestamp,
  });
}
