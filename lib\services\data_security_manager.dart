import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/encryption_service.dart';
import 'package:loadguard/services/secure_storage_service.dart';
import 'package:loadguard/exceptions/app_exceptions.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 数据安全管理器
/// 统一管理应用的数据安全策略，包括敏感数据保护、访问控制等
class DataSecurityManager {
  final EncryptionService _encryptionService;
  final SecureStorageService _secureStorageService;
  
  bool _initialized = false;
  SecurityPolicy? _currentPolicy;
  final List<SecurityEvent> _securityEvents = [];
  final StreamController<SecurityEvent> _securityEventStream = StreamController.broadcast();
  
  DataSecurityManager(this._encryptionService, this._secureStorageService);
  
  /// 安全事件流
  Stream<SecurityEvent> get securityEventStream => _securityEventStream.stream;
  
  /// 初始化数据安全管理器
  Future<void> initialize({SecurityPolicy? customPolicy}) async {
    try {
      AppLogger.info('🛡️ 初始化数据安全管理器...');
      
      // 初始化加密服务
      await _encryptionService.initialize();
      
      // 初始化安全存储
      await _secureStorageService.initialize(_encryptionService);
      
      // 设置安全策略
      _currentPolicy = customPolicy ?? await _loadOrCreateSecurityPolicy();
      
      // 执行安全检查
      await _performSecurityChecks();
      
      // 记录初始化事件
      await _recordSecurityEvent(SecurityEventType.initialization, 'Data security manager initialized');
      
      _initialized = true;
      AppLogger.info('✅ 数据安全管理器初始化完成');
    } catch (e) {
      AppLogger.error('❌ 数据安全管理器初始化失败', error: e);
      throw SystemException(
        message: 'Failed to initialize data security manager',
        code: 'SECURITY_MANAGER_INIT_FAILED',
        originalError: e,
      );
    }
  }
  
  /// 检查是否已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw SystemException(
        message: 'DataSecurityManager not initialized',
        code: 'SECURITY_MANAGER_NOT_INITIALIZED',
      );
    }
  }
  
  /// 保护敏感数据
  Future<void> protectSensitiveData(String dataType, dynamic data) async {
    _ensureInitialized();
    
    try {
      // 验证数据类型是否允许
      if (!_currentPolicy!.allowedDataTypes.contains(dataType)) {
        throw SecurityException(
          message: 'Data type not allowed: $dataType',
          code: 'DATA_TYPE_NOT_ALLOWED',
          details: {'dataType': dataType},
        );
      }
      
      // 根据安全级别选择保护方式
      final securityLevel = _getDataSecurityLevel(dataType);
      
      switch (securityLevel) {
        case SecurityLevel.critical:
          await _secureStorageService.storeSensitiveData(dataType, data);
          break;
        case SecurityLevel.high:
          await _secureStorageService.storeSecureData(dataType, data);
          break;
        case SecurityLevel.medium:
          await _secureStorageService.storeSecureData(dataType, data);
          break;
        case SecurityLevel.low:
          // 普通存储，但记录访问
          await _secureStorageService.storeSecureData(dataType, data);
          break;
      }
      
      await _recordSecurityEvent(
        SecurityEventType.dataProtection,
        'Protected $dataType data',
        details: {'dataType': dataType, 'securityLevel': securityLevel.name},
      );
      
      AppLogger.debug('🔒 敏感数据已保护: $dataType (级别: ${securityLevel.name})');
    } catch (e) {
      AppLogger.error('❌ 敏感数据保护失败: $dataType', error: e);
      await _recordSecurityEvent(
        SecurityEventType.securityViolation,
        'Failed to protect $dataType data: $e',
        details: {'dataType': dataType, 'error': e.toString()},
      );
      rethrow;
    }
  }
  
  /// 访问敏感数据
  Future<T?> accessSensitiveData<T>(String dataType, {String? reason}) async {
    _ensureInitialized();
    
    try {
      // 检查访问权限
      if (!_checkDataAccess(dataType)) {
        throw SecurityException(
          message: 'Access denied for data type: $dataType',
          code: 'DATA_ACCESS_DENIED',
          details: {'dataType': dataType},
        );
      }
      
      // 根据安全级别选择访问方式
      final securityLevel = _getDataSecurityLevel(dataType);
      T? data;
      
      switch (securityLevel) {
        case SecurityLevel.critical:
          data = await _secureStorageService.getSensitiveData<T>(dataType);
          break;
        case SecurityLevel.high:
        case SecurityLevel.medium:
        case SecurityLevel.low:
          data = await _secureStorageService.getSecureData<T>(dataType);
          break;
      }
      
      await _recordSecurityEvent(
        SecurityEventType.dataAccess,
        'Accessed $dataType data',
        details: {
          'dataType': dataType,
          'securityLevel': securityLevel.name,
          'reason': reason,
          'success': data != null,
        },
      );
      
      AppLogger.debug('🔓 敏感数据已访问: $dataType (级别: ${securityLevel.name})');
      return data;
    } catch (e) {
      AppLogger.error('❌ 敏感数据访问失败: $dataType', error: e);
      await _recordSecurityEvent(
        SecurityEventType.securityViolation,
        'Failed to access $dataType data: $e',
        details: {'dataType': dataType, 'error': e.toString()},
      );
      rethrow;
    }
  }
  
  /// 删除敏感数据
  Future<void> deleteSensitiveData(String dataType, {String? reason}) async {
    _ensureInitialized();
    
    try {
      // 检查删除权限
      if (!_checkDataDeletion(dataType)) {
        throw SecurityException(
          message: 'Deletion not allowed for data type: $dataType',
          code: 'DATA_DELETION_DENIED',
          details: {'dataType': dataType},
        );
      }
      
      final securityLevel = _getDataSecurityLevel(dataType);
      
      switch (securityLevel) {
        case SecurityLevel.critical:
          await _secureStorageService.deleteSensitiveData(dataType);
          break;
        case SecurityLevel.high:
        case SecurityLevel.medium:
        case SecurityLevel.low:
          await _secureStorageService.deleteSecureData(dataType);
          break;
      }
      
      await _recordSecurityEvent(
        SecurityEventType.dataDeletion,
        'Deleted $dataType data',
        details: {
          'dataType': dataType,
          'securityLevel': securityLevel.name,
          'reason': reason,
        },
      );
      
      AppLogger.debug('🗑️ 敏感数据已删除: $dataType');
    } catch (e) {
      AppLogger.error('❌ 敏感数据删除失败: $dataType', error: e);
      await _recordSecurityEvent(
        SecurityEventType.securityViolation,
        'Failed to delete $dataType data: $e',
        details: {'dataType': dataType, 'error': e.toString()},
      );
      rethrow;
    }
  }
  
  /// 执行安全审计
  Future<SecurityAuditReport> performSecurityAudit() async {
    _ensureInitialized();
    
    try {
      AppLogger.info('🔍 开始安全审计...');
      
      final auditResults = <String, dynamic>{};
      
      // 检查加密状态
      final encryptionStats = _encryptionService.getStatistics();
      auditResults['encryption'] = {
        'initialized': encryptionStats.initialized,
        'algorithm': encryptionStats.algorithm,
        'keyLength': encryptionStats.keyLength,
      };
      
      // 检查存储安全
      final storageStats = _secureStorageService.getStatistics();
      auditResults['storage'] = {
        'sensitiveDataCount': storageStats.sensitiveDataCount,
        'secureDataCount': storageStats.secureDataCount,
        'encryptionEnabled': storageStats.encryptionEnabled,
        'integrityCheckEnabled': storageStats.integrityCheckEnabled,
      };
      
      // 检查安全策略
      auditResults['policy'] = {
        'securityLevel': _currentPolicy?.defaultSecurityLevel.name,
        'allowedDataTypes': _currentPolicy?.allowedDataTypes.length,
        'accessControlEnabled': _currentPolicy?.enableAccessControl,
      };
      
      // 分析安全事件
      final recentEvents = _getRecentSecurityEvents(const Duration(hours: 24));
      auditResults['events'] = {
        'totalEvents': recentEvents.length,
        'violations': recentEvents.where((e) => e.type == SecurityEventType.securityViolation).length,
        'dataAccess': recentEvents.where((e) => e.type == SecurityEventType.dataAccess).length,
      };
      
      // 计算安全分数
      final securityScore = _calculateSecurityScore(auditResults);
      
      final report = SecurityAuditReport(
        timestamp: DateTime.now(),
        securityScore: securityScore,
        auditResults: auditResults,
        recommendations: _generateSecurityRecommendations(auditResults),
      );
      
      await _recordSecurityEvent(
        SecurityEventType.securityAudit,
        'Security audit completed',
        details: {'securityScore': securityScore},
      );
      
      AppLogger.info('✅ 安全审计完成 (安全分数: $securityScore)');
      return report;
    } catch (e) {
      AppLogger.error('❌ 安全审计失败', error: e);
      rethrow;
    }
  }
  
  /// 获取安全统计信息
  SecurityStatistics getSecurityStatistics() {
    _ensureInitialized();
    
    final recentEvents = _getRecentSecurityEvents(const Duration(hours: 24));
    
    return SecurityStatistics(
      totalSecurityEvents: _securityEvents.length,
      recentSecurityEvents: recentEvents.length,
      securityViolations: recentEvents.where((e) => e.type == SecurityEventType.securityViolation).length,
      dataAccessCount: recentEvents.where((e) => e.type == SecurityEventType.dataAccess).length,
      currentSecurityLevel: _currentPolicy?.defaultSecurityLevel ?? SecurityLevel.medium,
      encryptionEnabled: _encryptionService.isInitialized,
      lastAuditTime: _getLastAuditTime(),
    );
  }
  
  /// 加载或创建安全策略
  Future<SecurityPolicy> _loadOrCreateSecurityPolicy() async {
    try {
      final savedPolicy = await _secureStorageService.getSecuritySettings();
      if (savedPolicy != null) {
        return SecurityPolicy.fromMap(savedPolicy);
      }
    } catch (e) {
      AppLogger.warning('⚠️ 无法加载安全策略，使用默认策略: $e');
    }
    
    // 创建默认安全策略
    final defaultPolicy = SecurityPolicy.defaultPolicy();
    await _secureStorageService.storeSecuritySettings(defaultPolicy.toMap());
    return defaultPolicy;
  }
  
  /// 执行安全检查
  Future<void> _performSecurityChecks() async {
    try {
      // 检查设备安全状态
      await _checkDeviceSecurity();
      
      // 检查数据完整性
      await _checkDataIntegrity();
      
      // 检查访问模式
      await _checkAccessPatterns();
      
      AppLogger.debug('✅ 安全检查完成');
    } catch (e) {
      AppLogger.warning('⚠️ 安全检查发现问题: $e');
      await _recordSecurityEvent(
        SecurityEventType.securityViolation,
        'Security check failed: $e',
      );
    }
  }
  
  /// 检查设备安全状态
  Future<void> _checkDeviceSecurity() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        
        // 检查是否为模拟器
        if (androidInfo.isPhysicalDevice == false) {
          await _recordSecurityEvent(
            SecurityEventType.securityWarning,
            'Running on emulator/simulator',
          );
        }
        
        // 检查系统版本
        if (androidInfo.version.sdkInt < 21) {
          await _recordSecurityEvent(
            SecurityEventType.securityWarning,
            'Android version too old for optimal security',
          );
        }
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        
        if (iosInfo.isPhysicalDevice == false) {
          await _recordSecurityEvent(
            SecurityEventType.securityWarning,
            'Running on iOS simulator',
          );
        }
      }
    } catch (e) {
      AppLogger.warning('⚠️ 设备安全检查失败: $e');
    }
  }
  
  /// 检查数据完整性
  Future<void> _checkDataIntegrity() async {
    try {
      // 这里可以实现数据完整性检查逻辑
      AppLogger.debug('✅ 数据完整性检查通过');
    } catch (e) {
      AppLogger.warning('⚠️ 数据完整性检查失败: $e');
    }
  }
  
  /// 检查访问模式
  Future<void> _checkAccessPatterns() async {
    try {
      // 分析最近的访问模式，检测异常
      final recentEvents = _getRecentSecurityEvents(const Duration(hours: 1));
      final accessEvents = recentEvents.where((e) => e.type == SecurityEventType.dataAccess).toList();
      
      if (accessEvents.length > 100) {
        await _recordSecurityEvent(
          SecurityEventType.securityWarning,
          'High frequency data access detected',
          details: {'accessCount': accessEvents.length},
        );
      }
    } catch (e) {
      AppLogger.warning('⚠️ 访问模式检查失败: $e');
    }
  }
  
  /// 获取数据安全级别
  SecurityLevel _getDataSecurityLevel(String dataType) {
    return _currentPolicy?.dataTypeSecurityLevels[dataType] ?? _currentPolicy?.defaultSecurityLevel ?? SecurityLevel.medium;
  }
  
  /// 检查数据访问权限
  bool _checkDataAccess(String dataType) {
    if (_currentPolicy?.enableAccessControl != true) {
      return true;
    }
    
    return _currentPolicy?.allowedDataTypes.contains(dataType) ?? false;
  }
  
  /// 检查数据删除权限
  bool _checkDataDeletion(String dataType) {
    if (_currentPolicy?.enableAccessControl != true) {
      return true;
    }
    
    // 某些关键数据类型可能不允许删除
    const protectedDataTypes = ['activation_code', 'device_info'];
    return !protectedDataTypes.contains(dataType);
  }
  
  /// 记录安全事件
  Future<void> _recordSecurityEvent(
    SecurityEventType type,
    String message, {
    Map<String, dynamic>? details,
  }) async {
    final event = SecurityEvent(
      type: type,
      message: message,
      timestamp: DateTime.now(),
      details: details ?? {},
    );
    
    _securityEvents.add(event);
    _securityEventStream.add(event);
    
    // 保持事件历史大小
    while (_securityEvents.length > 1000) {
      _securityEvents.removeAt(0);
    }
    
    // 记录到日志
    switch (type) {
      case SecurityEventType.securityViolation:
        AppLogger.warning('🚨 安全违规: $message');
        break;
      case SecurityEventType.securityWarning:
        AppLogger.warning('⚠️ 安全警告: $message');
        break;
      default:
        AppLogger.debug('🛡️ 安全事件: $message');
        break;
    }
  }
  
  /// 获取最近的安全事件
  List<SecurityEvent> _getRecentSecurityEvents(Duration timeWindow) {
    final cutoff = DateTime.now().subtract(timeWindow);
    return _securityEvents.where((event) => event.timestamp.isAfter(cutoff)).toList();
  }
  
  /// 计算安全分数
  double _calculateSecurityScore(Map<String, dynamic> auditResults) {
    double score = 100.0;
    
    // 加密检查
    final encryption = auditResults['encryption'] as Map<String, dynamic>;
    if (encryption['initialized'] != true) score -= 30;
    
    // 存储检查
    final storage = auditResults['storage'] as Map<String, dynamic>;
    if (storage['encryptionEnabled'] != true) score -= 20;
    if (storage['integrityCheckEnabled'] != true) score -= 10;
    
    // 事件检查
    final events = auditResults['events'] as Map<String, dynamic>;
    final violations = events['violations'] as int;
    score -= violations * 5; // 每个违规扣5分
    
    return score.clamp(0.0, 100.0);
  }
  
  /// 生成安全建议
  List<String> _generateSecurityRecommendations(Map<String, dynamic> auditResults) {
    final recommendations = <String>[];
    
    final encryption = auditResults['encryption'] as Map<String, dynamic>;
    if (encryption['initialized'] != true) {
      recommendations.add('启用数据加密功能');
    }
    
    final storage = auditResults['storage'] as Map<String, dynamic>;
    if (storage['encryptionEnabled'] != true) {
      recommendations.add('启用存储加密');
    }
    
    final events = auditResults['events'] as Map<String, dynamic>;
    final violations = events['violations'] as int;
    if (violations > 0) {
      recommendations.add('调查并解决安全违规事件');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('当前安全状态良好');
    }
    
    return recommendations;
  }
  
  /// 获取最后审计时间
  DateTime? _getLastAuditTime() {
    final auditEvents = _securityEvents.where((e) => e.type == SecurityEventType.securityAudit);
    if (auditEvents.isEmpty) return null;
    return auditEvents.last.timestamp;
  }
  
  /// 释放资源
  Future<void> dispose() async {
    if (_initialized) {
      _securityEventStream.close();
      _securityEvents.clear();
      _initialized = false;
      AppLogger.info('🛡️ 数据安全管理器已释放');
    }
  }
}

/// 安全异常
class SecurityException extends AppException {
  const SecurityException({
    required super.message,
    super.code = 'SECURITY_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = false,
  }) : super(
          category: ErrorCategory.system,
          severity: ErrorSeverity.high,
          actionable: false,
        );
}

/// 安全级别
enum SecurityLevel {
  low,      // 低安全级别
  medium,   // 中等安全级别
  high,     // 高安全级别
  critical, // 关键安全级别
}

/// 安全事件类型
enum SecurityEventType {
  initialization,     // 初始化
  dataProtection,     // 数据保护
  dataAccess,         // 数据访问
  dataDeletion,       // 数据删除
  securityAudit,      // 安全审计
  securityViolation,  // 安全违规
  securityWarning,    // 安全警告
  accessDenied,       // 访问拒绝
  encryptionEvent,    // 加密事件
  integrityCheck,     // 完整性检查
}

/// 安全事件
class SecurityEvent {
  final SecurityEventType type;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic> details;

  const SecurityEvent({
    required this.type,
    required this.message,
    required this.timestamp,
    required this.details,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'details': details,
    };
  }

  factory SecurityEvent.fromMap(Map<String, dynamic> map) {
    return SecurityEvent(
      type: SecurityEventType.values.firstWhere((e) => e.name == map['type']),
      message: map['message'] as String,
      timestamp: DateTime.parse(map['timestamp'] as String),
      details: map['details'] as Map<String, dynamic>,
    );
  }
}

/// 安全策略
class SecurityPolicy {
  final SecurityLevel defaultSecurityLevel;
  final Map<String, SecurityLevel> dataTypeSecurityLevels;
  final List<String> allowedDataTypes;
  final bool enableAccessControl;
  final bool enableAuditLogging;
  final Duration auditRetentionPeriod;

  const SecurityPolicy({
    required this.defaultSecurityLevel,
    required this.dataTypeSecurityLevels,
    required this.allowedDataTypes,
    required this.enableAccessControl,
    required this.enableAuditLogging,
    required this.auditRetentionPeriod,
  });

  factory SecurityPolicy.defaultPolicy() {
    return SecurityPolicy(
      defaultSecurityLevel: SecurityLevel.medium,
      dataTypeSecurityLevels: {
        'activation_code': SecurityLevel.critical,
        'device_info': SecurityLevel.high,
        'user_credentials': SecurityLevel.critical,
        'app_config': SecurityLevel.medium,
        'security_settings': SecurityLevel.high,
      },
      allowedDataTypes: [
        'activation_code',
        'device_info',
        'user_credentials',
        'app_config',
        'security_settings',
        'session_tokens',
        'biometric_data',
        'backup_data',
        'audit_log',
      ],
      enableAccessControl: true,
      enableAuditLogging: true,
      auditRetentionPeriod: const Duration(days: 30),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'defaultSecurityLevel': defaultSecurityLevel.name,
      'dataTypeSecurityLevels': dataTypeSecurityLevels.map((k, v) => MapEntry(k, v.name)),
      'allowedDataTypes': allowedDataTypes,
      'enableAccessControl': enableAccessControl,
      'enableAuditLogging': enableAuditLogging,
      'auditRetentionPeriod': auditRetentionPeriod.inDays,
    };
  }

  factory SecurityPolicy.fromMap(Map<String, dynamic> map) {
    return SecurityPolicy(
      defaultSecurityLevel: SecurityLevel.values.firstWhere((e) => e.name == map['defaultSecurityLevel']),
      dataTypeSecurityLevels: (map['dataTypeSecurityLevels'] as Map<String, dynamic>)
          .map((k, v) => MapEntry(k, SecurityLevel.values.firstWhere((e) => e.name == v))),
      allowedDataTypes: List<String>.from(map['allowedDataTypes']),
      enableAccessControl: map['enableAccessControl'] as bool,
      enableAuditLogging: map['enableAuditLogging'] as bool,
      auditRetentionPeriod: Duration(days: map['auditRetentionPeriod'] as int),
    );
  }
}

/// 安全审计报告
class SecurityAuditReport {
  final DateTime timestamp;
  final double securityScore;
  final Map<String, dynamic> auditResults;
  final List<String> recommendations;

  const SecurityAuditReport({
    required this.timestamp,
    required this.securityScore,
    required this.auditResults,
    required this.recommendations,
  });

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'securityScore': securityScore,
      'auditResults': auditResults,
      'recommendations': recommendations,
    };
  }
}

/// 安全统计信息
class SecurityStatistics {
  final int totalSecurityEvents;
  final int recentSecurityEvents;
  final int securityViolations;
  final int dataAccessCount;
  final SecurityLevel currentSecurityLevel;
  final bool encryptionEnabled;
  final DateTime? lastAuditTime;

  const SecurityStatistics({
    required this.totalSecurityEvents,
    required this.recentSecurityEvents,
    required this.securityViolations,
    required this.dataAccessCount,
    required this.currentSecurityLevel,
    required this.encryptionEnabled,
    this.lastAuditTime,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalSecurityEvents': totalSecurityEvents,
      'recentSecurityEvents': recentSecurityEvents,
      'securityViolations': securityViolations,
      'dataAccessCount': dataAccessCount,
      'currentSecurityLevel': currentSecurityLevel.name,
      'encryptionEnabled': encryptionEnabled,
      'lastAuditTime': lastAuditTime?.toIso8601String(),
    };
  }
}

/// 数据安全管理器Provider
final dataSecurityManagerProvider = Provider<DataSecurityManager>((ref) {
  final encryptionService = ref.read(encryptionServiceProvider);
  final secureStorageService = ref.read(secureStorageServiceProvider);
  final manager = DataSecurityManager(encryptionService, secureStorageService);
  ref.onDispose(() => manager.dispose());
  return manager;
});
