// 工作量统计数据模型
// 手动实现freezed风格的不可变数据类，避免代码生成器依赖

/// 工作量统计结果
class WorkloadStatistics {
  final Map<String, WorkerStatistics> workerStats;
  final WorkloadOverview overview;
  final List<EfficiencyRanking> efficiencyRanking;
  final DateTime lastUpdated;
  final Map<String, dynamic>? dateRange;

  const WorkloadStatistics({
    required this.workerStats,
    required this.overview,
    required this.efficiencyRanking,
    required this.lastUpdated,
    this.dateRange,
  });

  /// 复制并修改
  WorkloadStatistics copyWith({
    Map<String, WorkerStatistics>? workerStats,
    WorkloadOverview? overview,
    List<EfficiencyRanking>? efficiencyRanking,
    DateTime? lastUpdated,
    Map<String, dynamic>? dateRange,
  }) {
    return WorkloadStatistics(
      workerStats: workerStats ?? this.workerStats,
      overview: overview ?? this.overview,
      efficiencyRanking: efficiencyRanking ?? this.efficiencyRanking,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      dateRange: dateRange ?? this.dateRange,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'workerStats': workerStats.map((k, v) => MapEntry(k, v.toJson())),
      'overview': overview.toJson(),
      'efficiencyRanking': efficiencyRanking.map((e) => e.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'dateRange': dateRange,
    };
  }

  /// 从JSON创建
  factory WorkloadStatistics.fromJson(Map<String, dynamic> json) {
    return WorkloadStatistics(
      workerStats: (json['workerStats'] as Map<String, dynamic>).map(
        (k, v) => MapEntry(k, WorkerStatistics.fromJson(v)),
      ),
      overview: WorkloadOverview.fromJson(json['overview']),
      efficiencyRanking: (json['efficiencyRanking'] as List)
          .map((e) => EfficiencyRanking.fromJson(e))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated']),
      dateRange: json['dateRange'],
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorkloadStatistics &&
          runtimeType == other.runtimeType &&
          workerStats == other.workerStats &&
          overview == other.overview &&
          efficiencyRanking == other.efficiencyRanking &&
          lastUpdated == other.lastUpdated &&
          dateRange == other.dateRange;

  @override
  int get hashCode =>
      workerStats.hashCode ^
      overview.hashCode ^
      efficiencyRanking.hashCode ^
      lastUpdated.hashCode ^
      dateRange.hashCode;
}

/// 工作人员统计数据
class WorkerStatistics {
  final String workerId;
  final String workerName;
  final String role;
  final String warehouse;
  final String group;
  final int totalTasks;
  final int completedTasks;
  final double totalTonnage;
  final double completedTonnage;
  final double efficiency;
  final double averageTonnagePerTask;

  const WorkerStatistics({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.totalTasks,
    required this.completedTasks,
    required this.totalTonnage,
    required this.completedTonnage,
    required this.efficiency,
    required this.averageTonnagePerTask,
  });

  /// 复制并修改
  WorkerStatistics copyWith({
    String? workerId,
    String? workerName,
    String? role,
    String? warehouse,
    String? group,
    int? totalTasks,
    int? completedTasks,
    double? totalTonnage,
    double? completedTonnage,
    double? efficiency,
    double? averageTonnagePerTask,
  }) {
    return WorkerStatistics(
      workerId: workerId ?? this.workerId,
      workerName: workerName ?? this.workerName,
      role: role ?? this.role,
      warehouse: warehouse ?? this.warehouse,
      group: group ?? this.group,
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      totalTonnage: totalTonnage ?? this.totalTonnage,
      completedTonnage: completedTonnage ?? this.completedTonnage,
      efficiency: efficiency ?? this.efficiency,
      averageTonnagePerTask: averageTonnagePerTask ?? this.averageTonnagePerTask,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'totalTonnage': totalTonnage,
      'completedTonnage': completedTonnage,
      'efficiency': efficiency,
      'averageTonnagePerTask': averageTonnagePerTask,
    };
  }

  /// 从JSON创建
  factory WorkerStatistics.fromJson(Map<String, dynamic> json) {
    return WorkerStatistics(
      workerId: json['workerId'],
      workerName: json['workerName'],
      role: json['role'],
      warehouse: json['warehouse'],
      group: json['group'],
      totalTasks: json['totalTasks'],
      completedTasks: json['completedTasks'],
      totalTonnage: json['totalTonnage'],
      completedTonnage: json['completedTonnage'],
      efficiency: json['efficiency'],
      averageTonnagePerTask: json['averageTonnagePerTask'],
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorkerStatistics &&
          runtimeType == other.runtimeType &&
          workerId == other.workerId &&
          workerName == other.workerName &&
          role == other.role &&
          warehouse == other.warehouse &&
          group == other.group &&
          totalTasks == other.totalTasks &&
          completedTasks == other.completedTasks &&
          totalTonnage == other.totalTonnage &&
          completedTonnage == other.completedTonnage &&
          efficiency == other.efficiency &&
          averageTonnagePerTask == other.averageTonnagePerTask;

  @override
  int get hashCode =>
      workerId.hashCode ^
      workerName.hashCode ^
      role.hashCode ^
      warehouse.hashCode ^
      group.hashCode ^
      totalTasks.hashCode ^
      completedTasks.hashCode ^
      totalTonnage.hashCode ^
      completedTonnage.hashCode ^
      efficiency.hashCode ^
      averageTonnagePerTask.hashCode;
}

/// 工作量概览数据
class WorkloadOverview {
  final int totalTasks;
  final int completedTasks;
  final int activeWorkers;
  final int totalWorkers;
  final double averageEfficiency;
  final double totalTonnage;
  final double completedTonnage;
  final double completionRate;

  const WorkloadOverview({
    required this.totalTasks,
    required this.completedTasks,
    required this.activeWorkers,
    required this.totalWorkers,
    required this.averageEfficiency,
    required this.totalTonnage,
    required this.completedTonnage,
    required this.completionRate,
  });

  /// 复制并修改
  WorkloadOverview copyWith({
    int? totalTasks,
    int? completedTasks,
    int? activeWorkers,
    int? totalWorkers,
    double? averageEfficiency,
    double? totalTonnage,
    double? completedTonnage,
    double? completionRate,
  }) {
    return WorkloadOverview(
      totalTasks: totalTasks ?? this.totalTasks,
      completedTasks: completedTasks ?? this.completedTasks,
      activeWorkers: activeWorkers ?? this.activeWorkers,
      totalWorkers: totalWorkers ?? this.totalWorkers,
      averageEfficiency: averageEfficiency ?? this.averageEfficiency,
      totalTonnage: totalTonnage ?? this.totalTonnage,
      completedTonnage: completedTonnage ?? this.completedTonnage,
      completionRate: completionRate ?? this.completionRate,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'activeWorkers': activeWorkers,
      'totalWorkers': totalWorkers,
      'averageEfficiency': averageEfficiency,
      'totalTonnage': totalTonnage,
      'completedTonnage': completedTonnage,
      'completionRate': completionRate,
    };
  }

  /// 从JSON创建
  factory WorkloadOverview.fromJson(Map<String, dynamic> json) {
    return WorkloadOverview(
      totalTasks: json['totalTasks'],
      completedTasks: json['completedTasks'],
      activeWorkers: json['activeWorkers'],
      totalWorkers: json['totalWorkers'],
      averageEfficiency: json['averageEfficiency'],
      totalTonnage: json['totalTonnage'],
      completedTonnage: json['completedTonnage'],
      completionRate: json['completionRate'],
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorkloadOverview &&
          runtimeType == other.runtimeType &&
          totalTasks == other.totalTasks &&
          completedTasks == other.completedTasks &&
          activeWorkers == other.activeWorkers &&
          totalWorkers == other.totalWorkers &&
          averageEfficiency == other.averageEfficiency &&
          totalTonnage == other.totalTonnage &&
          completedTonnage == other.completedTonnage &&
          completionRate == other.completionRate;

  @override
  int get hashCode =>
      totalTasks.hashCode ^
      completedTasks.hashCode ^
      activeWorkers.hashCode ^
      totalWorkers.hashCode ^
      averageEfficiency.hashCode ^
      totalTonnage.hashCode ^
      completedTonnage.hashCode ^
      completionRate.hashCode;
}

/// 效率排名数据
class EfficiencyRanking {
  final String workerId;
  final String workerName;
  final String role;
  final double efficiency;
  final int rank;

  const EfficiencyRanking({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.efficiency,
    required this.rank,
  });

  /// 复制并修改
  EfficiencyRanking copyWith({
    String? workerId,
    String? workerName,
    String? role,
    double? efficiency,
    int? rank,
  }) {
    return EfficiencyRanking(
      workerId: workerId ?? this.workerId,
      workerName: workerName ?? this.workerName,
      role: role ?? this.role,
      efficiency: efficiency ?? this.efficiency,
      rank: rank ?? this.rank,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'efficiency': efficiency,
      'rank': rank,
    };
  }

  /// 从JSON创建
  factory EfficiencyRanking.fromJson(Map<String, dynamic> json) {
    return EfficiencyRanking(
      workerId: json['workerId'],
      workerName: json['workerName'],
      role: json['role'],
      efficiency: json['efficiency'],
      rank: json['rank'],
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EfficiencyRanking &&
          runtimeType == other.runtimeType &&
          workerId == other.workerId &&
          workerName == other.workerName &&
          role == other.role &&
          efficiency == other.efficiency &&
          rank == other.rank;

  @override
  int get hashCode =>
      workerId.hashCode ^
      workerName.hashCode ^
      role.hashCode ^
      efficiency.hashCode ^
      rank.hashCode;
}
