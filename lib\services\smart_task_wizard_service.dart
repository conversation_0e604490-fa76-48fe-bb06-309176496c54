import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/services/task_data_service.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🧙‍♂️ 智能任务创建向导服务
/// 
/// 基于历史数据和用户行为，提供智能化的任务创建体验
/// 支持模板推荐、自动填充、实时验证和创建进度保存
class SmartTaskWizardService {
  final TaskDataService _dataService;
  
  // 缓存的历史数据
  List<TaskModel>? _cachedTasks;
  Map<String, List<String>>? _productBatchHistory;
  Map<String, int>? _quantityHistory;
  Map<String, List<String>>? _participantHistory;
  
  // 向导状态
  final Map<String, WizardProgress> _wizardStates = {};

  SmartTaskWizardService({TaskDataService? dataService})
      : _dataService = dataService ?? TaskDataService();

  /// 初始化向导服务
  Future<void> initialize() async {
    try {
      AppLogger.info('🧙‍♂️ 初始化智能任务向导...', tag: 'TaskWizard');
      
      await _loadHistoricalData();
      await _buildRecommendationCache();
      
      AppLogger.info('✅ 智能任务向导初始化完成', tag: 'TaskWizard');
    } catch (e) {
      AppLogger.error('❌ 智能任务向导初始化失败: $e', tag: 'TaskWizard');
      rethrow;
    }
  }

  /// 开始新的任务创建向导
  Future<String> startWizard({String? userId}) async {
    final wizardId = 'wizard_${DateTime.now().millisecondsSinceEpoch}';
    
    _wizardStates[wizardId] = WizardProgress(
      wizardId: wizardId,
      userId: userId,
      startTime: DateTime.now(),
      currentStep: WizardStep.templateSelection,
      data: {},
    );
    
    AppLogger.info('🧙‍♂️ 开始任务创建向导: $wizardId', tag: 'TaskWizard');
    return wizardId;
  }

  /// 获取模板推荐
  Future<List<TemplateRecommendation>> getTemplateRecommendations(String wizardId) async {
    try {
      final progress = _wizardStates[wizardId];
      if (progress == null) {
        throw Exception('向导状态不存在: $wizardId');
      }

      AppLogger.debug('🧙‍♂️ 获取模板推荐: $wizardId', tag: 'TaskWizard');

      final recommendations = <TemplateRecommendation>[];
      
      // 基于历史使用频率推荐
      final templateUsage = await _getTemplateUsageStats(progress.userId);
      
      for (final entry in templateUsage.entries) {
        final template = entry.key;
        final usage = entry.value;
        
        recommendations.add(TemplateRecommendation(
          template: template,
          confidence: _calculateTemplateConfidence(template, usage),
          reason: _getTemplateRecommendationReason(template, usage),
          recentUsage: usage.recentCount,
          totalUsage: usage.totalCount,
          lastUsed: usage.lastUsed,
          estimatedDuration: usage.averageDuration,
        ));
      }
      
      // 按置信度排序
      recommendations.sort((a, b) => b.confidence.compareTo(a.confidence));
      
      AppLogger.info('✅ 生成${recommendations.length}个模板推荐', tag: 'TaskWizard');
      return recommendations;
    } catch (e) {
      AppLogger.error('❌ 获取模板推荐失败: $e', tag: 'TaskWizard');
      rethrow;
    }
  }

  /// 选择模板并获取智能填充建议
  Future<SmartFillSuggestions> selectTemplate(String wizardId, String template) async {
    try {
      final progress = _wizardStates[wizardId];
      if (progress == null) {
        throw Exception('向导状态不存在: $wizardId');
      }

      AppLogger.info('🧙‍♂️ 选择模板: $wizardId, $template', tag: 'TaskWizard');

      // 更新向导状态
      progress.data['template'] = template;
      progress.currentStep = WizardStep.dataInput;
      progress.lastUpdateTime = DateTime.now();

      // 生成智能填充建议
      final suggestions = await _generateSmartFillSuggestions(template, progress.userId);
      
      AppLogger.info('✅ 生成智能填充建议', tag: 'TaskWizard');
      return suggestions;
    } catch (e) {
      AppLogger.error('❌ 选择模板失败: $e', tag: 'TaskWizard');
      rethrow;
    }
  }

  /// 实时验证输入数据
  Future<ValidationResult> validateInput(
    String wizardId,
    String field,
    dynamic value,
  ) async {
    try {
      final progress = _wizardStates[wizardId];
      if (progress == null) {
        throw Exception('向导状态不存在: $wizardId');
      }

      AppLogger.debug('🧙‍♂️ 验证输入: $wizardId, $field = $value', tag: 'TaskWizard');

      final template = progress.data['template'] as String?;
      if (template == null) {
        return ValidationResult.error('请先选择模板');
      }

      // 执行字段验证
      final result = await _validateField(template, field, value);
      
      // 如果验证通过，保存数据
      if (result.isValid) {
        progress.data[field] = value;
        progress.lastUpdateTime = DateTime.now();
      }

      return result;
    } catch (e) {
      AppLogger.error('❌ 验证输入失败: $e', tag: 'TaskWizard');
      return ValidationResult.error('验证失败: $e');
    }
  }

  /// 获取字段建议
  Future<List<String>> getFieldSuggestions(
    String wizardId,
    String field,
    String query,
  ) async {
    try {
      final progress = _wizardStates[wizardId];
      if (progress == null) return [];

      AppLogger.debug('🧙‍♂️ 获取字段建议: $field, $query', tag: 'TaskWizard');

      switch (field) {
        case 'productCode':
          return _getProductCodeSuggestions(query);
        case 'batchNumber':
          final productCode = progress.data['productCode'] as String?;
          return _getBatchNumberSuggestions(productCode, query);
        case 'participants':
          return _getParticipantSuggestions(query);
        default:
          return [];
      }
    } catch (e) {
      AppLogger.error('❌ 获取字段建议失败: $e', tag: 'TaskWizard');
      return [];
    }
  }

  /// 保存向导进度
  Future<void> saveProgress(String wizardId) async {
    try {
      final progress = _wizardStates[wizardId];
      if (progress == null) return;

      AppLogger.debug('🧙‍♂️ 保存向导进度: $wizardId', tag: 'TaskWizard');

      // 这里可以将进度保存到本地存储
      // 以便用户下次可以继续
      
      progress.lastSaveTime = DateTime.now();
    } catch (e) {
      AppLogger.error('❌ 保存向导进度失败: $e', tag: 'TaskWizard');
    }
  }

  /// 完成向导并创建任务
  Future<TaskCreationResult> completeWizard(String wizardId) async {
    try {
      final progress = _wizardStates[wizardId];
      if (progress == null) {
        throw Exception('向导状态不存在: $wizardId');
      }

      AppLogger.info('🧙‍♂️ 完成向导: $wizardId', tag: 'TaskWizard');

      // 最终验证
      final validationResult = await _validateCompleteData(progress.data);
      if (!validationResult.isValid) {
        return TaskCreationResult.failure(validationResult.message);
      }

      // 创建任务数据
      final taskData = TaskCreationData(
        template: progress.data['template'] as String,
        productCode: progress.data['productCode'] as String,
        batchNumber: progress.data['batchNumber'] as String,
        quantity: progress.data['quantity'] as int,
        participants: progress.data['participants'] as List<String>? ?? [],
      );

      // 记录向导使用统计
      await _recordWizardUsage(progress);

      // 清理向导状态
      _wizardStates.remove(wizardId);

      AppLogger.info('✅ 向导完成，任务数据准备就绪', tag: 'TaskWizard');
      return TaskCreationResult.success(taskData);
    } catch (e) {
      AppLogger.error('❌ 完成向导失败: $e', tag: 'TaskWizard');
      return TaskCreationResult.failure('向导完成失败: $e');
    }
  }

  /// 取消向导
  void cancelWizard(String wizardId) {
    final progress = _wizardStates.remove(wizardId);
    if (progress != null) {
      AppLogger.info('🧙‍♂️ 取消向导: $wizardId', tag: 'TaskWizard');
    }
  }

  /// 获取向导统计信息
  Map<String, dynamic> getWizardStats() {
    return {
      'activeWizards': _wizardStates.length,
      'cacheStatus': {
        'tasks': _cachedTasks?.length ?? 0,
        'productBatches': _productBatchHistory?.length ?? 0,
        'quantities': _quantityHistory?.length ?? 0,
        'participants': _participantHistory?.length ?? 0,
      },
    };
  }

  // 私有方法

  /// 加载历史数据
  Future<void> _loadHistoricalData() async {
    _cachedTasks = await _dataService.getAllTasks();
    AppLogger.debug('📊 加载历史任务: ${_cachedTasks!.length}个', tag: 'TaskWizard');
  }

  /// 构建推荐缓存
  Future<void> _buildRecommendationCache() async {
    if (_cachedTasks == null) return;

    _productBatchHistory = {};
    _quantityHistory = {};
    _participantHistory = {};

    for (final task in _cachedTasks!) {
      // 产品-批次关联
      _productBatchHistory!.putIfAbsent(task.productCode, () => []).add(task.batchNumber);
      
      // 数量历史
      _quantityHistory![task.productCode] = task.quantity;
      
      // 参与者历史
      for (final participant in task.participants) {
        _participantHistory!.putIfAbsent(task.template, () => []).add(participant);
      }
    }

    AppLogger.debug('📊 构建推荐缓存完成', tag: 'TaskWizard');
  }

  /// 获取模板使用统计
  Future<Map<String, TemplateUsage>> _getTemplateUsageStats(String? userId) async {
    final stats = <String, TemplateUsage>{};
    
    if (_cachedTasks == null) return stats;

    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(Duration(days: 30));

    for (final task in _cachedTasks!) {
      final template = task.template;
      
      if (!stats.containsKey(template)) {
        stats[template] = TemplateUsage(
          template: template,
          totalCount: 0,
          recentCount: 0,
          lastUsed: task.createTime,
          averageDuration: 0,
        );
      }

      final usage = stats[template]!;
      usage.totalCount++;
      
      if (task.createTime.isAfter(thirtyDaysAgo)) {
        usage.recentCount++;
      }
      
      if (task.createTime.isAfter(usage.lastUsed)) {
        usage.lastUsed = task.createTime;
      }
    }

    return stats;
  }

  /// 计算模板推荐置信度
  double _calculateTemplateConfidence(String template, TemplateUsage usage) {
    double confidence = 0.0;
    
    // 基于使用频率 (40%)
    confidence += (usage.recentCount / 10).clamp(0.0, 0.4);
    
    // 基于最近使用 (30%)
    final daysSinceLastUse = DateTime.now().difference(usage.lastUsed).inDays;
    confidence += (1.0 - (daysSinceLastUse / 30)).clamp(0.0, 0.3);
    
    // 基于总使用次数 (30%)
    confidence += (usage.totalCount / 50).clamp(0.0, 0.3);
    
    return confidence.clamp(0.0, 1.0);
  }

  /// 获取模板推荐原因
  String _getTemplateRecommendationReason(String template, TemplateUsage usage) {
    if (usage.recentCount > 5) {
      return '最近经常使用';
    } else if (usage.totalCount > 20) {
      return '历史使用频繁';
    } else if (DateTime.now().difference(usage.lastUsed).inDays < 7) {
      return '最近使用过';
    } else {
      return '推荐使用';
    }
  }

  /// 生成智能填充建议
  Future<SmartFillSuggestions> _generateSmartFillSuggestions(String template, String? userId) async {
    final suggestions = SmartFillSuggestions();
    
    if (_cachedTasks == null) return suggestions;

    // 基于模板的历史数据生成建议
    final templateTasks = _cachedTasks!.where((task) => task.template == template).toList();
    
    if (templateTasks.isNotEmpty) {
      // 最常用的产品代码
      final productCodes = <String, int>{};
      for (final task in templateTasks) {
        productCodes[task.productCode] = (productCodes[task.productCode] ?? 0) + 1;
      }
      suggestions.productCodes = productCodes.keys.toList()..sort((a, b) => productCodes[b]!.compareTo(productCodes[a]!));
      
      // 常用数量
      final quantities = templateTasks.map((task) => task.quantity).toList();
      if (quantities.isNotEmpty) {
        suggestions.suggestedQuantity = quantities.reduce((a, b) => a + b) ~/ quantities.length;
      }
      
      // 常用参与者
      final participants = <String>{};
      for (final task in templateTasks) {
        participants.addAll(task.participants);
      }
      suggestions.participants = participants.toList();
    }

    return suggestions;
  }

  /// 验证字段
  Future<ValidationResult> _validateField(String template, String field, dynamic value) async {
    switch (field) {
      case 'productCode':
        return _validateProductCode(value as String);
      case 'batchNumber':
        return _validateBatchNumber(value as String);
      case 'quantity':
        return _validateQuantity(template, value as int);
      default:
        return ValidationResult.success();
    }
  }

  ValidationResult _validateProductCode(String productCode) {
    if (productCode.isEmpty) {
      return ValidationResult.error('产品代码不能为空');
    }
    
    final regex = RegExp(r'^[A-Z]{2}-\d{4}[A-Z]?$');
    if (!regex.hasMatch(productCode)) {
      return ValidationResult.error('产品代码格式不正确');
    }
    
    return ValidationResult.success();
  }

  ValidationResult _validateBatchNumber(String batchNumber) {
    if (batchNumber.isEmpty) {
      return ValidationResult.error('批次号不能为空');
    }
    
    if (batchNumber.length < 3) {
      return ValidationResult.error('批次号长度至少3个字符');
    }
    
    return ValidationResult.success();
  }

  ValidationResult _validateQuantity(String template, int quantity) {
    if (quantity <= 0) {
      return ValidationResult.error('数量必须大于0');
    }
    
    // 基于模板的数量限制
    final maxQuantity = _getMaxQuantityForTemplate(template);
    if (quantity > maxQuantity) {
      return ValidationResult.error('$template 最大数量不能超过 $maxQuantity');
    }
    
    return ValidationResult.success();
  }

  int _getMaxQuantityForTemplate(String template) {
    switch (template) {
      case '集装箱':
        return 5000;
      case '平板车':
        return 2000;
      default:
        return 10000;
    }
  }

  List<String> _getProductCodeSuggestions(String query) {
    if (_productBatchHistory == null) return [];
    
    return _productBatchHistory!.keys
        .where((code) => code.toLowerCase().contains(query.toLowerCase()))
        .take(10)
        .toList();
  }

  List<String> _getBatchNumberSuggestions(String? productCode, String query) {
    if (_productBatchHistory == null || productCode == null) return [];
    
    final batches = _productBatchHistory![productCode] ?? [];
    return batches
        .where((batch) => batch.toLowerCase().contains(query.toLowerCase()))
        .take(10)
        .toList();
  }

  List<String> _getParticipantSuggestions(String query) {
    if (_participantHistory == null) return [];
    
    final allParticipants = <String>{};
    for (final participants in _participantHistory!.values) {
      allParticipants.addAll(participants);
    }
    
    return allParticipants
        .where((participant) => participant.toLowerCase().contains(query.toLowerCase()))
        .take(10)
        .toList();
  }

  Future<ValidationResult> _validateCompleteData(Map<String, dynamic> data) async {
    final requiredFields = ['template', 'productCode', 'batchNumber', 'quantity'];
    
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        return ValidationResult.error('缺少必填字段: $field');
      }
    }
    
    return ValidationResult.success();
  }

  Future<void> _recordWizardUsage(WizardProgress progress) async {
    final duration = DateTime.now().difference(progress.startTime);
    AppLogger.info('📊 向导使用统计: 耗时${duration.inSeconds}秒', tag: 'TaskWizard');
  }
}

// 数据模型类定义...
class WizardProgress {
  final String wizardId;
  final String? userId;
  final DateTime startTime;
  WizardStep currentStep;
  final Map<String, dynamic> data;
  DateTime lastUpdateTime;
  DateTime? lastSaveTime;

  WizardProgress({
    required this.wizardId,
    this.userId,
    required this.startTime,
    required this.currentStep,
    required this.data,
  }) : lastUpdateTime = DateTime.now();
}

enum WizardStep {
  templateSelection,
  dataInput,
  validation,
  completion,
}

class TemplateRecommendation {
  final String template;
  final double confidence;
  final String reason;
  final int recentUsage;
  final int totalUsage;
  final DateTime lastUsed;
  final int estimatedDuration;

  TemplateRecommendation({
    required this.template,
    required this.confidence,
    required this.reason,
    required this.recentUsage,
    required this.totalUsage,
    required this.lastUsed,
    required this.estimatedDuration,
  });
}

class TemplateUsage {
  final String template;
  int totalCount;
  int recentCount;
  DateTime lastUsed;
  int averageDuration;

  TemplateUsage({
    required this.template,
    required this.totalCount,
    required this.recentCount,
    required this.lastUsed,
    required this.averageDuration,
  });
}

class SmartFillSuggestions {
  List<String> productCodes = [];
  int? suggestedQuantity;
  List<String> participants = [];
}

class ValidationResult {
  final bool isValid;
  final String message;

  ValidationResult._(this.isValid, this.message);

  factory ValidationResult.success() => ValidationResult._(true, '');
  factory ValidationResult.error(String message) => ValidationResult._(false, message);
}

class TaskCreationData {
  final String template;
  final String productCode;
  final String batchNumber;
  final int quantity;
  final List<String> participants;

  TaskCreationData({
    required this.template,
    required this.productCode,
    required this.batchNumber,
    required this.quantity,
    required this.participants,
  });
}

class TaskCreationResult {
  final bool isSuccess;
  final TaskCreationData? data;
  final String? error;

  TaskCreationResult._(this.isSuccess, this.data, this.error);

  factory TaskCreationResult.success(TaskCreationData data) => TaskCreationResult._(true, data, null);
  factory TaskCreationResult.failure(String error) => TaskCreationResult._(false, null, error);
}
