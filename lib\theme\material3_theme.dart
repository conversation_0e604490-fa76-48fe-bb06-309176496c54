import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Material 3 主题配置类
/// 提供完整的 Material Design 3 主题系统
class Material3Theme {
  // 私有构造函数，防止实例化
  Material3Theme._();

  // ==================== 颜色系统 ====================
  
  /// 亮色主题色彩方案
  static const ColorScheme _lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    // 主色系
    primary: Color(0xFF6750A4),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFEADDFF),
    onPrimaryContainer: Color(0xFF21005D),
    
    // 次要色系
    secondary: Color(0xFF625B71),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFE8DEF8),
    onSecondaryContainer: Color(0xFF1D192B),
    
    // 第三色系
    tertiary: Color(0xFF7D5260),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFFFD8E4),
    onTertiaryContainer: Color(0xFF31111D),
    
    // 错误色系
    error: Color(0xFFBA1A1A),
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    
    // 背景色系
    background: Color(0xFFFFFBFE),
    onBackground: Color(0xFF1C1B1F),
    
    // 表面色系
    surface: Color(0xFFFFFBFE),
    onSurface: Color(0xFF1C1B1F),
    surfaceVariant: Color(0xFFE7E0EC),
    onSurfaceVariant: Color(0xFF49454F),
    
    // 轮廓色系
    outline: Color(0xFF79747E),
    outlineVariant: Color(0xFFCAC4D0),
    
    // 阴影和覆盖
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    
    // 反色表面
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFFD0BCFF),
  );

  /// 暗色主题色彩方案
  static const ColorScheme _darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    // 主色系
    primary: Color(0xFFD0BCFF),
    onPrimary: Color(0xFF381E72),
    primaryContainer: Color(0xFF4F378B),
    onPrimaryContainer: Color(0xFFEADDFF),
    
    // 次要色系
    secondary: Color(0xFFCCC2DC),
    onSecondary: Color(0xFF332D41),
    secondaryContainer: Color(0xFF4A4458),
    onSecondaryContainer: Color(0xFFE8DEF8),
    
    // 第三色系
    tertiary: Color(0xFFEFB8C8),
    onTertiary: Color(0xFF492532),
    tertiaryContainer: Color(0xFF633B48),
    onTertiaryContainer: Color(0xFFFFD8E4),
    
    // 错误色系
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    
    // 背景色系
    background: Color(0xFF1C1B1F),
    onBackground: Color(0xFFE6E1E5),
    
    // 表面色系
    surface: Color(0xFF1C1B1F),
    onSurface: Color(0xFFE6E1E5),
    surfaceVariant: Color(0xFF49454F),
    onSurfaceVariant: Color(0xFFCAC4D0),
    
    // 轮廓色系
    outline: Color(0xFF938F99),
    outlineVariant: Color(0xFF49454F),
    
    // 阴影和覆盖
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    
    // 反色表面
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF6750A4),
  );

  // ==================== 字体系统 ====================
  
  /// Material 3 字体主题
  static const TextTheme _textTheme = TextTheme(
    // Display 系列 - 大标题
    displayLarge: TextStyle(
      fontSize: 57,
      fontWeight: FontWeight.w400,
      letterSpacing: -0.25,
      height: 1.12,
    ),
    displayMedium: TextStyle(
      fontSize: 45,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.16,
    ),
    displaySmall: TextStyle(
      fontSize: 36,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.22,
    ),
    
    // Headline 系列 - 标题
    headlineLarge: TextStyle(
      fontSize: 32,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.25,
    ),
    headlineMedium: TextStyle(
      fontSize: 28,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.29,
    ),
    headlineSmall: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.33,
    ),
    
    // Title 系列 - 子标题
    titleLarge: TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.w400,
      letterSpacing: 0,
      height: 1.27,
    ),
    titleMedium: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.15,
      height: 1.50,
    ),
    titleSmall: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    
    // Label 系列 - 标签
    labelLarge: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.1,
      height: 1.43,
    ),
    labelMedium: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.33,
    ),
    labelSmall: TextStyle(
      fontSize: 11,
      fontWeight: FontWeight.w500,
      letterSpacing: 0.5,
      height: 1.45,
    ),
    
    // Body 系列 - 正文
    bodyLarge: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.5,
      height: 1.50,
    ),
    bodyMedium: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.25,
      height: 1.43,
    ),
    bodySmall: TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.w400,
      letterSpacing: 0.4,
      height: 1.33,
    ),
  );

  // ==================== 形状系统 ====================
  
  /// Material 3 形状主题
  static const OutlinedBorder _smallShape = RoundedRectangleBorder(
    borderRadius: BorderRadius.all(Radius.circular(8.0)),
  );
  
  static const OutlinedBorder _mediumShape = RoundedRectangleBorder(
    borderRadius: BorderRadius.all(Radius.circular(12.0)),
  );
  
  static const OutlinedBorder _largeShape = RoundedRectangleBorder(
    borderRadius: BorderRadius.all(Radius.circular(16.0)),
  );

  // ==================== 动画配置 ====================
  
  /// Material 3 动画持续时间
  static const Duration shortDuration = Duration(milliseconds: 100);
  static const Duration mediumDuration = Duration(milliseconds: 200);
  static const Duration longDuration = Duration(milliseconds: 300);
  static const Duration extraLongDuration = Duration(milliseconds: 500);

  /// Material 3 缓动曲线
  static const Curve standardCurve = Curves.easeInOut;
  static const Curve decelerateCurve = Curves.easeOut;
  static const Curve accelerateCurve = Curves.easeIn;
  static const Curve emphasizedCurve = Curves.easeInOutCubic;

  // ==================== 主题构建器 ====================
  
  /// 获取亮色主题
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: _lightColorScheme,
      textTheme: _textTheme,
      
      // AppBar 主题
      appBarTheme: AppBarTheme(
        backgroundColor: _lightColorScheme.surface,
        foregroundColor: _lightColorScheme.onSurface,
        elevation: 0,
        scrolledUnderElevation: 3,
        shadowColor: _lightColorScheme.shadow,
        surfaceTintColor: _lightColorScheme.surfaceTint,
        titleTextStyle: _textTheme.titleLarge?.copyWith(
          color: _lightColorScheme.onSurface,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      
      // 卡片主题
    cardTheme: CardThemeData(
        color: _lightColorScheme.surface,
        shadowColor: _lightColorScheme.shadow,
        surfaceTintColor: _lightColorScheme.surfaceTint,
        elevation: 1,
        shape: _mediumShape,
        margin: const EdgeInsets.all(8.0),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _lightColorScheme.primary,
          foregroundColor: _lightColorScheme.onPrimary,
          disabledBackgroundColor: _lightColorScheme.onSurface.withOpacity(0.12),
          disabledForegroundColor: _lightColorScheme.onSurface.withOpacity(0.38),
          shadowColor: _lightColorScheme.shadow,
          surfaceTintColor: _lightColorScheme.surfaceTint,
          elevation: 1,
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: _lightColorScheme.primary,
          foregroundColor: _lightColorScheme.onPrimary,
          disabledBackgroundColor: _lightColorScheme.onSurface.withOpacity(0.12),
          disabledForegroundColor: _lightColorScheme.onSurface.withOpacity(0.38),
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _lightColorScheme.primary,
          disabledForegroundColor: _lightColorScheme.onSurface.withOpacity(0.38),
          side: BorderSide(color: _lightColorScheme.outline),
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _lightColorScheme.primary,
          disabledForegroundColor: _lightColorScheme.onSurface.withOpacity(0.38),
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: _lightColorScheme.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _lightColorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _lightColorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _lightColorScheme.primary, width: 2.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _lightColorScheme.error),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        labelStyle: _textTheme.bodyLarge?.copyWith(
          color: _lightColorScheme.onSurfaceVariant,
        ),
        hintStyle: _textTheme.bodyLarge?.copyWith(
          color: _lightColorScheme.onSurfaceVariant,
        ),
      ),
      
      // 进度指示器主题
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: _lightColorScheme.primary,
        linearTrackColor: _lightColorScheme.surfaceVariant,
        circularTrackColor: _lightColorScheme.surfaceVariant,
      ),
      
      // 分割线主题
      dividerTheme: DividerThemeData(
        color: _lightColorScheme.outlineVariant,
        thickness: 1.0,
        space: 1.0,
      ),
      
      // 页面转换主题
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: PredictiveBackPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }

  /// 获取暗色主题
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: _darkColorScheme,
      textTheme: _textTheme,
      
      // AppBar 主题
      appBarTheme: AppBarTheme(
        backgroundColor: _darkColorScheme.surface,
        foregroundColor: _darkColorScheme.onSurface,
        elevation: 0,
        scrolledUnderElevation: 3,
        shadowColor: _darkColorScheme.shadow,
        surfaceTintColor: _darkColorScheme.surfaceTint,
        titleTextStyle: _textTheme.titleLarge?.copyWith(
          color: _darkColorScheme.onSurface,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      
      // 卡片主题
      cardTheme: CardThemeData(
        color: _darkColorScheme.surface,
        shadowColor: _darkColorScheme.shadow,
        surfaceTintColor: _darkColorScheme.surfaceTint,
        elevation: 1,
        shape: _mediumShape,
        margin: const EdgeInsets.all(8.0),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: _darkColorScheme.primary,
          foregroundColor: _darkColorScheme.onPrimary,
          disabledBackgroundColor: _darkColorScheme.onSurface.withOpacity(0.12),
          disabledForegroundColor: _darkColorScheme.onSurface.withOpacity(0.38),
          shadowColor: _darkColorScheme.shadow,
          surfaceTintColor: _darkColorScheme.surfaceTint,
          elevation: 1,
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: _darkColorScheme.primary,
          foregroundColor: _darkColorScheme.onPrimary,
          disabledBackgroundColor: _darkColorScheme.onSurface.withOpacity(0.12),
          disabledForegroundColor: _darkColorScheme.onSurface.withOpacity(0.38),
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _darkColorScheme.primary,
          disabledForegroundColor: _darkColorScheme.onSurface.withOpacity(0.38),
          side: BorderSide(color: _darkColorScheme.outline),
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _darkColorScheme.primary,
          disabledForegroundColor: _darkColorScheme.onSurface.withOpacity(0.38),
          shape: _smallShape,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          textStyle: _textTheme.labelLarge,
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: _darkColorScheme.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _darkColorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _darkColorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _darkColorScheme.primary, width: 2.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(4.0)),
          borderSide: BorderSide(color: _darkColorScheme.error),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        labelStyle: _textTheme.bodyLarge?.copyWith(
          color: _darkColorScheme.onSurfaceVariant,
        ),
        hintStyle: _textTheme.bodyLarge?.copyWith(
          color: _darkColorScheme.onSurfaceVariant,
        ),
      ),
      
      // 进度指示器主题
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: _darkColorScheme.primary,
        linearTrackColor: _darkColorScheme.surfaceVariant,
        circularTrackColor: _darkColorScheme.surfaceVariant,
      ),
      
      // 分割线主题
      dividerTheme: DividerThemeData(
        color: _darkColorScheme.outlineVariant,
        thickness: 1.0,
        space: 1.0,
      ),
      
      // 页面转换主题
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: PredictiveBackPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }

  // ==================== 工具方法 ====================
  
  /// 获取当前主题的颜色方案
  static ColorScheme getColorScheme(BuildContext context) {
    return Theme.of(context).colorScheme;
  }
  
  /// 获取当前主题的文本主题
  static TextTheme getTextTheme(BuildContext context) {
    return Theme.of(context).textTheme;
  }
  
  /// 判断是否为暗色主题
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }
  
  /// 获取表面色调
  static Color getSurfaceTint(BuildContext context) {
    return Theme.of(context).colorScheme.surfaceTint ?? 
           Theme.of(context).colorScheme.primary;
  }
  
  /// 获取阴影颜色
  static Color getShadowColor(BuildContext context) {
    return Theme.of(context).colorScheme.shadow;
  }
}

/// Material 3 设计令牌
class Material3Tokens {
  Material3Tokens._();
  
  // 间距系统
  static const double space4 = 4.0;
  static const double space8 = 8.0;
  static const double space12 = 12.0;
  static const double space16 = 16.0;
  static const double space20 = 20.0;
  static const double space24 = 24.0;
  static const double space32 = 32.0;
  static const double space40 = 40.0;
  static const double space48 = 48.0;
  static const double space56 = 56.0;
  static const double space64 = 64.0;
  
  // 圆角系统
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusXXL = 28.0;
  
  // 阴影系统
  static const double elevation0 = 0.0;
  static const double elevation1 = 1.0;
  static const double elevation2 = 3.0;
  static const double elevation3 = 6.0;
  static const double elevation4 = 8.0;
  static const double elevation5 = 12.0;
  
  // 动画持续时间
  static const int durationShort1 = 50;
  static const int durationShort2 = 100;
  static const int durationShort3 = 150;
  static const int durationShort4 = 200;
  static const int durationMedium1 = 250;
  static const int durationMedium2 = 300;
  static const int durationMedium3 = 350;
  static const int durationMedium4 = 400;
  static const int durationLong1 = 450;
  static const int durationLong2 = 500;
  static const int durationLong3 = 550;
  static const int durationLong4 = 600;
  
  // 透明度系统
  static const double opacity08 = 0.08;
  static const double opacity8 = 0.08;
  static const double opacity12 = 0.12;
  static const double opacity16 = 0.16;
  static const double opacity38 = 0.38;
  static const double opacity87 = 0.87;
}