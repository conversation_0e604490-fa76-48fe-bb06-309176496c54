import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/data/datasources/hive_task_data_source.dart';
import 'package:loadguard/data/datasources/shared_preferences_task_data_source.dart';
import 'package:loadguard/data/datasources/task_data_source.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/hive_storage_service.dart';

void main() {
  group('🗄️ 任务数据源测试', () {
    late TaskDataSource hiveDataSource;
    late TaskDataSource spDataSource;
    
    setUpAll(() async {
      // 初始化HiveStorageService
      await HiveStorageService.initialize();

      hiveDataSource = HiveTaskDataSource();
      spDataSource = SharedPreferencesTaskDataSource();
    });
    
    group('HiveTaskDataSource', () {
      test('应该能够保存和读取任务', () async {
        // 创建测试任务
        final testTask = TaskModel(
          id: 'test-task-1',
          template: '平板车',
          createTime: DateTime.now(),
        );
        
        // 保存任务
        final saveResult = await hiveDataSource.saveTask(testTask);
        expect(saveResult, true);
        
        // 读取任务
        final retrievedTask = await hiveDataSource.getTask('test-task-1');
        expect(retrievedTask, isNotNull);
        expect(retrievedTask!.id, testTask.id);
        expect(retrievedTask.template, testTask.template);
        expect(retrievedTask.status, testTask.status);
        
        print('✅ Hive数据源保存和读取测试通过');
      });
      
      test('应该能够获取所有任务', () async {
        final allTasks = await hiveDataSource.getAllTasks();
        expect(allTasks, isA<List<TaskModel>>());
        expect(allTasks.length, greaterThanOrEqualTo(0));
        
        print('✅ Hive数据源获取所有任务测试通过 (${allTasks.length}个任务)');
      });
      
      test('应该能够删除任务', () async {
        // 先保存一个任务
        final testTask = TaskModel(
          id: 'test-task-delete',
          template: '平板车',
          createTime: DateTime.now(),
        );
        
        await hiveDataSource.saveTask(testTask);
        
        // 确认任务存在
        final savedTask = await hiveDataSource.getTask('test-task-delete');
        expect(savedTask, isNotNull);
        
        // 删除任务
        final deleteResult = await hiveDataSource.deleteTask('test-task-delete');
        expect(deleteResult, true);
        
        // 确认任务已删除
        final deletedTask = await hiveDataSource.getTask('test-task-delete');
        expect(deletedTask, isNull);
        
        print('✅ Hive数据源删除任务测试通过');
      });
      
      test('应该能够管理任务统计', () async {
        final testStats = {
          'totalTasks': 10,
          'completedTasks': 5,
          'lastUpdate': DateTime.now().toIso8601String(),
        };
        
        // 保存统计
        final saveResult = await hiveDataSource.saveTaskStats(testStats);
        expect(saveResult, true);
        
        // 读取统计
        final retrievedStats = await hiveDataSource.getTaskStats();
        expect(retrievedStats['totalTasks'], testStats['totalTasks']);
        expect(retrievedStats['completedTasks'], testStats['completedTasks']);
        
        print('✅ Hive数据源任务统计测试通过');
      });
      
      test('应该能够管理任务历史', () async {
        final testHistory = {
          'action': 'task_completed',
          'taskId': 'test-task-1',
          'timestamp': DateTime.now().toIso8601String(),
          'details': '任务完成',
        };
        
        // 添加历史记录
        final addResult = await hiveDataSource.addTaskHistory(testHistory);
        expect(addResult, true);
        
        // 读取历史记录
        final history = await hiveDataSource.getTaskHistory();
        expect(history, isA<List<Map<String, dynamic>>>());
        expect(history.length, greaterThanOrEqualTo(1));
        
        // 检查最新的历史记录
        final latestHistory = history.last;
        expect(latestHistory['action'], testHistory['action']);
        expect(latestHistory['taskId'], testHistory['taskId']);
        
        print('✅ Hive数据源任务历史测试通过 (${history.length}条记录)');
      });
      
      test('应该能够获取存储统计', () async {
        final stats = await hiveDataSource.getStorageStats();
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('tasksCount'), true);
        expect(stats.containsKey('historyCount'), true);
        expect(stats.containsKey('lastUpdate'), true);
        
        print('✅ Hive数据源存储统计测试通过: $stats');
      });
    });
    
    group('SharedPreferencesTaskDataSource', () {
      test('应该能够保存和读取任务', () async {
        // 创建测试任务
        final testTask = TaskModel(
          id: 'test-task-sp-1',
          template: '集装箱',
          createTime: DateTime.now(),
        );
        
        // 保存任务
        final saveResult = await spDataSource.saveTask(testTask);
        expect(saveResult, true);
        
        // 读取任务
        final retrievedTask = await spDataSource.getTask('test-task-sp-1');
        expect(retrievedTask, isNotNull);
        expect(retrievedTask!.id, testTask.id);
        expect(retrievedTask.template, testTask.template);
        expect(retrievedTask.status, testTask.status);
        
        print('✅ SharedPreferences数据源保存和读取测试通过');
      });
      
      test('应该能够获取存储统计', () async {
        final stats = await spDataSource.getStorageStats();
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('tasksCount'), true);
        expect(stats.containsKey('storageType'), true);
        expect(stats['storageType'], 'SharedPreferences');
        
        print('✅ SharedPreferences数据源存储统计测试通过: $stats');
      });
    });
    
    group('数据源接口一致性', () {
      test('两种数据源应该有相同的接口', () {
        expect(hiveDataSource, isA<TaskDataSource>());
        expect(spDataSource, isA<TaskDataSource>());
        
        print('✅ 数据源接口一致性测试通过');
      });
      
      test('应该能够在不同数据源间切换', () async {
        // 创建测试任务
        final testTask = TaskModel(
          id: 'test-task-switch',
          template: '平板车',
          createTime: DateTime.now(),
        );
        
        // 在Hive中保存
        await hiveDataSource.saveTask(testTask);
        final hiveTask = await hiveDataSource.getTask('test-task-switch');
        expect(hiveTask, isNotNull);
        
        // 在SharedPreferences中保存
        await spDataSource.saveTask(testTask);
        final spTask = await spDataSource.getTask('test-task-switch');
        expect(spTask, isNotNull);
        
        // 验证数据一致性
        expect(hiveTask!.id, spTask!.id);
        expect(hiveTask.template, spTask.template);
        
        print('✅ 数据源切换测试通过');
      });
    });
    
    group('性能测试', () {
      test('批量操作性能', () async {
        final stopwatch = Stopwatch()..start();
        
        // 创建100个测试任务
        final tasks = List.generate(100, (index) => TaskModel(
          id: 'perf-test-$index',
          template: index % 2 == 0 ? '平板车' : '集装箱',
          createTime: DateTime.now(),
        ));
        
        // 批量保存到Hive
        await hiveDataSource.saveAllTasks(tasks);
        
        stopwatch.stop();
        final saveTime = stopwatch.elapsedMilliseconds;
        
        // 读取性能测试
        stopwatch.reset();
        stopwatch.start();
        
        final retrievedTasks = await hiveDataSource.getAllTasks();
        
        stopwatch.stop();
        final loadTime = stopwatch.elapsedMilliseconds;
        
        expect(retrievedTasks.length, greaterThanOrEqualTo(100));
        expect(saveTime, lessThan(5000)); // 保存应该在5秒内完成
        expect(loadTime, lessThan(1000)); // 读取应该在1秒内完成
        
        print('✅ 性能测试通过: 保存${saveTime}ms, 读取${loadTime}ms');
      });
    });
  });
}
