# Flutter项目优化设计文档

## 概述

本设计文档基于对现有LoadGuard Flutter项目的全面代码审查，针对发现的关键问题制定系统性的优化方案。重点解决数据一致性问题、工作量统计功能缺陷、架构优化和性能提升，同时保持本地应用特性和现有业务逻辑的完整性。

**特别强调**：保留ML Kit Text Recognition V2 0.15.0的核心识别能力，对现有12种识别算法进行评估优化，采用智能调用策略，确保不降低准确率和识别速度。

## 架构设计

### 1. 整体架构优化

#### 1.1 当前架构问题
- TaskService职责过重（1095行代码），混合了数据管理、状态管理、业务逻辑和持久化
- 数据一致性问题：`_currentTask`、`_tasks`列表和持久化存储之间的同步复杂且容易出错
- 状态管理混乱：混合使用ChangeNotifier和Riverpod
- 双重存储机制（Hive + SharedPreferences）增加复杂性
- **硬编码数据问题**：人员名单（88人）、小组信息、仓库信息、模板配置等大量静态数据硬编码在代码中，难以维护和更新

#### 1.2 目标架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                        UI Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Task Pages    │  │ Workload Pages  │  │ Report Pages │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    State Management Layer                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ TaskNotifier    │  │WorkloadNotifier │  │ ReportNotifier│ │
│  │   (Riverpod)    │  │   (Riverpod)    │  │   (Riverpod) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  TaskService    │  │ WorkloadService │  │ ReportService│ │
│  │  (Pure Logic)   │  │  (Pure Logic)   │  │ (Pure Logic) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Repository Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ TaskRepository  │  │WorkloadRepository│ │ ConfigRepo   │ │
│  │   (Interface)   │  │   (Interface)   │  │ (Interface)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 API Abstraction Layer (预留)                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   TaskApiClient │  │ WorkloadApiClient│ │ ConfigApiClient│ │
│  │   (Interface)   │  │   (Interface)   │  │ (Interface)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Data Access Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ HiveDataSource  │  │ SharedPrefSource│  │RemoteDataSource│ │
│  │   (Primary)     │  │   (Backup)      │  │  (预留)      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Network & Sync Layer (预留)                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ HttpClient      │  │ WebSocketClient │  │ SyncManager  │ │
│  │ (REST API)      │  │ (实时通信)      │  │ (数据同步)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. ML Kit V2识别系统优化设计

#### 2.1 当前识别算法分析

基于代码审查，发现系统中存在以下12种识别算法和策略：

1. **ML Kit核心OCR识别** - 基于Google ML Kit Text Recognition V2 0.15.0
2. **字符标准化算法** - `_normalizeCharacters()` 处理O/0、I/1混淆
3. **批号主串提取算法** - `_extractCoreBatchNumber()` 支持后缀处理
4. **产品代码匹配算法** - 基于正则表达式的产品代码识别
5. **逐行匹配策略** - `line_match` 最严格匹配，置信度0.95
6. **全文匹配策略** - `full_text_match` 宽松匹配，置信度0.85
7. **模糊匹配策略** - `fuzzy_match` 容错匹配，置信度0.75
8. **智能后处理算法** - `performIntelligentPostProcessing()` 纠错处理
9. **多批次匹配算法** - `tryMatchMultipleBatches()` 混装任务支持
10. **文本清理算法** - `_cleanRecognitionText()` 过滤乱码
11. **置信度计算算法** - 基于匹配策略的动态置信度评估
12. **图像质量评估算法** - 根据图像特征选择识别策略

#### 2.2 算法评估和优化方案

**保留的核心算法（高价值，继续优化）**：

```dart
// 1. ML Kit核心OCR - 保留并优化
class EnhancedMLKitRecognizer {
  final TextRecognizer _textRecognizer;
  
  // 保持ML Kit V2 0.15.0核心能力
  Future<RecognizedText> recognizeText(InputImage image) async {
    return await _textRecognizer.processImage(image);
  }
  
  // 新增：图像质量评估
  ImageQuality assessImageQuality(InputImage image) {
    // 评估亮度、对比度、清晰度
    return ImageQuality.fromMetrics(
      brightness: _calculateBrightness(image),
      contrast: _calculateContrast(image),
      sharpness: _calculateSharpness(image),
    );
  }
}

// 2. 智能策略选择器 - 新增核心组件
class IntelligentRecognitionStrategy {
  static RecognitionStrategy selectOptimalStrategy(
    ImageQuality quality,
    RecognitionContext context,
  ) {
    // 根据图像质量智能选择策略
    if (quality.isHighQuality) {
      return RecognitionStrategy.precise; // 使用逐行匹配
    } else if (quality.isMediumQuality) {
      return RecognitionStrategy.balanced; // 使用全文匹配
    } else {
      return RecognitionStrategy.tolerant; // 使用模糊匹配
    }
  }
}

// 3. 优化的字符标准化 - 保留并简化
class OptimizedCharacterNormalizer {
  static String normalize(String text, NormalizationConfig config) {
    String normalized = text.toUpperCase().trim();
    
    // 简化的字符映射，基于统计数据优化
    final mappings = config.getOptimizedMappings();
    for (final mapping in mappings) {
      normalized = normalized.replaceAll(mapping.from, mapping.to);
    }
    
    return normalized;
  }
}
```

**优化升级的算法**：

```dart
// 4. 增强的批号提取 - 升级版本
class EnhancedBatchExtractor {
  static String? extractBatchNumber(String text, List<BatchPattern> patterns) {
    // 支持多种批号格式，动态配置
    for (final pattern in patterns) {
      final match = pattern.regex.firstMatch(text);
      if (match != null && _validateBatchNumber(match.group(0)!)) {
        return match.group(0)!;
      }
    }
    return null;
  }
  
  static bool _validateBatchNumber(String batch) {
    // 增强的验证逻辑，包括日期合理性检查
    if (batch.length != 12) return false;
    
    final year = int.tryParse(batch.substring(0, 2)) ?? 0;
    final month = int.tryParse(batch.substring(2, 4)) ?? 0;
    final day = int.tryParse(batch.substring(4, 6)) ?? 0;
    
    return year >= 20 && year <= 35 && 
           month >= 1 && month <= 12 && 
           day >= 1 && day <= 31;
  }
}

// 5. 智能匹配引擎 - 整合多种策略
class SmartMatchingEngine {
  final List<MatchingStrategy> strategies;
  
  Future<MatchResult> performMatching(
    String ocrText,
    MatchingContext context,
  ) async {
    final results = <MatchResult>[];
    
    // 并行执行多种策略
    await Future.wait(strategies.map((strategy) async {
      final result = await strategy.match(ocrText, context);
      if (result.isValid) {
        results.add(result);
      }
    }));
    
    // 选择最佳结果
    return _selectBestResult(results);
  }
  
  MatchResult _selectBestResult(List<MatchResult> results) {
    if (results.isEmpty) return MatchResult.failed();
    
    // 按置信度和策略优先级排序
    results.sort((a, b) {
      final confidenceCompare = b.confidence.compareTo(a.confidence);
      if (confidenceCompare != 0) return confidenceCompare;
      
      return b.strategyPriority.compareTo(a.strategyPriority);
    });
    
    return results.first;
  }
}
```

**移除或简化的算法**：

```dart
// 移除过度复杂的字符映射逻辑
// 原有的 _digitToLetter 方法过于硬编码，替换为配置驱动

// 简化文本清理算法
class SimplifiedTextCleaner {
  static String clean(String text) {
    // 保留基本清理，移除过度复杂的正则表达式
    return text
        .replaceAll(RegExp(r'[^\w\s\-:/.()]'), '')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
  }
}
```

#### 2.3 智能调用架构

```dart
// 智能识别协调器
class IntelligentRecognitionCoordinator {
  final EnhancedMLKitRecognizer _mlkitRecognizer;
  final SmartMatchingEngine _matchingEngine;
  final ImageQualityAssessor _qualityAssessor;
  
  Future<RecognitionResult> recognizeWithIntelligentStrategy(
    String imagePath, {
    RecognitionContext? context,
    Function(double, String)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1, '正在评估图像质量...');
      
      // 1. 图像质量评估
      final image = InputImage.fromFilePath(imagePath);
      final quality = await _qualityAssessor.assess(image);
      
      onProgress?.call(0.2, '选择最优识别策略...');
      
      // 2. 策略选择
      final strategy = IntelligentRecognitionStrategy.selectOptimalStrategy(
        quality, 
        context ?? RecognitionContext.defaultContext(),
      );
      
      onProgress?.call(0.3, '执行ML Kit识别...');
      
      // 3. ML Kit核心识别（保持V2 0.15.0能力）
      final recognizedText = await _mlkitRecognizer.recognizeText(image);
      
      onProgress?.call(0.7, '执行智能匹配...');
      
      // 4. 智能匹配处理
      final matchResult = await _matchingEngine.performMatching(
        recognizedText.text,
        context?.toMatchingContext() ?? MatchingContext.defaultContext(),
      );
      
      onProgress?.call(0.9, '生成最终结果...');
      
      // 5. 结果整合
      return RecognitionResult(
        ocrText: recognizedText.text,
        matchResult: matchResult,
        confidence: matchResult.confidence,
        strategy: strategy.name,
        processingTime: DateTime.now().difference(startTime),
        imageQuality: quality,
      );
      
    } catch (e) {
      // 降级策略：如果智能识别失败，回退到基础ML Kit识别
      return await _fallbackRecognition(imagePath, onProgress);
    }
  }
  
  Future<RecognitionResult> _fallbackRecognition(
    String imagePath,
    Function(double, String)? onProgress,
  ) async {
    onProgress?.call(0.5, '使用基础识别模式...');
    
    final image = InputImage.fromFilePath(imagePath);
    final recognizedText = await _mlkitRecognizer.recognizeText(image);
    
    return RecognitionResult(
      ocrText: recognizedText.text,
      confidence: 0.6, // 基础置信度
      strategy: 'fallback',
      isFallback: true,
    );
  }
}
```

### 3. 本地应用API预留和服务器对接架构

#### 3.1 API抽象层设计

为未来的服务器部署和设备对接预留完整的API接口：

```dart
// 统一的API客户端接口
abstract class ApiClient {
  Future<ApiResponse<T>> get<T>(String endpoint, {Map<String, dynamic>? params});
  Future<ApiResponse<T>> post<T>(String endpoint, {Map<String, dynamic>? data});
  Future<ApiResponse<T>> put<T>(String endpoint, {Map<String, dynamic>? data});
  Future<ApiResponse<T>> delete<T>(String endpoint);
  
  // 文件上传（图像、PDF等）
  Future<ApiResponse<String>> uploadFile(String endpoint, File file);
  
  // 批量操作
  Future<ApiResponse<List<T>>> batchOperation<T>(String endpoint, List<Map<String, dynamic>> operations);
}

// 任务管理API接口
abstract class TaskApiClient {
  // 任务CRUD操作
  Future<ApiResponse<TaskModel>> createTask(CreateTaskRequest request);
  Future<ApiResponse<TaskModel>> getTask(String taskId);
  Future<ApiResponse<List<TaskModel>>> getTasks({TaskFilter? filter});
  Future<ApiResponse<TaskModel>> updateTask(String taskId, UpdateTaskRequest request);
  Future<ApiResponse<void>> deleteTask(String taskId);
  
  // 任务状态管理
  Future<ApiResponse<TaskModel>> startTask(String taskId);
  Future<ApiResponse<TaskModel>> completeTask(String taskId, CompleteTaskRequest request);
  Future<ApiResponse<TaskModel>> pauseTask(String taskId);
  
  // 图像识别相关
  Future<ApiResponse<RecognitionResult>> submitRecognition(String taskId, String photoId, File imageFile);
  Future<ApiResponse<List<RecognitionResult>>> getRecognitionHistory(String taskId);
  
  // 批量同步
  Future<ApiResponse<SyncResult>> syncTasks(List<TaskModel> localTasks);
}

// 工作量管理API接口
abstract class WorkloadApiClient {
  // 工作量分配
  Future<ApiResponse<WorkloadAssignment>> createAssignment(CreateAssignmentRequest request);
  Future<ApiResponse<List<WorkloadAssignment>>> getAssignments({AssignmentFilter? filter});
  Future<ApiResponse<WorkloadAssignment>> updateAssignment(String assignmentId, UpdateAssignmentRequest request);
  
  // 工作量统计
  Future<ApiResponse<WorkloadStatistics>> getWorkerStatistics(String workerId, {DateRange? dateRange});
  Future<ApiResponse<List<WorkloadStatistics>>> getTeamStatistics(String teamId, {DateRange? dateRange});
  Future<ApiResponse<WorkloadSummary>> getWorkloadSummary({DateRange? dateRange});
  
  // 报告生成
  Future<ApiResponse<String>> generateWorkloadReport(ReportRequest request);
}

// 配置管理API接口
abstract class ConfigApiClient {
  // 人员管理
  Future<ApiResponse<List<WorkerConfig>>> getWorkers({WorkerFilter? filter});
  Future<ApiResponse<WorkerConfig>> createWorker(CreateWorkerRequest request);
  Future<ApiResponse<WorkerConfig>> updateWorker(String workerId, UpdateWorkerRequest request);
  Future<ApiResponse<void>> deleteWorker(String workerId);
  
  // 模板管理
  Future<ApiResponse<List<TemplateConfig>>> getTemplates();
  Future<ApiResponse<TemplateConfig>> getTemplate(String templateId);
  Future<ApiResponse<TemplateConfig>> createTemplate(CreateTemplateRequest request);
  Future<ApiResponse<TemplateConfig>> updateTemplate(String templateId, UpdateTemplateRequest request);
  
  // 配置同步
  Future<ApiResponse<ConfigSyncResult>> syncConfigurations();
}
```

#### 3.2 本地优先的数据同步策略

```dart
// 混合模式Repository - 本地优先，服务器同步
class HybridTaskRepository implements TaskRepository {
  final LocalTaskDataSource _localDataSource;
  final RemoteTaskDataSource _remoteDataSource;
  final SyncManager _syncManager;
  final ConnectivityService _connectivity;
  
  @override
  Future<TaskModel?> getTask(String id) async {
    // 1. 优先从本地获取
    final localTask = await _localDataSource.getTask(id);
    if (localTask != null) {
      // 2. 后台异步同步（如果有网络）
      if (_connectivity.isConnected) {
        unawaited(_syncTaskInBackground(id));
      }
      return localTask;
    }
    
    // 3. 本地没有且有网络时，从服务器获取
    if (_connectivity.isConnected) {
      try {
        final remoteTask = await _remoteDataSource.getTask(id);
        if (remoteTask != null) {
          // 保存到本地
          await _localDataSource.saveTask(remoteTask);
          return remoteTask;
        }
      } catch (e) {
        AppLogger.warning('从服务器获取任务失败，使用本地数据: $e');
      }
    }
    
    return null;
  }
  
  @override
  Future<void> saveTask(TaskModel task) async {
    // 1. 立即保存到本地
    await _localDataSource.saveTask(task);
    
    // 2. 标记为需要同步
    await _syncManager.markForSync(task.id, SyncOperation.update);
    
    // 3. 如果有网络，尝试立即同步
    if (_connectivity.isConnected) {
      unawaited(_syncTaskToServer(task));
    }
  }
  
  Future<void> _syncTaskToServer(TaskModel task) async {
    try {
      await _remoteDataSource.saveTask(task);
      await _syncManager.markSynced(task.id);
    } catch (e) {
      AppLogger.warning('任务同步到服务器失败: $e');
      // 保持在同步队列中，稍后重试
    }
  }
}

// 数据同步管理器
class SyncManager {
  final SyncQueueStorage _queueStorage;
  final ConnectivityService _connectivity;
  
  // 启动后台同步
  Future<void> startBackgroundSync() async {
    Timer.periodic(const Duration(minutes: 5), (timer) async {
      if (_connectivity.isConnected) {
        await _processSyncQueue();
      }
    });
  }
  
  // 处理同步队列
  Future<void> _processSyncQueue() async {
    final pendingItems = await _queueStorage.getPendingItems();
    
    for (final item in pendingItems) {
      try {
        await _syncItem(item);
        await _queueStorage.markCompleted(item.id);
      } catch (e) {
        await _queueStorage.incrementRetryCount(item.id);
        if (item.retryCount >= 3) {
          await _queueStorage.markFailed(item.id);
        }
      }
    }
  }
  
  // 冲突解决策略
  Future<TaskModel> resolveConflict(TaskModel localTask, TaskModel remoteTask) async {
    // 策略1: 最后修改时间优先
    if (localTask.updatedAt.isAfter(remoteTask.updatedAt)) {
      return localTask;
    } else if (remoteTask.updatedAt.isAfter(localTask.updatedAt)) {
      return remoteTask;
    }
    
    // 策略2: 本地优先（用户正在操作的设备）
    return localTask;
  }
}
```

#### 3.3 设备间协作接口

```dart
// 设备发现和通信
abstract class DeviceDiscoveryService {
  Stream<List<DiscoveredDevice>> discoverDevices();
  Future<bool> connectToDevice(String deviceId);
  Future<void> disconnectFromDevice(String deviceId);
}

// 设备间数据共享
abstract class DeviceDataSharingService {
  // 任务分发
  Future<void> shareTask(String deviceId, TaskModel task);
  Future<void> requestTaskUpdate(String deviceId, String taskId);
  
  // 工作量协调
  Future<void> shareWorkloadAssignment(String deviceId, WorkloadAssignment assignment);
  
  // 配置同步
  Future<void> syncConfigurationToDevice(String deviceId, ConfigurationData config);
}

// WebSocket实时通信（预留）
class RealtimeCommunicationService {
  WebSocketChannel? _channel;
  
  Future<void> connect(String serverUrl) async {
    _channel = WebSocketChannel.connect(Uri.parse(serverUrl));
    
    _channel!.stream.listen((message) {
      _handleRealtimeMessage(jsonDecode(message));
    });
  }
  
  void _handleRealtimeMessage(Map<String, dynamic> message) {
    switch (message['type']) {
      case 'task_update':
        _handleTaskUpdate(message['data']);
        break;
      case 'workload_change':
        _handleWorkloadChange(message['data']);
        break;
      case 'config_update':
        _handleConfigUpdate(message['data']);
        break;
    }
  }
  
  Future<void> sendTaskUpdate(TaskModel task) async {
    if (_channel != null) {
      _channel!.sink.add(jsonEncode({
        'type': 'task_update',
        'data': task.toJson(),
        'timestamp': DateTime.now().toIso8601String(),
      }));
    }
  }
}
```

### 4. 业务流程优化（参考同类应用）

#### 4.1 任务生命周期管理优化

参考工业级任务管理应用的最佳实践：

```dart
// 增强的任务状态机
enum TaskStatus {
  draft,        // 草稿 - 刚创建，未开始
  assigned,     // 已分配 - 分配给工作人员
  inProgress,   // 进行中 - 正在执行
  paused,       // 暂停 - 临时停止
  reviewing,    // 审核中 - 等待质检
  completed,    // 已完成 - 正常完成
  cancelled,    // 已取消 - 取消执行
  failed,       // 失败 - 执行失败
}

class TaskLifecycleManager {
  // 状态转换规则
  static const Map<TaskStatus, List<TaskStatus>> _allowedTransitions = {
    TaskStatus.draft: [TaskStatus.assigned, TaskStatus.cancelled],
    TaskStatus.assigned: [TaskStatus.inProgress, TaskStatus.cancelled],
    TaskStatus.inProgress: [TaskStatus.paused, TaskStatus.reviewing, TaskStatus.failed],
    TaskStatus.paused: [TaskStatus.inProgress, TaskStatus.cancelled],
    TaskStatus.reviewing: [TaskStatus.completed, TaskStatus.inProgress],
    TaskStatus.completed: [], // 终态
    TaskStatus.cancelled: [], // 终态
    TaskStatus.failed: [TaskStatus.inProgress], // 可以重新开始
  };
  
  // 验证状态转换
  static bool canTransitionTo(TaskStatus from, TaskStatus to) {
    return _allowedTransitions[from]?.contains(to) ?? false;
  }
  
  // 自动状态推进
  static TaskStatus getNextAutoStatus(TaskModel task) {
    switch (task.status) {
      case TaskStatus.assigned:
        // 如果有人开始拍照，自动转为进行中
        if (task.photos.any((p) => p.imagePath != null)) {
          return TaskStatus.inProgress;
        }
        break;
      case TaskStatus.inProgress:
        // 如果所有必拍照片都完成且识别通过，转为审核中
        final requiredPhotos = task.photos.where((p) => p.isRequired);
        final completedPhotos = requiredPhotos.where((p) => 
          p.imagePath != null && p.isVerified);
        if (completedPhotos.length == requiredPhotos.length) {
          return TaskStatus.reviewing;
        }
        break;
      case TaskStatus.reviewing:
        // 质检通过后自动完成
        if (task.qualityCheckPassed) {
          return TaskStatus.completed;
        }
        break;
    }
    return task.status;
  }
}
```

#### 4.2 智能任务分配算法

参考现代WMS系统的任务分配策略：

```dart
class IntelligentTaskAssignmentService {
  // 基于多因素的任务分配算法
  static List<WorkerAssignment> assignTask(
    TaskModel task,
    List<WorkerConfig> availableWorkers,
    List<TaskModel> currentTasks,
  ) {
    final assignments = <WorkerAssignment>[];
    
    // 1. 计算每个工人的当前工作负载
    final workloadMap = _calculateCurrentWorkloads(availableWorkers, currentTasks);
    
    // 2. 根据任务类型和复杂度确定所需人员
    final requiredSkills = _analyzeRequiredSkills(task);
    final estimatedDuration = _estimateTaskDuration(task);
    
    // 3. 智能匹配算法
    final candidates = availableWorkers.where((worker) {
      return _isWorkerSuitable(worker, task, requiredSkills) &&
             workloadMap[worker.id]!.canTakeNewTask(estimatedDuration);
    }).toList();
    
    // 4. 按优先级排序
    candidates.sort((a, b) {
      final scoreA = _calculateWorkerScore(a, task, workloadMap[a.id]!);
      final scoreB = _calculateWorkerScore(b, task, workloadMap[b.id]!);
      return scoreB.compareTo(scoreA);
    });
    
    // 5. 分配最优人员组合
    return _selectOptimalTeam(candidates, task, requiredSkills);
  }
  
  static double _calculateWorkerScore(
    WorkerConfig worker,
    TaskModel task,
    WorkerWorkload workload,
  ) {
    double score = 0.0;
    
    // 技能匹配度 (40%)
    score += _calculateSkillMatch(worker, task) * 0.4;
    
    // 工作负载平衡 (30%)
    score += (1.0 - workload.utilizationRate) * 0.3;
    
    // 历史表现 (20%)
    score += _getHistoricalPerformance(worker.id) * 0.2;
    
    // 地理位置优势 (10%)
    score += _calculateLocationAdvantage(worker, task) * 0.1;
    
    return score;
  }
}
```

#### 4.3 质量控制流程优化

参考ISO质量管理体系：

```dart
class QualityControlService {
  // 多级质量检查
  static QualityCheckResult performQualityCheck(TaskModel task) {
    final checks = <QualityCheck>[];
    
    // 1. 自动质量检查
    checks.add(_performAutomaticChecks(task));
    
    // 2. 识别准确性检查
    checks.add(_performRecognitionAccuracyCheck(task));
    
    // 3. 完整性检查
    checks.add(_performCompletenessCheck(task));
    
    // 4. 一致性检查
    checks.add(_performConsistencyCheck(task));
    
    return QualityCheckResult.fromChecks(checks);
  }
  
  static QualityCheck _performAutomaticChecks(TaskModel task) {
    final issues = <QualityIssue>[];
    
    // 检查必拍照片
    final requiredPhotos = task.photos.where((p) => p.isRequired);
    for (final photo in requiredPhotos) {
      if (photo.imagePath == null) {
        issues.add(QualityIssue(
          type: QualityIssueType.missingPhoto,
          description: '缺少必拍照片: ${photo.label}',
          severity: QualityIssueSeverity.critical,
        ));
      }
    }
    
    // 检查识别结果
    final recognitionPhotos = task.photos.where((p) => p.needRecognition);
    for (final photo in recognitionPhotos) {
      if (photo.recognitionResult == null) {
        issues.add(QualityIssue(
          type: QualityIssueType.missingRecognition,
          description: '照片未进行识别: ${photo.label}',
          severity: QualityIssueSeverity.high,
        ));
      } else if (!photo.isVerified) {
        issues.add(QualityIssue(
          type: QualityIssueType.recognitionFailed,
          description: '识别验证失败: ${photo.label}',
          severity: QualityIssueSeverity.medium,
        ));
      }
    }
    
    return QualityCheck(
      type: QualityCheckType.automatic,
      passed: issues.where((i) => i.severity == QualityIssueSeverity.critical).isEmpty,
      issues: issues,
    );
  }
  
  // 智能质量评分
  static double calculateQualityScore(TaskModel task) {
    double score = 100.0;
    
    // 完整性评分 (40%)
    final completenessScore = _calculateCompletenessScore(task);
    score = score * 0.6 + completenessScore * 0.4;
    
    // 准确性评分 (35%)
    final accuracyScore = _calculateAccuracyScore(task);
    score = score * 0.65 + accuracyScore * 0.35;
    
    // 及时性评分 (15%)
    final timelinessScore = _calculateTimelinessScore(task);
    score = score * 0.85 + timelinessScore * 0.15;
    
    // 一致性评分 (10%)
    final consistencyScore = _calculateConsistencyScore(task);
    score = score * 0.9 + consistencyScore * 0.1;
    
    return score.clamp(0.0, 100.0);
  }
}
```

### 5. 硬编码数据动态化解决方案

#### 3.1 配置数据模型

```dart
@freezed
class WorkerConfig with _$WorkerConfig {
  const factory WorkerConfig({
    required String id,
    required String name,
    required WorkerRole role,
    required String warehouse,
    required String group,
    required bool isActive,
    required DateTime createdAt,
    DateTime? updatedAt,
    @Default({}) Map<String, dynamic> metadata,
  }) = _WorkerConfig;
  
  factory WorkerConfig.fromJson(Map<String, dynamic> json) =>
      _$WorkerConfigFromJson(json);
}

@freezed
class TemplateConfig with _$TemplateConfig {
  const factory TemplateConfig({
    required String id,
    required String name,
    required String description,
    required bool isActive,
    required List<PhotoGroupConfig> photoGroups,
    required List<PhotoConfig> photoConfigs,
    @Default({}) Map<String, dynamic> settings,
    required DateTime createdAt,
    DateTime? updatedAt,
  }) = _TemplateConfig;
}
```

#### 3.2 配置管理服务

```dart
class ConfigManagementService {
  final ConfigRepository _repository;
  
  // 数据迁移：从硬编码到动态配置
  Future<void> migrateHardcodedData() async {
    // 迁移88名工作人员数据
    await _migrateWorkerData();
    
    // 迁移模板配置数据
    await _migrateTemplateData();
    
    // 迁移仓库和小组数据
    await _migrateWarehouseData();
  }
  
  Future<void> _migrateWorkerData() async {
    // 从 worker_info_data.dart 中的 allWorkers 迁移
    final legacyWorkers = [
      {'id': '1', 'name': '葛峰', 'role': '仓管', 'warehouse': '1号库', 'group': '1组'},
      // ... 其他87名工作人员
    ];
    
    for (final workerData in legacyWorkers) {
      final worker = WorkerConfig(
        id: workerData['id']!,
        name: workerData['name']!,
        role: WorkerRole.fromString(workerData['role']!),
        warehouse: workerData['warehouse']!,
        group: workerData['group']!,
        isActive: true,
        createdAt: DateTime.now(),
      );
      await _repository.saveWorker(worker);
    }
  }
}
```

### 4. 数据一致性解决方案

#### 4.1 Repository模式实现

```dart
abstract class TaskRepository {
  Stream<List<TaskModel>> watchTasks();
  Future<TaskModel?> getTask(String id);
  Future<void> saveTask(TaskModel task);
  Future<void> saveTasks(List<TaskModel> tasks);
  Future<void> deleteTask(String id);
  Future<List<TaskModel>> getAllTasks();
}

class TaskRepositoryImpl implements TaskRepository {
  final HiveDataSource _hiveDataSource;
  final SharedPreferencesDataSource _backupDataSource;
  final StreamController<List<TaskModel>> _tasksController;
  
  // 单一数据源，确保一致性
  List<TaskModel> _cachedTasks = [];
  
  @override
  Future<void> saveTask(TaskModel task) async {
    // 1. 更新缓存
    final index = _cachedTasks.indexWhere((t) => t.id == task.id);
    if (index >= 0) {
      _cachedTasks[index] = task;
    } else {
      _cachedTasks.add(task);
    }
    
    // 2. 保存到主存储
    await _hiveDataSource.saveTask(task);
    
    // 3. 异步备份到SharedPreferences
    unawaited(_backupDataSource.saveTask(task));
    
    // 4. 通知监听者
    _tasksController.add(List.from(_cachedTasks));
  }
}
```

### 5. 工作量统计系统重构

```dart
@freezed
class WorkloadStatistics with _$WorkloadStatistics {
  const factory WorkloadStatistics({
    required String workerId,
    required String workerName,
    required WorkerRole role,
    required String warehouse,
    required String group,
    required double totalTonnage,
    required int totalPallets,
    required int completedTasks,
    required DateTime lastUpdated,
    required List<WorkloadDetail> details,
  }) = _WorkloadStatistics;
}

class WorkloadCalculationService {
  static WorkloadStatistics calculateWorkerStatistics(
    String workerId,
    List<TaskModel> completedTasks,
    List<WorkloadAssignment> assignments,
  ) {
    final workerTasks = completedTasks.where((task) => 
      task.assignedWorkers.any((w) => w.id == workerId)
    ).toList();
    
    double totalTonnage = 0;
    int totalPallets = 0;
    final details = <WorkloadDetail>[];
    
    for (final task in workerTasks) {
      final assignment = assignments.firstWhere(
        (a) => a.taskId == task.id,
        orElse: () => throw StateError('Assignment not found for task ${task.id}'),
      );
      
      final workerRecord = assignment.records.firstWhere(
        (r) => r.workerId == workerId,
      );
      
      totalTonnage += workerRecord.allocatedTonnage;
      totalPallets += (workerRecord.allocatedTonnage / 1.5).round();
      
      details.add(WorkloadDetail(
        taskId: task.id,
        taskName: task.name,
        tonnage: workerRecord.allocatedTonnage,
        pallets: (workerRecord.allocatedTonnage / 1.5).round(),
        completedAt: task.completedAt!,
        productCode: task.productCode,
        batchNumber: task.batchNumber,
      ));
    }
    
    return WorkloadStatistics(
      workerId: workerId,
      workerName: details.first.workerName,
      role: WorkerRole.forklift,
      warehouse: 'default',
      group: 'default',
      totalTonnage: totalTonnage,
      totalPallets: totalPallets,
      completedTasks: workerTasks.length,
      lastUpdated: DateTime.now(),
      details: details,
    );
  }
}
```

## 组件和接口设计

### 1. 错误处理系统

```dart
abstract class AppException implements Exception {
  final String message;
  final String? userMessage;
  final Object? originalError;
  final StackTrace? stackTrace;
  
  const AppException(
    this.message, {
    this.userMessage,
    this.originalError,
    this.stackTrace,
  });
  
  String get displayMessage => userMessage ?? message;
}

class GlobalErrorHandler {
  static void handleError(
    Object error, {
    StackTrace? stackTrace,
    bool showToUser = true,
    String? context,
  }) {
    // 记录错误日志
    AppLogger.error(
      'Error in ${context ?? 'unknown'}: $error',
      stackTrace: stackTrace,
    );
    
    // 用户友好的错误显示
    if (showToUser) {
      String userMessage = '操作失败，请重试';
      
      if (error is AppException) {
        userMessage = error.displayMessage;
      } else if (error is TimeoutException) {
        userMessage = '操作超时，请检查网络连接';
      } else if (error is FileSystemException) {
        userMessage = '文件操作失败，请检查存储权限';
      }
      
      _showErrorSnackBar(userMessage);
    }
  }
}
```

### 2. 性能优化组件

```dart
class OptimizedImageProcessor {
  static const int maxImageSize = 1920;
  static const int jpegQuality = 85;
  static const int maxMemoryUsage = 100 * 1024 * 1024; // 100MB
  
  static Future<ProcessedImage> processImage(String imagePath) async {
    try {
      final file = File(imagePath);
      final imageBytes = await file.readAsBytes();
      
      if (imageBytes.length > maxMemoryUsage) {
        throw ImageTooLargeException('图像文件过大，请选择较小的图片');
      }
      
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw ImageFormatException('不支持的图像格式');
      }
      
      final resizedImage = _resizeImage(image);
      final compressedBytes = img.encodeJpg(resizedImage, quality: jpegQuality);
      final processedPath = await _saveProcessedImage(compressedBytes, imagePath);
      
      return ProcessedImage(
        originalPath: imagePath,
        processedPath: processedPath,
        originalSize: imageBytes.length,
        processedSize: compressedBytes.length,
        width: resizedImage.width,
        height: resizedImage.height,
      );
    } catch (e) {
      throw ImageProcessingException('Image processing failed: $e', originalError: e);
    }
  }
}
```

## 测试策略

### 1. ML Kit识别算法测试

```dart
class RecognitionAlgorithmTest {
  @Test('should maintain ML Kit V2 0.15.0 core functionality')
  void testMLKitCoreRecognition() async {
    // 测试ML Kit核心识别能力保持不变
    final recognizer = EnhancedMLKitRecognizer();
    final testImage = await loadTestImage('sample_label.jpg');
    
    final result = await recognizer.recognizeText(testImage);
    
    expect(result.text, isNotEmpty);
    expect(result.blocks, isNotEmpty);
  }
  
  @Test('should select optimal strategy based on image quality')
  void testIntelligentStrategySelection() {
    final highQualityImage = ImageQuality(brightness: 0.8, contrast: 0.9, sharpness: 0.85);
    final lowQualityImage = ImageQuality(brightness: 0.3, contrast: 0.4, sharpness: 0.3);
    
    final highQualityStrategy = IntelligentRecognitionStrategy.selectOptimalStrategy(
      highQualityImage, 
      RecognitionContext.defaultContext(),
    );
    
    final lowQualityStrategy = IntelligentRecognitionStrategy.selectOptimalStrategy(
      lowQualityImage, 
      RecognitionContext.defaultContext(),
    );
    
    expect(highQualityStrategy, equals(RecognitionStrategy.precise));
    expect(lowQualityStrategy, equals(RecognitionStrategy.tolerant));
  }
  
  @Test('should not decrease recognition accuracy after optimization')
  void testRecognitionAccuracyMaintained() async {
    // 使用标准测试数据集验证识别准确率
    final testCases = await loadRecognitionTestCases();
    final coordinator = IntelligentRecognitionCoordinator();
    
    int correctRecognitions = 0;
    
    for (final testCase in testCases) {
      final result = await coordinator.recognizeWithIntelligentStrategy(
        testCase.imagePath,
        context: testCase.context,
      );
      
      if (result.matchResult.isMatched && 
          result.matchResult.extractedData == testCase.expectedData) {
        correctRecognitions++;
      }
    }
    
    final accuracy = correctRecognitions / testCases.length;
    expect(accuracy, greaterThanOrEqualTo(0.85)); // 至少85%准确率
  }
}
```

### 2. 性能测试

```dart
class PerformanceTest {
  @Test('should maintain recognition speed after optimization')
  void testRecognitionSpeed() async {
    final testImages = await loadPerformanceTestImages();
    final coordinator = IntelligentRecognitionCoordinator();
    
    final stopwatch = Stopwatch()..start();
    
    for (final imagePath in testImages) {
      await coordinator.recognizeWithIntelligentStrategy(imagePath);
    }
    
    final averageTime = stopwatch.elapsedMilliseconds / testImages.length;
    
    // 平均识别时间应在3秒内
    expect(averageTime, lessThan(3000));
  }
}
```

## 总结

本设计文档提供了一个全面的优化方案，特别强调：

1. **保留ML Kit V2 0.15.0核心能力**：确保识别引擎的稳定性和准确性
2. **智能算法优化**：对12种识别算法进行评估，保留高价值算法，优化升级有潜力的算法，移除过度复杂的部分
3. **智能调用策略**：根据图像质量动态选择最优识别策略，确保准确率和速度
4. **数据一致性修复**：通过Repository模式解决工作量统计显示问题
5. **硬编码数据动态化**：将88名工作人员等静态数据迁移到动态配置
6. **向后兼容性**：确保现有页面调用逻辑不受影响

所有优化都以不降低识别准确率和处理速度为前提，采用渐进式实施策略，确保系统稳定性。