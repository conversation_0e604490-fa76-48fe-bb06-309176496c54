import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'dart:math';

// // part 'task_model.g.dart'; // 暂时注释，等待生成 // 暂时注释掉，手动实现





/// 简单的UUID生成函数
String generateUuid() {
  final random = Random();
  final values = List<int>.generate(16, (i) => random.nextInt(256));
  values[6] = (values[6] & 0x0f) | 0x40; // 版本4
  values[8] = (values[8] & 0x3f) | 0x80; // 变体
  return values.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
}
/// 任务状态枚举
@HiveType(typeId: 0)
enum TaskStatus {
  @HiveField(0)
  pending,     // 待处理
  @HiveField(1)
  inProgress,  // 进行中
  @HiveField(2)
  completed,   // 已完成
  @HiveField(3)
  failed,      // 失败
  @HiveField(4)
  cancelled,   // 已取消
}

/// 🔢 批次信息模型
@HiveType(typeId: 1)
class BatchInfo {
  @HiveField(0)
  final String id;
  @HiveField(1)
  final String productCode; // 牌号 (如 LLD-7042)
  @HiveField(2)
  final String batchNumber; // 批号 (如 250615F10422)
  @HiveField(3)
  final int plannedQuantity; // 计划数量（托）
  @HiveField(4)
  int recognizedQuantity; // 识别数量
  @HiveField(5)
  final List<String> recognizedItems; // 识别到的具体项目

  BatchInfo({
    String? id,
    required this.productCode,
    required this.batchNumber,
    required this.plannedQuantity,
    this.recognizedQuantity = 0,
    List<String>? recognizedItems,
  })  : id = id ?? generateUuid(),
        recognizedItems = recognizedItems ?? [];

  /// 是否完成（识别数量达到计划数量）
  bool get isCompleted => recognizedQuantity >= plannedQuantity;

  /// 完成度百分比
  double get completionRate => plannedQuantity > 0
      ? (recognizedQuantity / plannedQuantity).clamp(0.0, 1.0)
      : 0.0;

  /// 兼容性属性：数量（返回计划数量）
  num get quantity => plannedQuantity;

  /// 📊 计算实际验证通过的照片数量
  int calculateVerifiedCount(List<PhotoItem> photos) {
    return photos
        .where((photo) =>
            photo.imagePath != null &&
            photo.isVerified &&
            (photo.matchedBatchIds?.contains(id) == true ||
             photo.matchedProductCode == productCode))
        .length;
  }

  /// 🔄 更新识别数量（基于实际验证照片）
  void updateRecognizedQuantity(List<PhotoItem> photos) {
    recognizedQuantity = calculateVerifiedCount(photos);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productCode': productCode,
      'batchNumber': batchNumber,
      'plannedQuantity': plannedQuantity,
      'recognizedQuantity': recognizedQuantity,
      'recognizedItems': recognizedItems,
    };
  }

  /// 从JSON创建
  factory BatchInfo.fromJson(Map<String, dynamic> json) {
    return BatchInfo(
      id: json['id'],
      productCode: json['productCode'],
      batchNumber: json['batchNumber'],
      plannedQuantity: json['plannedQuantity'],
      recognizedQuantity: json['recognizedQuantity'] ?? 0,
      recognizedItems: List<String>.from(json['recognizedItems'] ?? []),
    );
  }

  /// 复制并修改
  BatchInfo copyWith({
    String? productCode,
    String? batchNumber,
    int? plannedQuantity,
    int? recognizedQuantity,
    List<String>? recognizedItems,
  }) {
    return BatchInfo(
      id: id,
      productCode: productCode ?? this.productCode,
      batchNumber: batchNumber ?? this.batchNumber,
      plannedQuantity: plannedQuantity ?? this.plannedQuantity,
      recognizedQuantity: recognizedQuantity ?? this.recognizedQuantity,
      recognizedItems: recognizedItems ?? this.recognizedItems,
    );
  }
}
/// 任务模型 - 支持多批次
@HiveType(typeId: 5)
class TaskModel {
  @HiveField(0)
  final String id;
  @HiveField(1)
  final String template; // 平板车 or 集装箱
  @HiveField(2)
  final List<BatchInfo> batches; // 多批次信息
  @HiveField(3)
  final DateTime createTime;
  @HiveField(4)
  final List<PhotoItem> photos;
  @HiveField(5)
  DateTime? completedAt; // 任务完成时间
  @HiveField(6)
  Map<String, dynamic>? recognitionMetadata; // 识别元数据，用于存储工作量等信息
  @HiveField(7)
  final List<String> participants; // 参与人员ID列表

  /// 🔧 计算属性：任务是否完成（基于必需照片完成情况）
  bool get isCompleted {
    final requiredPhotos = photos.where((p) => p.isRequired);
    final completedPhotos = requiredPhotos.where((p) => p.imagePath != null && p.imagePath!.isNotEmpty);
    return requiredPhotos.isNotEmpty && completedPhotos.length >= requiredPhotos.length;
  }

  // 兼容属性：使用第一个批次的信息
  String get productCode => batches.isNotEmpty ? batches.first.productCode : '';
  String get batchNumber => batches.isNotEmpty ? batches.first.batchNumber : '';
  int get quantity =>
      batches.fold(0, (sum, batch) => sum + batch.plannedQuantity);

  // 为了兼容现有代码，添加一些别名属性
  String? get imagePath => photos.isNotEmpty ? photos.first.imagePath : null;
  TaskStatus get status =>
      isCompleted ? TaskStatus.completed : TaskStatus.inProgress;
  RecognitionResult? get result =>
      photos.isNotEmpty ? photos.first.recognitionResult : null;
  DateTime get createdAt => createTime;

  TaskModel({
    String? id,
    required this.template,
    List<BatchInfo>? batches,
    // 兼容旧版本的参数
    String? productCode,
    String? batchNumber,
    int? quantity,
    TaskStatus? status, // 兼容性参数，会被忽略（使用计算属性）
    DateTime? createTime,
    List<PhotoItem>? photos,
    this.completedAt,
    this.recognitionMetadata,
    this.participants = const [],
  })  : id = id ?? generateUuid(),
        createTime = createTime ?? DateTime.now(),
        photos = photos ?? [],
        batches = batches ??
            (productCode != null && batchNumber != null && quantity != null
                ? [
                    BatchInfo(
                      productCode: productCode,
                      batchNumber: batchNumber,
                      plannedQuantity: quantity,
                    )
                  ]
                : []);

  /// 🎯 混装统计结果
  MixedLoadStatistics get statistics => MixedLoadStatistics.fromTask(this);

  /// 添加批次
  void addBatch(BatchInfo batch) {
    batches.add(batch);
  }

  /// 移除批次
  void removeBatch(String batchId) {
    batches.removeWhere((batch) => batch.id == batchId);
  }

  /// 更新批次识别结果
  void updateBatchRecognition(
      String productCode, String batchNumber, String recognizedItem) {
    final batchList = batches
        .where(
            (b) => b.productCode == productCode && b.batchNumber == batchNumber)
        .toList();
    final batch = batchList.isNotEmpty ? batchList.first : null;

    if (batch != null && !batch.recognizedItems.contains(recognizedItem)) {
      batch.recognizedItems.add(recognizedItem);
      batch.recognizedQuantity = batch.recognizedItems.length;
      // 回滚：不在此处直接持久化和刷新UI，统一由业务流程末端控制
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'template': template,
      'batches': batches.map((b) => b.toJson()).toList(),
      'createTime': createTime.toIso8601String(),
      'photos': photos.map((p) => p.toJson()).toList(),
      'completedAt': completedAt?.toIso8601String(),
      'recognitionMetadata': recognitionMetadata,
      'participants': participants,
      // 注意：isCompleted现在是计算属性，不需要存储
    };
  }

  /// 从JSON创建
  factory TaskModel.fromJson(Map<String, dynamic> json) {
    // 兼容旧版本数据
    List<BatchInfo> batches = [];
    if (json['batches'] != null) {
      batches =
          (json['batches'] as List).map((b) => BatchInfo.fromJson(b)).toList();
    } else if (json['productCode'] != null) {
      // 旧版本兼容
      batches = [
        BatchInfo(
          productCode: json['productCode'],
          batchNumber: json['batchNumber'],
          plannedQuantity: json['quantity'] ?? 0,
        )
      ];
    }

    return TaskModel(
      id: json['id'],
      template: json['template'],
      batches: batches,
      createTime: DateTime.parse(json['createTime']),
      photos: (json['photos'] as List?)
              ?.map((p) => PhotoItem.fromJson(p))
              .toList() ??
          [],
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      recognitionMetadata: json['recognitionMetadata'],
      participants:
          (json['participants'] as List?)?.map((e) => e.toString()).toList() ??
              [],
    );
  }

  /// 获取已拍照片数量
  int get photosTaken => photos.where((p) => p.imagePath != null).length;

  /// 获取识别成功的照片数量
  int get photosVerified => photos.where((p) => p.isVerified).length;

  /// 🔄 复制并修改 - 支持Riverpod状态管理
  TaskModel copyWith({
    String? id,
    String? template,
    List<BatchInfo>? batches,
    DateTime? createTime,
    List<PhotoItem>? photos,
    DateTime? completedAt,
    Map<String, dynamic>? recognitionMetadata,
    List<String>? participants,
  }) {
    return TaskModel(
      id: id ?? this.id,
      template: template ?? this.template,
      batches: batches ?? List<BatchInfo>.from(this.batches),
      createTime: createTime ?? this.createTime,
      photos: photos ?? List<PhotoItem>.from(this.photos),
      completedAt: completedAt ?? this.completedAt,
      recognitionMetadata: recognitionMetadata ?? this.recognitionMetadata,
      participants: participants ?? List<String>.from(this.participants),
    );
  }
}

/// 🔢 混装统计结果
class MixedLoadStatistics {
  final int totalPlannedQuantity; // 总计划数量
  final int totalRecognizedQuantity; // 总识别数量
  final List<BatchStatistic> batchStatistics; // 各批次统计
  final List<String> missingItems; // 缺失项目
  final List<String> extraItems; // 多余项目
  final bool isComplete; // 是否完成

  MixedLoadStatistics({
    required this.totalPlannedQuantity,
    required this.totalRecognizedQuantity,
    required this.batchStatistics,
    required this.missingItems,
    required this.extraItems,
    required this.isComplete,
  });

  factory MixedLoadStatistics.fromTask(TaskModel task) {
    final batchStats = task.batches
        .map((batch) => BatchStatistic(
              productCode: batch.productCode,
              batchNumber: batch.batchNumber,
              plannedQuantity: batch.plannedQuantity,
              recognizedQuantity: batch.recognizedQuantity,
              completionRate: batch.completionRate,
              isComplete: batch.isCompleted,
            ))
        .toList();

    final totalPlanned =
        task.batches.fold(0, (sum, b) => sum + b.plannedQuantity);
    final totalRecognized =
        task.batches.fold(0, (sum, b) => sum + b.recognizedQuantity);

    // 计算缺失和多余项目（这里可以根据具体业务逻辑实现）
    final missingItems = <String>[];
    final extraItems = <String>[];

    return MixedLoadStatistics(
      totalPlannedQuantity: totalPlanned,
      totalRecognizedQuantity: totalRecognized,
      batchStatistics: batchStats,
      missingItems: missingItems,
      extraItems: extraItems,
      isComplete: totalRecognized >= totalPlanned,
    );
  }

  /// 完成度百分比
  double get completionRate => totalPlannedQuantity > 0
      ? (totalRecognizedQuantity / totalPlannedQuantity).clamp(0.0, 1.0)
      : 0.0;
}

/// 单批次统计
class BatchStatistic {
  final String productCode;
  final String batchNumber;
  final int plannedQuantity;
  final int recognizedQuantity;
  final double completionRate;
  final bool isComplete;

  BatchStatistic({
    required this.productCode,
    required this.batchNumber,
    required this.plannedQuantity,
    required this.recognizedQuantity,
    required this.completionRate,
    required this.isComplete,
  });
}

/// 照片项模型
@HiveType(typeId: 2)
class PhotoItem {
  @HiveField(0)
  final String id;
  @HiveField(1)
  String label; // 照片标签（如：车头车牌、提货单照等）
  @HiveField(2)
  String? configId; // 模板配置ID（如：cargo_label_1），用于识别匹配
  @HiveField(3)
  bool isRequired; // 是否必拍
  @HiveField(4)
  String? imagePath; // 照片路径
  @HiveField(5)
  RecognitionResult? recognitionResult; // 识别结果
  @HiveField(6)
  bool isVerified; // 是否验证通过
  @HiveField(7)
  bool needRecognition;
  @HiveField(8)
  bool recognitionFailed;
  @HiveField(9)
  bool isRecognitionCompleted; // 🔧 新增：识别是否已完成（无论成功失败）
  @HiveField(10)
  String? ocrText; // OCR识别文本
  @HiveField(11)
  String? matchedProductCode; // 匹配的产品代码
  @HiveField(12)
  String? matchedBatchNumber; // 匹配的批号
  @HiveField(13)
  DateTime? lastRecognitionTime; // 最后识别时间

  // 🔧 新增：增强状态管理
  @HiveField(14)
  RecognitionStatus recognitionStatus; // 识别状态
  @HiveField(15)
  DateTime? recognitionStartTime; // 识别开始时间
  @HiveField(16)
  DateTime? recognitionEndTime; // 识别结束时间
  @HiveField(17)
  String? recognitionErrorMessage; // 识别错误信息
  @HiveField(18)
  Map<String, dynamic>? recognitionMetadata; // 识别元数据
  @HiveField(19)
  List<String>? matchedBatchIds; // 匹配的批次ID列表（混装任务）
  @HiveField(20)
  bool manualVerified;
  @HiveField(21)
  String? manualConfirmationNotes; // 人工确认备注
  @HiveField(22)
  bool isCustom;
  @HiveField(23)
  String? description; // 🔧 新增：照片描述
  @HiveField(24)
  DateTime? updateTime; // 更新时间
  @HiveField(25)
  Map<String, dynamic>? metadata; // 元数据

  PhotoItem({
    String? id,
    required this.label,
    String? name, // 兼容性参数，会被映射到label
    this.configId,
    required this.isRequired,
    this.imagePath,
    this.recognitionResult,
    this.isVerified = false,
    required this.needRecognition,
    this.recognitionFailed = false,
    this.isRecognitionCompleted = false, // 🔧 新增：默认为false
    this.ocrText,
    this.matchedProductCode,
    this.matchedBatchNumber,
    this.lastRecognitionTime,
    this.recognitionStatus = RecognitionStatus.pending, // 🔧 新增
    this.recognitionStartTime, // 🔧 新增
    this.recognitionEndTime, // 🔧 新增
    this.recognitionErrorMessage, // 🔧 新增
    this.recognitionMetadata, // 🔧 新增
    this.matchedBatchIds, // 🔧 新增
    this.manualVerified = false,
    this.manualConfirmationNotes, // 🔧 新增：人工确认备注
    this.isCustom = false,
    this.description, // 🔧 新增：描述属性
    this.updateTime,
    this.metadata,
  }) : id = id ?? generateUuid() {
    // 如果提供了name参数，使用它作为label
    if (name != null) {
      label = name;
    }
  }

  /// 兼容性属性：名称（返回标签）
  String get name => label;

  /// 🔧 新增：获取识别状态文本
  String get recognitionStatusText {
    switch (recognitionStatus) {
      case RecognitionStatus.pending:
        return '等待识别';
      case RecognitionStatus.processing:
        return '识别中...';
      case RecognitionStatus.completed:
        return isVerified ? '识别通过' : '识别完成';
      case RecognitionStatus.failed:
        return '识别失败';
      case RecognitionStatus.cancelled:
        return '识别取消';
    }
  }

  /// 🔧 新增：获取识别状态颜色
  Color get recognitionStatusColor {
    switch (recognitionStatus) {
      case RecognitionStatus.pending:
        return Colors.grey;
      case RecognitionStatus.processing:
        return Colors.blue;
      case RecognitionStatus.completed:
        return isVerified ? Colors.green : Colors.orange;
      case RecognitionStatus.failed:
        return Colors.red;
      case RecognitionStatus.cancelled:
        return Colors.grey;
    }
  }

  /// 🔧 新增：获取识别持续时间
  Duration? get recognitionDuration {
    if (recognitionStartTime != null && recognitionEndTime != null) {
      return recognitionEndTime!.difference(recognitionStartTime!);
    }
    return null;
  }

  /// 🔧 新增：是否为混装任务匹配
  bool get isMixedLoadMatch {
    return matchedBatchIds != null && matchedBatchIds!.isNotEmpty;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'configId': configId,
      'isRequired': isRequired,
      'imagePath': imagePath,
      'recognitionResult': recognitionResult?.toJson(),
      'isVerified': isVerified,
      'needRecognition': needRecognition,
      'recognitionFailed': recognitionFailed,
      'isRecognitionCompleted': isRecognitionCompleted, // 🔧 新增
      'ocrText': ocrText,
      'matchedProductCode': matchedProductCode,
      'matchedBatchNumber': matchedBatchNumber,
      'lastRecognitionTime': lastRecognitionTime?.toIso8601String(),
      'recognitionStatus': recognitionStatus.name, // 🔧 新增
      'recognitionStartTime': recognitionStartTime?.toIso8601String(), // 🔧 新增
      'recognitionEndTime': recognitionEndTime?.toIso8601String(), // 🔧 新增
      'recognitionErrorMessage': recognitionErrorMessage, // 🔧 新增
      'recognitionMetadata': recognitionMetadata, // 🔧 新增
      'matchedBatchIds': matchedBatchIds, // 🔧 新增
      'manualVerified': manualVerified,
      'isCustom': isCustom,
    };
  }

  /// 从JSON创建
  factory PhotoItem.fromJson(Map<String, dynamic> json) {
    return PhotoItem(
      id: json['id'],
      label: json['label'],
      configId: json['configId'],
      isRequired: json['isRequired'],
      imagePath: json['imagePath'],
      recognitionResult: json['recognitionResult'] != null
          ? RecognitionResult.fromJson(json['recognitionResult'])
          : null,
      isVerified: json['isVerified'] ?? false,
      needRecognition: json['needRecognition'] ?? false,
      recognitionFailed: json['recognitionFailed'] ?? false,
      isRecognitionCompleted: json['isRecognitionCompleted'] ?? false, // 🔧 新增
      ocrText: json['ocrText'],
      matchedProductCode: json['matchedProductCode'],
      matchedBatchNumber: json['matchedBatchNumber'],
      lastRecognitionTime: json['lastRecognitionTime'] != null
          ? DateTime.parse(json['lastRecognitionTime'])
          : null,
      recognitionStatus: RecognitionStatus.values.firstWhere(
        // 🔧 新增
        (s) => s.name == json['recognitionStatus'],
        orElse: () => RecognitionStatus.pending,
      ),
      recognitionStartTime: json['recognitionStartTime'] != null // 🔧 新增
          ? DateTime.parse(json['recognitionStartTime'])
          : null,
      recognitionEndTime: json['recognitionEndTime'] != null // 🔧 新增
          ? DateTime.parse(json['recognitionEndTime'])
          : null,
      recognitionErrorMessage: json['recognitionErrorMessage'], // 🔧 新增
      recognitionMetadata: json['recognitionMetadata'], // 🔧 新增
      matchedBatchIds: json['matchedBatchIds'] != null // 🔧 新增
          ? List<String>.from(json['matchedBatchIds'])
          : null,
      manualVerified: json['manualVerified'] ?? false,
      isCustom: json['isCustom'] ?? false,
    );
  }

  /// 🔄 复制并修改 - 支持Riverpod状态管理
  PhotoItem copyWith({
    String? id,
    String? label,
    String? configId,
    bool? isRequired,
    String? imagePath,
    RecognitionResult? recognitionResult,
    bool? isVerified,
    bool? needRecognition,
    bool? recognitionFailed,
    bool? isRecognitionCompleted,
    String? ocrText,
    String? matchedProductCode,
    String? matchedBatchNumber,
    DateTime? lastRecognitionTime,
    RecognitionStatus? recognitionStatus,
    DateTime? recognitionStartTime,
    DateTime? recognitionEndTime,
    String? recognitionErrorMessage,
    Map<String, dynamic>? recognitionMetadata,
    List<String>? matchedBatchIds,
    bool? manualVerified,
    String? manualConfirmationNotes,
    bool? isCustom,
  }) {
    return PhotoItem(
      id: id ?? this.id,
      label: label ?? this.label,
      configId: configId ?? this.configId,
      isRequired: isRequired ?? this.isRequired,
      imagePath: imagePath ?? this.imagePath,
      recognitionResult: recognitionResult ?? this.recognitionResult,
      isVerified: isVerified ?? this.isVerified,
      needRecognition: needRecognition ?? this.needRecognition,
      recognitionFailed: recognitionFailed ?? this.recognitionFailed,
      isRecognitionCompleted: isRecognitionCompleted ?? this.isRecognitionCompleted,
      ocrText: ocrText ?? this.ocrText,
      matchedProductCode: matchedProductCode ?? this.matchedProductCode,
      matchedBatchNumber: matchedBatchNumber ?? this.matchedBatchNumber,
      lastRecognitionTime: lastRecognitionTime ?? this.lastRecognitionTime,
      recognitionStatus: recognitionStatus ?? this.recognitionStatus,
      recognitionStartTime: recognitionStartTime ?? this.recognitionStartTime,
      recognitionEndTime: recognitionEndTime ?? this.recognitionEndTime,
      recognitionErrorMessage: recognitionErrorMessage ?? this.recognitionErrorMessage,
      recognitionMetadata: recognitionMetadata ?? this.recognitionMetadata,
      matchedBatchIds: matchedBatchIds ?? this.matchedBatchIds,
      manualVerified: manualVerified ?? this.manualVerified,
      manualConfirmationNotes: manualConfirmationNotes ?? this.manualConfirmationNotes,
      isCustom: isCustom ?? this.isCustom,
    );
  }
}

/// 识别结果模型
@HiveType(typeId: 4)
class RecognitionResult {
  @HiveField(0)
  final String? qrCode; // 二维码内容
  @HiveField(1)
  final String? ocrText; // OCR识别的文本
  @HiveField(2)
  final String? extractedProductCode; // 从识别结果中提取的牌号
  @HiveField(3)
  final String? extractedBatchNumber; // 从识别结果中提取的批号
  @HiveField(4)
  final bool isQrOcrConsistent; // 二维码和OCR是否一致
  @HiveField(5)
  final bool matchesPreset; // 是否与预设信息匹配
  @HiveField(6)
  final double? confidence; // 识别置信度 (0-100)
  @HiveField(7)
  final DateTime recognitionTime;
  @HiveField(8)
  final Map<String, double>? boundingBox; // 边界框 {left, top, right, bottom}
  @HiveField(9)
  final double? processingTime; // 处理时间(毫秒)
  @HiveField(10)
  final String? extractedQrData; // 提取的二维码数据

  // 🔧 新增：多批次匹配支持
  @HiveField(11)
  final List<BatchMatchResult>? batchMatches; // 多批次匹配结果
  @HiveField(12)
  final RecognitionStatus status; // 识别状态
  @HiveField(13)
  final String? errorMessage; // 错误信息
  @HiveField(14)
  final Map<String, dynamic>? metadata; // 额外元数据

  RecognitionResult({
    this.qrCode,
    this.ocrText,
    this.extractedProductCode,
    this.extractedBatchNumber,
    required this.isQrOcrConsistent,
    required this.matchesPreset,
    this.confidence,
    DateTime? recognitionTime,
    this.boundingBox,
    this.processingTime, // 🔧 新增
    this.extractedQrData, // 🔧 新增
    this.batchMatches, // 🔧 新增
    this.status = RecognitionStatus.completed, // 🔧 新增
    this.errorMessage, // 🔧 新增
    this.metadata, // 🔧 新增
  }) : recognitionTime = recognitionTime ?? DateTime.now();

  /// 兼容性属性：识别文本（返回OCR文本）
  String? get recognizedText => ocrText;

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'qrCode': qrCode,
      'ocrText': ocrText,
      'extractedProductCode': extractedProductCode,
      'extractedBatchNumber': extractedBatchNumber,
      'isQrOcrConsistent': isQrOcrConsistent,
      'matchesPreset': matchesPreset,
      'confidence': confidence,
      'recognitionTime': recognitionTime.toIso8601String(),
      'boundingBox': boundingBox,
      'processingTime': processingTime, // 🔧 新增
      'extractedQrData': extractedQrData, // 🔧 新增
      'batchMatches': batchMatches?.map((b) => b.toJson()).toList(), // 🔧 新增
      'status': status.name, // 🔧 新增
      'errorMessage': errorMessage, // 🔧 新增
      'metadata': metadata, // 🔧 新增
    };
  }

  /// 转换为Map（别名方法）
  Map<String, dynamic> toMap() => toJson();

  /// 复制并修改部分属性
  RecognitionResult copyWith({
    String? qrCode,
    String? ocrText,
    String? extractedProductCode,
    String? extractedBatchNumber,
    bool? isQrOcrConsistent,
    bool? matchesPreset,
    double? confidence,
    DateTime? recognitionTime,
    Map<String, double>? boundingBox,
    double? processingTime,
    String? extractedQrData,
    List<BatchMatchResult>? batchMatches,
    RecognitionStatus? status,
    String? errorMessage,
    Map<String, dynamic>? metadata,
  }) {
    return RecognitionResult(
      qrCode: qrCode ?? this.qrCode,
      ocrText: ocrText ?? this.ocrText,
      extractedProductCode: extractedProductCode ?? this.extractedProductCode,
      extractedBatchNumber: extractedBatchNumber ?? this.extractedBatchNumber,
      isQrOcrConsistent: isQrOcrConsistent ?? this.isQrOcrConsistent,
      matchesPreset: matchesPreset ?? this.matchesPreset,
      confidence: confidence ?? this.confidence,
      recognitionTime: recognitionTime ?? this.recognitionTime,
      boundingBox: boundingBox ?? this.boundingBox,
      processingTime: processingTime ?? this.processingTime,
      extractedQrData: extractedQrData ?? this.extractedQrData,
      batchMatches: batchMatches ?? this.batchMatches,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 从JSON创建
  factory RecognitionResult.fromJson(Map<String, dynamic> json) {
    return RecognitionResult(
      qrCode: json['qrCode'],
      ocrText: json['ocrText'],
      extractedProductCode: json['extractedProductCode'],
      extractedBatchNumber: json['extractedBatchNumber'],
      isQrOcrConsistent: json['isQrOcrConsistent'],
      matchesPreset: json['matchesPreset'],
      confidence: json['confidence']?.toDouble(),
      recognitionTime: DateTime.parse(json['recognitionTime']),
      boundingBox: json['boundingBox'] != null
          ? Map<String, double>.from(json['boundingBox'])
          : null,
      processingTime: json['processingTime']?.toDouble(), // 🔧 新增
      extractedQrData: json['extractedQrData'], // 🔧 新增
      batchMatches: json['batchMatches'] != null // 🔧 新增
          ? (json['batchMatches'] as List)
              .map((b) => BatchMatchResult.fromJson(b))
              .toList()
          : null,
      status: RecognitionStatus.values.firstWhere(
        // 🔧 新增
        (s) => s.name == json['status'],
        orElse: () => RecognitionStatus.completed,
      ),
      errorMessage: json['errorMessage'], // 🔧 新增
      metadata: json['metadata'], // 🔧 新增
    );
  }
}

/// 🔧 新增：识别状态枚举
@HiveType(typeId: 3)
enum RecognitionStatus {
  @HiveField(0)
  pending, // 等待识别
  @HiveField(1)
  processing, // 识别中
  @HiveField(2)
  completed, // 识别完成
  @HiveField(3)
  failed, // 识别失败
  @HiveField(4)
  cancelled, // 识别取消
}

/// 🔧 新增：批次匹配结果
@HiveType(typeId: 6)
class BatchMatchResult {
  @HiveField(0)
  final String batchId;
  @HiveField(1)
  final String productCode;
  @HiveField(2)
  final String batchNumber;
  @HiveField(3)
  final bool isMatched;
  @HiveField(4)
  final double confidence;
  @HiveField(5)
  final String? matchedText;
  @HiveField(6)
  final Map<String, dynamic>? details;

  BatchMatchResult({
    required this.batchId,
    required this.productCode,
    required this.batchNumber,
    required this.isMatched,
    required this.confidence,
    this.matchedText,
    this.details,
  });

  Map<String, dynamic> toJson() {
    return {
      'batchId': batchId,
      'productCode': productCode,
      'batchNumber': batchNumber,
      'isMatched': isMatched,
      'confidence': confidence,
      'matchedText': matchedText,
      'details': details,
    };
  }

  factory BatchMatchResult.fromJson(Map<String, dynamic> json) {
    return BatchMatchResult(
      batchId: json['batchId'],
      productCode: json['productCode'],
      batchNumber: json['batchNumber'],
      isMatched: json['isMatched'],
      confidence: json['confidence']?.toDouble() ?? 0.0,
      matchedText: json['matchedText'],
      details: json['details'],
    );
  }
}
