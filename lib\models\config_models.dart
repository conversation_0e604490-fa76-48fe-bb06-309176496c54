/// 工作人员配置模型
/// 手动实现freezed风格的不可变数据类
class WorkerConfig {
  const WorkerConfig({
    required this.id,
    required this.name,
    required this.role,
    required this.warehouse,
    required this.group,
    this.isActive = true,
    this.phone = '',
    this.email = '',
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String name;
  final String role;
  final String warehouse;
  final String group;
  final bool isActive;
  final String phone;
  final String email;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  WorkerConfig copyWith({
    String? id,
    String? name,
    String? role,
    String? warehouse,
    String? group,
    bool? isActive,
    String? phone,
    String? email,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WorkerConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      warehouse: warehouse ?? this.warehouse,
      group: group ?? this.group,
      isActive: isActive ?? this.isActive,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'isActive': isActive,
      'phone': phone,
      'email': email,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory WorkerConfig.fromJson(Map<String, dynamic> json) {
    return WorkerConfig(
      id: json['id'] as String,
      name: json['name'] as String,
      role: json['role'] as String,
      warehouse: json['warehouse'] as String,
      group: json['group'] as String,
      isActive: json['isActive'] as bool? ?? true,
      phone: json['phone'] as String? ?? '',
      email: json['email'] as String? ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WorkerConfig &&
        other.id == id &&
        other.name == name &&
        other.role == role &&
        other.warehouse == warehouse &&
        other.group == group &&
        other.isActive == isActive &&
        other.phone == phone &&
        other.email == email &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      role,
      warehouse,
      group,
      isActive,
      phone,
      email,
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'WorkerConfig(id: $id, name: $name, role: $role, warehouse: $warehouse, group: $group, isActive: $isActive, phone: $phone, email: $email, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 仓库配置模型
/// 手动实现freezed风格的不可变数据类
class WarehouseConfig {
  const WarehouseConfig({
    required this.id,
    required this.name,
    required this.location,
    this.isActive = true,
    this.description = '',
    this.supportedTemplates = const [],
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String name;
  final String location;
  final bool isActive;
  final String description;
  final List<String> supportedTemplates;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  WarehouseConfig copyWith({
    String? id,
    String? name,
    String? location,
    bool? isActive,
    String? description,
    List<String>? supportedTemplates,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WarehouseConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      location: location ?? this.location,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      supportedTemplates: supportedTemplates ?? this.supportedTemplates,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'location': location,
      'isActive': isActive,
      'description': description,
      'supportedTemplates': supportedTemplates,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory WarehouseConfig.fromJson(Map<String, dynamic> json) {
    return WarehouseConfig(
      id: json['id'] as String,
      name: json['name'] as String,
      location: json['location'] as String,
      isActive: json['isActive'] as bool? ?? true,
      description: json['description'] as String? ?? '',
      supportedTemplates: (json['supportedTemplates'] as List<dynamic>?)
          ?.cast<String>() ?? const [],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WarehouseConfig &&
        other.id == id &&
        other.name == name &&
        other.location == location &&
        other.isActive == isActive &&
        other.description == description &&
        _listEquals(other.supportedTemplates, supportedTemplates) &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      location,
      isActive,
      description,
      Object.hashAll(supportedTemplates),
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'WarehouseConfig(id: $id, name: $name, location: $location, isActive: $isActive, description: $description, supportedTemplates: $supportedTemplates, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 工作组配置模型
/// 手动实现freezed风格的不可变数据类
class GroupConfig {
  const GroupConfig({
    required this.id,
    required this.name,
    required this.warehouseId,
    this.isActive = true,
    this.description = '',
    this.memberIds = const [],
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String name;
  final String warehouseId;
  final bool isActive;
  final String description;
  final List<String> memberIds;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  GroupConfig copyWith({
    String? id,
    String? name,
    String? warehouseId,
    bool? isActive,
    String? description,
    List<String>? memberIds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return GroupConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      warehouseId: warehouseId ?? this.warehouseId,
      isActive: isActive ?? this.isActive,
      description: description ?? this.description,
      memberIds: memberIds ?? this.memberIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'warehouseId': warehouseId,
      'isActive': isActive,
      'description': description,
      'memberIds': memberIds,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory GroupConfig.fromJson(Map<String, dynamic> json) {
    return GroupConfig(
      id: json['id'] as String,
      name: json['name'] as String,
      warehouseId: json['warehouseId'] as String,
      isActive: json['isActive'] as bool? ?? true,
      description: json['description'] as String? ?? '',
      memberIds: (json['memberIds'] as List<dynamic>?)
          ?.cast<String>() ?? const [],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroupConfig &&
        other.id == id &&
        other.name == name &&
        other.warehouseId == warehouseId &&
        other.isActive == isActive &&
        other.description == description &&
        _listEquals(other.memberIds, memberIds) &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      warehouseId,
      isActive,
      description,
      Object.hashAll(memberIds),
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'GroupConfig(id: $id, name: $name, warehouseId: $warehouseId, isActive: $isActive, description: $description, memberIds: $memberIds, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 模板配置模型
/// 手动实现freezed风格的不可变数据类
class TemplateConfigModel {
  const TemplateConfigModel({
    required this.id,
    required this.name,
    required this.type,
    this.description = '',
    this.isActive = true,
    this.photoConfigs = const [],
    this.photoGroups = const [],
    this.totalPhotos = 0,
    this.requiredPhotos = 0,
    this.recognitionPhotos = 0,
    this.version = '1.0.0',
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String name;
  final String type; // '平板车', '集装箱'
  final String description;
  final bool isActive;
  final List<PhotoConfigModel> photoConfigs;
  final List<PhotoGroupModel> photoGroups;
  final int totalPhotos;
  final int requiredPhotos;
  final int recognitionPhotos;
  final String version;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  TemplateConfigModel copyWith({
    String? id,
    String? name,
    String? type,
    String? description,
    bool? isActive,
    List<PhotoConfigModel>? photoConfigs,
    List<PhotoGroupModel>? photoGroups,
    int? totalPhotos,
    int? requiredPhotos,
    int? recognitionPhotos,
    String? version,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TemplateConfigModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      photoConfigs: photoConfigs ?? this.photoConfigs,
      photoGroups: photoGroups ?? this.photoGroups,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      requiredPhotos: requiredPhotos ?? this.requiredPhotos,
      recognitionPhotos: recognitionPhotos ?? this.recognitionPhotos,
      version: version ?? this.version,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'description': description,
      'isActive': isActive,
      'photoConfigs': photoConfigs.map((e) => e.toJson()).toList(),
      'photoGroups': photoGroups.map((e) => e.toJson()).toList(),
      'totalPhotos': totalPhotos,
      'requiredPhotos': requiredPhotos,
      'recognitionPhotos': recognitionPhotos,
      'version': version,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory TemplateConfigModel.fromJson(Map<String, dynamic> json) {
    return TemplateConfigModel(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      description: json['description'] as String? ?? '',
      isActive: json['isActive'] as bool? ?? true,
      photoConfigs: (json['photoConfigs'] as List<dynamic>?)
          ?.map((e) => PhotoConfigModel.fromJson(e as Map<String, dynamic>))
          .toList() ?? const [],
      photoGroups: (json['photoGroups'] as List<dynamic>?)
          ?.map((e) => PhotoGroupModel.fromJson(e as Map<String, dynamic>))
          .toList() ?? const [],
      totalPhotos: json['totalPhotos'] as int? ?? 0,
      requiredPhotos: json['requiredPhotos'] as int? ?? 0,
      recognitionPhotos: json['recognitionPhotos'] as int? ?? 0,
      version: json['version'] as String? ?? '1.0.0',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TemplateConfigModel &&
        other.id == id &&
        other.name == name &&
        other.type == type &&
        other.description == description &&
        other.isActive == isActive &&
        _listEquals(other.photoConfigs, photoConfigs) &&
        _listEquals(other.photoGroups, photoGroups) &&
        other.totalPhotos == totalPhotos &&
        other.requiredPhotos == requiredPhotos &&
        other.recognitionPhotos == recognitionPhotos &&
        other.version == version &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      type,
      description,
      isActive,
      Object.hashAll(photoConfigs),
      Object.hashAll(photoGroups),
      totalPhotos,
      requiredPhotos,
      recognitionPhotos,
      version,
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'TemplateConfigModel(id: $id, name: $name, type: $type, description: $description, isActive: $isActive, totalPhotos: $totalPhotos, requiredPhotos: $requiredPhotos, recognitionPhotos: $recognitionPhotos, version: $version, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 照片配置模型
/// 手动实现freezed风格的不可变数据类
class PhotoConfigModel {
  const PhotoConfigModel({
    required this.id,
    required this.label,
    required this.groupId,
    this.description = '',
    this.isRequired = true,
    this.needRecognition = false,
    this.recognitionType = RecognitionType.none,
    this.stage = 1,
    this.order = 1,
    this.tips = '',
    this.retryPrompt,
    this.isCustom = false,
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String label;
  final String groupId;
  final String description;
  final bool isRequired;
  final bool needRecognition;
  final RecognitionType recognitionType;
  final int stage;
  final int order;
  final String tips;
  final String? retryPrompt;
  final bool isCustom;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  PhotoConfigModel copyWith({
    String? id,
    String? label,
    String? groupId,
    String? description,
    bool? isRequired,
    bool? needRecognition,
    RecognitionType? recognitionType,
    int? stage,
    int? order,
    String? tips,
    String? retryPrompt,
    bool? isCustom,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PhotoConfigModel(
      id: id ?? this.id,
      label: label ?? this.label,
      groupId: groupId ?? this.groupId,
      description: description ?? this.description,
      isRequired: isRequired ?? this.isRequired,
      needRecognition: needRecognition ?? this.needRecognition,
      recognitionType: recognitionType ?? this.recognitionType,
      stage: stage ?? this.stage,
      order: order ?? this.order,
      tips: tips ?? this.tips,
      retryPrompt: retryPrompt ?? this.retryPrompt,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'label': label,
      'groupId': groupId,
      'description': description,
      'isRequired': isRequired,
      'needRecognition': needRecognition,
      'recognitionType': recognitionType.name,
      'stage': stage,
      'order': order,
      'tips': tips,
      'retryPrompt': retryPrompt,
      'isCustom': isCustom,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory PhotoConfigModel.fromJson(Map<String, dynamic> json) {
    return PhotoConfigModel(
      id: json['id'] as String,
      label: json['label'] as String,
      groupId: json['groupId'] as String,
      description: json['description'] as String? ?? '',
      isRequired: json['isRequired'] as bool? ?? true,
      needRecognition: json['needRecognition'] as bool? ?? false,
      recognitionType: RecognitionType.values.firstWhere(
        (e) => e.name == json['recognitionType'],
        orElse: () => RecognitionType.none,
      ),
      stage: json['stage'] as int? ?? 1,
      order: json['order'] as int? ?? 1,
      tips: json['tips'] as String? ?? '',
      retryPrompt: json['retryPrompt'] as String?,
      isCustom: json['isCustom'] as bool? ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhotoConfigModel &&
        other.id == id &&
        other.label == label &&
        other.groupId == groupId &&
        other.description == description &&
        other.isRequired == isRequired &&
        other.needRecognition == needRecognition &&
        other.recognitionType == recognitionType &&
        other.stage == stage &&
        other.order == order &&
        other.tips == tips &&
        other.retryPrompt == retryPrompt &&
        other.isCustom == isCustom &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      id,
      label,
      groupId,
      description,
      isRequired,
      needRecognition,
      recognitionType,
      stage,
      order,
      tips,
      retryPrompt,
      isCustom,
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'PhotoConfigModel(id: $id, label: $label, groupId: $groupId, description: $description, isRequired: $isRequired, needRecognition: $needRecognition, recognitionType: $recognitionType, stage: $stage, order: $order, tips: $tips, retryPrompt: $retryPrompt, isCustom: $isCustom, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 照片分组模型
/// 手动实现freezed风格的不可变数据类
class PhotoGroupModel {
  const PhotoGroupModel({
    required this.id,
    required this.name,
    this.description = '',
    this.icon = '📷',
    this.color = 0xFF2196F3,
    this.isRequired = true,
    this.includeInStatistics = true,
    this.stage = 1,
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String name;
  final String description;
  final String icon;
  final int color;
  final bool isRequired;
  final bool includeInStatistics;
  final int stage;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  PhotoGroupModel copyWith({
    String? id,
    String? name,
    String? description,
    String? icon,
    int? color,
    bool? isRequired,
    bool? includeInStatistics,
    int? stage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PhotoGroupModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isRequired: isRequired ?? this.isRequired,
      includeInStatistics: includeInStatistics ?? this.includeInStatistics,
      stage: stage ?? this.stage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'color': color,
      'isRequired': isRequired,
      'includeInStatistics': includeInStatistics,
      'stage': stage,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory PhotoGroupModel.fromJson(Map<String, dynamic> json) {
    return PhotoGroupModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      icon: json['icon'] as String? ?? '📷',
      color: json['color'] as int? ?? 0xFF2196F3,
      isRequired: json['isRequired'] as bool? ?? true,
      includeInStatistics: json['includeInStatistics'] as bool? ?? true,
      stage: json['stage'] as int? ?? 1,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhotoGroupModel &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.icon == icon &&
        other.color == color &&
        other.isRequired == isRequired &&
        other.includeInStatistics == includeInStatistics &&
        other.stage == stage &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      icon,
      color,
      isRequired,
      includeInStatistics,
      stage,
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'PhotoGroupModel(id: $id, name: $name, description: $description, icon: $icon, color: $color, isRequired: $isRequired, includeInStatistics: $includeInStatistics, stage: $stage, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 识别类型枚举
enum RecognitionType {
  none, // 不识别
  qrCode, // 二维码识别
  barcode, // 条形码识别
  text, // 文字识别(OCR)
  mixed, // 混合识别(支持多种类型)
}

/// 角色配置模型
/// 手动实现freezed风格的不可变数据类
class RoleConfig {
  const RoleConfig({
    required this.id,
    required this.name,
    this.description = '',
    this.permissions = const [],
    this.restrictions = const [],
    this.isActive = true,
    this.level = 1,
    this.maxUsers = 100,
    this.createdAt,
    this.updatedAt,
  });

  final String id;
  final String name;
  final String description;
  final List<String> permissions;
  final List<String> restrictions;
  final bool isActive;
  final int level; // 权限级别
  final int maxUsers; // 最大用户数
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  RoleConfig copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? permissions,
    List<String>? restrictions,
    bool? isActive,
    int? level,
    int? maxUsers,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RoleConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      permissions: permissions ?? this.permissions,
      restrictions: restrictions ?? this.restrictions,
      isActive: isActive ?? this.isActive,
      level: level ?? this.level,
      maxUsers: maxUsers ?? this.maxUsers,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'permissions': permissions,
      'restrictions': restrictions,
      'isActive': isActive,
      'level': level,
      'maxUsers': maxUsers,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory RoleConfig.fromJson(Map<String, dynamic> json) {
    return RoleConfig(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      permissions: (json['permissions'] as List<dynamic>?)
          ?.cast<String>() ?? const [],
      restrictions: (json['restrictions'] as List<dynamic>?)
          ?.cast<String>() ?? const [],
      isActive: json['isActive'] as bool? ?? true,
      level: json['level'] as int? ?? 1,
      maxUsers: json['maxUsers'] as int? ?? 100,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RoleConfig &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        _listEquals(other.permissions, permissions) &&
        _listEquals(other.restrictions, restrictions) &&
        other.isActive == isActive &&
        other.level == level &&
        other.maxUsers == maxUsers &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      Object.hashAll(permissions),
      Object.hashAll(restrictions),
      isActive,
      level,
      maxUsers,
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'RoleConfig(id: $id, name: $name, description: $description, permissions: $permissions, restrictions: $restrictions, isActive: $isActive, level: $level, maxUsers: $maxUsers, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 系统配置模型
/// 手动实现freezed风格的不可变数据类
class SystemConfig {
  const SystemConfig({
    required this.key,
    required this.value,
    this.category = 'general',
    this.description = '',
    this.dataType = 'string',
    this.isReadOnly = false,
    this.isEncrypted = false,
    this.validationRules = const [],
    this.defaultValue,
    this.createdAt,
    this.updatedAt,
  });

  final String key;
  final dynamic value;
  final String category;
  final String description;
  final String dataType; // 'string', 'int', 'bool', 'double', 'json'
  final bool isReadOnly;
  final bool isEncrypted;
  final List<String> validationRules;
  final dynamic defaultValue;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  /// 创建副本并修改指定字段
  SystemConfig copyWith({
    String? key,
    dynamic value,
    String? category,
    String? description,
    String? dataType,
    bool? isReadOnly,
    bool? isEncrypted,
    List<String>? validationRules,
    dynamic defaultValue,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SystemConfig(
      key: key ?? this.key,
      value: value ?? this.value,
      category: category ?? this.category,
      description: description ?? this.description,
      dataType: dataType ?? this.dataType,
      isReadOnly: isReadOnly ?? this.isReadOnly,
      isEncrypted: isEncrypted ?? this.isEncrypted,
      validationRules: validationRules ?? this.validationRules,
      defaultValue: defaultValue ?? this.defaultValue,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'value': value,
      'category': category,
      'description': description,
      'dataType': dataType,
      'isReadOnly': isReadOnly,
      'isEncrypted': isEncrypted,
      'validationRules': validationRules,
      'defaultValue': defaultValue,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// JSON反序列化
  factory SystemConfig.fromJson(Map<String, dynamic> json) {
    return SystemConfig(
      key: json['key'] as String,
      value: json['value'],
      category: json['category'] as String? ?? 'general',
      description: json['description'] as String? ?? '',
      dataType: json['dataType'] as String? ?? 'string',
      isReadOnly: json['isReadOnly'] as bool? ?? false,
      isEncrypted: json['isEncrypted'] as bool? ?? false,
      validationRules: (json['validationRules'] as List<dynamic>?)
          ?.cast<String>() ?? const [],
      defaultValue: json['defaultValue'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
    );
  }

  /// 相等性比较
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemConfig &&
        other.key == key &&
        other.value == value &&
        other.category == category &&
        other.description == description &&
        other.dataType == dataType &&
        other.isReadOnly == isReadOnly &&
        other.isEncrypted == isEncrypted &&
        _listEquals(other.validationRules, validationRules) &&
        other.defaultValue == defaultValue &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  /// 哈希码
  @override
  int get hashCode {
    return Object.hash(
      key,
      value,
      category,
      description,
      dataType,
      isReadOnly,
      isEncrypted,
      Object.hashAll(validationRules),
      defaultValue,
      createdAt,
      updatedAt,
    );
  }

  /// 字符串表示
  @override
  String toString() {
    return 'SystemConfig(key: $key, value: $value, category: $category, description: $description, dataType: $dataType, isReadOnly: $isReadOnly, isEncrypted: $isEncrypted, validationRules: $validationRules, defaultValue: $defaultValue, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// 列表相等性比较辅助函数
bool _listEquals<T>(List<T>? a, List<T>? b) {
  if (a == null) return b == null;
  if (b == null || a.length != b.length) return false;
  for (int index = 0; index < a.length; index += 1) {
    if (a[index] != b[index]) return false;
  }
  return true;
}
