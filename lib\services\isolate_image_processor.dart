import 'dart:isolate';
import 'dart:io';
import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:loadguard/utils/app_logger.dart';

/// 🚀 【Isolate图像处理服务】
/// 
/// 解决Flutter主线程阻塞的根本性方案
/// 在独立的Isolate中执行耗时的图像处理操作
/// 
/// 核心优势：
/// 1. 真正的并行处理 - 不阻塞主线程UI
/// 2. 独立内存空间 - 避免主线程内存压力
/// 3. 高效通信机制 - 通过SendPort/ReceivePort通信
/// 4. 容错处理 - Isolate崩溃不影响主应用
class IsolateImageProcessor {
  static IsolateImageProcessor? _instance;
  static IsolateImageProcessor get instance {
    _instance ??= IsolateImageProcessor._();
    return _instance!;
  }
  
  IsolateImageProcessor._();
  
  Isolate? _isolate;
  SendPort? _sendPort;
  ReceivePort? _receivePort;
  bool _isInitialized = false;
  int _taskId = 0;
  
  // 任务回调映射
  final Map<int, Function(dynamic)> _pendingTasks = {};
  
  /// 初始化Isolate处理器
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('🚀 初始化Isolate图像处理器...');
    
    try {
      _receivePort = ReceivePort();
      
      // 启动Isolate
      _isolate = await Isolate.spawn(
        _isolateEntryPoint,
        _receivePort!.sendPort,
        debugName: 'ImageProcessingIsolate',
      );
      
      // 监听Isolate消息
      _receivePort!.listen(_handleIsolateMessage);
      
      // 等待Isolate初始化完成
      final completer = Completer<void>();
      _pendingTasks[0] = (result) {
        if (result['type'] == 'initialized') {
          _sendPort = result['sendPort'];
          _isInitialized = true;
          completer.complete();
          AppLogger.info('✅ Isolate图像处理器初始化完成');
        }
      };
      
      await completer.future.timeout(const Duration(seconds: 10));
    } catch (e) {
      AppLogger.error('❌ Isolate初始化失败: $e');
      rethrow;
    }
  }
  
  /// 🔵 在Isolate中处理蓝光背景
  Future<String> processBlueBackgroundInIsolate(String imagePath) async {
    if (!_isInitialized) await initialize();
    
    final taskId = ++_taskId;
    final completer = Completer<String>();
    
    _pendingTasks[taskId] = (result) {
      if (result['success']) {
        completer.complete(result['outputPath']);
      } else {
        completer.completeError(result['error']);
      }
    };
    
    // 发送处理任务到Isolate
    _sendPort!.send({
      'type': 'processBlueBackground',
      'taskId': taskId,
      'imagePath': imagePath,
    });
    
    return await completer.future.timeout(const Duration(seconds: 30));
  }
  
  /// ✨ 在Isolate中处理反光抑制
  Future<String> suppressReflectionInIsolate(String imagePath) async {
    if (!_isInitialized) await initialize();
    
    final taskId = ++_taskId;
    final completer = Completer<String>();
    
    _pendingTasks[taskId] = (result) {
      if (result['success']) {
        completer.complete(result['outputPath']);
      } else {
        completer.completeError(result['error']);
      }
    };
    
    _sendPort!.send({
      'type': 'suppressReflection',
      'taskId': taskId,
      'imagePath': imagePath,
    });
    
    return await completer.future.timeout(const Duration(seconds: 30));
  }
  
  /// 🔧 智能图像预处理（包含多种算法）
  Future<String> smartPreprocessInIsolate(
    String imagePath, {
    bool enableBlueBackground = true,
    bool enableReflectionSuppression = true,
    bool enableAutoEnhancement = true,
  }) async {
    if (!_isInitialized) await initialize();
    
    final taskId = ++_taskId;
    final completer = Completer<String>();
    
    _pendingTasks[taskId] = (result) {
      if (result['success']) {
        completer.complete(result['outputPath']);
      } else {
        completer.completeError(result['error']);
      }
    };
    
    _sendPort!.send({
      'type': 'smartPreprocess',
      'taskId': taskId,
      'imagePath': imagePath,
      'enableBlueBackground': enableBlueBackground,
      'enableReflectionSuppression': enableReflectionSuppression,
      'enableAutoEnhancement': enableAutoEnhancement,
    });
    
    return await completer.future.timeout(const Duration(seconds: 45));
  }
  
  /// 处理Isolate消息
  void _handleIsolateMessage(dynamic message) {
    final taskId = message['taskId'] as int;
    final callback = _pendingTasks.remove(taskId);
    callback?.call(message);
  }
  
  /// 销毁Isolate
  void dispose() {
    _isolate?.kill(priority: Isolate.immediate);
    _receivePort?.close();
    _pendingTasks.clear();
    _isInitialized = false;
    AppLogger.info('🧹 Isolate图像处理器已销毁');
  }
  
  bool get isInitialized => _isInitialized;
}

/// 🔧 Isolate入口点 - 独立运行的图像处理逻辑
void _isolateEntryPoint(SendPort mainSendPort) async {
  final receivePort = ReceivePort();
  
  // 发送初始化消息到主线程
  mainSendPort.send({
    'type': 'initialized',
    'taskId': 0,
    'sendPort': receivePort.sendPort,
  });
  
  // 监听主线程消息
  await for (final message in receivePort) {
    try {
      await _processIsolateTask(message, mainSendPort);
    } catch (e) {
      // 发送错误结果
      mainSendPort.send({
        'taskId': message['taskId'],
        'success': false,
        'error': e.toString(),
      });
    }
  }
}

/// 🔧 在Isolate中处理具体任务
Future<void> _processIsolateTask(
  Map<String, dynamic> message,
  SendPort mainSendPort,
) async {
  final type = message['type'] as String;
  final taskId = message['taskId'] as int;
  final imagePath = message['imagePath'] as String;
  
  String? outputPath;
  
  switch (type) {
    case 'processBlueBackground':
      outputPath = await _processBlueBackgroundIsolate(imagePath);
      break;
      
    case 'suppressReflection':
      outputPath = await _suppressReflectionIsolate(imagePath);
      break;
      
    case 'smartPreprocess':
      outputPath = await _smartPreprocessIsolate(
        imagePath,
        message['enableBlueBackground'] as bool,
        message['enableReflectionSuppression'] as bool,
        message['enableAutoEnhancement'] as bool,
      );
      break;
      
    default:
      throw Exception('未知任务类型: $type');
  }
  
  // 发送成功结果
  mainSendPort.send({
    'taskId': taskId,
    'success': true,
    'outputPath': outputPath,
  });
}

/// 🔵 Isolate中的蓝光处理实现
Future<String> _processBlueBackgroundIsolate(String imagePath) async {
  final bytes = await File(imagePath).readAsBytes();
  var image = img.decodeImage(bytes);
  if (image == null) throw Exception('无法解码图像');
  
  // 🔵 优化的蓝光处理算法 - 专为Isolate优化
  image = _optimizedBlueBackgroundProcessing(image);
  
  // 生成输出路径
  final outputPath = imagePath.replaceAll('.jpg', '_blue_processed.jpg');
  final processedBytes = img.encodeJpg(image, quality: 90);
  await File(outputPath).writeAsBytes(processedBytes);
  
  return outputPath;
}

/// ✨ Isolate中的反光抑制实现
Future<String> _suppressReflectionIsolate(String imagePath) async {
  final bytes = await File(imagePath).readAsBytes();
  var image = img.decodeImage(bytes);
  if (image == null) throw Exception('无法解码图像');
  
  // ✨ 优化的反光抑制算法
  image = _optimizedReflectionSuppression(image);
  
  final outputPath = imagePath.replaceAll('.jpg', '_reflection_suppressed.jpg');
  final processedBytes = img.encodeJpg(image, quality: 90);
  await File(outputPath).writeAsBytes(processedBytes);
  
  return outputPath;
}

/// 🧠 智能预处理 - 根据图像特征自动选择算法
Future<String> _smartPreprocessIsolate(
  String imagePath,
  bool enableBlueBackground,
  bool enableReflectionSuppression,
  bool enableAutoEnhancement,
) async {
  final bytes = await File(imagePath).readAsBytes();
  var image = img.decodeImage(bytes);
  if (image == null) throw Exception('无法解码图像');
  
  // 1. 图像特征分析
  final features = _analyzeImageFeatures(image);
  
  // 2. 智能算法选择
  if (enableBlueBackground && (features['hasBlueBackground'] ?? false)) {
    image = _optimizedBlueBackgroundProcessing(image);
  }
  
  if (enableReflectionSuppression && (features['hasReflection'] ?? false)) {
    image = _optimizedReflectionSuppression(image);
  }
  
  if (enableAutoEnhancement && (features['needsEnhancement'] ?? false)) {
    image = _autoEnhanceImage(image);
  }
  
  final outputPath = imagePath.replaceAll('.jpg', '_smart_processed.jpg');
  final processedBytes = img.encodeJpg(image, quality: 92);
  await File(outputPath).writeAsBytes(processedBytes);
  
  return outputPath;
}

/// 📊 图像特征分析
Map<String, bool> _analyzeImageFeatures(img.Image image) {
  // 快速采样分析（每16个像素采样一次，提高速度）
  int bluePixels = 0;
  int brightPixels = 0;
  int totalSamples = 0;
  
  for (int y = 0; y < image.height; y += 16) {
    for (int x = 0; x < image.width; x += 16) {
      final pixel = image.getPixel(x, y);
      totalSamples++;
      
      // 检测蓝色像素
      if (pixel.b > pixel.r * 1.5 && pixel.b > pixel.g * 1.2) {
        bluePixels++;
      }
      
      // 检测高亮像素（可能是反光）
      final brightness = (pixel.r + pixel.g + pixel.b) / 3;
      if (brightness > 200) {
        brightPixels++;
      }
    }
  }
  
  final blueRatio = bluePixels / totalSamples;
  final brightRatio = brightPixels / totalSamples;
  
  return {
    'hasBlueBackground': blueRatio > 0.3,
    'hasReflection': brightRatio > 0.15,
    'needsEnhancement': brightRatio < 0.05, // 图像可能过暗
  };
}

/// 🔵 优化的蓝光处理算法（内存友好）
img.Image _optimizedBlueBackgroundProcessing(img.Image image) {
  // 创建处理后的图像
  final processed = img.Image.from(image);
  
  // 并行处理每个像素（在Isolate中安全）
  for (int y = 0; y < processed.height; y++) {
    for (int x = 0; x < processed.width; x++) {
      final pixel = processed.getPixel(x, y);
      
      // 蓝色通道抑制和对比度增强
      final r = (pixel.r * 1.2).clamp(0, 255).toInt();
      final g = (pixel.g * 1.15).clamp(0, 255).toInt();
      final b = (pixel.b * 0.7).clamp(0, 255).toInt();
      
      // 设置新像素值
      processed.setPixel(x, y, img.ColorRgb8(r, g, b));
    }
  }
  
  return processed;
}

/// ✨ 优化的反光抑制算法
img.Image _optimizedReflectionSuppression(img.Image image) {
  final processed = img.Image.from(image);
  
  for (int y = 1; y < processed.height - 1; y++) {
    for (int x = 1; x < processed.width - 1; x++) {
      final pixel = processed.getPixel(x, y);
      final brightness = (pixel.r + pixel.g + pixel.b) / 3;
      
      // 检测过亮像素（反光）
      if (brightness > 180) {
        // 使用周围像素的平均值替代
        final neighbors = [
          processed.getPixel(x-1, y-1), processed.getPixel(x, y-1), processed.getPixel(x+1, y-1),
          processed.getPixel(x-1, y), processed.getPixel(x+1, y),
          processed.getPixel(x-1, y+1), processed.getPixel(x, y+1), processed.getPixel(x+1, y+1),
        ];
        
        final avgR = neighbors.map((p) => p.r).reduce((a, b) => a + b) ~/ neighbors.length;
        final avgG = neighbors.map((p) => p.g).reduce((a, b) => a + b) ~/ neighbors.length;
        final avgB = neighbors.map((p) => p.b).reduce((a, b) => a + b) ~/ neighbors.length;
        
        processed.setPixel(x, y, img.ColorRgb8(avgR, avgG, avgB));
      }
    }
  }
  
  return processed;
}

/// 🎨 自动图像增强
img.Image _autoEnhanceImage(img.Image image) {
  // 自动对比度和亮度调整
  return img.adjustColor(image, 
    contrast: 1.2,
    brightness: 1.1,
    saturation: 1.05,
  );
}