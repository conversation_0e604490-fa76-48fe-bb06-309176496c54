import 'package:flutter/material.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🚀 性能优化的列表组件
/// 
/// 支持虚拟滚动、分页加载、预加载等性能优化特性
class PerformanceOptimizedList<T> extends StatefulWidget {
  /// 数据项列表
  final List<T> items;
  
  /// 列表项构建器
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  
  /// 每页加载数量
  final int pageSize;
  
  /// 预加载阈值（距离底部多少项时开始预加载）
  final int preloadThreshold;
  
  /// 加载更多回调
  final Future<List<T>> Function(int page)? onLoadMore;
  
  /// 是否启用虚拟滚动
  final bool enableVirtualScrolling;
  
  /// 列表项高度（启用虚拟滚动时必需）
  final double? itemHeight;
  
  /// 加载指示器
  final Widget? loadingIndicator;
  
  /// 空状态组件
  final Widget? emptyWidget;
  
  /// 错误状态组件
  final Widget Function(String error)? errorBuilder;
  
  /// 滚动控制器
  final ScrollController? scrollController;

  const PerformanceOptimizedList({
    Key? key,
    required this.items,
    required this.itemBuilder,
    this.pageSize = 50,
    this.preloadThreshold = 5,
    this.onLoadMore,
    this.enableVirtualScrolling = false,
    this.itemHeight,
    this.loadingIndicator,
    this.emptyWidget,
    this.errorBuilder,
    this.scrollController,
  }) : super(key: key);

  @override
  State<PerformanceOptimizedList<T>> createState() => _PerformanceOptimizedListState<T>();
}

class _PerformanceOptimizedListState<T> extends State<PerformanceOptimizedList<T>> {
  late ScrollController _scrollController;
  
  /// 当前显示的数据
  List<T> _displayItems = [];
  
  /// 当前页码
  int _currentPage = 0;
  
  /// 是否正在加载
  bool _isLoading = false;
  
  /// 是否还有更多数据
  bool _hasMore = true;
  
  /// 错误信息
  String? _error;
  
  /// 虚拟滚动相关
  double _scrollOffset = 0.0;
  int _firstVisibleIndex = 0;
  int _lastVisibleIndex = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _scrollController.addListener(_onScroll);
    
    // 初始化显示数据
    _initializeData();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(PerformanceOptimizedList<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 如果数据源发生变化，重新初始化
    if (widget.items != oldWidget.items) {
      _initializeData();
    }
  }

  /// 初始化数据
  void _initializeData() {
    setState(() {
      _displayItems = widget.items.take(widget.pageSize).toList();
      _currentPage = 0;
      _hasMore = widget.items.length > widget.pageSize;
      _error = null;
    });
    
    AppLogger.info('📊 初始化列表数据: ${_displayItems.length}/${widget.items.length}', tag: 'PerformanceList');
  }

  /// 滚动监听
  void _onScroll() {
    _scrollOffset = _scrollController.offset;
    
    if (widget.enableVirtualScrolling && widget.itemHeight != null) {
      _updateVisibleRange();
    }
    
    // 检查是否需要加载更多
    if (_shouldLoadMore()) {
      _loadMore();
    }
  }

  /// 更新可见范围（虚拟滚动）
  void _updateVisibleRange() {
    if (widget.itemHeight == null) return;
    
    final viewportHeight = _scrollController.position.viewportDimension;
    final itemHeight = widget.itemHeight!;
    
    final newFirstIndex = (_scrollOffset / itemHeight).floor().clamp(0, _displayItems.length - 1);
    final visibleCount = (viewportHeight / itemHeight).ceil() + 2; // 额外渲染2个项目
    final newLastIndex = (newFirstIndex + visibleCount).clamp(0, _displayItems.length - 1);
    
    if (newFirstIndex != _firstVisibleIndex || newLastIndex != _lastVisibleIndex) {
      setState(() {
        _firstVisibleIndex = newFirstIndex;
        _lastVisibleIndex = newLastIndex;
      });
    }
  }

  /// 是否应该加载更多
  bool _shouldLoadMore() {
    if (_isLoading || !_hasMore || widget.onLoadMore == null) return false;
    
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    final currentScrollExtent = _scrollController.position.pixels;
    final threshold = maxScrollExtent * 0.8; // 滚动到80%时开始加载
    
    return currentScrollExtent >= threshold;
  }

  /// 加载更多数据
  Future<void> _loadMore() async {
    if (_isLoading || !_hasMore) return;
    
    setState(() {
      _isLoading = true;
      _error = null;
    });
    
    try {
      AppLogger.info('📥 开始加载更多数据: 第${_currentPage + 1}页', tag: 'PerformanceList');
      
      final newItems = await widget.onLoadMore!(_currentPage + 1);
      
      setState(() {
        _displayItems.addAll(newItems);
        _currentPage++;
        _hasMore = newItems.length >= widget.pageSize;
        _isLoading = false;
      });
      
      AppLogger.info('✅ 加载更多数据完成: +${newItems.length}项，总计${_displayItems.length}项', tag: 'PerformanceList');
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
      
      AppLogger.error('❌ 加载更多数据失败: $e', tag: 'PerformanceList');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 空状态
    if (_displayItems.isEmpty && !_isLoading) {
      return widget.emptyWidget ?? _buildDefaultEmptyWidget();
    }
    
    // 错误状态
    if (_error != null && widget.errorBuilder != null) {
      return widget.errorBuilder!(_error!);
    }
    
    // 虚拟滚动模式
    if (widget.enableVirtualScrolling && widget.itemHeight != null) {
      return _buildVirtualScrollList();
    }
    
    // 普通模式
    return _buildNormalList();
  }

  /// 构建虚拟滚动列表
  Widget _buildVirtualScrollList() {
    final itemHeight = widget.itemHeight!;
    final totalHeight = _displayItems.length * itemHeight;
    
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverToBoxAdapter(
          child: SizedBox(
            height: totalHeight,
            child: Stack(
              children: [
                // 渲染可见项目
                for (int i = _firstVisibleIndex; i <= _lastVisibleIndex && i < _displayItems.length; i++)
                  Positioned(
                    top: i * itemHeight,
                    left: 0,
                    right: 0,
                    height: itemHeight,
                    child: widget.itemBuilder(context, _displayItems[i], i),
                  ),
              ],
            ),
          ),
        ),
        
        // 加载指示器
        if (_isLoading)
          SliverToBoxAdapter(
            child: widget.loadingIndicator ?? _buildDefaultLoadingIndicator(),
          ),
      ],
    );
  }

  /// 构建普通列表
  Widget _buildNormalList() {
    return ListView.builder(
      controller: _scrollController,
      itemCount: _displayItems.length + (_isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        // 加载指示器
        if (index >= _displayItems.length) {
          return widget.loadingIndicator ?? _buildDefaultLoadingIndicator();
        }
        
        // 普通列表项
        return widget.itemBuilder(context, _displayItems[index], index);
      },
    );
  }

  /// 默认空状态组件
  Widget _buildDefaultEmptyWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  /// 默认加载指示器
  Widget _buildDefaultLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            SizedBox(width: 12),
            Text('加载中...'),
          ],
        ),
      ),
    );
  }
}

/// 🚀 性能优化器工具类
class PerformanceOptimizer {
  /// 构建虚拟化列表
  static Widget buildVirtualizedList<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    double? itemHeight,
    int pageSize = 50,
    Future<List<T>> Function(int page)? onLoadMore,
    ScrollController? scrollController,
    Widget? loadingIndicator,
    Widget? emptyWidget,
  }) {
    return PerformanceOptimizedList<T>(
      items: items,
      itemBuilder: itemBuilder,
      itemHeight: itemHeight,
      pageSize: pageSize,
      onLoadMore: onLoadMore,
      enableVirtualScrolling: itemHeight != null,
      scrollController: scrollController,
      loadingIndicator: loadingIndicator,
      emptyWidget: emptyWidget,
    );
  }

  /// 构建分页列表
  static Widget buildPaginatedList<T>({
    required List<T> items,
    required Widget Function(BuildContext context, T item, int index) itemBuilder,
    int pageSize = 50,
    Future<List<T>> Function(int page)? onLoadMore,
    ScrollController? scrollController,
    Widget? loadingIndicator,
    Widget? emptyWidget,
  }) {
    return PerformanceOptimizedList<T>(
      items: items,
      itemBuilder: itemBuilder,
      pageSize: pageSize,
      onLoadMore: onLoadMore,
      enableVirtualScrolling: false,
      scrollController: scrollController,
      loadingIndicator: loadingIndicator,
      emptyWidget: emptyWidget,
    );
  }

  /// 批量处理数据
  static List<List<T>> batchProcess<T>(List<T> items, int batchSize) {
    final batches = <List<T>>[];
    for (int i = 0; i < items.length; i += batchSize) {
      final end = (i + batchSize < items.length) ? i + batchSize : items.length;
      batches.add(items.sublist(i, end));
    }
    return batches;
  }

  /// 延迟执行
  static Future<void> delayedExecution(VoidCallback callback, {Duration delay = const Duration(milliseconds: 100)}) async {
    await Future.delayed(delay);
    callback();
  }
}

/// 📊 性能监控器
class PerformanceMonitor {
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, List<int>> _durations = {};

  /// 开始监控
  static void startMonitoring(String operation) {
    _startTimes[operation] = DateTime.now();
  }

  /// 结束监控
  static void endMonitoring(String operation) {
    final startTime = _startTimes[operation];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      _durations.putIfAbsent(operation, () => []).add(duration);
      _startTimes.remove(operation);
      
      AppLogger.info('⏱️ 性能监控 [$operation]: ${duration}ms', tag: 'Performance');
    }
  }

  /// 获取性能统计
  static Map<String, dynamic> getStats() {
    final stats = <String, dynamic>{};
    
    for (final entry in _durations.entries) {
      final durations = entry.value;
      if (durations.isNotEmpty) {
        final avg = durations.reduce((a, b) => a + b) / durations.length;
        final min = durations.reduce((a, b) => a < b ? a : b);
        final max = durations.reduce((a, b) => a > b ? a : b);
        
        stats[entry.key] = {
          'average': avg.round(),
          'min': min,
          'max': max,
          'count': durations.length,
        };
      }
    }
    
    return stats;
  }

  /// 清除统计数据
  static void clearStats() {
    _durations.clear();
    _startTimes.clear();
  }
}
