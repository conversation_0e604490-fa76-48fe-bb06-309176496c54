import 'package:hive/hive.dart';

part 'preset_product_model.g.dart';

/// 🎯 预设产品牌号模型 - Hive存储版本
/// 
/// 从硬编码迁移到Hive数据库存储，支持：
/// - 动态添加/删除产品牌号
/// - 批量导入/导出
/// - 优先级管理
/// - 批次前缀管理
@HiveType(typeId: 10)
class PresetProductModel extends HiveObject {
  /// 产品代码（主键）
  @HiveField(0)
  String code;
  
  /// 产品名称
  @HiveField(1)
  String name;
  
  /// 产品类别
  @HiveField(2)
  String category;
  
  /// 常用批次前缀
  @HiveField(3)
  List<String> commonBatchPrefixes;
  
  /// 优先级（1-10，10为最高）
  @HiveField(4)
  int priority;
  
  /// 是否启用
  @HiveField(5)
  bool isEnabled;
  
  /// 创建时间
  @HiveField(6)
  DateTime createdAt;
  
  /// 更新时间
  @HiveField(7)
  DateTime updatedAt;
  
  /// 使用次数（统计用）
  @HiveField(8)
  int usageCount;
  
  /// 备注信息
  @HiveField(9)
  String? remarks;
  
  PresetProductModel({
    required this.code,
    required this.name,
    required this.category,
    required this.commonBatchPrefixes,
    this.priority = 5,
    this.isEnabled = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.usageCount = 0,
    this.remarks,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();
  
  /// 从旧的ProductInfo转换
  factory PresetProductModel.fromProductInfo(String code, ProductInfo info) {
    return PresetProductModel(
      code: code,
      name: info.name,
      category: info.category,
      commonBatchPrefixes: List<String>.from(info.commonBatchPrefixes),
      priority: info.priority,
      isEnabled: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      usageCount: 0,
    );
  }
  
  /// 转换为ProductInfo（向后兼容）
  ProductInfo toProductInfo() {
    return ProductInfo(
      code: code,
      name: name,
      category: category,
      commonBatchPrefixes: commonBatchPrefixes,
      priority: priority,
    );
  }
  
  /// 更新使用次数
  void incrementUsage() {
    usageCount++;
    updatedAt = DateTime.now();
    save(); // 自动保存到Hive
  }
  
  /// 更新产品信息
  void updateInfo({
    String? name,
    String? category,
    List<String>? commonBatchPrefixes,
    int? priority,
    bool? isEnabled,
    String? remarks,
  }) {
    if (name != null) this.name = name;
    if (category != null) this.category = category;
    if (commonBatchPrefixes != null) this.commonBatchPrefixes = commonBatchPrefixes;
    if (priority != null) this.priority = priority;
    if (isEnabled != null) this.isEnabled = isEnabled;
    if (remarks != null) this.remarks = remarks;
    
    updatedAt = DateTime.now();
    save(); // 自动保存到Hive
  }
  
  /// 检查是否匹配批次前缀
  bool matchesBatchPrefix(String batchNumber) {
    if (batchNumber.length < 6) return false;
    final prefix = batchNumber.substring(0, 6);
    return commonBatchPrefixes.contains(prefix);
  }
  
  /// 计算与识别文本的相似度
  double calculateSimilarity(String recognizedText) {
    final cleanText = recognizedText.toUpperCase().trim();
    
    // 完全匹配
    if (cleanText == code) return 1.0;
    
    // 包含匹配
    if (cleanText.contains(code) || code.contains(cleanText)) {
      return 0.8;
    }
    
    // Levenshtein距离相似度
    final distance = _levenshteinDistance(cleanText, code);
    final maxLength = [cleanText.length, code.length].reduce((a, b) => a > b ? a : b);
    
    if (maxLength == 0) return 0.0;
    return 1.0 - (distance / maxLength);
  }
  
  /// 计算Levenshtein距离
  int _levenshteinDistance(String s1, String s2) {
    if (s1.isEmpty) return s2.length;
    if (s2.isEmpty) return s1.length;
    
    List<int> v0 = List<int>.filled(s2.length + 1, 0);
    List<int> v1 = List<int>.filled(s2.length + 1, 0);
    
    for (int i = 0; i <= s2.length; i++) {
      v0[i] = i;
    }
    
    for (int i = 0; i < s1.length; i++) {
      v1[0] = i + 1;
      
      for (int j = 0; j < s2.length; j++) {
        int cost = s1[i] == s2[j] ? 0 : 1;
        v1[j + 1] = [v1[j] + 1, v0[j + 1] + 1, v0[j] + cost]
            .reduce((a, b) => a < b ? a : b);
      }
      
      List<int> temp = v0;
      v0 = v1;
      v1 = temp;
    }
    
    return v0[s2.length];
  }
  
  @override
  String toString() {
    return 'PresetProductModel(code: $code, name: $name, category: $category, priority: $priority, enabled: $isEnabled)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PresetProductModel && other.code == code;
  }
  
  @override
  int get hashCode => code.hashCode;
}

/// 预设批次号模型
@HiveType(typeId: 11)
class PresetBatchModel extends HiveObject {
  /// 批次号
  @HiveField(0)
  String batchNumber;
  
  /// 关联的产品代码
  @HiveField(1)
  String productCode;
  
  /// 是否启用
  @HiveField(2)
  bool isEnabled;
  
  /// 创建时间
  @HiveField(3)
  DateTime createdAt;
  
  /// 使用次数
  @HiveField(4)
  int usageCount;
  
  /// 备注
  @HiveField(5)
  String? remarks;
  
  PresetBatchModel({
    required this.batchNumber,
    required this.productCode,
    this.isEnabled = true,
    DateTime? createdAt,
    this.usageCount = 0,
    this.remarks,
  }) : createdAt = createdAt ?? DateTime.now();
  
  /// 更新使用次数
  void incrementUsage() {
    usageCount++;
    save();
  }
  
  /// 验证批次号格式
  bool isValidFormat() {
    final pattern = RegExp(r'^\d{6}[A-Z]\d{5}$');
    return pattern.hasMatch(batchNumber);
  }
  
  /// 解析批次号结构
  Map<String, String>? parseBatchStructure() {
    if (!isValidFormat()) return null;
    
    return {
      'date': batchNumber.substring(0, 6),
      'letter': batchNumber.substring(6, 7),
      'sequence': batchNumber.substring(7, 12),
    };
  }
  
  @override
  String toString() {
    return 'PresetBatchModel(batch: $batchNumber, product: $productCode, enabled: $isEnabled)';
  }
}

/// 产品类别枚举
@HiveType(typeId: 12)
enum ProductCategory {
  @HiveField(0)
  lldpe('LLDPE', '线性低密度聚乙烯'),
  
  @HiveField(1)
  hdpe('HDPE', '高密度聚乙烯'),
  
  @HiveField(2)
  pp('PP', '聚丙烯'),
  
  @HiveField(3)
  ps('PS', '聚苯乙烯'),
  
  @HiveField(4)
  abs('ABS', 'ABS树脂'),
  
  @HiveField(5)
  pvc('PVC', '聚氯乙烯'),
  
  @HiveField(6)
  other('OTHER', '其他');
  
  const ProductCategory(this.code, this.name);
  
  final String code;
  final String name;
  
  @override
  String toString() => name;
}

/// 向后兼容的ProductInfo类
class ProductInfo {
  final String code;
  final String name;
  final String category;
  final List<String> commonBatchPrefixes;
  final int priority;
  
  const ProductInfo({
    required this.code,
    required this.name,
    required this.category,
    required this.commonBatchPrefixes,
    required this.priority,
  });
}
