import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/exceptions/app_exceptions.dart';
import 'package:loadguard/services/global_error_handler.dart';

void main() {
  group('GlobalErrorHandler Tests', () {
    late GlobalErrorHandler errorHandler;
    
    setUp(() {
      errorHandler = GlobalErrorHandler.instance;
      errorHandler.initialize();
    });
    
    tearDown(() {
      errorHandler.clearErrorHistory();
    });
    
    group('异常处理测试', () {
      test('should handle AppException correctly', () async {
        final exception = NetworkException.connectionTimeout();
        
        await errorHandler.handleException(
          exception,
          operation: 'test_operation',
          showUserMessage: false,
        );
        
        final history = errorHandler.getErrorHistory();
        expect(history.length, 1);
        expect(history.first.exception, exception);
        expect(history.first.operation, 'test_operation');
      });
      
      test('should convert generic exception to AppException', () async {
        final genericError = Exception('Generic error');
        
        await errorHandler.handleException(
          genericError,
          operation: 'test_operation',
          showUserMessage: false,
        );
        
        final history = errorHandler.getErrorHistory();
        expect(history.length, 1);
        expect(history.first.exception, isA<SystemException>());
        expect(history.first.exception.originalError, genericError);
      });
      
      test('should handle network errors correctly', () async {
        final socketException = Exception('SocketException: Connection failed');
        
        await errorHandler.handleNetworkError(
          socketException,
          operation: 'network_request',
        );
        
        final history = errorHandler.getErrorHistory();
        expect(history.length, 1);
        expect(history.first.exception, isA<NetworkException>());
        expect(history.first.exception.category, ErrorCategory.network);
      });
      
      test('should handle storage errors correctly', () async {
        final fileError = Exception('FileSystemException: Cannot write file');
        
        await errorHandler.handleStorageError(
          fileError,
          operation: 'file_write',
          path: '/test/path',
        );
        
        final history = errorHandler.getErrorHistory();
        expect(history.length, 1);
        expect(history.first.exception, isA<StorageException>());
        expect(history.first.exception.category, ErrorCategory.storage);
      });
      
      test('should handle recognition errors correctly', () async {
        final recognitionError = Exception('ML Kit recognition failed');
        
        await errorHandler.handleRecognitionError(
          recognitionError,
          operation: 'text_recognition',
        );
        
        final history = errorHandler.getErrorHistory();
        expect(history.length, 1);
        expect(history.first.exception, isA<RecognitionException>());
        expect(history.first.exception.category, ErrorCategory.recognition);
      });
    });
    
    group('错误历史管理测试', () {
      test('should maintain error history', () async {
        // 添加多个错误
        for (int i = 0; i < 5; i++) {
          await errorHandler.handleException(
            NetworkException(message: 'Error $i'),
            operation: 'test_$i',
            showUserMessage: false,
          );
        }
        
        final history = errorHandler.getErrorHistory();
        expect(history.length, 5);
        
        // 验证顺序
        for (int i = 0; i < 5; i++) {
          expect(history[i].exception.message, 'Error $i');
          expect(history[i].operation, 'test_$i');
        }
      });
      
      test('should filter error history by time window', () async {
        // 添加错误
        await errorHandler.handleException(
          NetworkException(message: 'Recent error'),
          showUserMessage: false,
        );
        
        // 获取最近1小时的错误
        final recentHistory = errorHandler.getErrorHistory(
          timeWindow: const Duration(hours: 1),
        );
        expect(recentHistory.length, 1);
        
        // 获取最近1毫秒的错误（应该为空）
        final veryRecentHistory = errorHandler.getErrorHistory(
          timeWindow: const Duration(milliseconds: 1),
        );
        expect(veryRecentHistory.length, 0);
      });
      
      test('should filter error history by category', () async {
        // 添加不同类别的错误
        await errorHandler.handleException(
          NetworkException(message: 'Network error'),
          showUserMessage: false,
        );
        await errorHandler.handleException(
          StorageException(message: 'Storage error'),
          showUserMessage: false,
        );
        await errorHandler.handleException(
          RecognitionException(message: 'Recognition error'),
          showUserMessage: false,
        );
        
        final networkErrors = errorHandler.getErrorHistory(
          category: ErrorCategory.network,
        );
        expect(networkErrors.length, 1);
        expect(networkErrors.first.exception.category, ErrorCategory.network);
        
        final storageErrors = errorHandler.getErrorHistory(
          category: ErrorCategory.storage,
        );
        expect(storageErrors.length, 1);
        expect(storageErrors.first.exception.category, ErrorCategory.storage);
      });
      
      test('should filter error history by severity', () async {
        // 添加不同严重程度的错误
        await errorHandler.handleException(
          NetworkException(message: 'Network error'), // medium severity
          showUserMessage: false,
        );
        await errorHandler.handleException(
          SystemException(message: 'System error'), // high severity
          showUserMessage: false,
        );
        
        final mediumErrors = errorHandler.getErrorHistory(
          severity: ErrorSeverity.medium,
        );
        expect(mediumErrors.length, 1);
        expect(mediumErrors.first.exception.severity, ErrorSeverity.medium);
        
        final highErrors = errorHandler.getErrorHistory(
          severity: ErrorSeverity.high,
        );
        expect(highErrors.length, 1);
        expect(highErrors.first.exception.severity, ErrorSeverity.high);
      });
      
      test('should clear error history', () async {
        // 添加错误
        await errorHandler.handleException(
          NetworkException(message: 'Test error'),
          showUserMessage: false,
        );
        
        expect(errorHandler.getErrorHistory().length, 1);
        
        // 清理历史
        errorHandler.clearErrorHistory();
        expect(errorHandler.getErrorHistory().length, 0);
      });
    });
    
    group('错误统计测试', () {
      test('should generate error statistics correctly', () async {
        // 添加不同类型的错误
        await errorHandler.handleException(
          NetworkException(message: 'Network error 1'),
          showUserMessage: false,
        );
        await errorHandler.handleException(
          NetworkException(message: 'Network error 2'),
          showUserMessage: false,
        );
        await errorHandler.handleException(
          StorageException(message: 'Storage error'),
          showUserMessage: false,
        );
        await errorHandler.handleException(
          SystemException(message: 'System error'),
          showUserMessage: false,
        );
        
        final stats = errorHandler.getErrorStatistics();
        
        expect(stats.totalErrors, 4);
        expect(stats.errorsByCategory[ErrorCategory.network], 2);
        expect(stats.errorsByCategory[ErrorCategory.storage], 1);
        expect(stats.errorsByCategory[ErrorCategory.system], 1);
        expect(stats.mostCommonCategory, ErrorCategory.network);
        expect(stats.overallSeverity, ErrorSeverity.high); // 因为有SystemException
      });
      
      test('should return empty statistics when no errors', () {
        final stats = errorHandler.getErrorStatistics();
        
        expect(stats.totalErrors, 0);
        expect(stats.errorsByCategory.isEmpty, true);
        expect(stats.errorsBySeverity.isEmpty, true);
        expect(stats.errorsByCode.isEmpty, true);
      });
      
      test('should calculate error rate correctly', () async {
        // 添加错误
        for (int i = 0; i < 10; i++) {
          await errorHandler.handleException(
            NetworkException(message: 'Error $i'),
            showUserMessage: false,
          );
        }
        
        final stats = errorHandler.getErrorStatistics(
          timeWindow: const Duration(hours: 2),
        );
        
        expect(stats.totalErrors, 10);
        expect(stats.timeWindow, const Duration(hours: 2));
        expect(stats.errorRate, 5.0); // 10 errors / 2 hours = 5 errors/hour
      });
    });
    
    group('错误流测试', () {
      test('should emit error events', () async {
        final events = <ErrorEvent>[];
        final subscription = errorHandler.errorStream.listen(events.add);
        
        final exception = NetworkException(message: 'Test error');
        await errorHandler.handleException(
          exception,
          operation: 'test_operation',
          showUserMessage: false,
        );
        
        // 等待事件处理
        await Future.delayed(const Duration(milliseconds: 10));
        
        expect(events.length, 1);
        expect(events.first.exception, exception);
        expect(events.first.operation, 'test_operation');
        
        await subscription.cancel();
      });
    });
  });
  
  group('AppException Tests', () {
    group('NetworkException Tests', () {
      test('should create connection timeout exception', () {
        final exception = NetworkException.connectionTimeout();
        
        expect(exception.code, 'NETWORK_TIMEOUT');
        expect(exception.category, ErrorCategory.network);
        expect(exception.severity, ErrorSeverity.medium);
        expect(exception.retryable, true);
        expect(exception.actionable, true);
        expect(exception.userMessage, '网络连接超时，请检查网络后重试');
      });
      
      test('should create no connection exception', () {
        final exception = NetworkException.noConnection();
        
        expect(exception.code, 'NO_CONNECTION');
        expect(exception.userMessage, '无网络连接，请检查网络设置');
      });
      
      test('should create server error exception', () {
        final exception = NetworkException.serverError(500);
        
        expect(exception.code, 'SERVER_ERROR_500');
        expect(exception.details?['statusCode'], 500);
        expect(exception.userMessage, '服务器异常，请稍后重试');
      });
    });
    
    group('StorageException Tests', () {
      test('should create read failed exception', () {
        final exception = StorageException.readFailed('/test/path');
        
        expect(exception.code, 'STORAGE_READ_FAILED');
        expect(exception.details?['path'], '/test/path');
        expect(exception.userMessage, '数据读取失败，请重试');
      });
      
      test('should create write failed exception', () {
        final exception = StorageException.writeFailed('/test/path');
        
        expect(exception.code, 'STORAGE_WRITE_FAILED');
        expect(exception.details?['path'], '/test/path');
        expect(exception.userMessage, '数据保存失败，请重试');
      });
      
      test('should create insufficient space exception', () {
        final exception = StorageException.insufficientSpace();
        
        expect(exception.code, 'INSUFFICIENT_SPACE');
        expect(exception.retryable, false);
        expect(exception.userMessage, '存储空间不足，请清理后重试');
      });
    });
    
    group('RecognitionException Tests', () {
      test('should create ML Kit failed exception', () {
        final exception = RecognitionException.mlkitFailed();
        
        expect(exception.code, 'MLKIT_FAILED');
        expect(exception.category, ErrorCategory.recognition);
        expect(exception.userMessage, '文字识别失败，请重新拍照');
      });
      
      test('should create no text found exception', () {
        final exception = RecognitionException.noTextFound();
        
        expect(exception.code, 'NO_TEXT_FOUND');
        expect(exception.retryable, false);
        expect(exception.userMessage, '未识别到文字，请确保图像清晰');
      });
      
      test('should create timeout exception', () {
        final exception = RecognitionException.timeout();
        
        expect(exception.code, 'RECOGNITION_TIMEOUT');
        expect(exception.retryable, true);
        expect(exception.userMessage, '识别超时，请重试');
      });
    });
    
    test('should convert exception to map correctly', () {
      final exception = NetworkException(
        message: 'Test network error',
        code: 'TEST_ERROR',
        details: {'key': 'value'},
        userMessage: 'Test user message',
      );
      
      final map = exception.toMap();
      
      expect(map['type'], 'NetworkException');
      expect(map['message'], 'Test network error');
      expect(map['code'], 'TEST_ERROR');
      expect(map['details'], {'key': 'value'});
      expect(map['userMessage'], 'Test user message');
      expect(map['severity'], 'medium');
      expect(map['category'], 'network');
      expect(map['actionable'], true);
      expect(map['retryable'], true);
    });
    
    test('should provide default user messages', () {
      final networkException = NetworkException(message: 'Test');
      expect(networkException.displayMessage, contains('网络'));
      
      final storageException = StorageException(message: 'Test');
      expect(storageException.displayMessage, contains('存储'));
      
      final recognitionException = RecognitionException(message: 'Test');
      expect(recognitionException.displayMessage, contains('识别'));
    });
  });
}
