import 'package:flutter/material.dart';
import '../utils/theme_colors.dart';

enum CardType { standard, gradient, glass }

class ThemedCard extends StatelessWidget {
  final Widget child;
  final CardType type;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool elevated;

  const ThemedCard({
    Key? key,
    required this.child,
    this.type = CardType.standard,
    this.padding,
    this.margin,
    this.onTap,
    this.elevated = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget cardContent = Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: _getDecoration(),
      child: child,
    );

    if (onTap != null) {
      cardContent = Material(
        color: Colors.transparent,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
        shadowColor: Theme.of(context).colorScheme.shadow,
        elevation: elevated ? 4 : 0,
        borderRadius: BorderRadius.circular(ThemeColors.cardRadius),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(ThemeColors.cardRadius),
          child: cardContent,
        ),
      );
    }

    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      child: cardContent,
    );
  }

  BoxDecoration _getDecoration() {
    switch (type) {
      case CardType.standard:
        return BoxDecoration(
          color: ThemeColors.cardBackground,
          borderRadius: BorderRadius.circular(ThemeColors.cardRadius),
          boxShadow: elevated ? ThemeColors.cardShadow : null,
        );

      case CardType.gradient:
        return BoxDecoration(
          gradient: ThemeColors.primaryGradient,
          borderRadius: BorderRadius.circular(ThemeColors.cardRadius),
          boxShadow: elevated ? ThemeColors.cardShadow : null,
        );

      case CardType.glass:
        return BoxDecoration(
          color: ThemeColors.glassBackground,
          borderRadius: BorderRadius.circular(ThemeColors.cardRadius),
          border: Border.all(
            color: ThemeColors.glassBorder,
            width: 1,
          ),
          boxShadow: elevated ? ThemeColors.cardShadow : null,
        );
    }
  }
}
