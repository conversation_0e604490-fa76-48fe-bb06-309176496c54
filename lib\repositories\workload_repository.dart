import 'package:loadguard/models/workload_models.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/config_models.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 工作量数据仓库接口
/// 定义工作量数据的CRUD操作和统计查询
abstract class WorkloadRepository {
  /// 初始化Repository
  Future<void> initialize();

  /// 计算工作人员统计数据
  Future<Map<String, WorkerStatistics>> calculateWorkerStatistics({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 计算工作量概览
  Future<WorkloadOverview> calculateWorkloadOverview({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 计算效率排名
  Future<List<EfficiencyRanking>> calculateEfficiencyRanking({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  });

  /// 获取完整的工作量统计
  Future<WorkloadStatistics> getWorkloadStatistics({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 获取工作人员详细信息
  Future<WorkerStatistics?> getWorkerStatistics(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 获取工作量趋势数据
  Future<Map<String, double>> getWorkloadTrends(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
    String period = 'daily', // daily, weekly, monthly
  });

  /// 获取团队对比数据
  Future<Map<String, Map<String, dynamic>>> getTeamComparison({
    DateTime? startDate,
    DateTime? endDate,
    String groupBy = 'role', // role, warehouse, group
  });

  /// 验证工作量数据一致性
  Future<Map<String, dynamic>> validateWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
  });

  /// 获取工作量分配历史
  Future<List<Map<String, dynamic>>> getWorkloadAssignmentHistory({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  });

  /// 保存工作量分配
  Future<void> saveWorkloadAssignment(Map<String, dynamic> assignment);

  /// 获取当前工作量分配
  Future<Map<String, dynamic>?> getCurrentWorkloadAssignment();

  /// 清理过期的工作量数据
  Future<void> cleanupExpiredData({
    Duration? retentionPeriod,
  });

  /// 导出工作量数据
  Future<Map<String, dynamic>> exportWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? workerIds,
  });

  /// 导入工作量数据
  Future<void> importWorkloadData(Map<String, dynamic> data);

  /// 获取工作量统计缓存
  Future<WorkloadStatistics?> getCachedStatistics();

  /// 保存工作量统计缓存
  Future<void> setCachedStatistics(WorkloadStatistics statistics);

  /// 清除工作量统计缓存
  Future<void> clearCachedStatistics();

  /// 释放资源
  void dispose();
}

/// 工作量数据仓库实现
/// 基于TaskRepository和现有工作量服务的统一实现
class WorkloadRepositoryImpl implements WorkloadRepository {
  final TaskRepository _taskRepository;
  final dynamic _configDataSource; // 使用dynamic以支持测试

  // 缓存管理
  WorkloadStatistics? _cachedStatistics;
  DateTime? _cacheExpiry;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  WorkloadRepositoryImpl({
    required TaskRepository taskRepository,
    dynamic configDataSource,
  }) : _taskRepository = taskRepository,
       _configDataSource = configDataSource ?? HiveConfigDataSource();

  @override
  Future<void> initialize() async {
    try {
      await _taskRepository.initialize();
      await _configDataSource.initialize();
      AppLogger.info('✅ WorkloadRepository初始化完成');
    } catch (e) {
      AppLogger.error('❌ WorkloadRepository初始化失败', error: e);
      rethrow;
    }
  }

  @override
  Future<Map<String, WorkerStatistics>> calculateWorkerStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final tasks = await _getFilteredTasks(startDate: startDate, endDate: endDate);
      final stats = <String, WorkerStatistics>{};

      AppLogger.info('🔍 开始工作量统计，任务数量: ${tasks.length}');

      // 初始化所有工作人员的统计数据
      final allWorkers = await _getAllWorkers();
      for (final worker in allWorkers) {
        stats[worker.id] = WorkerStatistics(
          workerId: worker.id,
          workerName: worker.name,
          role: worker.role,
          warehouse: worker.warehouse,
          group: worker.group,
          totalTasks: 0,
          completedTasks: 0,
          totalTonnage: 0.0,
          completedTonnage: 0.0,
          efficiency: 0.0,
          averageTonnagePerTask: 0.0,
        );
      }

      // 处理任务数据
      for (final task in tasks) {
        final workload = task.recognitionMetadata?['workload'];
        if (workload == null) continue;

        try {
          final assignment = WorkloadAssignment.fromMap(workload as Map<String, dynamic>);

          for (final record in assignment.records) {
            final workerId = record.workerId;
            if (!stats.containsKey(workerId)) {
              // 如果工作人员不在配置中，创建临时统计
              stats[workerId] = WorkerStatistics(
                workerId: workerId,
                workerName: record.workerName,
                role: record.role,
                warehouse: record.warehouse,
                group: record.group,
                totalTasks: 0,
                completedTasks: 0,
                totalTonnage: 0.0,
                completedTonnage: 0.0,
                efficiency: 0.0,
                averageTonnagePerTask: 0.0,
              );
            }

            final currentStats = stats[workerId]!;

            // 更新统计数据
            stats[workerId] = currentStats.copyWith(
              totalTasks: currentStats.totalTasks + 1,
              totalTonnage: currentStats.totalTonnage + record.allocatedTonnage,
              completedTasks: task.isCompleted
                  ? currentStats.completedTasks + 1
                  : currentStats.completedTasks,
              completedTonnage: task.isCompleted
                  ? currentStats.completedTonnage + record.allocatedTonnage
                  : currentStats.completedTonnage,
            );
          }
        } catch (e) {
          AppLogger.error('处理任务工作量数据失败: $e');
        }
      }

      // 计算效率和平均值
      for (final entry in stats.entries) {
        final workerId = entry.key;
        final workerStats = entry.value;

        final efficiency = workerStats.totalTasks > 0
            ? workerStats.completedTasks / workerStats.totalTasks
            : 0.0;

        final averageTonnagePerTask = workerStats.totalTasks > 0
            ? workerStats.totalTonnage / workerStats.totalTasks
            : 0.0;

        stats[workerId] = workerStats.copyWith(
          efficiency: efficiency,
          averageTonnagePerTask: averageTonnagePerTask,
        );
      }

      AppLogger.info('✅ 工作量统计完成，统计了${stats.length}名工作人员');
      return stats;
    } catch (e) {
      AppLogger.error('❌ 计算工作人员统计失败: $e');
      rethrow;
    }
  }

  @override
  Future<WorkloadOverview> calculateWorkloadOverview({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final tasks = await _getFilteredTasks(startDate: startDate, endDate: endDate);
      final workerStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);

      // 计算活跃工人数量
      final activeWorkers = workerStats.values
          .where((stat) => stat.totalTasks > 0)
          .length;

      // 计算平均效率
      double totalEfficiency = 0.0;
      int efficientWorkers = 0;
      for (final stat in workerStats.values) {
        if (stat.efficiency > 0) {
          totalEfficiency += stat.efficiency;
          efficientWorkers++;
        }
      }

      final averageEfficiency = efficientWorkers > 0 ? totalEfficiency / efficientWorkers : 0.0;

      // 计算吨数统计
      double totalTonnage = 0.0;
      double completedTonnage = 0.0;
      for (final stat in workerStats.values) {
        totalTonnage += stat.totalTonnage;
        completedTonnage += stat.completedTonnage;
      }

      final completionRate = totalTonnage > 0 ? completedTonnage / totalTonnage : 0.0;

      return WorkloadOverview(
        totalTasks: tasks.length,
        completedTasks: tasks.where((t) => t.isCompleted).length,
        activeWorkers: activeWorkers,
        totalWorkers: workerStats.length,
        averageEfficiency: averageEfficiency,
        totalTonnage: totalTonnage,
        completedTonnage: completedTonnage,
        completionRate: completionRate,
      );
    } catch (e) {
      AppLogger.error('❌ 计算工作量概览失败: $e');
      rethrow;
    }
  }

  @override
  Future<List<EfficiencyRanking>> calculateEfficiencyRanking({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async {
    try {
      final workerStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);

      // 过滤有效数据并排序
      final validStats = workerStats.values
          .where((stat) => stat.totalTasks > 0)
          .toList()
        ..sort((a, b) => b.efficiency.compareTo(a.efficiency));

      // 生成排名
      final ranking = <EfficiencyRanking>[];
      for (int i = 0; i < validStats.length && i < limit; i++) {
        final stat = validStats[i];
        ranking.add(EfficiencyRanking(
          workerId: stat.workerId,
          workerName: stat.workerName,
          role: stat.role,
          efficiency: stat.efficiency,
          rank: i + 1,
        ));
      }

      return ranking;
    } catch (e) {
      AppLogger.error('❌ 计算效率排名失败: $e');
      rethrow;
    }
  }

  @override
  Future<WorkloadStatistics> getWorkloadStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // 检查缓存
      if (_isCacheValid() && _cachedStatistics != null) {
        AppLogger.debug('📋 使用缓存的工作量统计数据');
        return _cachedStatistics!;
      }

      // 计算新的统计数据
      final workerStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);
      final overview = await calculateWorkloadOverview(startDate: startDate, endDate: endDate);
      final ranking = await calculateEfficiencyRanking(startDate: startDate, endDate: endDate);

      final statistics = WorkloadStatistics(
        workerStats: workerStats,
        overview: overview,
        efficiencyRanking: ranking,
        lastUpdated: DateTime.now(),
        dateRange: startDate != null || endDate != null
            ? {'startDate': startDate?.toIso8601String(), 'endDate': endDate?.toIso8601String()}
            : null,
      );

      // 更新缓存
      _cachedStatistics = statistics;
      _cacheExpiry = DateTime.now().add(_cacheValidDuration);

      return statistics;
    } catch (e) {
      AppLogger.error('❌ 获取工作量统计失败: $e');
      rethrow;
    }
  }

  @override
  Future<WorkerStatistics?> getWorkerStatistics(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final allStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);
      return allStats[workerId];
    } catch (e) {
      AppLogger.error('❌ 获取工作人员统计失败: $e');
      return null;
    }
  }

  @override
  Future<Map<String, double>> getWorkloadTrends(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
    String period = 'daily',
  }) async {
    try {
      // 这里实现趋势数据计算
      // 目前返回模拟数据，实际项目中需要根据period参数计算真实趋势
      final trends = <String, double>{};

      final now = DateTime.now();
      switch (period) {
        case 'daily':
          for (int i = 6; i >= 0; i--) {
            final date = now.subtract(Duration(days: i));
            final key = '${date.month}/${date.day}';
            trends[key] = (i * 2.5 + 10.0); // 模拟数据
          }
          break;
        case 'weekly':
          for (int i = 3; i >= 0; i--) {
            final key = '第${4-i}周';
            trends[key] = (i * 15.0 + 50.0); // 模拟数据
          }
          break;
        case 'monthly':
          for (int i = 5; i >= 0; i--) {
            final date = DateTime(now.year, now.month - i, 1);
            final key = '${date.month}月';
            trends[key] = (i * 30.0 + 100.0); // 模拟数据
          }
          break;
      }

      return trends;
    } catch (e) {
      AppLogger.error('❌ 获取工作量趋势失败: $e');
      return {};
    }
  }

  @override
  Future<Map<String, Map<String, dynamic>>> getTeamComparison({
    DateTime? startDate,
    DateTime? endDate,
    String groupBy = 'role',
  }) async {
    try {
      final workerStats = await calculateWorkerStatistics(startDate: startDate, endDate: endDate);
      final comparison = <String, Map<String, dynamic>>{};

      // 按指定字段分组
      for (final stat in workerStats.values) {
        String groupKey;
        switch (groupBy) {
          case 'role':
            groupKey = stat.role;
            break;
          case 'warehouse':
            groupKey = stat.warehouse;
            break;
          case 'group':
            groupKey = stat.group;
            break;
          default:
            groupKey = stat.role;
        }

        if (!comparison.containsKey(groupKey)) {
          comparison[groupKey] = {
            'totalTasks': 0,
            'completedTasks': 0,
            'totalTonnage': 0.0,
            'completedTonnage': 0.0,
            'workerCount': 0,
            'averageEfficiency': 0.0,
          };
        }

        final group = comparison[groupKey]!;
        group['totalTasks'] = (group['totalTasks'] as int) + stat.totalTasks;
        group['completedTasks'] = (group['completedTasks'] as int) + stat.completedTasks;
        group['totalTonnage'] = (group['totalTonnage'] as double) + stat.totalTonnage;
        group['completedTonnage'] = (group['completedTonnage'] as double) + stat.completedTonnage;
        group['workerCount'] = (group['workerCount'] as int) + 1;
        group['averageEfficiency'] = (group['averageEfficiency'] as double) + stat.efficiency;
      }

      // 计算平均效率
      for (final group in comparison.values) {
        final workerCount = group['workerCount'] as int;
        if (workerCount > 0) {
          group['averageEfficiency'] = (group['averageEfficiency'] as double) / workerCount;
        }
      }

      return comparison;
    } catch (e) {
      AppLogger.error('❌ 获取团队对比数据失败: $e');
      return {};
    }
  }

  @override
  Future<Map<String, dynamic>> validateWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final tasks = await _getFilteredTasks(startDate: startDate, endDate: endDate);
      final issues = <String>[];
      int tasksWithWorkload = 0;
      int tasksWithoutWorkload = 0;

      for (final task in tasks) {
        final workload = task.recognitionMetadata?['workload'];
        if (workload != null) {
          tasksWithWorkload++;

          try {
            final assignment = WorkloadAssignment.fromMap(workload as Map<String, dynamic>);

            // 验证工作量分配是否合理
            final totalAllocated = assignment.records.fold<double>(
              0.0, (sum, record) => sum + record.allocatedTonnage);
            final expectedTotal = task.quantity * 1.5;

            if ((totalAllocated - expectedTotal).abs() > 0.1) {
              issues.add('任务${task.id}工作量分配不一致: 分配$totalAllocated吨, 期望$expectedTotal吨');
            }
          } catch (e) {
            issues.add('任务${task.id}工作量数据格式错误: $e');
          }
        } else {
          tasksWithoutWorkload++;
        }
      }

      return {
        'totalTasks': tasks.length,
        'tasksWithWorkload': tasksWithWorkload,
        'tasksWithoutWorkload': tasksWithoutWorkload,
        'issues': issues,
        'dataIntegrity': issues.isEmpty ? 'good' : 'warning',
        'lastValidated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('❌ 验证工作量数据失败: $e');
      return {
        'error': e.toString(),
        'dataIntegrity': 'error',
        'lastValidated': DateTime.now().toIso8601String(),
      };
    }
  }

  // 简化实现的其他方法
  @override
  Future<List<Map<String, dynamic>>> getWorkloadAssignmentHistory({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  }) async {
    // 目前返回空列表，实际项目中需要实现完整的历史记录存储
    return [];
  }

  @override
  Future<void> saveWorkloadAssignment(Map<String, dynamic> assignment) async {
    try {
      await _configDataSource.saveConfig('current_workload_assignment', assignment);
      AppLogger.info('✅ 工作量分配已保存');
    } catch (e) {
      AppLogger.error('❌ 保存工作量分配失败: $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> getCurrentWorkloadAssignment() async {
    try {
      return await _configDataSource.getConfig('current_workload_assignment');
    } catch (e) {
      AppLogger.error('❌ 获取当前工作量分配失败: $e');
      return null;
    }
  }

  @override
  Future<void> cleanupExpiredData({Duration? retentionPeriod}) async {
    // 清理过期数据的实现
    final period = retentionPeriod ?? const Duration(days: 90);
    final cutoffDate = DateTime.now().subtract(period);

    AppLogger.info('🧹 清理${cutoffDate.toIso8601String()}之前的工作量数据');
    // 实际实现中需要清理过期的缓存和历史数据
  }

  @override
  Future<Map<String, dynamic>> exportWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? workerIds,
  }) async {
    try {
      final statistics = await getWorkloadStatistics(startDate: startDate, endDate: endDate);

      Map<String, WorkerStatistics> filteredStats = statistics.workerStats;
      if (workerIds != null) {
        filteredStats = Map.fromEntries(
          statistics.workerStats.entries.where((e) => workerIds.contains(e.key))
        );
      }

      return {
        'exportTime': DateTime.now().toIso8601String(),
        'dateRange': statistics.dateRange,
        'overview': statistics.overview.toJson(),
        'workerStatistics': filteredStats.map((k, v) => MapEntry(k, v.toJson())),
        'efficiencyRanking': statistics.efficiencyRanking.map((e) => e.toJson()).toList(),
      };
    } catch (e) {
      AppLogger.error('❌ 导出工作量数据失败: $e');
      rethrow;
    }
  }

  @override
  Future<void> importWorkloadData(Map<String, dynamic> data) async {
    try {
      // 导入数据的实现
      AppLogger.info('📥 导入工作量数据: ${data.keys}');
      // 实际实现中需要验证和导入数据
    } catch (e) {
      AppLogger.error('❌ 导入工作量数据失败: $e');
      rethrow;
    }
  }

  @override
  Future<WorkloadStatistics?> getCachedStatistics() async {
    return _isCacheValid() ? _cachedStatistics : null;
  }

  @override
  Future<void> setCachedStatistics(WorkloadStatistics statistics) async {
    _cachedStatistics = statistics;
    _cacheExpiry = DateTime.now().add(_cacheValidDuration);
  }

  @override
  Future<void> clearCachedStatistics() async {
    _cachedStatistics = null;
    _cacheExpiry = null;
  }

  @override
  void dispose() {
    _cachedStatistics = null;
    _cacheExpiry = null;
    AppLogger.info('🧹 WorkloadRepository资源已释放');
  }

  /// 检查缓存是否有效
  bool _isCacheValid() {
    return _cacheExpiry != null && DateTime.now().isBefore(_cacheExpiry!);
  }

  /// 获取过滤后的任务列表
  Future<List<TaskModel>> _getFilteredTasks({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final allTasks = await _taskRepository.getAllTasks();

    return allTasks.where((task) {
      if (startDate != null && task.createTime.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && task.createTime.isAfter(endDate)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// 获取所有工作人员配置
  Future<List<WorkerConfig>> _getAllWorkers() async {
    try {
      final keys = await _configDataSource.getAllConfigKeys();
      final workers = <WorkerConfig>[];

      for (final key in keys) {
        if (key.startsWith('worker_')) {
          final data = await _configDataSource.getConfig(key);
          if (data != null) {
            try {
              workers.add(WorkerConfig.fromJson(data));
            } catch (e) {
              AppLogger.warning('解析工作人员配置失败: $key - $e');
            }
          }
        }
      }

      return workers;
    } catch (e) {
      AppLogger.error('❌ 获取工作人员配置失败: $e');
      return [];
    }
  }
}

// 工作量分配相关的数据模型

// 临时的WorkloadAssignment类定义（如果不存在的话）
class WorkloadAssignment {
  final List<WorkloadRecord> records;
  final double totalTonnage;
  final int palletCount;
  final DateTime assignedAt;
  final String assignedBy;

  WorkloadAssignment({
    required this.records,
    required this.totalTonnage,
    required this.palletCount,
    required this.assignedAt,
    required this.assignedBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'records': records.map((r) => r.toMap()).toList(),
      'totalTonnage': totalTonnage,
      'palletCount': palletCount,
      'assignedAt': assignedAt.toIso8601String(),
      'assignedBy': assignedBy,
    };
  }

  factory WorkloadAssignment.fromMap(Map<String, dynamic> map) {
    return WorkloadAssignment(
      records: (map['records'] as List).map((r) => WorkloadRecord.fromMap(r)).toList(),
      totalTonnage: map['totalTonnage'],
      palletCount: map['palletCount'],
      assignedAt: DateTime.parse(map['assignedAt']),
      assignedBy: map['assignedBy'],
    );
  }
}

class WorkloadRecord {
  final String workerId;
  final String workerName;
  final String role;
  final String warehouse;
  final String group;
  final double allocatedTonnage;
  final DateTime assignedAt;

  WorkloadRecord({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.allocatedTonnage,
    required this.assignedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'allocatedTonnage': allocatedTonnage,
      'assignedAt': assignedAt.toIso8601String(),
    };
  }

  factory WorkloadRecord.fromMap(Map<String, dynamic> map) {
    return WorkloadRecord(
      workerId: map['workerId'],
      workerName: map['workerName'],
      role: map['role'],
      warehouse: map['warehouse'],
      group: map['group'],
      allocatedTonnage: map['allocatedTonnage'],
      assignedAt: DateTime.parse(map['assignedAt']),
    );
  }
}
