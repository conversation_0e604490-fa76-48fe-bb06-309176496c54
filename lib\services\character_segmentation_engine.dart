import 'dart:io';
import 'dart:math' as math;
import 'package:image/image.dart' as img;
import 'package:flutter/material.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// ✂️ 字符分割识别引擎
/// 
/// 传统的字符分割识别算法
/// 专门处理高密度文本和连续字符
/// 
/// 🎯 核心算法：
/// 1. 水平投影分割行
/// 2. 垂直投影分割字符
/// 3. 连通组件分析
/// 4. 字符轮廓提取
/// 5. 基于规则的字符识别
class CharacterSegmentationEngine {
  bool _isInitialized = false;
  
  /// 初始化引擎
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('✂️ 初始化字符分割识别引擎...');
    
    try {
      _isInitialized = true;
      AppLogger.info('✅ 字符分割引擎初始化完成');
    } catch (e) {
      AppLogger.error('❌ 字符分割引擎初始化失败: $e');
      rethrow;
    }
  }
  
  /// ✂️ 字符分割识别
  Future<List<RecognitionResult>> recognize(String imagePath) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final stopwatch = Stopwatch()..start();
    AppLogger.debug('✂️ 开始字符分割识别: $imagePath');
    
    try {
      // 1. 加载和预处理图像
      final image = await _loadAndPreprocessImage(imagePath);
      
      // 2. 文本行分割
      final textLines = _segmentTextLines(image);
      
      // 3. 字符分割
      final characters = _segmentCharacters(textLines, image);
      
      // 4. 字符识别
      final results = _recognizeCharacters(characters);
      
      stopwatch.stop();
      AppLogger.debug('✅ 字符分割识别完成，耗时: ${stopwatch.elapsedMilliseconds}ms，识别到${results.length}个结果');
      
      return results;
    } catch (e) {
      AppLogger.error('❌ 字符分割识别失败: $e');
      return [];
    }
  }
  
  /// 加载和预处理图像
  Future<img.Image> _loadAndPreprocessImage(String imagePath) async {
    final bytes = await File(imagePath).readAsBytes();
    var image = img.decodeImage(bytes);
    
    if (image == null) {
      throw Exception('无法解码图像: $imagePath');
    }
    
    // 转换为灰度图
    image = img.grayscale(image);
    
    // 二值化
    image = _binarizeImage(image);
    
    // 去噪
    image = _removeNoise(image);
    
    return image;
  }
  
  /// 图像二值化
  img.Image _binarizeImage(img.Image image) {
    // 使用自适应阈值
    return _adaptiveThreshold(image);
  }
  
  /// 自适应阈值
  img.Image _adaptiveThreshold(img.Image image) {
    final result = img.Image(width: image.width, height: image.height);
    const blockSize = 15;
    const c = 10;
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        // 计算局部平均值
        double sum = 0;
        int count = 0;
        
        for (int dy = -blockSize ~/ 2; dy <= blockSize ~/ 2; dy++) {
          for (int dx = -blockSize ~/ 2; dx <= blockSize ~/ 2; dx++) {
            final nx = (x + dx).clamp(0, image.width - 1);
            final ny = (y + dy).clamp(0, image.height - 1);
            sum += image.getPixel(nx, ny).r;
            count++;
          }
        }
        
        final threshold = sum / count - c;
        final pixel = image.getPixel(x, y);
        final value = pixel.r > threshold ? 255 : 0;
        
        result.setPixel(x, y, img.ColorRgb8(value, value, value));
      }
    }
    
    return result;
  }
  
  /// 去噪
  img.Image _removeNoise(img.Image image) {
    // 使用形态学操作去噪
    var result = _morphologyOpen(image, 2);
    result = _morphologyClose(result, 2);
    return result;
  }
  
  /// 形态学开运算
  img.Image _morphologyOpen(img.Image image, int kernelSize) {
    var result = _erode(image, kernelSize);
    result = _dilate(result, kernelSize);
    return result;
  }
  
  /// 形态学闭运算
  img.Image _morphologyClose(img.Image image, int kernelSize) {
    var result = _dilate(image, kernelSize);
    result = _erode(result, kernelSize);
    return result;
  }
  
  /// 腐蚀操作
  img.Image _erode(img.Image image, int kernelSize) {
    final result = img.Image.from(image);
    final radius = kernelSize ~/ 2;
    
    for (int y = radius; y < image.height - radius; y++) {
      for (int x = radius; x < image.width - radius; x++) {
        int minValue = 255;
        
        for (int dy = -radius; dy <= radius; dy++) {
          for (int dx = -radius; dx <= radius; dx++) {
            final pixel = image.getPixel(x + dx, y + dy);
            if (pixel.r.toInt() < minValue) {
              minValue = pixel.r.toInt();
            }
          }
        }
        
        result.setPixel(x, y, img.ColorRgb8(minValue, minValue, minValue));
      }
    }
    
    return result;
  }
  
  /// 膨胀操作
  img.Image _dilate(img.Image image, int kernelSize) {
    final result = img.Image.from(image);
    final radius = kernelSize ~/ 2;
    
    for (int y = radius; y < image.height - radius; y++) {
      for (int x = radius; x < image.width - radius; x++) {
        int maxValue = 0;
        
        for (int dy = -radius; dy <= radius; dy++) {
          for (int dx = -radius; dx <= radius; dx++) {
            final pixel = image.getPixel(x + dx, y + dy);
            if (pixel.r.toInt() > maxValue) {
              maxValue = pixel.r.toInt();
            }
          }
        }
        
        result.setPixel(x, y, img.ColorRgb8(maxValue, maxValue, maxValue));
      }
    }
    
    return result;
  }
  
  /// 分割文本行
  List<Rect> _segmentTextLines(img.Image image) {
    // 水平投影
    final projection = List.filled(image.height, 0);
    
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        if (image.getPixel(x, y).r == 0) { // 黑色像素
          projection[y]++;
        }
      }
    }
    
    // 查找文本行
    final lines = <Rect>[];
    bool inLine = false;
    int lineStart = 0;
    
    for (int y = 0; y < projection.length; y++) {
      if (projection[y] > 0 && !inLine) {
        // 行开始
        inLine = true;
        lineStart = y;
      } else if (projection[y] == 0 && inLine) {
        // 行结束
        inLine = false;
        final lineHeight = y - lineStart;
        
        if (lineHeight > 8) { // 过滤太矮的行
          lines.add(Rect.fromLTRB(
            0,
            lineStart.toDouble(),
            image.width.toDouble(),
            y.toDouble(),
          ));
        }
      }
    }
    
    // 处理最后一行
    if (inLine) {
      final lineHeight = projection.length - lineStart;
      if (lineHeight > 8) {
        lines.add(Rect.fromLTRB(
          0,
          lineStart.toDouble(),
          image.width.toDouble(),
          projection.length.toDouble(),
        ));
      }
    }
    
    return lines;
  }
  
  /// 分割字符
  List<CharacterRegion> _segmentCharacters(List<Rect> textLines, img.Image image) {
    final characters = <CharacterRegion>[];
    
    for (final line in textLines) {
      // 提取文本行图像
      final lineImage = img.copyCrop(
        image,
        x: line.left.toInt(),
        y: line.top.toInt(),
        width: line.width.toInt(),
        height: line.height.toInt(),
      );
      
      // 垂直投影分割字符
      final charRects = _verticalProjectionSegmentation(lineImage);
      
      for (final charRect in charRects) {
        final charImage = img.copyCrop(
          lineImage,
          x: charRect.left.toInt(),
          y: charRect.top.toInt(),
          width: charRect.width.toInt(),
          height: charRect.height.toInt(),
        );
        
        characters.add(CharacterRegion(
          boundingBox: Rect.fromLTRB(
            line.left + charRect.left,
            line.top + charRect.top,
            line.left + charRect.right,
            line.top + charRect.bottom,
          ),
          image: charImage,
        ));
      }
    }
    
    return characters;
  }
  
  /// 垂直投影分割
  List<Rect> _verticalProjectionSegmentation(img.Image image) {
    // 计算垂直投影
    final projection = List.filled(image.width, 0);
    
    for (int x = 0; x < image.width; x++) {
      for (int y = 0; y < image.height; y++) {
        if (image.getPixel(x, y).r == 0) { // 黑色像素
          projection[x]++;
        }
      }
    }
    
    // 查找字符边界
    final segments = <Rect>[];
    bool inCharacter = false;
    int charStart = 0;
    
    for (int x = 0; x < projection.length; x++) {
      if (projection[x] > 0 && !inCharacter) {
        // 字符开始
        inCharacter = true;
        charStart = x;
      } else if (projection[x] == 0 && inCharacter) {
        // 字符结束
        inCharacter = false;
        final charWidth = x - charStart;
        
        if (charWidth > 3) { // 过滤太窄的分割
          segments.add(Rect.fromLTRB(
            charStart.toDouble(),
            0,
            x.toDouble(),
            image.height.toDouble(),
          ));
        }
      }
    }
    
    // 处理最后一个字符
    if (inCharacter) {
      final charWidth = projection.length - charStart;
      if (charWidth > 3) {
        segments.add(Rect.fromLTRB(
          charStart.toDouble(),
          0,
          projection.length.toDouble(),
          image.height.toDouble(),
        ));
      }
    }
    
    return segments;
  }
  
  /// 识别字符
  List<RecognitionResult> _recognizeCharacters(List<CharacterRegion> characters) {
    final results = <RecognitionResult>[];
    
    for (final character in characters) {
      try {
        final recognizedChar = _recognizeSingleCharacter(character);
        
        if (recognizedChar.isNotEmpty) {
          final result = RecognitionResult(
            ocrText: recognizedChar,
            confidence: 60.0, // 字符分割的默认置信度(0-100范围)
            boundingBox: {
              'left': character.boundingBox.left,
              'top': character.boundingBox.top,
              'right': character.boundingBox.right,
              'bottom': character.boundingBox.bottom,
            },
            isQrOcrConsistent: false, // 必需参数
            matchesPreset: false, // 必需参数
            metadata: {
              'recognizedElements': [recognizedChar],
            },
          );
          results.add(result);
        }
      } catch (e) {
        AppLogger.debug('字符识别失败: $e');
      }
    }
    
    return results;
  }
  
  /// 识别单个字符
  String _recognizeSingleCharacter(CharacterRegion character) {
    // 提取字符特征
    final features = _extractCharacterFeatures(character.image);
    
    // 基于规则的识别
    return _ruleBasedRecognition(features);
  }
  
  /// 提取字符特征
  Map<String, double> _extractCharacterFeatures(img.Image charImage) {
    final features = <String, double>{};
    
    // 1. 宽高比
    features['aspectRatio'] = charImage.width / charImage.height;
    
    // 2. 像素密度
    int blackPixels = 0;
    for (int y = 0; y < charImage.height; y++) {
      for (int x = 0; x < charImage.width; x++) {
        if (charImage.getPixel(x, y).r == 0) {
          blackPixels++;
        }
      }
    }
    features['density'] = blackPixels / (charImage.width * charImage.height);
    
    // 3. 水平线数量
    features['horizontalLines'] = _countHorizontalLines(charImage).toDouble();
    
    // 4. 垂直线数量
    features['verticalLines'] = _countVerticalLines(charImage).toDouble();
    
    // 5. 闭合区域数量
    features['closedRegions'] = _countClosedRegions(charImage).toDouble();
    
    return features;
  }
  
  /// 计算水平线数量
  int _countHorizontalLines(img.Image image) {
    int lines = 0;
    
    for (int y = 0; y < image.height; y++) {
      bool inLine = false;
      int lineLength = 0;
      
      for (int x = 0; x < image.width; x++) {
        if (image.getPixel(x, y).r == 0) {
          if (!inLine) {
            inLine = true;
            lineLength = 1;
          } else {
            lineLength++;
          }
        } else {
          if (inLine && lineLength > image.width * 0.3) {
            lines++;
          }
          inLine = false;
          lineLength = 0;
        }
      }
      
      if (inLine && lineLength > image.width * 0.3) {
        lines++;
      }
    }
    
    return lines;
  }
  
  /// 计算垂直线数量
  int _countVerticalLines(img.Image image) {
    int lines = 0;
    
    for (int x = 0; x < image.width; x++) {
      bool inLine = false;
      int lineLength = 0;
      
      for (int y = 0; y < image.height; y++) {
        if (image.getPixel(x, y).r == 0) {
          if (!inLine) {
            inLine = true;
            lineLength = 1;
          } else {
            lineLength++;
          }
        } else {
          if (inLine && lineLength > image.height * 0.3) {
            lines++;
          }
          inLine = false;
          lineLength = 0;
        }
      }
      
      if (inLine && lineLength > image.height * 0.3) {
        lines++;
      }
    }
    
    return lines;
  }
  
  /// 计算闭合区域数量
  int _countClosedRegions(img.Image image) {
    // 简化的闭合区域检测
    // 实际应用中需要更复杂的算法
    return 0;
  }
  
  /// 基于规则的识别
  String _ruleBasedRecognition(Map<String, double> features) {
    final aspectRatio = features['aspectRatio'] ?? 0.0;
    final density = features['density'] ?? 0.0;
    final horizontalLines = features['horizontalLines'] ?? 0.0;
    final verticalLines = features['verticalLines'] ?? 0.0;
    
    // 简化的规则识别
    if (aspectRatio < 0.3 && verticalLines > 0) {
      return '1';
    } else if (aspectRatio > 1.5 && horizontalLines > 1) {
      return '-';
    } else if (density > 0.6) {
      return '8';
    } else if (density < 0.3) {
      return '7';
    }
    
    // 默认返回
    return '?';
  }
  
  /// 释放资源
  Future<void> dispose() async {
    _isInitialized = false;
  }
}

/// 字符区域
class CharacterRegion {
  final Rect boundingBox;
  final img.Image image;
  
  CharacterRegion({
    required this.boundingBox,
    required this.image,
  });
}
