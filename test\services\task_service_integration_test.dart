import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/repositories/hive_task_data_source.dart';
import 'package:loadguard/repositories/shared_prefs_task_data_source.dart';
import 'package:loadguard/repositories/task_repository.dart';

/// 模拟的TaskRepository用于测试
class MockTaskRepository implements TaskRepository {
  final Map<String, TaskModel> _tasks = {};
  String? _currentTaskId;

  // 添加流控制器用于模拟监听
  final StreamController<List<TaskModel>> _tasksController = StreamController.broadcast();
  final StreamController<TaskModel?> _currentTaskController = StreamController.broadcast();

  @override
  Future<void> initialize() async {}

  @override
  Future<List<TaskModel>> getAllTasks() async {
    return _tasks.values.toList()..sort((a, b) => b.createTime.compareTo(a.createTime));
  }

  @override
  Future<TaskModel?> getTaskById(String id) async {
    return _tasks[id];
  }

  @override
  Future<void> saveTask(TaskModel task) async {
    _tasks[task.id] = task;
  }

  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    for (final task in tasks) {
      _tasks[task.id] = task;
    }
  }

  @override
  Future<void> deleteTask(String id) async {
    _tasks.remove(id);
    if (_currentTaskId == id) {
      _currentTaskId = null;
    }
  }

  @override
  Future<void> clearAllTasks() async {
    _tasks.clear();
    _currentTaskId = null;
  }

  @override
  Future<TaskModel?> getCurrentTask() async {
    return _currentTaskId != null ? _tasks[_currentTaskId] : null;
  }

  @override
  Future<void> setCurrentTask(TaskModel? task) async {
    _currentTaskId = task?.id;
    if (task != null) {
      _tasks[task.id] = task;
    }
  }

  @override
  Future<List<TaskModel>> queryTasks({
    DateTime? startDate,
    DateTime? endDate,
    String? template,
    bool? isCompleted,
  }) async {
    var tasks = _tasks.values.toList();
    
    if (startDate != null) {
      tasks = tasks.where((t) => t.createTime.isAfter(startDate)).toList();
    }
    if (endDate != null) {
      tasks = tasks.where((t) => t.createTime.isBefore(endDate)).toList();
    }
    if (template != null) {
      tasks = tasks.where((t) => t.template == template).toList();
    }
    if (isCompleted != null) {
      tasks = tasks.where((t) => t.isCompleted == isCompleted).toList();
    }
    
    return tasks..sort((a, b) => b.createTime.compareTo(a.createTime));
  }

  @override
  Future<Map<String, dynamic>> getTaskStatistics() async {
    final tasks = await getAllTasks();
    return {
      'total': tasks.length,
      'completed': tasks.where((t) => t.isCompleted).length,
      'pending': tasks.where((t) => !t.isCompleted).length,
    };
  }

  @override
  Future<void> migrateData() async {}

  @override
  Future<void> backupData() async {}

  @override
  Future<void> restoreData() async {}

  /// 监听任务列表变化（用于状态管理）
  Stream<List<TaskModel>> watchTasks() {
    return _tasksController.stream;
  }

  /// 监听当前任务变化（用于状态管理）
  Stream<TaskModel?> watchCurrentTask() {
    return _currentTaskController.stream;
  }

  /// 释放资源
  void dispose() {
    _tasksController.close();
    _currentTaskController.close();
  }
}

// 移除TestTaskService，直接测试Repository功能

void main() {
  group('TaskService Integration Tests', () {
    late MockTaskRepository mockRepository;

    setUp(() {
      mockRepository = MockTaskRepository();
    });

    tearDown(() {
      mockRepository.dispose();
    });

    group('Repository基本功能测试', () {
      test('should initialize successfully', () async {
        await mockRepository.initialize();
        final tasks = await mockRepository.getAllTasks();
        expect(tasks, isEmpty);

        final currentTask = await mockRepository.getCurrentTask();
        expect(currentTask, isNull);
      });

      test('should save and retrieve task', () async {
        await mockRepository.initialize();

        final task = TaskModel(
          id: 'test-task',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          participants: ['worker1', 'worker2'],
          createTime: DateTime.now(),
        );

        await mockRepository.saveTask(task);

        // 验证任务已保存
        final savedTask = await mockRepository.getTaskById(task.id);
        expect(savedTask, isNotNull);
        expect(savedTask!.id, task.id);
        expect(savedTask.template, '平板车');
        expect(savedTask.productCode, 'P001');
        expect(savedTask.batchNumber, 'B001');
        expect(savedTask.quantity, 100);
        expect(savedTask.participants, ['worker1', 'worker2']);
      });

      test('should update task', () async {
        await mockRepository.initialize();

        // 创建任务
        final task = TaskModel(
          id: 'test-task',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
        );

        await mockRepository.saveTask(task);

        // 更新任务的批次信息
        final updatedBatch = task.batches.first.copyWith(
          productCode: 'P002',
          plannedQuantity: 200,
        );
        final updatedTask = task.copyWith(
          batches: [updatedBatch],
        );

        await mockRepository.saveTask(updatedTask);

        // 验证更新
        final savedTask = await mockRepository.getTaskById(task.id);
        expect(savedTask, isNotNull);
        expect(savedTask!.batches.first.productCode, 'P002');
        expect(savedTask.batches.first.plannedQuantity, 200);
      });

      test('should set and clear current task', () async {
        await mockRepository.initialize();

        // 创建任务
        final task = TaskModel(
          id: 'test-task',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
        );

        await mockRepository.saveTask(task);

        // 设置当前任务
        await mockRepository.setCurrentTask(task);
        final currentTask = await mockRepository.getCurrentTask();
        expect(currentTask?.id, task.id);

        // 清除当前任务
        await mockRepository.setCurrentTask(null);
        final clearedTask = await mockRepository.getCurrentTask();
        expect(clearedTask, isNull);

        // 重新设置当前任务
        await mockRepository.setCurrentTask(task);
        final resetTask = await mockRepository.getCurrentTask();
        expect(resetTask?.id, task.id);
      });

      test('should delete task', () async {
        await mockRepository.initialize();

        // 创建任务
        final task = TaskModel(
          id: 'test-task',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
        );

        await mockRepository.saveTask(task);
        await mockRepository.setCurrentTask(task);

        // 删除任务
        await mockRepository.deleteTask(task.id);

        // 验证任务已删除
        final deletedTask = await mockRepository.getTaskById(task.id);
        expect(deletedTask, isNull);

        // 验证当前任务已清除
        final currentTask = await mockRepository.getCurrentTask();
        expect(currentTask, isNull);
      });

      test('should handle multiple tasks', () async {
        await mockRepository.initialize();

        // 创建多个任务
        final task1 = TaskModel(
          id: 'task1',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
        );

        final task2 = TaskModel(
          id: 'task2',
          template: '集装箱',
          productCode: 'P002',
          batchNumber: 'B002',
          quantity: 200,
          createTime: DateTime.now().add(Duration(seconds: 1)),
        );

        await mockRepository.saveTask(task1);
        await mockRepository.saveTask(task2);

        // 验证任务列表
        final tasks = await mockRepository.getAllTasks();
        expect(tasks.length, 2);
        expect(tasks.map((t) => t.id), containsAll([task1.id, task2.id]));

        // 验证按创建时间倒序排序（最新的在前面）
        expect(tasks.first.id, task2.id);
      });

      test('should save multiple tasks', () async {
        await mockRepository.initialize();

        // 创建多个任务
        final tasks = [
          TaskModel(
            id: 'task1',
            template: '平板车',
            productCode: 'P001',
            batchNumber: 'B001',
            quantity: 100,
            createTime: DateTime.now(),
          ),
          TaskModel(
            id: 'task2',
            template: '集装箱',
            productCode: 'P002',
            batchNumber: 'B002',
            quantity: 200,
            createTime: DateTime.now(),
          ),
        ];

        // 批量保存
        await mockRepository.saveTasks(tasks);

        // 验证数据已保存
        final allTasks = await mockRepository.getAllTasks();
        expect(allTasks.length, 2);
        expect(allTasks.map((t) => t.id), containsAll(['task1', 'task2']));
      });

      test('should query tasks with filters', () async {
        await mockRepository.initialize();

        // 创建不同类型的任务
        final task1 = TaskModel(
          id: 'task1',
          template: '平板车',
          productCode: 'P001',
          batchNumber: 'B001',
          quantity: 100,
          createTime: DateTime.now(),
        );

        final task2 = TaskModel(
          id: 'task2',
          template: '集装箱',
          productCode: 'P002',
          batchNumber: 'B002',
          quantity: 200,
          createTime: DateTime.now(),
        );

        await mockRepository.saveTask(task1);
        await mockRepository.saveTask(task2);

        // 按模板查询
        final flatbedTasks = await mockRepository.queryTasks(template: '平板车');
        expect(flatbedTasks.length, 1);
        expect(flatbedTasks.first.template, '平板车');

        final containerTasks = await mockRepository.queryTasks(template: '集装箱');
        expect(containerTasks.length, 1);
        expect(containerTasks.first.template, '集装箱');
      });
    });
  });
}
