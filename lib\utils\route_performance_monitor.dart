/// 🆕 16.0.0优化：路由性能监控器
class RoutePerformanceMonitor {
  static final Map<String, DateTime> _routeStartTimes = {};
  static final Map<String, List<Duration>> _routeDurations = {};

  /// 开始监控路由
  static void startMonitoring(String routeName) {
    _routeStartTimes[routeName] = DateTime.now();
  }

  /// 结束监控路由
  static void endMonitoring(String routeName) {
    final startTime = _routeStartTimes[routeName];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      _routeDurations.putIfAbsent(routeName, () => []).add(duration);
      _routeStartTimes.remove(routeName);
    }
  }

  /// 获取路由平均加载时间
  static Duration? getAverageLoadTime(String routeName) {
    final durations = _routeDurations[routeName];
    if (durations == null || durations.isEmpty) {
      return null;
    }

    final totalMicroseconds = durations.fold<int>(
      0,
      (sum, duration) => sum + duration.inMicroseconds,
    );
    return Duration(microseconds: totalMicroseconds ~/ durations.length);
  }

  /// 获取所有路由的性能数据
  static Map<String, Duration?> getAllRoutePerformance() {
    return Map.fromEntries(
      _routeDurations.keys.map(
          (routeName) => MapEntry(routeName, getAverageLoadTime(routeName))),
    );
  }

  /// 清空性能数据
  static void clearPerformanceData() {
    _routeDurations.clear();
    _routeStartTimes.clear();
  }
}
