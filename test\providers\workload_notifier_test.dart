import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/providers/workload_notifier.dart';
import 'package:loadguard/models/workload_models.dart';
import 'package:loadguard/repositories/workload_repository.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/config_models.dart';

/// 模拟的WorkloadRepository用于测试
class MockWorkloadRepository implements WorkloadRepository {
  final Map<String, WorkerStatistics> _workerStats = {};
  WorkloadOverview _overview = const WorkloadOverview(
    totalTasks: 0,
    completedTasks: 0,
    activeWorkers: 0,
    totalWorkers: 0,
    averageEfficiency: 0.0,
    totalTonnage: 0.0,
    completedTonnage: 0.0,
    completionRate: 0.0,
  );
  List<EfficiencyRanking> _ranking = [];
  WorkloadStatistics? _cachedStatistics;

  @override
  Future<void> initialize() async {}

  @override
  Future<Map<String, WorkerStatistics>> calculateWorkerStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async => _workerStats;

  @override
  Future<WorkloadOverview> calculateWorkloadOverview({
    DateTime? startDate,
    DateTime? endDate,
  }) async => _overview;

  @override
  Future<List<EfficiencyRanking>> calculateEfficiencyRanking({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 10,
  }) async => _ranking;

  @override
  Future<WorkloadStatistics> getWorkloadStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return WorkloadStatistics(
      workerStats: _workerStats,
      overview: _overview,
      efficiencyRanking: _ranking,
      lastUpdated: DateTime.now(),
      dateRange: startDate != null || endDate != null 
          ? {'startDate': startDate?.toIso8601String(), 'endDate': endDate?.toIso8601String()}
          : null,
    );
  }

  @override
  Future<WorkerStatistics?> getWorkerStatistics(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async => _workerStats[workerId];

  @override
  Future<Map<String, double>> getWorkloadTrends(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
    String period = 'daily',
  }) async => {'day1': 10.0, 'day2': 15.0, 'day3': 12.0};

  @override
  Future<Map<String, Map<String, dynamic>>> getTeamComparison({
    DateTime? startDate,
    DateTime? endDate,
    String groupBy = 'role',
  }) async => {
    'role1': {'totalTasks': 10, 'completedTasks': 8},
    'role2': {'totalTasks': 15, 'completedTasks': 12},
  };

  @override
  Future<Map<String, dynamic>> validateWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
  }) async => {
    'totalTasks': 25,
    'tasksWithWorkload': 20,
    'tasksWithoutWorkload': 5,
    'issues': [],
    'dataIntegrity': 'good',
  };

  @override
  Future<List<Map<String, dynamic>>> getWorkloadAssignmentHistory({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 50,
  }) async => [];

  @override
  Future<void> saveWorkloadAssignment(Map<String, dynamic> assignment) async {}

  @override
  Future<Map<String, dynamic>?> getCurrentWorkloadAssignment() async => null;

  @override
  Future<void> cleanupExpiredData({Duration? retentionPeriod}) async {}

  @override
  Future<Map<String, dynamic>> exportWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? workerIds,
  }) async => {'exported': true, 'count': _workerStats.length};

  @override
  Future<void> importWorkloadData(Map<String, dynamic> data) async {}

  @override
  Future<WorkloadStatistics?> getCachedStatistics() async => _cachedStatistics;

  @override
  Future<void> setCachedStatistics(WorkloadStatistics statistics) async {
    _cachedStatistics = statistics;
  }

  @override
  Future<void> clearCachedStatistics() async {
    _cachedStatistics = null;
  }

  @override
  void dispose() {}

  // 测试辅助方法
  void setMockData({
    Map<String, WorkerStatistics>? workerStats,
    WorkloadOverview? overview,
    List<EfficiencyRanking>? ranking,
  }) {
    if (workerStats != null) _workerStats.addAll(workerStats);
    if (overview != null) _overview = overview;
    if (ranking != null) _ranking = ranking;
  }
}

/// 模拟的TaskRepository用于测试
class MockTaskRepository implements TaskRepository {
  @override
  Future<void> initialize() async {}

  @override
  Future<List<TaskModel>> getAllTasks() async => [];

  @override
  Future<TaskModel?> getTaskById(String id) async => null;

  @override
  Future<void> saveTask(TaskModel task) async {}

  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {}

  @override
  Future<void> deleteTask(String id) async {}

  @override
  Future<void> clearAllTasks() async {}

  @override
  Future<TaskModel?> getCurrentTask() async => null;

  @override
  Future<void> setCurrentTask(TaskModel? task) async {}

  @override
  Future<List<TaskModel>> queryTasks({
    DateTime? startDate,
    DateTime? endDate,
    String? template,
    bool? isCompleted,
  }) async => [];

  @override
  Future<Map<String, dynamic>> getTaskStatistics() async => {};

  @override
  Future<void> migrateData() async {}

  @override
  Future<void> backupData() async {}

  @override
  Future<void> restoreData() async {}
}

void main() {
  group('WorkloadNotifier Tests', () {
    late ProviderContainer container;
    late MockWorkloadRepository mockWorkloadRepository;
    late MockTaskRepository mockTaskRepository;

    setUp(() {
      mockWorkloadRepository = MockWorkloadRepository();
      mockTaskRepository = MockTaskRepository();
      
      container = ProviderContainer(
        overrides: [
          workloadRepositoryProvider.overrideWithValue(mockWorkloadRepository),
          taskRepositoryProvider.overrideWithValue(mockTaskRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('基本功能测试', () {
      test('should initialize with empty data', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        final state = container.read(workloadNotifierProvider);
        
        expect(state, isA<AsyncLoading>());
        
        // 等待初始化完成
        await container.read(workloadNotifierProvider.future);
        
        final finalState = container.read(workloadNotifierProvider);
        expect(finalState, isA<AsyncData>());
        
        final statistics = finalState.value!;
        expect(statistics.workerStats, isEmpty);
        expect(statistics.overview.totalTasks, 0);
        expect(statistics.efficiencyRanking, isEmpty);
      });

      test('should load workload statistics with mock data', () async {
        // 设置模拟数据
        mockWorkloadRepository.setMockData(
          workerStats: {
            'worker1': const WorkerStatistics(
              workerId: 'worker1',
              workerName: '张三',
              role: '叉车',
              warehouse: '1号库',
              group: '第一组',
              totalTasks: 10,
              completedTasks: 8,
              totalTonnage: 150.0,
              completedTonnage: 120.0,
              efficiency: 0.8,
              averageTonnagePerTask: 15.0,
            ),
          },
          overview: const WorkloadOverview(
            totalTasks: 10,
            completedTasks: 8,
            activeWorkers: 1,
            totalWorkers: 1,
            averageEfficiency: 0.8,
            totalTonnage: 150.0,
            completedTonnage: 120.0,
            completionRate: 0.8,
          ),
          ranking: [
            const EfficiencyRanking(
              workerId: 'worker1',
              workerName: '张三',
              role: '叉车',
              efficiency: 0.8,
              rank: 1,
            ),
          ],
        );

        // 等待数据加载
        final statistics = await container.read(workloadNotifierProvider.future);
        
        expect(statistics.workerStats.length, 1);
        expect(statistics.workerStats['worker1']?.workerName, '张三');
        expect(statistics.overview.totalTasks, 10);
        expect(statistics.overview.completionRate, 0.8);
        expect(statistics.efficiencyRanking.length, 1);
        expect(statistics.efficiencyRanking.first.workerName, '张三');
      });

      test('should refresh statistics', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        
        // 初始加载
        await container.read(workloadNotifierProvider.future);
        
        // 刷新数据
        await notifier.refreshStatistics();
        
        final state = container.read(workloadNotifierProvider);
        expect(state, isA<AsyncData>());
      });

      test('should handle optimistic refresh', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        
        // 初始加载
        await container.read(workloadNotifierProvider.future);
        
        // 乐观刷新
        await notifier.optimisticRefresh();
        
        final state = container.read(workloadNotifierProvider);
        expect(state, isA<AsyncData>());
      });
    });

    group('便捷Provider测试', () {
      test('should provide workload overview', () async {
        // 设置模拟数据
        mockWorkloadRepository.setMockData(
          overview: const WorkloadOverview(
            totalTasks: 20,
            completedTasks: 15,
            activeWorkers: 3,
            totalWorkers: 5,
            averageEfficiency: 0.75,
            totalTonnage: 300.0,
            completedTonnage: 225.0,
            completionRate: 0.75,
          ),
        );

        // 等待数据加载
        await container.read(workloadNotifierProvider.future);
        
        final overview = container.read(workloadOverviewProvider);
        expect(overview, isNotNull);
        expect(overview!.totalTasks, 20);
        expect(overview.completionRate, 0.75);
      });

      test('should provide worker statistics', () async {
        // 设置模拟数据
        mockWorkloadRepository.setMockData(
          workerStats: {
            'worker1': const WorkerStatistics(
              workerId: 'worker1',
              workerName: '张三',
              role: '叉车',
              warehouse: '1号库',
              group: '第一组',
              totalTasks: 5,
              completedTasks: 4,
              totalTonnage: 75.0,
              completedTonnage: 60.0,
              efficiency: 0.8,
              averageTonnagePerTask: 15.0,
            ),
            'worker2': const WorkerStatistics(
              workerId: 'worker2',
              workerName: '李四',
              role: '仓管',
              warehouse: '1号库',
              group: '第一组',
              totalTasks: 8,
              completedTasks: 6,
              totalTonnage: 120.0,
              completedTonnage: 90.0,
              efficiency: 0.75,
              averageTonnagePerTask: 15.0,
            ),
          },
        );

        // 等待数据加载
        await container.read(workloadNotifierProvider.future);
        
        final workerStats = container.read(workerStatisticsProvider);
        expect(workerStats.length, 2);
        expect(workerStats['worker1']?.workerName, '张三');
        expect(workerStats['worker2']?.workerName, '李四');
      });

      test('should provide efficiency ranking', () async {
        // 设置模拟数据
        mockWorkloadRepository.setMockData(
          ranking: [
            const EfficiencyRanking(
              workerId: 'worker1',
              workerName: '张三',
              role: '叉车',
              efficiency: 0.9,
              rank: 1,
            ),
            const EfficiencyRanking(
              workerId: 'worker2',
              workerName: '李四',
              role: '仓管',
              efficiency: 0.8,
              rank: 2,
            ),
          ],
        );

        // 等待数据加载
        await container.read(workloadNotifierProvider.future);
        
        final ranking = container.read(efficiencyRankingProvider);
        expect(ranking.length, 2);
        expect(ranking.first.workerName, '张三');
        expect(ranking.first.rank, 1);
        expect(ranking.last.workerName, '李四');
        expect(ranking.last.rank, 2);
      });
    });

    group('高级功能测试', () {
      test('should get worker details', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        
        final workerStats = await notifier.getWorkerStatistics('worker1');
        expect(workerStats, isNull); // 因为没有设置模拟数据
      });

      test('should get workload trends', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        
        final trends = await notifier.getWorkloadTrends('worker1');
        expect(trends, isNotEmpty);
        expect(trends['day1'], 10.0);
      });

      test('should get team comparison', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        
        final comparison = await notifier.getTeamComparison();
        expect(comparison, isNotEmpty);
        expect(comparison['role1']?['totalTasks'], 10);
      });

      test('should validate workload data', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        
        final validation = await notifier.validateWorkloadData();
        expect(validation['dataIntegrity'], 'good');
        expect(validation['totalTasks'], 25);
      });

      test('should export workload data', () async {
        final notifier = container.read(workloadNotifierProvider.notifier);
        
        final exported = await notifier.exportWorkloadData();
        expect(exported['exported'], true);
      });
    });
  });
}
