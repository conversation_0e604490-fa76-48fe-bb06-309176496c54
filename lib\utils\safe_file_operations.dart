import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🛡️ 安全文件操作工具类
/// 提供带有完整错误处理的文件操作方法
class SafeFileOperations {
  /// 安全检查文件是否存在
  static Future<bool> safeExists(String path) async {
    try {
      if (path.isEmpty) return false;
      return await File(path).exists();
    } on FileSystemException catch (e) {
      AppLogger.warning('文件存在性检查失败: ${e.message} - ${e.path}', tag: 'SafeFile');
      return false;
    } catch (e) {
      AppLogger.warning('文件存在性检查异常: $e', tag: 'SafeFile');
      return false;
    }
  }

  /// 安全读取文件字节数据
  static Future<Uint8List> safeReadBytes(String path, {int? maxSize}) async {
    try {
      if (path.isEmpty) {
        throw FileSystemException('文件路径为空', path);
      }

      final file = File(path);
      if (!await safeExists(path)) {
        throw FileSystemException('文件不存在', path);
      }

      final fileSize = await file.length();
      if (fileSize == 0) {
        throw FileSystemException('文件为空', path);
      }

      if (maxSize != null && fileSize > maxSize) {
        throw FileSizeException(
            '文件过大: ${fileSize} bytes (限制: ${maxSize} bytes)', path);
      }

      return await file.readAsBytes();
    } on FileSystemException catch (e) {
      Log.e('文件读取失败: ${e.message} - ${e.path}', tag: 'SafeFile');
      rethrow;
    } on FileSizeException catch (e) {
      Log.e('文件大小超限: ${e.message}', tag: 'SafeFile');
      rethrow;
    } catch (e) {
      Log.e('文件读取异常: $e', tag: 'SafeFile');
      throw FileSystemException('文件读取失败: $e', path);
    }
  }

  /// 安全写入文件字节数据
  static Future<void> safeWriteBytes(String path, Uint8List bytes) async {
    try {
      if (path.isEmpty) {
        throw FileSystemException('文件路径为空', path);
      }

      final file = File(path);

      // 确保父目录存在
      final directory = file.parent;
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      await file.writeAsBytes(bytes);
    } on FileSystemException catch (e) {
      Log.e('文件写入失败: ${e.message} - ${e.path}', tag: 'SafeFile');
      rethrow;
    } catch (e) {
      Log.e('文件写入异常: $e', tag: 'SafeFile');
      throw FileSystemException('文件写入失败: $e', path);
    }
  }

  /// 安全获取文件大小
  static Future<int> safeGetFileSize(String path) async {
    try {
      if (!await safeExists(path)) {
        return 0;
      }

      final file = File(path);
      return await file.length();
    } on FileSystemException catch (e) {
      Log.w('获取文件大小失败: ${e.message} - ${e.path}', tag: 'SafeFile');
      return 0;
    } catch (e) {
      Log.w('获取文件大小异常: $e', tag: 'SafeFile');
      return 0;
    }
  }

  /// 安全创建Image.file控件，带错误处理
  static Widget safeImageFile(
    String? imagePath, {
    double? width,
    double? height,
    BoxFit? fit,
    Widget? errorWidget,
  }) {
    if (imagePath == null || imagePath.isEmpty) {
      return errorWidget ?? const Icon(Icons.image_not_supported, size: 50);
    }

    return FutureBuilder<bool>(
      future: safeExists(imagePath),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            width: width ?? 50,
            height: height ?? 50,
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
        }

        if (snapshot.hasError || snapshot.data != true) {
          return errorWidget ??
              Container(
                width: width ?? 50,
                height: height ?? 50,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.broken_image, color: Colors.grey),
              );
        }

        return Image.file(
          File(imagePath),
          width: width,
          height: height,
          fit: fit ?? BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            Log.w('图片加载失败: $imagePath - $error', tag: 'SafeFile');
            return errorWidget ??
                Container(
                  width: width ?? 50,
                  height: height ?? 50,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.error, color: Colors.red),
                );
          },
        );
      },
    );
  }

  /// 带重试机制的文件操作
  static Future<T> withRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(milliseconds: 100),
  }) async {
    Exception? lastException;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } on FileSystemException catch (e) {
        lastException = e;
        Log.w('文件操作失败 (尝试 $attempt/$maxRetries): ${e.message}',
            tag: 'SafeFile');

        if (attempt < maxRetries) {
          await Future.delayed(delay * attempt);
        }
      } catch (e) {
        lastException = Exception('文件操作异常: $e');
        Log.w('文件操作异常 (尝试 $attempt/$maxRetries): $e', tag: 'SafeFile');

        if (attempt < maxRetries) {
          await Future.delayed(delay * attempt);
        }
      }
    }

    throw lastException ?? Exception('文件操作重试失败');
  }
}

/// 自定义文件大小异常
class FileSizeException implements Exception {
  final String message;
  final String? path;

  FileSizeException(this.message, [this.path]);

  @override
  String toString() =>
      'FileSizeException: $message${path != null ? ' (路径: $path)' : ''}';
}
