import 'package:flutter/foundation.dart';
import 'package:loadguard/utils/app_config.dart';

/// 🔧 应用日志管理器
/// 在发布版本中禁用调试输出，提高性能
class AppLogger {
  /// 调试日志 - 仅在开发模式下输出
  static void debug(String message, {String? tag, Object? error}) {
    if (AppConfig.enableDebugLogs) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('🔍 $tagStr$message');
      if (error != null) {
        debugPrint('   Debug Error: $error');
      }
    }
  }

  /// 信息日志 - 生产环境关闭
  static void info(String message, {String? tag, Object? error}) {
    if (AppConfig.enableDebugLogs) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('ℹ️ $tagStr$message');
      if (error != null) {
        debugPrint('   Info Error: $error');
      }
    }
  }

  /// 警告日志 - 保留重要警告
  static void warning(String message, {String? tag, Object? error}) {
    if (AppConfig.enableDebugLogs || AppConfig.enableDetailedErrorReporting) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('⚠️ $tagStr$message');
      if (error != null) {
        debugPrint('   Warning Error: $error');
      }
    }
  }

  /// 识别调试日志 - 强制输出重要识别信息
  static void recognition(String message, {String? tag, Object? error}) {
    final tagStr = tag != null ? '[$tag] ' : '';
    debugPrint('🎯 $tagStr$message');
    if (error != null) {
      debugPrint('   Recognition Error: $error');
    }
  }

  /// 错误日志 - 保留
  static void error(String message,
      {String? tag, Object? error, StackTrace? stackTrace}) {
    final tagStr = tag != null ? '[$tag] ' : '';
    debugPrint('❌ $tagStr$message');
    if (error != null && AppConfig.enableDetailedErrorReporting) {
      debugPrint('   Error: $error');
    }
    if (stackTrace != null && AppConfig.enableDetailedErrorReporting) {
      debugPrint('   StackTrace: $stackTrace');
    }
  }

  /// 性能日志 - 仅在开发模式下输出
  static void performance(String operation, Duration duration, {String? tag}) {
    if (AppConfig.enablePerformanceMonitoring) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('⏱️ $tagStr$operation: ${duration.inMilliseconds}ms');
    }
  }

  /// UI调试日志 - 生产环境完全关闭
  static void ui(String message, {String? tag}) {
    if (AppConfig.enableUIDebugging) {
      final tagStr = tag != null ? '[$tag] ' : '';
      debugPrint('🎨 $tagStr$message');
    }
  }
}

/// 兼容性Log类 - 替换原有的Log调用
class Log {
  static void d(String message, {String? tag}) =>
      AppLogger.debug(message, tag: tag);
  static void i(String message, {String? tag}) =>
      AppLogger.info(message, tag: tag);
  static void w(String message, {String? tag}) =>
      AppLogger.warning(message, tag: tag);
  static void e(String message,
          {String? tag, Object? error, StackTrace? stackTrace}) =>
      AppLogger.error(message, tag: tag, error: error, stackTrace: stackTrace);
  static void perf(String operation, Duration duration, {String? tag}) =>
      AppLogger.performance(operation, duration, tag: tag);
}
