import 'package:flutter/material.dart';
import '../utils/theme_colors.dart';
import '../utils/gradient_extensions.dart';

enum MetricCardType { standard, premium, enterprise }

class TechMetricCard extends StatelessWidget {
  final String title;
  final List<Map<String, dynamic>> metrics;
  final MetricCardType cardType;
  final VoidCallback? onTap;
  final bool isExpanded;
  final Widget? customHeader;

  const TechMetricCard({
    Key? key,
    required this.title,
    required this.metrics,
    this.cardType = MetricCardType.standard,
    this.onTap,
    this.isExpanded = false,
    this.customHeader,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color cardColor;
    LinearGradient? cardGradient;

    switch (cardType) {
      case MetricCardType.premium:
        cardColor = ThemeColors.premiumCardBackgroundColor;
        cardGradient = ThemeColors.premiumCardGradient;
        break;
      case MetricCardType.enterprise:
        cardColor = ThemeColors.enterpriseCardBackgroundColor;
        cardGradient = ThemeColors.enterpriseCardGradient;
        break;
      case MetricCardType.standard:
        cardColor = ThemeColors.cardBackgroundColor;
        cardGradient = null;
        break;
    }

    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
        side: BorderSide(
          color: cardType == MetricCardType.standard
              ? Colors.transparent
              : ThemeColors.cardBorderColor.withValues(alpha: 0.3),
          width: 1.0,
        ),
      ),
      color: cardGradient != null ? null : cardColor,
      clipBehavior: Clip.antiAlias,
      child: Container(
        decoration:
            cardGradient != null ? BoxDecoration(gradient: cardGradient) : null,
        child: InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                customHeader ?? _buildHeader(),
                const SizedBox(height: 16),
                ...metrics.map((metric) => _buildMetricItem(context, metric)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: cardType == MetricCardType.standard
                  ? ThemeColors.textPrimaryColor
                  : ThemeColors.textPrimaryLightColor,
            ),
          ),
        ),
        if (onTap != null)
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: cardType == MetricCardType.standard
                ? ThemeColors.iconColor
                : ThemeColors.iconLightColor,
          ),
      ],
    );
  }

  Widget _buildMetricItem(BuildContext context, Map<String, dynamic> metric) {
    final String label = metric['label'] ?? '';
    final String value = metric['value']?.toString() ?? '';
    final String description = metric['description'] ?? '';
    final IconData? icon = metric['icon'];
    final Color? valueColor = metric['valueColor'];
    final bool isGradientText = metric['isGradientText'] ?? false;
    final LinearGradient? textGradient = metric['textGradient'];

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (icon != null) ...[
            Container(
              padding: const EdgeInsets.only(top: 4.0),
              child: Icon(
                icon,
                size: 22,
                color: cardType == MetricCardType.standard
                    ? ThemeColors.iconColor
                    : ThemeColors.iconLightColor,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: cardType == MetricCardType.standard
                        ? ThemeColors.textPrimaryColor
                        : ThemeColors.textPrimaryLightColor,
                  ),
                ),
                if (description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 13,
                      color: cardType == MetricCardType.standard
                          ? ThemeColors.textSecondaryColor
                          : ThemeColors.textSecondaryLightColor
                              .withValues(alpha: 0.8),
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                isGradientText && textGradient != null
                    ? Text(
                        value,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ).applyGradient(textGradient)
                    : Text(
                        value,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: valueColor ??
                              (cardType == MetricCardType.standard
                                  ? ThemeColors.textPrimaryColor
                                      .withValues(alpha: 0.9)
                                  : ThemeColors.textPrimaryLightColor
                                      .withValues(alpha: 0.9)),
                        ),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class TechMetricPair extends StatelessWidget {
  final String title;
  final List<Map<String, dynamic>> metrics;

  const TechMetricPair({
    Key? key,
    required this.title,
    required this.metrics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: ThemeColors.textPrimaryColor,
            ),
          ),
        ),
        ...metrics.map((metric) => _buildMetricRow(context, metric)),
      ],
    );
  }

  Widget _buildMetricRow(BuildContext context, Map<String, dynamic> metric) {
    final String label = metric['label'] ?? '';
    final String value = metric['value']?.toString() ?? '';
    final IconData? icon = metric['icon'];
    final Color? valueColor = metric['valueColor'];

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(icon, size: 16, color: ThemeColors.iconColor),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: ThemeColors.textSecondaryColor,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: valueColor ?? ThemeColors.textPrimaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
