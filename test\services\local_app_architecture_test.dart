import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/local_data_manager.dart';
import 'package:loadguard/services/sync_service.dart';

void main() {
  group('Local App Architecture Tests', () {
    group('LocalDataStatistics Tests', () {
      test('should create statistics correctly', () {
        final stats = LocalDataStatistics(
          taskCount: 10,
          configItemCount: 5,
          dataSize: 1024 * 1024, // 1MB
          lastModified: DateTime.now(),
          dataVersion: '1.0.0',
          deviceId: 'test_device_123',
          isLocalApp: true,
          syncEnabled: false,
        );
        
        expect(stats.taskCount, 10);
        expect(stats.configItemCount, 5);
        expect(stats.dataSize, 1024 * 1024);
        expect(stats.formattedDataSize, '1.0MB');
        expect(stats.isLocalApp, true);
        expect(stats.syncEnabled, false);
        
        final map = stats.toMap();
        expect(map['taskCount'], 10);
        expect(map['formattedDataSize'], '1.0MB');
        expect(map['isLocalApp'], true);
      });
      
      test('should format data size correctly', () {
        // Test bytes
        final bytesStats = LocalDataStatistics(
          taskCount: 0,
          configItemCount: 0,
          dataSize: 512,
          lastModified: DateTime.now(),
          dataVersion: '1.0.0',
          deviceId: 'test',
          isLocalApp: true,
          syncEnabled: false,
        );
        expect(bytesStats.formattedDataSize, '512B');
        
        // Test KB
        final kbStats = LocalDataStatistics(
          taskCount: 0,
          configItemCount: 0,
          dataSize: 1536, // 1.5KB
          lastModified: DateTime.now(),
          dataVersion: '1.0.0',
          deviceId: 'test',
          isLocalApp: true,
          syncEnabled: false,
        );
        expect(kbStats.formattedDataSize, '1.5KB');
        
        // Test MB
        final mbStats = LocalDataStatistics(
          taskCount: 0,
          configItemCount: 0,
          dataSize: 2 * 1024 * 1024 + 512 * 1024, // 2.5MB
          lastModified: DateTime.now(),
          dataVersion: '1.0.0',
          deviceId: 'test',
          isLocalApp: true,
          syncEnabled: false,
        );
        expect(mbStats.formattedDataSize, '2.5MB');
      });
      
      test('should have meaningful toString', () {
        final stats = LocalDataStatistics(
          taskCount: 5,
          configItemCount: 3,
          dataSize: 1024,
          lastModified: DateTime.now(),
          dataVersion: '1.0.0',
          deviceId: 'test',
          isLocalApp: true,
          syncEnabled: false,
        );
        
        final string = stats.toString();
        expect(string, contains('tasks: 5'));
        expect(string, contains('config: 3'));
        expect(string, contains('1.0KB'));
      });
    });
    
    group('Sync Service Data Models Tests', () {
      test('should create SyncEvent correctly', () {
        final event = SyncEvent(
          type: SyncEventType.syncStarted,
          message: 'Sync started',
          timestamp: DateTime.now(),
          details: {'key': 'value'},
        );
        
        expect(event.type, SyncEventType.syncStarted);
        expect(event.message, 'Sync started');
        expect(event.details?['key'], 'value');
        
        final string = event.toString();
        expect(string, contains('syncStarted'));
        expect(string, contains('Sync started'));
      });
      
      test('should create SyncResult correctly', () {
        final successResult = SyncResult.success(
          message: 'Sync completed',
          syncStats: SyncStatistics(
            totalChanges: 10,
            appliedChanges: 8,
            failedChanges: 2,
            syncTime: DateTime.now(),
          ),
        );
        
        expect(successResult.success, true);
        expect(successResult.message, 'Sync completed');
        expect(successResult.syncStats?.totalChanges, 10);
        expect(successResult.error, null);
        
        final failureResult = SyncResult.failure(
          'Sync failed',
          error: Exception('Network error'),
        );
        
        expect(failureResult.success, false);
        expect(failureResult.message, 'Sync failed');
        expect(failureResult.syncStats, null);
        expect(failureResult.error, isA<Exception>());
      });
      
      test('should create SyncStatus correctly', () {
        final status = SyncStatus(
          enabled: true,
          syncing: false,
          lastSyncTime: DateTime.now(),
          serverEndpoint: 'https://api.example.com',
          syncInterval: const Duration(minutes: 30),
          hasConfiguration: true,
        );
        
        expect(status.enabled, true);
        expect(status.syncing, false);
        expect(status.serverEndpoint, 'https://api.example.com');
        expect(status.syncInterval.inMinutes, 30);
        expect(status.hasConfiguration, true);
        
        final map = status.toMap();
        expect(map['enabled'], true);
        expect(map['syncIntervalMinutes'], 30);
        expect(map['hasConfiguration'], true);
        
        final string = status.toString();
        expect(string, contains('enabled: true'));
        expect(string, contains('configured: true'));
      });
      
      test('should create ServerConnectionStatus correctly', () {
        final connectedStatus = ServerConnectionStatus(
          isConnected: true,
          message: 'Connected successfully',
          statusCode: 200,
        );
        
        expect(connectedStatus.isConnected, true);
        expect(connectedStatus.message, 'Connected successfully');
        expect(connectedStatus.statusCode, 200);
        
        final disconnectedStatus = ServerConnectionStatus(
          isConnected: false,
          message: 'Connection failed',
        );
        
        expect(disconnectedStatus.isConnected, false);
        expect(disconnectedStatus.message, 'Connection failed');
        expect(disconnectedStatus.statusCode, null);
        
        final string = connectedStatus.toString();
        expect(string, contains('connected: true'));
        expect(string, contains('Connected successfully'));
      });
      
      test('should create DataChange correctly', () {
        final change = DataChange(
          type: DataChangeType.taskUpdated,
          id: 'task_123',
          timestamp: DateTime.now(),
          data: {'status': 'completed', 'progress': 100},
        );
        
        expect(change.type, DataChangeType.taskUpdated);
        expect(change.id, 'task_123');
        expect(change.data['status'], 'completed');
        expect(change.data['progress'], 100);
        
        // Test serialization
        final map = change.toMap();
        expect(map['type'], 'taskUpdated');
        expect(map['id'], 'task_123');
        expect(map['data']['status'], 'completed');
        
        // Test deserialization
        final fromMap = DataChange.fromMap(map);
        expect(fromMap.type, change.type);
        expect(fromMap.id, change.id);
        expect(fromMap.data, change.data);
        expect(fromMap.timestamp, change.timestamp);
        
        final string = change.toString();
        expect(string, contains('taskUpdated'));
        expect(string, contains('task_123'));
      });
      
      test('should create SyncStatistics correctly', () {
        final stats = SyncStatistics(
          totalChanges: 10,
          appliedChanges: 8,
          failedChanges: 2,
          syncTime: DateTime.now(),
        );
        
        expect(stats.totalChanges, 10);
        expect(stats.appliedChanges, 8);
        expect(stats.failedChanges, 2);
        expect(stats.successRate, 0.8); // 8/10 = 0.8
        
        final map = stats.toMap();
        expect(map['totalChanges'], 10);
        expect(map['appliedChanges'], 8);
        expect(map['failedChanges'], 2);
        expect(map['successRate'], 0.8);
        
        final string = stats.toString();
        expect(string, contains('total: 10'));
        expect(string, contains('applied: 8'));
        expect(string, contains('failed: 2'));
        
        // Test edge case: no changes
        final emptyStats = SyncStatistics(
          totalChanges: 0,
          appliedChanges: 0,
          failedChanges: 0,
          syncTime: DateTime.now(),
        );
        expect(emptyStats.successRate, 0.0);
      });
    });
    
    group('Enum Tests', () {
      test('should have all expected SyncEventType values', () {
        final types = SyncEventType.values;
        expect(types, contains(SyncEventType.configurationChanged));
        expect(types, contains(SyncEventType.syncStarted));
        expect(types, contains(SyncEventType.syncCompleted));
        expect(types, contains(SyncEventType.syncFailed));
        expect(types, contains(SyncEventType.dataImported));
        expect(types, contains(SyncEventType.dataExported));
        expect(types, contains(SyncEventType.conflictResolved));
        expect(types.length, 7);
      });
      
      test('should have all expected DataChangeType values', () {
        final types = DataChangeType.values;
        expect(types, contains(DataChangeType.taskCreated));
        expect(types, contains(DataChangeType.taskUpdated));
        expect(types, contains(DataChangeType.taskDeleted));
        expect(types, contains(DataChangeType.configUpdated));
        expect(types, contains(DataChangeType.statisticsUpdated));
        expect(types.length, 5);
      });
    });
    
    group('Local App Architecture Principles Tests', () {
      test('should demonstrate local-first architecture', () {
        // 本地优先原则：所有核心功能都应该能够离线工作
        
        // 1. 数据统计应该基于本地数据
        final localStats = LocalDataStatistics(
          taskCount: 100,
          configItemCount: 20,
          dataSize: 1024 * 1024,
          lastModified: DateTime.now(),
          dataVersion: '1.0.0',
          deviceId: 'local_device_123',
          isLocalApp: true, // 明确标识为本地应用
          syncEnabled: false, // 同步是可选的
        );
        
        expect(localStats.isLocalApp, true);
        expect(localStats.syncEnabled, false);
        
        // 2. 同步状态应该反映可选性
        final syncStatus = SyncStatus(
          enabled: false, // 默认不启用
          syncing: false,
          lastSyncTime: null, // 从未同步也是正常的
          serverEndpoint: null, // 可以没有服务器配置
          syncInterval: const Duration(minutes: 30),
          hasConfiguration: false, // 没有配置也能正常工作
        );
        
        expect(syncStatus.enabled, false);
        expect(syncStatus.hasConfiguration, false);
        expect(syncStatus.lastSyncTime, null);
      });
      
      test('should demonstrate future extensibility', () {
        // 未来扩展性：同步功能为未来服务器部署预留
        
        // 1. 同步配置可以后续添加
        final futureSync = SyncStatus(
          enabled: true,
          syncing: false,
          lastSyncTime: DateTime.now(),
          serverEndpoint: 'https://api.loadguard.com',
          syncInterval: const Duration(minutes: 15),
          hasConfiguration: true,
        );
        
        expect(futureSync.enabled, true);
        expect(futureSync.hasConfiguration, true);
        expect(futureSync.serverEndpoint, isNotNull);
        
        // 2. 数据变更可以被跟踪用于同步
        final dataChange = DataChange(
          type: DataChangeType.taskUpdated,
          id: 'task_456',
          timestamp: DateTime.now(),
          data: {'syncRequired': true},
        );
        
        expect(dataChange.type, DataChangeType.taskUpdated);
        expect(dataChange.data['syncRequired'], true);
        
        // 3. 同步结果可以提供详细信息
        final syncResult = SyncResult.success(
          message: 'Multi-device sync completed',
          syncStats: SyncStatistics(
            totalChanges: 50,
            appliedChanges: 48,
            failedChanges: 2,
            syncTime: DateTime.now(),
          ),
        );
        
        expect(syncResult.success, true);
        expect(syncResult.syncStats?.successRate, 0.96);
      });
      
      test('should demonstrate data portability', () {
        // 数据可移植性：支持导出导入，便于迁移和备份
        
        final exportableStats = LocalDataStatistics(
          taskCount: 200,
          configItemCount: 50,
          dataSize: 5 * 1024 * 1024, // 5MB
          lastModified: DateTime.now(),
          dataVersion: '1.0.0',
          deviceId: 'portable_device_789',
          isLocalApp: true,
          syncEnabled: false,
        );
        
        // 数据应该可以序列化
        final exportMap = exportableStats.toMap();
        expect(exportMap['taskCount'], 200);
        expect(exportMap['dataVersion'], '1.0.0');
        expect(exportMap['isLocalApp'], true);
        
        // 数据变更应该可以序列化用于传输
        final portableChange = DataChange(
          type: DataChangeType.configUpdated,
          id: 'config_export',
          timestamp: DateTime.now(),
          data: {'exportable': true, 'format': 'json'},
        );
        
        final changeMap = portableChange.toMap();
        final restoredChange = DataChange.fromMap(changeMap);
        
        expect(restoredChange.type, portableChange.type);
        expect(restoredChange.id, portableChange.id);
        expect(restoredChange.data['exportable'], true);
      });
    });
  });
}
