import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 📡 任务事件服务
/// 
/// 专注于事件处理和通知，实现松耦合的事件驱动架构
/// 提供统一的事件发布和订阅机制
class TaskEventService {
  // 事件流控制器
  final StreamController<TaskEvent> _eventController = StreamController.broadcast();
  
  // 事件监听器注册表
  final Map<TaskEventType, List<TaskEventHandler>> _handlers = {};
  
  // 事件历史记录
  final List<TaskEvent> _eventHistory = [];
  static const int _maxHistorySize = 1000;

  /// 事件流
  Stream<TaskEvent> get events => _eventController.stream;

  /// 注册事件处理器
  void registerHandler(TaskEventType eventType, TaskEventHandler handler) {
    _handlers.putIfAbsent(eventType, () => []).add(handler);
    AppLogger.debug('📡 注册事件处理器: $eventType', tag: 'TaskEvent');
  }

  /// 取消注册事件处理器
  void unregisterHandler(TaskEventType eventType, TaskEventHandler handler) {
    _handlers[eventType]?.remove(handler);
    AppLogger.debug('📡 取消注册事件处理器: $eventType', tag: 'TaskEvent');
  }

  /// 任务创建事件
  Future<void> onTaskCreated(TaskModel task) async {
    final event = TaskEvent.taskCreated(task);
    await _publishEvent(event);
    
    AppLogger.info('📡 任务创建事件: ${task.id}', tag: 'TaskEvent');
  }

  /// 混装任务创建事件
  Future<void> onMixedLoadTaskCreated(TaskModel task, List<BatchInfo> batches) async {
    final event = TaskEvent.mixedLoadTaskCreated(task, batches);
    await _publishEvent(event);
    
    AppLogger.info('📡 混装任务创建事件: ${task.id}, ${batches.length}个批次', tag: 'TaskEvent');
  }

  /// 任务完成事件
  Future<void> onTaskCompleted(TaskModel task) async {
    final event = TaskEvent.taskCompleted(task);
    await _publishEvent(event);
    
    AppLogger.info('📡 任务完成事件: ${task.id}', tag: 'TaskEvent');
  }

  /// 照片更新事件
  Future<void> onPhotoUpdated(TaskModel task, String photoId) async {
    final event = TaskEvent.photoUpdated(task, photoId);
    await _publishEvent(event);
    
    AppLogger.debug('📡 照片更新事件: ${task.id}, $photoId', tag: 'TaskEvent');
  }

  /// 识别结果更新事件
  Future<void> onRecognitionResultUpdated(TaskModel task, String photoId, RecognitionResult result) async {
    final event = TaskEvent.recognitionResultUpdated(task, photoId, result);
    await _publishEvent(event);
    
    AppLogger.info('📡 识别结果更新事件: ${task.id}, $photoId, 匹配: ${result.matchesPreset}', tag: 'TaskEvent');
  }

  /// 任务状态变更事件
  Future<void> onTaskStatusChanged(TaskModel task, TaskStatus oldStatus, TaskStatus newStatus) async {
    final event = TaskEvent.taskStatusChanged(task, oldStatus, newStatus);
    await _publishEvent(event);
    
    AppLogger.info('📡 任务状态变更事件: ${task.id}, $oldStatus -> $newStatus', tag: 'TaskEvent');
  }

  /// 任务删除事件
  Future<void> onTaskDeleted(String taskId) async {
    final event = TaskEvent.taskDeleted(taskId);
    await _publishEvent(event);
    
    AppLogger.info('📡 任务删除事件: $taskId', tag: 'TaskEvent');
  }

  /// 批量操作事件
  Future<void> onBatchOperationCompleted(List<String> taskIds, String operation, Map<String, bool> results) async {
    final event = TaskEvent.batchOperationCompleted(taskIds, operation, results);
    await _publishEvent(event);
    
    final successCount = results.values.where((success) => success).length;
    AppLogger.info('📡 批量操作完成事件: ${taskIds.length}个任务, 操作: $operation, 成功: $successCount', tag: 'TaskEvent');
  }

  /// 工作量分配事件
  Future<void> onWorkloadAssigned(TaskModel task, List<String> workers) async {
    final event = TaskEvent.workloadAssigned(task, workers);
    await _publishEvent(event);
    
    AppLogger.info('📡 工作量分配事件: ${task.id}, ${workers.length}个工人', tag: 'TaskEvent');
  }

  /// 性能指标事件
  Future<void> onPerformanceMetric(String taskId, String metricName, double value) async {
    final event = TaskEvent.performanceMetric(taskId, metricName, value);
    await _publishEvent(event);
    
    AppLogger.debug('📡 性能指标事件: $taskId, $metricName: $value', tag: 'TaskEvent');
  }

  /// 错误事件
  Future<void> onError(String taskId, String operation, String error) async {
    final event = TaskEvent.error(taskId, operation, error);
    await _publishEvent(event);
    
    AppLogger.error('📡 错误事件: $taskId, $operation, 错误: $error', tag: 'TaskEvent');
  }

  /// 获取事件历史
  List<TaskEvent> getEventHistory({
    TaskEventType? eventType,
    String? taskId,
    DateTime? startTime,
    DateTime? endTime,
    int? limit,
  }) {
    var filteredEvents = _eventHistory.where((event) {
      if (eventType != null && event.type != eventType) return false;
      if (taskId != null && event.taskId != taskId) return false;
      if (startTime != null && event.timestamp.isBefore(startTime)) return false;
      if (endTime != null && event.timestamp.isAfter(endTime)) return false;
      return true;
    }).toList();

    // 按时间倒序排列
    filteredEvents.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    if (limit != null && filteredEvents.length > limit) {
      filteredEvents = filteredEvents.take(limit).toList();
    }

    return filteredEvents;
  }

  /// 获取事件统计
  Map<String, dynamic> getEventStats() {
    final stats = <String, dynamic>{};
    
    // 按类型统计
    final typeStats = <TaskEventType, int>{};
    for (final event in _eventHistory) {
      typeStats[event.type] = (typeStats[event.type] ?? 0) + 1;
    }
    stats['byType'] = typeStats.map((key, value) => MapEntry(key.toString(), value));
    
    // 按小时统计
    final hourlyStats = <int, int>{};
    final now = DateTime.now();
    for (final event in _eventHistory) {
      final hoursDiff = now.difference(event.timestamp).inHours;
      if (hoursDiff < 24) {
        hourlyStats[hoursDiff] = (hourlyStats[hoursDiff] ?? 0) + 1;
      }
    }
    stats['last24Hours'] = hourlyStats;
    
    // 总体统计
    stats['totalEvents'] = _eventHistory.length;
    stats['handlersRegistered'] = _handlers.length;
    
    return stats;
  }

  /// 清除事件历史
  void clearEventHistory() {
    _eventHistory.clear();
    AppLogger.info('📡 清除事件历史', tag: 'TaskEvent');
  }

  /// 释放资源
  void dispose() {
    _eventController.close();
    _handlers.clear();
    _eventHistory.clear();
    AppLogger.info('📡 释放TaskEventService资源', tag: 'TaskEvent');
  }

  // 私有方法

  /// 发布事件
  Future<void> _publishEvent(TaskEvent event) async {
    try {
      // 添加到历史记录
      _addToHistory(event);
      
      // 发送到事件流
      _eventController.add(event);
      
      // 调用注册的处理器
      await _callHandlers(event);
      
    } catch (e) {
      AppLogger.error('📡 发布事件失败: ${event.type}, 错误: $e', tag: 'TaskEvent');
    }
  }

  /// 添加到历史记录
  void _addToHistory(TaskEvent event) {
    _eventHistory.add(event);
    
    // 限制历史记录大小
    while (_eventHistory.length > _maxHistorySize) {
      _eventHistory.removeAt(0);
    }
  }

  /// 调用事件处理器
  Future<void> _callHandlers(TaskEvent event) async {
    final handlers = _handlers[event.type] ?? [];
    
    for (final handler in handlers) {
      try {
        await handler(event);
      } catch (e) {
        AppLogger.error('📡 事件处理器执行失败: ${event.type}, 错误: $e', tag: 'TaskEvent');
      }
    }
  }
}

/// 任务事件
class TaskEvent {
  final TaskEventType type;
  final String? taskId;
  final TaskModel? task;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  TaskEvent._({
    required this.type,
    this.taskId,
    this.task,
    required this.data,
  }) : timestamp = DateTime.now();

  factory TaskEvent.taskCreated(TaskModel task) => TaskEvent._(
    type: TaskEventType.taskCreated,
    taskId: task.id,
    task: task,
    data: {'task': task.toJson()},
  );

  factory TaskEvent.mixedLoadTaskCreated(TaskModel task, List<BatchInfo> batches) => TaskEvent._(
    type: TaskEventType.mixedLoadTaskCreated,
    taskId: task.id,
    task: task,
    data: {
      'task': task.toJson(),
      'batches': batches.map((b) => b.toJson()).toList(),
    },
  );

  factory TaskEvent.taskCompleted(TaskModel task) => TaskEvent._(
    type: TaskEventType.taskCompleted,
    taskId: task.id,
    task: task,
    data: {'task': task.toJson()},
  );

  factory TaskEvent.photoUpdated(TaskModel task, String photoId) => TaskEvent._(
    type: TaskEventType.photoUpdated,
    taskId: task.id,
    task: task,
    data: {'photoId': photoId},
  );

  factory TaskEvent.recognitionResultUpdated(TaskModel task, String photoId, RecognitionResult result) => TaskEvent._(
    type: TaskEventType.recognitionResultUpdated,
    taskId: task.id,
    task: task,
    data: {
      'photoId': photoId,
      'result': result.toJson(),
    },
  );

  factory TaskEvent.taskStatusChanged(TaskModel task, TaskStatus oldStatus, TaskStatus newStatus) => TaskEvent._(
    type: TaskEventType.taskStatusChanged,
    taskId: task.id,
    task: task,
    data: {
      'oldStatus': oldStatus.toString(),
      'newStatus': newStatus.toString(),
    },
  );

  factory TaskEvent.taskDeleted(String taskId) => TaskEvent._(
    type: TaskEventType.taskDeleted,
    taskId: taskId,
    data: {'taskId': taskId},
  );

  factory TaskEvent.batchOperationCompleted(List<String> taskIds, String operation, Map<String, bool> results) => TaskEvent._(
    type: TaskEventType.batchOperationCompleted,
    data: {
      'taskIds': taskIds,
      'operation': operation,
      'results': results,
    },
  );

  factory TaskEvent.workloadAssigned(TaskModel task, List<String> workers) => TaskEvent._(
    type: TaskEventType.workloadAssigned,
    taskId: task.id,
    task: task,
    data: {'workers': workers},
  );

  factory TaskEvent.performanceMetric(String taskId, String metricName, double value) => TaskEvent._(
    type: TaskEventType.performanceMetric,
    taskId: taskId,
    data: {
      'metricName': metricName,
      'value': value,
    },
  );

  factory TaskEvent.error(String taskId, String operation, String error) => TaskEvent._(
    type: TaskEventType.error,
    taskId: taskId,
    data: {
      'operation': operation,
      'error': error,
    },
  );
}

/// 任务事件类型
enum TaskEventType {
  taskCreated,
  mixedLoadTaskCreated,
  taskCompleted,
  photoUpdated,
  recognitionResultUpdated,
  taskStatusChanged,
  taskDeleted,
  batchOperationCompleted,
  workloadAssigned,
  performanceMetric,
  error,
}

/// 事件处理器类型定义
typedef TaskEventHandler = Future<void> Function(TaskEvent event);
