import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

/// 🚀 增强的导航助手 - 适配go_router 16.0.0
/// 提供现代化的导航体验和Material 3集成
class EnhancedNavigationHelper {
  // 双击退出确认间隔
  static DateTime? _lastBackPressTime;
  static const Duration _exitConfirmDuration = Duration(seconds: 2);

  /// 🆕 16.0.0优化：命名路由导航
  static void goToNamed(BuildContext context, String routeName,
      {Map<String, String>? pathParameters,
      Map<String, String>? queryParameters}) {
    try {
      context.goNamed(
        routeName,
        pathParameters: pathParameters ?? <String, String>{},
        queryParameters: queryParameters ?? <String, String>{},
      );
      // 🆕 16.0.0新特性：触觉反馈
      HapticFeedback.lightImpact();
    } catch (e) {
      // 降级到路径导航
      final path = _getPathFromName(routeName);
      if (path != null) {
        context.go(path);
      }
    }
  }

  /// 🆕 16.0.0优化：推入命名路由
  static void pushNamed(BuildContext context, String routeName,
      {Map<String, String>? pathParameters,
      Map<String, String>? queryParameters}) {
    try {
      context.pushNamed(
        routeName,
        pathParameters: pathParameters ?? <String, String>{},
        queryParameters: queryParameters ?? <String, String>{},
      );
      HapticFeedback.lightImpact();
    } catch (e) {
      // 降级到路径导航
      final path = _getPathFromName(routeName);
      if (path != null) {
        context.push(path);
      }
    }
  }

  /// 🆕 16.0.0优化：替换命名路由
  static void replaceNamed(BuildContext context, String routeName,
      {Map<String, String>? pathParameters,
      Map<String, String>? queryParameters}) {
    try {
      context.replaceNamed(
        routeName,
        pathParameters: pathParameters ?? <String, String>{},
        queryParameters: queryParameters ?? <String, String>{},
      );
      HapticFeedback.lightImpact();
    } catch (e) {
      // 降级到路径导航
      final path = _getPathFromName(routeName);
      if (path != null) {
        context.replace(path);
      }
    }
  }

  /// 🆕 16.0.0优化：智能返回
  static void smartPop(BuildContext context) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
      HapticFeedback.lightImpact();
    } else {
      // 无法返回时跳转到首页
      goToNamed(context, 'home');
    }
  }

  /// 🆕 16.0.0优化：带参数的智能返回
  static void smartPopWithResult(BuildContext context, dynamic result) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop(result);
      HapticFeedback.lightImpact();
    } else {
      goToNamed(context, 'home');
    }
  }

  /// 🆕 16.0.0优化：任务相关导航
  static void goToNewTask(BuildContext context,
      {required String type, String? template}) {
    goToNamed(
      context,
      'new-task',
      queryParameters: {
        'type': type,
        if (template != null) 'template': template,
      },
    );
  }

  /// 🆕 16.0.0优化：任务详情导航
  static void goToTaskDetail(BuildContext context,
      {required String taskId, String? type}) {
    goToNamed(
      context,
      'task-detail',
      pathParameters: {'taskId': taskId},
      queryParameters: type != null ? {'type': type} : null,
    );
  }

  /// 🆕 16.0.0优化：结果页面导航
  static void goToResult(BuildContext context, {required String taskId}) {
    goToNamed(
      context,
      'result',
      pathParameters: {'taskId': taskId},
    );
  }

  /// 🆕 16.0.0优化：工人选择导航
  static void goToWorkerSelection(
    BuildContext context, {
    Set<String>? warehouses,
    Set<String>? workerIds,
    int? quantity,
  }) {
    goToNamed(
      context,
      'worker-selection',
      queryParameters: {
        if (warehouses != null && warehouses.isNotEmpty)
          'warehouses': warehouses.join(','),
        if (workerIds != null && workerIds.isNotEmpty)
          'workerIds': workerIds.join(','),
        if (quantity != null) 'quantity': quantity.toString(),
      },
    );
  }

  /// 🆕 16.0.0优化：企业功能导航
  static void goToEnterpriseControl(BuildContext context) {
    goToNamed(context, 'enterprise-control');
  }

  static void goToEnterpriseActivation(BuildContext context) {
    goToNamed(context, 'enterprise-activation');
  }

  /// 🆕 16.0.0优化：管理功能导航
  static void goToSecurityManagement(BuildContext context) {
    goToNamed(context, 'security-management');
  }

  static void goToEnhancedSecurityManagement(BuildContext context) {
    goToNamed(context, 'enhanced-security-management');
  }

  static void goToAdminManagement(BuildContext context) {
    goToNamed(context, 'admin-management');
  }

  /// 🆕 16.0.0优化：统计功能导航
  static void goToPerformanceStats(BuildContext context) {
    goToNamed(context, 'performance-stats');
  }

  static void goToWorkloadManagement(BuildContext context) {
    goToNamed(context, 'workload-management');
  }

  static void goToWorkloadStatistics(BuildContext context) {
    goToNamed(context, 'workload-statistics');
  }

  /// 🆕 16.0.0优化：其他页面导航
  static void goToAbout(BuildContext context) {
    goToNamed(context, 'about');
  }

  static void goToTemplateSelection(BuildContext context) {
    goToNamed(context, 'template-selection');
  }

  static void goToActivation(BuildContext context) {
    goToNamed(context, 'activation');
  }

  /// 🆕 16.0.0优化：页面构建器
  static Widget buildEnhancedPage({
    required Widget child,
    bool enableSwipeBack = true,
    bool isRootPage = false,
    VoidCallback? onBackPressed,
    String? exitMessage,
    bool showAppBar = true,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
    Color? backgroundColor,
    bool resizeToAvoidBottomInset = true,
  }) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;

        // 根页面双击退出逻辑
        if (isRootPage) {
          final shouldExit = await _handleRootPageExit(child, exitMessage);
          if (shouldExit) {
            SystemNavigator.pop();
          }
          return;
        }

        // 执行自定义或默认返回逻辑
        if (onBackPressed != null) {
          onBackPressed();
        } else {
          _performDefaultBack(child);
        }
      },
      child: Scaffold(
        appBar: showAppBar ? appBar : null,
        body: enableSwipeBack
            ? _buildSwipeBackDetector(child, onBackPressed, isRootPage)
            : child,
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: bottomNavigationBar,
        backgroundColor: backgroundColor,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      ),
    );
  }

  /// 🆕 16.0.0优化：根页面构建器
  static Widget buildRootPage({
    required Widget child,
    String? exitMessage,
    bool enableSwipeBack = false,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
  }) {
    return buildEnhancedPage(
      child: child,
      isRootPage: true,
      enableSwipeBack: enableSwipeBack,
      exitMessage: exitMessage ?? '再按一次退出应用',
      appBar: appBar,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
    );
  }

  /// 🆕 16.0.0优化：标准页面构建器
  static Widget buildStandardPage({
    required Widget child,
    VoidCallback? onBackPressed,
    bool enableSwipeBack = true,
    PreferredSizeWidget? appBar,
    Widget? floatingActionButton,
    Widget? bottomNavigationBar,
  }) {
    return buildEnhancedPage(
      child: child,
      enableSwipeBack: enableSwipeBack,
      onBackPressed: onBackPressed,
      appBar: appBar,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
    );
  }

  /// 🆕 16.0.0优化：侧滑返回检测器
  static Widget _buildSwipeBackDetector(
    Widget child,
    VoidCallback? onBackPressed,
    bool isRootPage,
  ) {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        // 优化的侧滑逻辑：从左边缘向右滑动
        if (details.globalPosition.dx > 100 &&
            details.velocity.pixelsPerSecond.dx > 300) {
          if (isRootPage) {
            // 根页面不响应侧滑返回
            return;
          }

          // 触觉反馈
          HapticFeedback.lightImpact();

          // 执行返回
          if (onBackPressed != null) {
            onBackPressed();
          } else {
            _performDefaultBack(child);
          }
        }
      },
      child: child,
    );
  }

  /// 🆕 16.0.0优化：默认返回逻辑
  static void _performDefaultBack(Widget child) {
    final context = _findContext(child);
    if (context != null && context.mounted) {
      smartPop(context);
    }
  }

  /// 🆕 16.0.0优化：根页面双击退出处理
  static Future<bool> _handleRootPageExit(
      Widget child, String? exitMessage) async {
    final now = DateTime.now();
    if (_lastBackPressTime == null ||
        now.difference(_lastBackPressTime!) > _exitConfirmDuration) {
      _lastBackPressTime = now;

      final context = _findContext(child);
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(exitMessage ?? '再按一次退出应用'),
            duration: _exitConfirmDuration,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      }
      return false;
    }
    return true;
  }

  /// 🆕 16.0.0优化：查找上下文
  static BuildContext? _findContext(Widget widget) {
    // 这里可以实现更复杂的上下文查找逻辑
    return null;
  }

  /// 🆕 16.0.0优化：路径映射
  static String? _getPathFromName(String routeName) {
    final pathMap = {
      'launcher': '/',
      'home': '/home',
      'activation': '/activation',
      'template-selection': '/template-selection',
      'new-task': '/enhanced-task/new',
      'task-detail': '/task-detail',
      'result': '/result',
      'security-management': '/security-management',
      'about': '/about',
      'performance-stats': '/performance-stats',
      'enterprise-control': '/enterprise-control',
      'enterprise-activation': '/enterprise-activation',
      'workload-management': '/workload-management',
      'workload-statistics': '/workload-statistics',
      'admin-management': '/admin-management',
      'enhanced-security-management': '/enhanced-security-management',
      'worker-selection': '/worker-selection',
    };
    return pathMap[routeName];
  }

  /// 🆕 16.0.0优化：底部弹窗
  static Future<T?> showEnhancedBottomSheet<T>({
    required BuildContext context,
    required WidgetBuilder builder,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    Color? barrierColor,
    bool isScrollControlled = false,
    bool useRootNavigator = false,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      builder: builder,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      barrierColor: barrierColor,
      isScrollControlled: isScrollControlled,
      useRootNavigator: useRootNavigator,
    );
  }

  /// 🆕 16.0.0优化：对话框
  static Future<T?> showEnhancedDialog<T>({
    required BuildContext context,
    required WidgetBuilder builder,
    bool barrierDismissible = true,
    Color? barrierColor,
    bool useSafeArea = true,
    bool useRootNavigator = true,
    RouteSettings? routeSettings,
  }) {
    return showDialog<T>(
      context: context,
      builder: builder,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      useSafeArea: useSafeArea,
      useRootNavigator: useRootNavigator,
      routeSettings: routeSettings,
    );
  }

  /// 🆕 16.0.0优化：快速导航方法
  static void goBack(BuildContext context) {
    smartPop(context);
  }

  static void goHome(BuildContext context) {
    goToNamed(context, 'home');
  }

  static void goToPage(BuildContext context, String routeName) {
    goToNamed(context, routeName);
  }

  /// 🆕 16.0.0优化：路由状态检查
  static bool isCurrentRoute(BuildContext context, String routeName) {
    final router = GoRouter.of(context);
    // 🆕 16.0.0优化：使用正确的API获取位置
    final location = router.routerDelegate.currentConfiguration.uri.toString();
    final path = _getPathFromName(routeName);
    return path != null && location.startsWith(path);
  }

  /// 🆕 16.0.0优化：获取当前路由信息
  static Map<String, dynamic> getCurrentRouteInfo(BuildContext context) {
    final router = GoRouter.of(context);
    // 🆕 16.0.0优化：使用正确的API获取位置
    final location = router.routerDelegate.currentConfiguration.uri.toString();
    final uri = Uri.parse(location);

    return {
      'location': location,
      'path': uri.path,
      'queryParameters': uri.queryParameters,
      'fragment': uri.fragment,
    };
  }
}
