import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/utils/app_logger.dart';
// ✅ 合并ML Kit性能监控功能
import 'package:loadguard/models/recognition_algorithm.dart';

/// 性能管理器
/// 负责监控应用性能、内存使用、资源管理和清理
class PerformanceManager {
  static PerformanceManager? _instance;
  static PerformanceManager get instance => _instance ??= PerformanceManager._();
  
  PerformanceManager._();
  
  // 性能监控
  final List<PerformanceMetric> _performanceMetrics = [];
  final StreamController<PerformanceUpdate> _performanceStream = StreamController.broadcast();
  
  // 资源管理
  final Set<Disposable> _disposableResources = {};
  final List<Timer> _activeTimers = [];
  final List<StreamSubscription> _activeSubscriptions = [];
  
  // 内存监控
  Timer? _memoryMonitorTimer;
  int _lastMemoryUsage = 0;
  
  bool _initialized = false;
  
  /// 性能更新流
  Stream<PerformanceUpdate> get performanceStream => _performanceStream.stream;
  
  /// 初始化性能管理器
  void initialize() {
    if (_initialized) return;
    
    try {
      AppLogger.info('🚀 初始化性能管理器...');
      
      // 启动内存监控
      _startMemoryMonitoring();
      
      // 监听应用生命周期
      _setupLifecycleObserver();
      
      // 设置错误处理
      _setupErrorHandling();
      
      _initialized = true;
      AppLogger.info('✅ 性能管理器初始化完成');
    } catch (e) {
      AppLogger.error('❌ 性能管理器初始化失败', error: e);
    }
  }
  
  /// 记录性能指标
  void recordMetric(String name, double value, {Map<String, dynamic>? metadata}) {
    try {
      final metric = PerformanceMetric(
        name: name,
        value: value,
        timestamp: DateTime.now(),
        metadata: metadata ?? {},
      );
      
      _performanceMetrics.add(metric);
      
      // 保持指标历史大小
      while (_performanceMetrics.length > 1000) {
        _performanceMetrics.removeAt(0);
      }
      
      // 发送性能更新
      _performanceStream.add(PerformanceUpdate(
        type: PerformanceUpdateType.metricRecorded,
        metric: metric,
        timestamp: DateTime.now(),
      ));
      
      AppLogger.debug('📊 性能指标记录: $name = $value');
    } catch (e) {
      AppLogger.error('❌ 记录性能指标失败', error: e);
    }
  }
  
  /// 测量执行时间
  Future<T> measureExecutionTime<T>(
    String operationName,
    Future<T> Function() operation, {
    Map<String, dynamic>? metadata,
  }) async {
    final stopwatch = Stopwatch()..start();
    
    try {
      final result = await operation();
      stopwatch.stop();
      
      recordMetric(
        'execution_time_$operationName',
        stopwatch.elapsedMilliseconds.toDouble(),
        metadata: {
          'operation': operationName,
          'success': true,
          ...?metadata,
        },
      );
      
      return result;
    } catch (e) {
      stopwatch.stop();
      
      recordMetric(
        'execution_time_$operationName',
        stopwatch.elapsedMilliseconds.toDouble(),
        metadata: {
          'operation': operationName,
          'success': false,
          'error': e.toString(),
          ...?metadata,
        },
      );
      
      rethrow;
    }
  }
  
  /// 注册可释放资源
  void registerDisposable(Disposable resource) {
    _disposableResources.add(resource);
    AppLogger.debug('📝 注册可释放资源: ${resource.runtimeType}');
  }
  
  /// 注销可释放资源
  void unregisterDisposable(Disposable resource) {
    _disposableResources.remove(resource);
    AppLogger.debug('📝 注销可释放资源: ${resource.runtimeType}');
  }
  
  /// 注册定时器
  void registerTimer(Timer timer) {
    _activeTimers.add(timer);
    AppLogger.debug('⏰ 注册定时器');
  }
  
  /// 注册订阅
  void registerSubscription(StreamSubscription subscription) {
    _activeSubscriptions.add(subscription);
    AppLogger.debug('📡 注册流订阅');
  }
  
  /// 清理所有资源
  Future<void> cleanupAllResources() async {
    try {
      AppLogger.info('🧹 开始清理所有资源...');
      
      int disposedCount = 0;
      
      // 清理可释放资源
      for (final resource in _disposableResources.toList()) {
        try {
          await resource.dispose();
          disposedCount++;
        } catch (e) {
          AppLogger.warning('⚠️ 清理资源失败: ${resource.runtimeType} - $e');
        }
      }
      _disposableResources.clear();
      
      // 清理定时器
      for (final timer in _activeTimers.toList()) {
        try {
          timer.cancel();
        } catch (e) {
          AppLogger.warning('⚠️ 取消定时器失败: $e');
        }
      }
      _activeTimers.clear();
      
      // 清理订阅
      for (final subscription in _activeSubscriptions.toList()) {
        try {
          await subscription.cancel();
        } catch (e) {
          AppLogger.warning('⚠️ 取消订阅失败: $e');
        }
      }
      _activeSubscriptions.clear();
      
      // 强制垃圾回收
      await _forceGarbageCollection();
      
      AppLogger.info('✅ 资源清理完成: 清理了 $disposedCount 个资源');
    } catch (e) {
      AppLogger.error('❌ 资源清理失败', error: e);
    }
  }
  
  /// 获取性能统计
  PerformanceStatistics getPerformanceStatistics({Duration? timeWindow}) {
    try {
      final window = timeWindow ?? const Duration(hours: 1);
      final cutoff = DateTime.now().subtract(window);
      
      final recentMetrics = _performanceMetrics
          .where((metric) => metric.timestamp.isAfter(cutoff))
          .toList();
      
      if (recentMetrics.isEmpty) {
        return PerformanceStatistics.empty();
      }
      
      // 按名称分组统计
      final metricsByName = <String, List<PerformanceMetric>>{};
      for (final metric in recentMetrics) {
        metricsByName.putIfAbsent(metric.name, () => []).add(metric);
      }
      
      // 计算统计信息
      final metricStats = <String, MetricStatistics>{};
      for (final entry in metricsByName.entries) {
        final values = entry.value.map((m) => m.value).toList();
        values.sort();
        
        metricStats[entry.key] = MetricStatistics(
          name: entry.key,
          count: values.length,
          min: values.first,
          max: values.last,
          average: values.reduce((a, b) => a + b) / values.length,
          median: values[values.length ~/ 2],
        );
      }
      
      return PerformanceStatistics(
        timeWindow: window,
        totalMetrics: recentMetrics.length,
        metricStats: metricStats,
        memoryUsage: _lastMemoryUsage,
        activeResources: _disposableResources.length,
        activeTimers: _activeTimers.length,
        activeSubscriptions: _activeSubscriptions.length,
        generatedAt: DateTime.now(),
      );
    } catch (e) {
      AppLogger.error('❌ 获取性能统计失败', error: e);
      return PerformanceStatistics.empty();
    }
  }
  
  /// 获取内存使用情况
  Future<MemoryInfo> getMemoryInfo() async {
    try {
      int memoryUsage = 0;
      
      if (Platform.isAndroid) {
        // Android内存信息
        try {
          final result = await const MethodChannel('loadguard/memory')
              .invokeMethod<Map>('getMemoryInfo');
          memoryUsage = result?['usedMemory'] ?? 0;
        } catch (e) {
          // 如果原生方法不可用，使用估算值
          memoryUsage = _estimateMemoryUsage();
        }
      } else {
        // 其他平台使用估算值
        memoryUsage = _estimateMemoryUsage();
      }
      
      _lastMemoryUsage = memoryUsage;
      
      return MemoryInfo(
        usedMemory: memoryUsage,
        timestamp: DateTime.now(),
        activeObjects: _disposableResources.length,
      );
    } catch (e) {
      AppLogger.error('❌ 获取内存信息失败', error: e);
      return MemoryInfo(
        usedMemory: _lastMemoryUsage,
        timestamp: DateTime.now(),
        activeObjects: _disposableResources.length,
      );
    }
  }
  
  /// 优化性能
  Future<void> optimizePerformance() async {
    try {
      AppLogger.info('⚡ 开始性能优化...');
      
      // 1. 清理过期资源
      await _cleanupExpiredResources();
      
      // 2. 优化内存使用
      await _optimizeMemoryUsage();
      
      // 3. 清理缓存
      await _cleanupCaches();
      
      // 4. 强制垃圾回收
      await _forceGarbageCollection();
      
      AppLogger.info('✅ 性能优化完成');
      
      _performanceStream.add(PerformanceUpdate(
        type: PerformanceUpdateType.optimizationCompleted,
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      AppLogger.error('❌ 性能优化失败', error: e);
    }
  }
  
  /// 启动内存监控
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(const Duration(minutes: 1), (timer) async {
      try {
        final memoryInfo = await getMemoryInfo();
        
        recordMetric('memory_usage', memoryInfo.usedMemory.toDouble(), metadata: {
          'activeObjects': memoryInfo.activeObjects,
        });
        
        // 检查内存泄漏
        if (_lastMemoryUsage > 0 && memoryInfo.usedMemory > _lastMemoryUsage * 1.5) {
          AppLogger.warning('⚠️ 检测到可能的内存泄漏: ${memoryInfo.usedMemory}MB');
          
          _performanceStream.add(PerformanceUpdate(
            type: PerformanceUpdateType.memoryWarning,
            timestamp: DateTime.now(),
            data: {'memoryUsage': memoryInfo.usedMemory},
          ));
        }
      } catch (e) {
        AppLogger.error('❌ 内存监控失败', error: e);
      }
    });
  }
  
  /// 设置生命周期观察者
  void _setupLifecycleObserver() {
    // 这里可以添加应用生命周期监听
    AppLogger.debug('📱 设置应用生命周期观察者');
  }
  
  /// 设置错误处理
  void _setupErrorHandling() {
    // 捕获未处理的异常
    FlutterError.onError = (FlutterErrorDetails details) {
      recordMetric('flutter_error', 1.0, metadata: {
        'error': details.exception.toString(),
        'stack': details.stack.toString(),
      });
    };
  }
  
  /// 清理过期资源
  Future<void> _cleanupExpiredResources() async {
    // 移除已经无效的定时器
    _activeTimers.removeWhere((timer) => !timer.isActive);
    
    // 这里可以添加更多的过期资源清理逻辑
    AppLogger.debug('🧹 清理过期资源完成');
  }
  
  /// 优化内存使用
  Future<void> _optimizeMemoryUsage() async {
    // 清理性能指标历史
    if (_performanceMetrics.length > 500) {
      _performanceMetrics.removeRange(0, _performanceMetrics.length - 500);
    }
    
    AppLogger.debug('💾 内存使用优化完成');
  }
  
  /// 清理缓存
  Future<void> _cleanupCaches() async {
    try {
      // 清理图片缓存
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      AppLogger.debug('🗑️ 缓存清理完成');
    } catch (e) {
      AppLogger.warning('⚠️ 缓存清理失败: $e');
    }
  }
  
  /// 强制垃圾回收
  Future<void> _forceGarbageCollection() async {
    if (kDebugMode) {
      // 在调试模式下可以尝试强制GC
      try {
        await Future.delayed(const Duration(milliseconds: 100));
        AppLogger.debug('🗑️ 垃圾回收完成');
      } catch (e) {
        AppLogger.warning('⚠️ 垃圾回收失败: $e');
      }
    }
  }
  
  /// 估算内存使用
  int _estimateMemoryUsage() {
    // 基于活跃对象数量估算内存使用（MB）
    final baseMemory = 50; // 基础内存使用
    final objectMemory = _disposableResources.length * 0.1; // 每个对象约0.1MB
    final metricMemory = _performanceMetrics.length * 0.001; // 每个指标约0.001MB
    
    return (baseMemory + objectMemory + metricMemory).round();
  }
  
  /// 释放资源
  Future<void> dispose() async {
    if (!_initialized) return;

    try {
      _memoryMonitorTimer?.cancel();
      await cleanupAllResources();
      _performanceStream.close();
      _performanceMetrics.clear();

      _initialized = false;
      AppLogger.info('🚀 性能管理器已释放');
    } catch (e) {
      AppLogger.error('❌ 性能管理器释放失败', error: e);
    }
  }
}

/// 可释放资源接口
abstract class Disposable {
  Future<void> dispose();
}

/// 性能指标
class PerformanceMetric {
  final String name;
  final double value;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const PerformanceMetric({
    required this.name,
    required this.value,
    required this.timestamp,
    required this.metadata,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  @override
  String toString() {
    return 'PerformanceMetric($name: $value)';
  }
}

/// 性能更新类型
enum PerformanceUpdateType {
  metricRecorded,
  memoryWarning,
  optimizationCompleted,
  resourceCleaned,
}

/// 性能更新
class PerformanceUpdate {
  final PerformanceUpdateType type;
  final PerformanceMetric? metric;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const PerformanceUpdate({
    required this.type,
    this.metric,
    required this.timestamp,
    this.data,
  });

  @override
  String toString() {
    return 'PerformanceUpdate(${type.name})';
  }
}

/// 指标统计
class MetricStatistics {
  final String name;
  final int count;
  final double min;
  final double max;
  final double average;
  final double median;

  const MetricStatistics({
    required this.name,
    required this.count,
    required this.min,
    required this.max,
    required this.average,
    required this.median,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'count': count,
      'min': min,
      'max': max,
      'average': average,
      'median': median,
    };
  }

  @override
  String toString() {
    return 'MetricStatistics($name: avg=$average, count=$count)';
  }
}

/// 性能统计
class PerformanceStatistics {
  final Duration timeWindow;
  final int totalMetrics;
  final Map<String, MetricStatistics> metricStats;
  final int memoryUsage;
  final int activeResources;
  final int activeTimers;
  final int activeSubscriptions;
  final DateTime generatedAt;

  const PerformanceStatistics({
    required this.timeWindow,
    required this.totalMetrics,
    required this.metricStats,
    required this.memoryUsage,
    required this.activeResources,
    required this.activeTimers,
    required this.activeSubscriptions,
    required this.generatedAt,
  });

  factory PerformanceStatistics.empty() {
    return PerformanceStatistics(
      timeWindow: Duration.zero,
      totalMetrics: 0,
      metricStats: {},
      memoryUsage: 0,
      activeResources: 0,
      activeTimers: 0,
      activeSubscriptions: 0,
      generatedAt: DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'timeWindow': timeWindow.inMinutes,
      'totalMetrics': totalMetrics,
      'metricStats': metricStats.map((k, v) => MapEntry(k, v.toMap())),
      'memoryUsage': memoryUsage,
      'activeResources': activeResources,
      'activeTimers': activeTimers,
      'activeSubscriptions': activeSubscriptions,
      'generatedAt': generatedAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PerformanceStatistics(metrics: $totalMetrics, memory: ${memoryUsage}MB, resources: $activeResources)';
  }
}

/// 内存信息
class MemoryInfo {
  final int usedMemory; // MB
  final DateTime timestamp;
  final int activeObjects;

  const MemoryInfo({
    required this.usedMemory,
    required this.timestamp,
    required this.activeObjects,
  });

  Map<String, dynamic> toMap() {
    return {
      'usedMemory': usedMemory,
      'timestamp': timestamp.toIso8601String(),
      'activeObjects': activeObjects,
    };
  }

  @override
  String toString() {
    return 'MemoryInfo(${usedMemory}MB, objects: $activeObjects)';
  }
}

/// ✅ ML Kit性能监控扩展
extension MLKitPerformanceExtension on PerformanceManager {
  /// 记录ML Kit识别性能
  void recordMLKitPerformance({
    required RecognitionAlgorithm algorithm,
    required Duration processingTime,
    required bool successful,
    required double confidence,
    Map<String, dynamic>? additionalData,
  }) {
    final metric = PerformanceMetric(
      name: 'mlkit_recognition',
      value: processingTime.inMilliseconds.toDouble(),
      timestamp: DateTime.now(),
      metadata: {
        'algorithm': algorithm.chineseName,
        'successful': successful,
        'confidence': confidence,
        'processing_time_ms': processingTime.inMilliseconds,
        ...?additionalData,
      },
    );

    recordMetric(metric.name, metric.value, metadata: metric.metadata);

    // 检查性能阈值（3秒）
    if (processingTime.inMilliseconds > 3000) {
      AppLogger.warning('⚠️ ML Kit识别性能下降: ${processingTime.inMilliseconds}ms > 3000ms');
    }
  }

  /// 获取ML Kit性能统计
  Map<String, dynamic> getMLKitPerformanceStats() {
    final mlkitMetrics = _performanceMetrics
        .where((m) => m.name == 'mlkit_recognition')
        .toList();

    if (mlkitMetrics.isEmpty) {
      return {
        'totalRecognitions': 0,
        'averageTime': 0.0,
        'successRate': 0.0,
        'averageConfidence': 0.0,
      };
    }

    final totalTime = mlkitMetrics.fold(0.0, (sum, m) => sum + m.value);
    final successfulCount = mlkitMetrics
        .where((m) => m.metadata?['successful'] == true)
        .length;
    final totalConfidence = mlkitMetrics.fold(0.0,
        (sum, m) => sum + (m.metadata?['confidence'] ?? 0.0));

    return {
      'totalRecognitions': mlkitMetrics.length,
      'averageTime': totalTime / mlkitMetrics.length,
      'successRate': successfulCount / mlkitMetrics.length,
      'averageConfidence': totalConfidence / mlkitMetrics.length,
    };
  }
}

/// ⚠️ 已废弃：兼容性类，请使用 PerformanceManager 替代
@Deprecated('使用 PerformanceManager 替代')
class PerformanceOptimizer {
  static PerformanceOptimizer? _instance;
  static PerformanceOptimizer get instance => _instance ??= PerformanceOptimizer._();

  PerformanceOptimizer._();

  /// 重定向到新的性能管理器
  final PerformanceManager _performanceManager = PerformanceManager.instance;

  /// ⚠️ 已废弃：请使用 PerformanceManager.initialize() 替代
  @Deprecated('使用 PerformanceManager.initialize() 替代')
  Future<void> initialize() async {
    AppLogger.warning('⚠️ PerformanceOptimizer已废弃，请使用PerformanceManager');
    _performanceManager.initialize(); // 移除await，因为initialize()返回void
  }

  /// ⚠️ 已废弃：请使用 PerformanceManager.dispose() 替代
  @Deprecated('使用 PerformanceManager.dispose() 替代')
  void dispose() {
    AppLogger.warning('⚠️ PerformanceOptimizer.dispose已废弃');
    // 不调用_performanceManager.dispose()，因为它可能被其他地方使用
  }
}

/// 性能管理器Provider
final performanceManagerProvider = Provider<PerformanceManager>((ref) {
  final manager = PerformanceManager.instance;
  manager.initialize();
  ref.onDispose(() => manager.dispose());
  return manager;
});
