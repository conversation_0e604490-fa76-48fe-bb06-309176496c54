import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'hardware_fingerprint.dart';
import 'secure_key_manager.dart';
import 'security_audit_logger.dart';

/// 🛡️ 试用期保护服务
/// 防止用户通过清除数据等方式重复获取试用期
class TrialProtectionService {
  // 存储键
  static const String _trialFingerprintKey = 'trial_device_fingerprint_v2';
  static const String _trialStartTimeKey = 'trial_start_timestamp_v2';
  static const String _trialMarkersKey = 'trial_protection_markers_v2';
  static const String _trialAttemptKey = 'trial_activation_attempts_v2';
  static const String _systemMarkersKey = 'system_protection_markers_v2';

  // 试用期配置
  static const int _trialDurationDays = 7;
  static const int _maxTrialAttempts = 3;
  static const int _suspiciousActivityThreshold = 5;

  /// 检查是否可以开始试用期
  static Future<TrialEligibilityResult> checkTrialEligibility() async {
    try {
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.trialEligibilityCheck(
          description: '检查试用期资格',
          metadata: {'timestamp': DateTime.now().toIso8601String()},
        ),
      );

      final prefs = await SharedPreferences.getInstance();

      // 1. 检查试用尝试次数
      final attempts = await _getTrialAttempts();
      if (attempts >= _maxTrialAttempts) {
        return TrialEligibilityResult.rejected('试用次数已达上限');
      }

      // 2. 获取当前设备指纹
      final currentFingerprint =
          await HardwareFingerprint.generateFingerprint();

      // 3. 检查是否已有试用记录
      final storedFingerprint = prefs.getString(_trialFingerprintKey);

      if (storedFingerprint == null) {
        // 首次使用，可以开始试用
        final now = DateTime.now();
        final expiryDate = now.add(Duration(days: _trialDurationDays));
        return TrialEligibilityResult.eligible(
          '首次使用，可以开始试用',
          expiryDate: expiryDate,
          remainingDays: _trialDurationDays,
          totalDays: _trialDurationDays,
          startDate: now,
        );
      }

      // 4. 验证设备指纹
      if (storedFingerprint == currentFingerprint) {
        // 同一设备，检查试用期状态
        return await _checkExistingTrial();
      }

      // 5. 设备指纹不匹配，进行深度验证
      final deepVerificationResult = await _performDeepDeviceVerification(
          storedFingerprint, currentFingerprint);

      if (deepVerificationResult.isSameDevice) {
        // 可能是设备信息轻微变化，检查现有试用状态
        return await _checkExistingTrial();
      } else {
        // 疑似不同设备或恶意绕过，记录并增加尝试次数
        await _recordSuspiciousActivity('Device fingerprint mismatch');
        await _incrementTrialAttempts();

        // 检查系统级保护标记
        if (await _hasSystemProtectionMarkers()) {
          return TrialEligibilityResult.rejected('检测到系统级试用保护标记');
        }

        // 新设备，可以开始试用
        final now = DateTime.now();
        final expiryDate = now.add(Duration(days: _trialDurationDays));
        return TrialEligibilityResult.eligible(
          '新设备，可以开始试用',
          expiryDate: expiryDate,
          remainingDays: _trialDurationDays,
          totalDays: _trialDurationDays,
          startDate: now,
        );
      }
    } catch (e) {
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.trialError(
          description: '试用期检查失败',
          metadata: {'error': e.toString()},
        ),
      );

      return TrialEligibilityResult.error('试用期检查失败: $e');
    }
  }

  /// 开始试用期
  static Future<TrialActivationResult> startTrial() async {
    try {
      // 1. 再次验证资格
      final eligibility = await checkTrialEligibility();
      if (!eligibility.isEligible) {
        return TrialActivationResult.failed(eligibility.reason);
      }

      // 2. 获取设备指纹
      final deviceFingerprint = await HardwareFingerprint.generateFingerprint();

      // 3. 生成试用保护数据
      final trialData = await _generateTrialProtectionData(deviceFingerprint);

      // 4. 存储试用信息
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();

      await prefs.setString(_trialFingerprintKey, deviceFingerprint);
      await prefs.setInt(_trialStartTimeKey, now.millisecondsSinceEpoch);
      await prefs.setString(_trialMarkersKey, jsonEncode(trialData));

      // 5. 设置系统级保护标记
      await _setSystemProtectionMarkers(deviceFingerprint);

      // 6. 记录试用激活事件
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.trialActivated(
          description: '试用期已激活',
          metadata: {
            'device_fingerprint': deviceFingerprint.substring(0, 8),
            'start_time': now.toIso8601String(),
            'duration_days': _trialDurationDays,
          },
        ),
      );

      final expiryDate = now.add(Duration(days: _trialDurationDays));

      return TrialActivationResult.success(
        startDate: now,
        expiryDate: expiryDate,
        remainingDays: _trialDurationDays,
      );
    } catch (e) {
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.trialError(
          description: '试用期激活失败',
          metadata: {'error': e.toString()},
        ),
      );

      return TrialActivationResult.failed('试用期激活失败: $e');
    }
  }

  /// 获取试用期状态
  static Future<TrialStatus> getTrialStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 1. 检查是否有试用记录
      final storedFingerprint = prefs.getString(_trialFingerprintKey);
      final startTime = prefs.getInt(_trialStartTimeKey);

      if (storedFingerprint == null || startTime == null) {
        return TrialStatus.notStarted();
      }

      // 2. 验证设备指纹
      final currentFingerprint =
          await HardwareFingerprint.generateFingerprint();
      if (storedFingerprint != currentFingerprint) {
        // 设备指纹不匹配，可能是恶意绕过
        await _recordSuspiciousActivity('Device fingerprint validation failed');
        return TrialStatus.invalid('设备验证失败');
      }

      // 3. 验证试用保护数据
      final markersJson = prefs.getString(_trialMarkersKey);
      if (markersJson != null) {
        final markers = jsonDecode(markersJson) as Map<String, dynamic>;
        if (!await _validateTrialProtectionData(markers, currentFingerprint)) {
          await _recordSuspiciousActivity(
              'Trial protection data validation failed');
          return TrialStatus.invalid('试用数据验证失败');
        }
      }

      // 4. 计算试用状态
      final startDate = DateTime.fromMillisecondsSinceEpoch(startTime);
      final now = DateTime.now();
      final elapsedDays = now.difference(startDate).inDays;
      final remainingDays = _trialDurationDays - elapsedDays;

      if (remainingDays > 0) {
        return TrialStatus.active(
          startDate: startDate,
          remainingDays: remainingDays,
          totalDays: _trialDurationDays,
        );
      } else {
        return TrialStatus.expired(
          startDate: startDate,
          expiredDays: -remainingDays,
        );
      }
    } catch (e) {
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.trialError(
          description: '获取试用状态失败',
          metadata: {'error': e.toString()},
        ),
      );

      return TrialStatus.error('获取试用状态失败: $e');
    }
  }

  /// 检查系统级保护
  static Future<bool> hasSystemLevelProtection() async {
    try {
      return await _hasSystemProtectionMarkers();
    } catch (e) {
      return false;
    }
  }

  /// 获取保护状态统计
  static Future<Map<String, dynamic>> getProtectionStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final attempts = await _getTrialAttempts();
      final hasMarkers = await _hasSystemProtectionMarkers();
      final trialStatus = await getTrialStatus();

      return {
        'trial_attempts': attempts,
        'max_attempts': _maxTrialAttempts,
        'has_system_markers': hasMarkers,
        'trial_status': trialStatus.status,
        'protection_active': hasMarkers || attempts > 0,
        'last_check': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'last_check': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 重置试用期数据（用于修复数据损坏问题）
  static Future<bool> resetTrialData() async {
    try {
      print('重置试用期数据...');

      final prefs = await SharedPreferences.getInstance();

      // 清除所有试用期相关数据
      await prefs.remove(_trialFingerprintKey);
      await prefs.remove(_trialStartTimeKey);
      await prefs.remove(_trialMarkersKey);
      await prefs.remove(_trialAttemptKey);
      await prefs.remove(_systemMarkersKey);

      print('试用期数据重置完成');
      return true;
    } catch (e) {
      print('重置试用期数据失败: $e');
      return false;
    }
  }

  // 私有方法实现

  /// 检查现有试用状态
  static Future<TrialEligibilityResult> _checkExistingTrial() async {
    final status = await getTrialStatus();

    switch (status.status) {
      case 'active':
        return TrialEligibilityResult.rejected('试用期正在进行中');
      case 'expired':
        return TrialEligibilityResult.rejected('试用期已过期');
      case 'invalid':
        return TrialEligibilityResult.rejected('试用数据无效');
      default:
        return TrialEligibilityResult.eligible('可以开始试用');
    }
  }

  /// 深度设备验证
  static Future<DeviceVerificationResult> _performDeepDeviceVerification(
      String storedFingerprint, String currentFingerprint) async {
    // 使用模糊匹配算法检查设备指纹相似度
    final similarity =
        _calculateFingerprintSimilarity(storedFingerprint, currentFingerprint);

    // 如果相似度超过80%，认为是同一设备（可能是系统更新等原因导致轻微变化）
    final isSameDevice = similarity >= 0.8;

    return DeviceVerificationResult(
      isSameDevice: isSameDevice,
      similarity: similarity,
      reason: isSameDevice ? '设备指纹相似度高' : '设备指纹差异过大',
    );
  }

  /// 计算指纹相似度
  static double _calculateFingerprintSimilarity(String fp1, String fp2) {
    if (fp1.length != fp2.length) return 0.0;

    int matchCount = 0;
    for (int i = 0; i < fp1.length; i++) {
      if (fp1[i] == fp2[i]) matchCount++;
    }

    return matchCount / fp1.length;
  }

  /// 生成试用保护数据
  static Future<Map<String, dynamic>> _generateTrialProtectionData(
      String deviceFingerprint) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random.secure();

    // 生成多个保护标记
    final protectionKey = SecureKeyManager.getTrialProtectionKey();
    final seed = '$deviceFingerprint:$timestamp:${random.nextInt(999999)}';

    return {
      'protection_hash': _generateProtectionHash(seed, protectionKey),
      'timestamp': timestamp,
      'device_context': _generateDeviceContext(deviceFingerprint),
      'validation_token':
          _generateValidationToken(deviceFingerprint, timestamp),
      'entropy_seed': random.nextInt(999999),
    };
  }

  /// 验证试用保护数据
  static Future<bool> _validateTrialProtectionData(
      Map<String, dynamic> data, String currentFingerprint) async {
    try {
      final protectionKey = SecureKeyManager.getTrialProtectionKey();
      final timestamp = data['timestamp'] as int;
      final entropySeed = data['entropy_seed'] as int;

      // 重新生成并验证保护哈希
      final seed = '$currentFingerprint:$timestamp:$entropySeed';
      final expectedHash = _generateProtectionHash(seed, protectionKey);
      final storedHash = data['protection_hash'] as String;

      if (expectedHash != storedHash) return false;

      // 验证设备上下文
      final expectedContext = _generateDeviceContext(currentFingerprint);
      final storedContext = data['device_context'] as String;

      if (expectedContext != storedContext) return false;

      // 验证令牌
      final expectedToken =
          _generateValidationToken(currentFingerprint, timestamp);
      final storedToken = data['validation_token'] as String;

      return expectedToken == storedToken;
    } catch (e) {
      return false;
    }
  }

  /// 生成保护哈希
  static String _generateProtectionHash(String seed, String key) {
    final combined = '$seed:$key';
    final bytes = utf8.encode(combined);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 生成设备上下文
  static String _generateDeviceContext(String deviceFingerprint) {
    final contextData = '$deviceFingerprint:TRIAL_CONTEXT:2024';
    final bytes = utf8.encode(contextData);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16);
  }

  /// 生成验证令牌
  static String _generateValidationToken(
      String deviceFingerprint, int timestamp) {
    final tokenData = '$deviceFingerprint:$timestamp:VALIDATION';
    final bytes = utf8.encode(tokenData);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 24);
  }

  /// 设置系统级保护标记
  static Future<void> _setSystemProtectionMarkers(
      String deviceFingerprint) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final markers = {
        'device_fingerprint': deviceFingerprint,
        'created_at': DateTime.now().millisecondsSinceEpoch,
        'protection_version': 2,
        'system_marker': _generateSystemMarker(deviceFingerprint),
      };

      await prefs.setString(_systemMarkersKey, jsonEncode(markers));
    } catch (e) {
      print('设置系统保护标记失败: $e');
    }
  }

  /// 检查系统级保护标记
  static Future<bool> _hasSystemProtectionMarkers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final markersJson = prefs.getString(_systemMarkersKey);

      if (markersJson == null) return false;

      final markers = jsonDecode(markersJson) as Map<String, dynamic>;
      final storedFingerprint = markers['device_fingerprint'] as String;
      final currentFingerprint =
          await HardwareFingerprint.generateFingerprint();

      // 验证系统标记
      final expectedMarker = _generateSystemMarker(storedFingerprint);
      final storedMarker = markers['system_marker'] as String;

      return expectedMarker == storedMarker;
    } catch (e) {
      return false;
    }
  }

  /// 生成系统标记
  static String _generateSystemMarker(String deviceFingerprint) {
    final markerData = '$deviceFingerprint:SYSTEM_MARKER:LoadGuard_2024';
    final bytes = utf8.encode(markerData);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 32);
  }

  /// 记录可疑活动
  static Future<void> _recordSuspiciousActivity(String activity) async {
    try {
      await SecurityAuditLogger.logSecurityEvent(
        SecurityEvent.suspiciousActivity(
          description: activity,
          metadata: {
            'timestamp': DateTime.now().toIso8601String(),
            'activity_type': 'trial_protection',
          },
        ),
      );
    } catch (e) {
      print('记录可疑活动失败: $e');
    }
  }

  /// 获取试用尝试次数
  static Future<int> _getTrialAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_trialAttemptKey) ?? 0;
    } catch (e) {
      return 0;
    }
  }

  /// 增加试用尝试次数
  static Future<void> _incrementTrialAttempts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentAttempts = await _getTrialAttempts();
      await prefs.setInt(_trialAttemptKey, currentAttempts + 1);
    } catch (e) {
      print('增加试用尝试次数失败: $e');
    }
  }
}

// 数据类定义

/// 试用资格检查结果
class TrialEligibilityResult {
  final bool isEligible;
  final String reason;
  final DateTime? expiryDate;
  final int? remainingDays;
  final int? totalDays;
  final DateTime? startDate;

  TrialEligibilityResult._(
    this.isEligible,
    this.reason, {
    this.expiryDate,
    this.remainingDays,
    this.totalDays,
    this.startDate,
  });

  factory TrialEligibilityResult.eligible(
    String reason, {
    DateTime? expiryDate,
    int? remainingDays,
    int? totalDays,
    DateTime? startDate,
  }) =>
      TrialEligibilityResult._(
        true,
        reason,
        expiryDate: expiryDate,
        remainingDays: remainingDays,
        totalDays: totalDays,
        startDate: startDate,
      );

  factory TrialEligibilityResult.rejected(String reason) =>
      TrialEligibilityResult._(false, reason);

  factory TrialEligibilityResult.error(String reason) =>
      TrialEligibilityResult._(false, reason);
}

/// 试用激活结果
class TrialActivationResult {
  final bool isSuccess;
  final String? message;
  final DateTime? startDate;
  final DateTime? expiryDate;
  final int? remainingDays;

  TrialActivationResult._({
    required this.isSuccess,
    this.message,
    this.startDate,
    this.expiryDate,
    this.remainingDays,
  });

  factory TrialActivationResult.success({
    required DateTime startDate,
    required DateTime expiryDate,
    required int remainingDays,
  }) =>
      TrialActivationResult._(
        isSuccess: true,
        startDate: startDate,
        expiryDate: expiryDate,
        remainingDays: remainingDays,
      );

  factory TrialActivationResult.failed(String message) =>
      TrialActivationResult._(isSuccess: false, message: message);
}

/// 试用状态
class TrialStatus {
  final String status;
  final String? message;
  final DateTime? startDate;
  final int? remainingDays;
  final int? totalDays;
  final int? expiredDays;

  TrialStatus._({
    required this.status,
    this.message,
    this.startDate,
    this.remainingDays,
    this.totalDays,
    this.expiredDays,
  });

  factory TrialStatus.notStarted() => TrialStatus._(status: 'not_started');

  factory TrialStatus.active({
    required DateTime startDate,
    required int remainingDays,
    required int totalDays,
  }) =>
      TrialStatus._(
        status: 'active',
        startDate: startDate,
        remainingDays: remainingDays,
        totalDays: totalDays,
      );

  factory TrialStatus.expired({
    required DateTime startDate,
    required int expiredDays,
  }) =>
      TrialStatus._(
        status: 'expired',
        startDate: startDate,
        expiredDays: expiredDays,
      );

  factory TrialStatus.invalid(String message) =>
      TrialStatus._(status: 'invalid', message: message);

  factory TrialStatus.error(String message) =>
      TrialStatus._(status: 'error', message: message);
}

/// 设备验证结果
class DeviceVerificationResult {
  final bool isSameDevice;
  final double similarity;
  final String reason;

  DeviceVerificationResult({
    required this.isSameDevice,
    required this.similarity,
    required this.reason,
  });
}
