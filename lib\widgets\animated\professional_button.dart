import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/material3_theme.dart';

/// 🎯 Material 3 专业级按钮组件
/// 完全符合 Material Design 3 规范的按钮系统
class ProfessionalButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final Material3ButtonType type;
  final Material3ButtonSize size;
  final bool isLoading;
  final bool hasIcon;
  final Widget? customChild;
  final EdgeInsetsGeometry? padding;
  final double? width;
  final double? height;

  const ProfessionalButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.icon,
    this.type = Material3ButtonType.filled,
    this.size = Material3ButtonSize.medium,
    this.isLoading = false,
    this.hasIcon = false,
    this.customChild,
    this.padding,
    this.width,
    this.height,
  }) : super(key: key);

  /// 创建 Filled 按钮（主要操作）
  const ProfessionalButton.filled({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    Material3ButtonSize size = Material3ButtonSize.medium,
    bool isLoading = false,
    Widget? customChild,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          icon: icon,
          type: Material3ButtonType.filled,
          size: size,
          isLoading: isLoading,
          hasIcon: icon != null,
          customChild: customChild,
          padding: padding,
          width: width,
          height: height,
        );

  /// 创建 Outlined 按钮（次要操作）
  const ProfessionalButton.outlined({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    Material3ButtonSize size = Material3ButtonSize.medium,
    bool isLoading = false,
    Widget? customChild,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          icon: icon,
          type: Material3ButtonType.outlined,
          size: size,
          isLoading: isLoading,
          hasIcon: icon != null,
          customChild: customChild,
          padding: padding,
          width: width,
          height: height,
        );

  /// 创建 Text 按钮（最低优先级操作）
  const ProfessionalButton.text({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    Material3ButtonSize size = Material3ButtonSize.medium,
    bool isLoading = false,
    Widget? customChild,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          icon: icon,
          type: Material3ButtonType.text,
          size: size,
          isLoading: isLoading,
          hasIcon: icon != null,
          customChild: customChild,
          padding: padding,
          width: width,
          height: height,
        );

  /// 创建 Elevated 按钮（需要强调的操作）
  const ProfessionalButton.elevated({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    Material3ButtonSize size = Material3ButtonSize.medium,
    bool isLoading = false,
    Widget? customChild,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
  }) : this(
          key: key,
          text: text,
          onPressed: onPressed,
          icon: icon,
          type: Material3ButtonType.elevated,
          size: size,
          isLoading: isLoading,
          hasIcon: icon != null,
          customChild: customChild,
          padding: padding,
          width: width,
          height: height,
        );

  @override
  State<ProfessionalButton> createState() => _ProfessionalButtonState();
}

class _ProfessionalButtonState extends State<ProfessionalButton>
    with TickerProviderStateMixin {
  late AnimationController _stateLayerController;
  late AnimationController _loadingController;
  late AnimationController _pressController;
  
  late Animation<double> _stateLayerAnimation;
  late Animation<double> _pressAnimation;
  
  bool _isHovered = false;
  bool _isPressed = false;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    
    // 状态层动画控制器
    _stateLayerController = AnimationController(
      duration: Material3Theme.shortDuration,
      vsync: this,
    );
    
    // 加载动画控制器
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    // 按压动画控制器
    _pressController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    // 状态层透明度动画
    _stateLayerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stateLayerController,
      curve: Material3Theme.standardCurve,
    ));
    
    // 按压缩放动画
    _pressAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _pressController,
      curve: Material3Theme.accelerateCurve,
    ));
    
    // 如果正在加载，启动加载动画
    if (widget.isLoading) {
      _loadingController.repeat();
    }
  }

  @override
  void didUpdateWidget(ProfessionalButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // 处理加载状态变化
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _loadingController.repeat();
      } else {
        _loadingController.stop();
        _loadingController.reset();
      }
    }
  }

  @override
  void dispose() {
    _stateLayerController.dispose();
    _loadingController.dispose();
    _pressController.dispose();
    super.dispose();
  }

  void _handleHoverEnter() {
    if (!_isEnabled) return;
    setState(() => _isHovered = true);
    _stateLayerController.forward();
  }

  void _handleHoverExit() {
    setState(() => _isHovered = false);
    if (!_isPressed && !_isFocused) {
      _stateLayerController.reverse();
    }
  }

  void _handleTapDown(TapDownDetails details) {
    if (!_isEnabled) return;
    
    setState(() => _isPressed = true);
    _pressController.forward();
    _stateLayerController.forward();
    
    // Material 3 触觉反馈
    HapticFeedback.lightImpact();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!_isEnabled) return;
    
    setState(() => _isPressed = false);
    _pressController.reverse();
    
    if (!_isHovered && !_isFocused) {
      _stateLayerController.reverse();
    }
    
    // 执行回调
    widget.onPressed?.call();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _pressController.reverse();
    
    if (!_isHovered && !_isFocused) {
      _stateLayerController.reverse();
    }
  }

  void _handleFocusChange(bool hasFocus) {
    setState(() => _isFocused = hasFocus);
    
    if (hasFocus) {
      _stateLayerController.forward();
    } else if (!_isHovered && !_isPressed) {
      _stateLayerController.reverse();
    }
  }

  bool get _isEnabled => widget.onPressed != null && !widget.isLoading;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Material3Theme.getColorScheme(context);
    final textTheme = Material3Theme.getTextTheme(context);
    final buttonConfig = _getButtonConfiguration(colorScheme);
    final sizeConfig = _getSizeConfiguration();

    return MouseRegion(
      onEnter: (_) => _handleHoverEnter(),
      onExit: (_) => _handleHoverExit(),
      child: Focus(
        onFocusChange: _handleFocusChange,
        child: GestureDetector(
          onTapDown: _handleTapDown,
          onTapUp: _handleTapUp,
          onTapCancel: _handleTapCancel,
          child: AnimatedBuilder(
            animation: Listenable.merge([
              _stateLayerAnimation,
              _pressAnimation,
            ]),
            builder: (context, child) {
              return Transform.scale(
                scale: _pressAnimation.value,
                child: Container(
                  width: widget.width,
                  height: widget.height ?? sizeConfig.height,
                  constraints: BoxConstraints(
                    minWidth: sizeConfig.minWidth,
                    minHeight: sizeConfig.height,
                  ),
                  child: Material(
                    type: MaterialType.button,
                    color: _isEnabled 
                        ? buttonConfig.backgroundColor 
                        : buttonConfig.disabledBackgroundColor,
                    elevation: buttonConfig.elevation,
                    shadowColor: colorScheme.shadow,
                    surfaceTintColor: buttonConfig.surfaceTintColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(sizeConfig.borderRadius),
                      side: buttonConfig.borderSide ?? BorderSide.none,
                    ),
                    child: InkWell(
                      onTap: _isEnabled ? () {} : null,
                      borderRadius: BorderRadius.circular(sizeConfig.borderRadius),
                      splashColor: buttonConfig.splashColor,
                      highlightColor: buttonConfig.highlightColor,
                      child: Stack(
                        children: [
                          // 状态层
                          if (_stateLayerAnimation.value > 0)
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: _getStateLayerColor(buttonConfig)
                                      .withOpacity(_stateLayerAnimation.value * _getStateLayerOpacity()),
                                  borderRadius: BorderRadius.circular(sizeConfig.borderRadius),
                                ),
                              ),
                            ),
                          
                          // 按钮内容
                          Container(
                            padding: widget.padding ?? sizeConfig.padding,
                            child: Center(
                              child: widget.customChild ?? _buildButtonContent(
                                buttonConfig,
                                textTheme,
                                sizeConfig,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 构建按钮内容
  Widget _buildButtonContent(
    _ButtonConfiguration config,
    TextTheme textTheme,
    _SizeConfiguration sizeConfig,
  ) {
    final textColor = _isEnabled ? config.foregroundColor : config.disabledForegroundColor;
    final textStyle = sizeConfig.textStyle.copyWith(color: textColor);

    if (widget.isLoading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: sizeConfig.iconSize,
            height: sizeConfig.iconSize,
            child: AnimatedBuilder(
              animation: _loadingController,
              builder: (context, child) {
                return CircularProgressIndicator(
                  value: null,
                  strokeWidth: 2.0,
                  valueColor: AlwaysStoppedAnimation<Color>(textColor),
                );
              },
            ),
          ),
          SizedBox(width: Material3Tokens.space8),
          Text('加载中...', style: textStyle),
        ],
      );
    }

    if (widget.hasIcon && widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.icon,
            size: sizeConfig.iconSize,
            color: textColor,
          ),
          SizedBox(width: Material3Tokens.space8),
          Text(widget.text, style: textStyle),
        ],
      );
    }

    return Text(widget.text, style: textStyle);
  }

  /// 获取按钮配置
  _ButtonConfiguration _getButtonConfiguration(ColorScheme colorScheme) {
    switch (widget.type) {
      case Material3ButtonType.filled:
        return _ButtonConfiguration(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity12),
          disabledForegroundColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity38),
          elevation: Material3Tokens.elevation0,
          surfaceTintColor: null,
          splashColor: colorScheme.onPrimary.withOpacity(Material3Tokens.opacity12),
          highlightColor: colorScheme.onPrimary.withOpacity(Material3Tokens.opacity08),
          stateLayerColor: colorScheme.onPrimary,
        );
        
      case Material3ButtonType.outlined:
        return _ButtonConfiguration(
          backgroundColor: Colors.transparent,
          foregroundColor: colorScheme.primary,
          disabledBackgroundColor: Colors.transparent,
          disabledForegroundColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity38),
          elevation: Material3Tokens.elevation0,
          surfaceTintColor: null,
          borderSide: BorderSide(
            color: _isEnabled ? colorScheme.outline : colorScheme.onSurface.withOpacity(Material3Tokens.opacity12),
            width: 1.0,
          ),
          splashColor: colorScheme.primary.withOpacity(Material3Tokens.opacity12),
          highlightColor: colorScheme.primary.withOpacity(Material3Tokens.opacity08),
          stateLayerColor: colorScheme.primary,
        );
        
      case Material3ButtonType.text:
        return _ButtonConfiguration(
          backgroundColor: Colors.transparent,
          foregroundColor: colorScheme.primary,
          disabledBackgroundColor: Colors.transparent,
          disabledForegroundColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity38),
          elevation: Material3Tokens.elevation0,
          surfaceTintColor: null,
          splashColor: colorScheme.primary.withOpacity(Material3Tokens.opacity12),
          highlightColor: colorScheme.primary.withOpacity(Material3Tokens.opacity08),
          stateLayerColor: colorScheme.primary,
        );
        
      case Material3ButtonType.elevated:
        return _ButtonConfiguration(
          backgroundColor: colorScheme.surface,
          foregroundColor: colorScheme.primary,
          disabledBackgroundColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity12),
          disabledForegroundColor: colorScheme.onSurface.withOpacity(Material3Tokens.opacity38),
          elevation: Material3Tokens.elevation1,
          surfaceTintColor: colorScheme.surfaceTint,
          splashColor: colorScheme.primary.withOpacity(Material3Tokens.opacity12),
          highlightColor: colorScheme.primary.withOpacity(Material3Tokens.opacity08),
          stateLayerColor: colorScheme.primary,
        );
    }
  }

  /// 获取尺寸配置
  _SizeConfiguration _getSizeConfiguration() {
    switch (widget.size) {
      case Material3ButtonSize.small:
        return _SizeConfiguration(
          height: 32.0,
          minWidth: 64.0,
          padding: const EdgeInsets.symmetric(horizontal: Material3Tokens.space16, vertical: Material3Tokens.space8),
          borderRadius: Material3Tokens.radiusS,
          textStyle: Material3Theme.getTextTheme(context).labelMedium!,
          iconSize: 16.0,
        );
        
      case Material3ButtonSize.medium:
        return _SizeConfiguration(
          height: 40.0,
          minWidth: 88.0,
          padding: const EdgeInsets.symmetric(horizontal: Material3Tokens.space24, vertical: Material3Tokens.space12),
          borderRadius: Material3Tokens.radiusS,
          textStyle: Material3Theme.getTextTheme(context).labelLarge!,
          iconSize: 18.0,
        );
        
      case Material3ButtonSize.large:
        return _SizeConfiguration(
          height: 48.0,
          minWidth: 112.0,
          padding: const EdgeInsets.symmetric(horizontal: Material3Tokens.space32, vertical: Material3Tokens.space16),
          borderRadius: Material3Tokens.radiusM,
          textStyle: Material3Theme.getTextTheme(context).labelLarge!,
          iconSize: 20.0,
        );
    }
  }

  /// 获取状态层颜色
  Color _getStateLayerColor(_ButtonConfiguration config) {
    return config.stateLayerColor;
  }

  /// 获取状态层透明度
  double _getStateLayerOpacity() {
    if (_isPressed) return Material3Tokens.opacity12;
    if (_isHovered) return Material3Tokens.opacity08;
    if (_isFocused) return Material3Tokens.opacity12;
    return 0.0;
  }
}

/// Material 3 按钮类型枚举
enum Material3ButtonType {
  filled,    // 填充按钮 - 主要操作
  outlined,  // 轮廓按钮 - 次要操作
  text,      // 文本按钮 - 最低优先级操作
  elevated,  // 悬浮按钮 - 需要强调的操作
}

/// Material 3 按钮尺寸枚举
enum Material3ButtonSize {
  small,   // 小尺寸
  medium,  // 中等尺寸
  large,   // 大尺寸
}

/// 按钮配置类
class _ButtonConfiguration {
  final Color backgroundColor;
  final Color foregroundColor;
  final Color disabledBackgroundColor;
  final Color disabledForegroundColor;
  final double elevation;
  final Color? surfaceTintColor;
  final BorderSide? borderSide;
  final Color splashColor;
  final Color highlightColor;
  final Color stateLayerColor;

  const _ButtonConfiguration({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.disabledBackgroundColor,
    required this.disabledForegroundColor,
    required this.elevation,
    this.surfaceTintColor,
    this.borderSide,
    required this.splashColor,
    required this.highlightColor,
    required this.stateLayerColor,
  });
}

/// 尺寸配置类
class _SizeConfiguration {
  final double height;
  final double minWidth;
  final EdgeInsetsGeometry padding;
  final double borderRadius;
  final TextStyle textStyle;
  final double iconSize;

  const _SizeConfiguration({
    required this.height,
    required this.minWidth,
    required this.padding,
    required this.borderRadius,
    required this.textStyle,
    required this.iconSize,
  });
}