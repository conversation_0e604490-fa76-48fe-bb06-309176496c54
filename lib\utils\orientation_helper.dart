import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'responsive_helper.dart';

/// 📱 屏幕方向助手类
/// 提供横屏模式的特殊处理和优化
class OrientationHelper {
  /// 强制设置屏幕方向
  static Future<void> setPreferredOrientations(
      List<DeviceOrientation> orientations) async {
    await SystemChrome.setPreferredOrientations(orientations);
  }

  /// 锁定竖屏模式
  static Future<void> lockPortrait() async {
    await setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// 锁定横屏模式
  static Future<void> lockLandscape() async {
    await setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// 允许所有方向
  static Future<void> allowAllOrientations() async {
    await setPreferredOrientations(DeviceOrientation.values);
  }

  /// 检查是否应该使用横屏布局
  static bool shouldUseLandscapeLayout(BuildContext context) {
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final isTabletOrLarger = ResponsiveHelper.isTablet(context) ||
        ResponsiveHelper.isDesktop(context);

    // 平板和桌面设备在横屏时使用特殊布局
    return isLandscape && isTabletOrLarger;
  }

  /// 获取横屏模式下的列数
  static int getLandscapeColumns(BuildContext context) {
    if (!ResponsiveHelper.isLandscape(context)) {
      return 1;
    }

    final deviceType = ResponsiveHelper.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
      case DeviceType.mobileLarge:
        return 2;
      case DeviceType.tablet:
        return 3;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return 4;
    }
  }

  /// 获取横屏模式下的间距
  static double getLandscapeSpacing(BuildContext context) {
    if (!ResponsiveHelper.isLandscape(context)) {
      return 16.0;
    }

    final deviceType = ResponsiveHelper.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
      case DeviceType.mobileLarge:
        return 12.0;
      case DeviceType.tablet:
        return 20.0;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return 24.0;
    }
  }

  /// 获取横屏模式下的边距
  static EdgeInsets getLandscapePadding(BuildContext context) {
    if (!ResponsiveHelper.isLandscape(context)) {
      return const EdgeInsets.all(16.0);
    }

    final deviceType = ResponsiveHelper.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
      case DeviceType.mobileLarge:
        return const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0);
      case DeviceType.tablet:
        return const EdgeInsets.symmetric(horizontal: 32.0, vertical: 16.0);
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return const EdgeInsets.symmetric(horizontal: 48.0, vertical: 24.0);
    }
  }

  /// 获取横屏模式下的AppBar高度
  static double getLandscapeAppBarHeight(BuildContext context) {
    if (!ResponsiveHelper.isLandscape(context)) {
      return kToolbarHeight;
    }

    final deviceType = ResponsiveHelper.getDeviceType(context);
    switch (deviceType) {
      case DeviceType.mobile:
      case DeviceType.mobileLarge:
        return kToolbarHeight * 0.8;
      case DeviceType.tablet:
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return kToolbarHeight;
    }
  }

  /// 检查是否需要隐藏系统UI（全屏模式）
  static bool shouldHideSystemUI(BuildContext context) {
    return ResponsiveHelper.isLandscape(context) &&
        ResponsiveHelper.isMobilePlatform();
  }

  /// 设置全屏模式（隐藏状态栏和导航栏）
  static Future<void> enterFullscreenMode() async {
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
    );
  }

  /// 退出全屏模式（显示状态栏和导航栏）
  static Future<void> exitFullscreenMode() async {
    await SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
    );
  }

  /// 自动处理横屏模式的系统UI
  static Future<void> handleLandscapeSystemUI(BuildContext context) async {
    if (shouldHideSystemUI(context)) {
      await enterFullscreenMode();
    } else {
      await exitFullscreenMode();
    }
  }
}

/// 横屏模式感知的布局构建器
class OrientationAwareBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, bool isLandscape) builder;
  final Widget Function(BuildContext context)? portraitBuilder;
  final Widget Function(BuildContext context)? landscapeBuilder;

  const OrientationAwareBuilder({
    Key? key,
    required this.builder,
    this.portraitBuilder,
    this.landscapeBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        final isLandscape = orientation == Orientation.landscape;

        // 如果提供了专门的构建器，使用它们
        if (isLandscape && landscapeBuilder != null) {
          return landscapeBuilder!(context);
        }
        if (!isLandscape && portraitBuilder != null) {
          return portraitBuilder!(context);
        }

        // 否则使用通用构建器
        return builder(context, isLandscape);
      },
    );
  }
}

/// 横屏模式优化的卡片布局
class LandscapeOptimizedCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final double? elevation;
  final Color? color;

  const LandscapeOptimizedCard({
    Key? key,
    required this.child,
    this.padding,
    this.elevation,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final effectivePadding = padding ??
        (isLandscape
            ? OrientationHelper.getLandscapePadding(context)
            : const EdgeInsets.all(16.0));

    return Card(
      elevation: elevation ?? (isLandscape ? 2.0 : 4.0),
      color: color,
      child: Padding(
        padding: effectivePadding,
        child: child,
      ),
    );
  }
}

/// 横屏模式优化的网格视图
class LandscapeOptimizedGrid extends StatelessWidget {
  final List<Widget> children;
  final double? spacing;
  final EdgeInsets? padding;

  const LandscapeOptimizedGrid({
    Key? key,
    required this.children,
    this.spacing,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final columns = OrientationHelper.getLandscapeColumns(context);
    final effectiveSpacing =
        spacing ?? OrientationHelper.getLandscapeSpacing(context);
    final effectivePadding =
        padding ?? OrientationHelper.getLandscapePadding(context);

    return Padding(
      padding: effectivePadding,
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          crossAxisSpacing: effectiveSpacing,
          mainAxisSpacing: effectiveSpacing,
          childAspectRatio: ResponsiveHelper.isLandscape(context) ? 1.2 : 1.0,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}
