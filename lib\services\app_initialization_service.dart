import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/config_migration_service.dart';
import 'package:loadguard/repositories/config_repository_impl.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 应用初始化服务
/// 负责应用启动时的各种初始化任务，包括配置数据迁移
class AppInitializationService {
  final ConfigMigrationService _migrationService;
  
  AppInitializationService(this._migrationService);
  
  /// 执行应用初始化
  Future<InitializationResult> initialize() async {
    final stopwatch = Stopwatch()..start();
    final steps = <String>[];
    
    try {
      AppLogger.info('🚀 开始应用初始化...');
      
      // 步骤1: 初始化数据存储
      await _initializeDataStorage();
      steps.add('数据存储初始化完成');
      
      // 步骤2: 执行配置数据迁移
      final migrationResult = await _migrationService.checkAndMigrate();
      if (migrationResult.success) {
        if (migrationResult.migratedItems > 0) {
          steps.add('配置数据迁移完成: ${migrationResult.migratedItems}项');
        } else {
          steps.add('配置数据已是最新版本');
        }
      } else {
        steps.add('配置数据迁移失败: ${migrationResult.message}');
        AppLogger.warning('⚠️ 配置数据迁移失败，但应用将继续运行');
      }
      
      // 步骤3: 验证核心配置
      await _validateCoreConfigs();
      steps.add('核心配置验证完成');
      
      // 步骤4: 初始化其他服务
      await _initializeOtherServices();
      steps.add('其他服务初始化完成');
      
      stopwatch.stop();
      final duration = stopwatch.elapsedMilliseconds;
      
      AppLogger.info('✅ 应用初始化完成，耗时: ${duration}ms');
      
      return InitializationResult.success(
        message: '应用初始化完成',
        duration: duration,
        steps: steps,
      );
    } catch (e, stackTrace) {
      stopwatch.stop();
      AppLogger.error('❌ 应用初始化失败', error: e, stackTrace: stackTrace);
      
      return InitializationResult.failure(
        message: '应用初始化失败: $e',
        duration: stopwatch.elapsedMilliseconds,
        error: e,
        steps: steps,
      );
    }
  }
  
  /// 初始化数据存储
  Future<void> _initializeDataStorage() async {
    try {
      AppLogger.info('📦 初始化数据存储...');
      
      // 初始化Hive数据源
      final dataSource = HiveConfigDataSource();
      await dataSource.initialize();
      
      AppLogger.info('✅ 数据存储初始化完成');
    } catch (e) {
      AppLogger.error('❌ 数据存储初始化失败', error: e);
      rethrow;
    }
  }
  
  /// 验证核心配置
  Future<void> _validateCoreConfigs() async {
    try {
      AppLogger.info('🔍 验证核心配置...');
      
      final repository = ConfigRepositoryImpl(dataSource: HiveConfigDataSource());
      
      // 检查是否有工作人员配置
      final workers = await repository.getAllWorkers();
      if (workers.isEmpty) {
        AppLogger.warning('⚠️ 未找到工作人员配置，可能需要手动配置');
      } else {
        AppLogger.info('✅ 找到 ${workers.length} 名工作人员配置');
      }
      
      // 检查是否有仓库配置
      final warehouses = await repository.getAllWarehouses();
      if (warehouses.isEmpty) {
        AppLogger.warning('⚠️ 未找到仓库配置，可能需要手动配置');
      } else {
        AppLogger.info('✅ 找到 ${warehouses.length} 个仓库配置');
      }
      
      // 检查是否有模板配置
      final templates = await repository.getAllTemplates();
      if (templates.isEmpty) {
        AppLogger.warning('⚠️ 未找到模板配置，可能需要手动配置');
      } else {
        AppLogger.info('✅ 找到 ${templates.length} 个模板配置');
      }
      
      AppLogger.info('✅ 核心配置验证完成');
    } catch (e) {
      AppLogger.error('❌ 核心配置验证失败', error: e);
      // 不抛出异常，允许应用继续运行
    }
  }
  
  /// 初始化其他服务
  Future<void> _initializeOtherServices() async {
    try {
      AppLogger.info('🔧 初始化其他服务...');
      
      // 这里可以添加其他服务的初始化逻辑
      // 例如：网络服务、推送服务、分析服务等
      
      // 模拟一些初始化时间
      await Future.delayed(const Duration(milliseconds: 100));
      
      AppLogger.info('✅ 其他服务初始化完成');
    } catch (e) {
      AppLogger.error('❌ 其他服务初始化失败', error: e);
      // 不抛出异常，允许应用继续运行
    }
  }
  
  /// 获取初始化状态摘要
  Future<Map<String, dynamic>> getInitializationSummary() async {
    try {
      final repository = ConfigRepositoryImpl(dataSource: HiveConfigDataSource());
      
      final workers = await repository.getAllWorkers();
      final warehouses = await repository.getAllWarehouses();
      final templates = await repository.getAllTemplates();
      
      return {
        'workers': {
          'total': workers.length,
          'active': workers.where((w) => w.isActive).length,
        },
        'warehouses': {
          'total': warehouses.length,
          'active': warehouses.where((w) => w.isActive).length,
        },
        'templates': {
          'total': templates.length,
          'active': templates.where((t) => t.isActive).length,
        },
        'lastChecked': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('❌ 获取初始化状态摘要失败', error: e);
      return {
        'error': e.toString(),
        'lastChecked': DateTime.now().toIso8601String(),
      };
    }
  }
}

/// 初始化结果
class InitializationResult {
  final bool success;
  final String message;
  final int duration;
  final List<String> steps;
  final Object? error;
  
  const InitializationResult._({
    required this.success,
    required this.message,
    required this.duration,
    required this.steps,
    this.error,
  });
  
  factory InitializationResult.success({
    required String message,
    required int duration,
    required List<String> steps,
  }) {
    return InitializationResult._(
      success: true,
      message: message,
      duration: duration,
      steps: steps,
    );
  }
  
  factory InitializationResult.failure({
    required String message,
    required int duration,
    required Object error,
    required List<String> steps,
  }) {
    return InitializationResult._(
      success: false,
      message: message,
      duration: duration,
      steps: steps,
      error: error,
    );
  }
  
  @override
  String toString() {
    return 'InitializationResult(success: $success, message: $message, duration: ${duration}ms, steps: ${steps.length})';
  }
}

/// AppInitializationService Provider
final appInitializationServiceProvider = Provider<AppInitializationService>((ref) {
  final migrationService = ref.read(configMigrationServiceProvider);
  return AppInitializationService(migrationService);
});

/// 初始化状态Provider
final initializationProvider = FutureProvider<InitializationResult>((ref) async {
  final service = ref.read(appInitializationServiceProvider);
  return await service.initialize();
});

/// 初始化摘要Provider
final initializationSummaryProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.read(appInitializationServiceProvider);
  return await service.getInitializationSummary();
});
