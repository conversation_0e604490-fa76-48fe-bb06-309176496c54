import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/models/config_models.dart';
import 'package:loadguard/repositories/config_repository.dart';
import 'package:loadguard/repositories/config_repository_impl.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';
import 'package:loadguard/services/config_migration_service.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 配置数据状态管理
/// 管理工作人员、仓库、模板等配置数据的状态
class ConfigNotifier extends AsyncNotifier<ConfigState> {
  late final ConfigRepository _repository;
  late final ConfigMigrationService _migrationService;
  
  @override
  Future<ConfigState> build() async {
    // 初始化Repository和迁移服务
    _repository = ConfigRepositoryImpl(dataSource: HiveConfigDataSource());
    _migrationService = ConfigMigrationService(_repository);
    
    // 初始化Repository
    await _repository.initialize();
    
    // 检查并执行数据迁移
    await _checkAndMigrate();
    
    // 加载配置数据
    return await _loadConfigData();
  }
  
  /// 检查并执行数据迁移
  Future<void> _checkAndMigrate() async {
    try {
      final migrationResult = await _migrationService.checkAndMigrate();
      if (migrationResult.success && migrationResult.migratedItems > 0) {
        AppLogger.info('✅ 配置数据迁移完成: ${migrationResult.migratedItems}项');
      }
    } catch (e) {
      AppLogger.error('❌ 配置数据迁移失败', error: e);
      // 不抛出异常，允许应用继续运行
    }
  }
  
  /// 加载配置数据
  Future<ConfigState> _loadConfigData() async {
    try {
      // 并行加载所有配置数据
      final results = await Future.wait([
        _repository.getAllWorkers(),
        _repository.getAllWarehouses(),
        _repository.getAllGroups(),
        _repository.getAllTemplates(),
        _repository.getAllRoles(),
      ]);
      
      final workers = results[0] as List<WorkerConfig>;
      final warehouses = results[1] as List<WarehouseConfig>;
      final groups = results[2] as List<GroupConfig>;
      final templates = results[3] as List<TemplateConfigModel>;
      final roles = results[4] as List<RoleConfig>;
      
      AppLogger.info('📊 配置数据加载完成: ${workers.length}名工作人员, ${warehouses.length}个仓库, ${templates.length}个模板');
      
      return ConfigState(
        workers: workers,
        warehouses: warehouses,
        groups: groups,
        templates: templates,
        roles: roles,
        lastUpdated: DateTime.now(),
        isInitialized: true,
      );
    } catch (e) {
      AppLogger.error('❌ 配置数据加载失败', error: e);
      rethrow;
    }
  }
  
  /// 刷新配置数据
  Future<void> refreshConfigs() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _loadConfigData());
  }
  
  /// 强制重新迁移数据
  Future<void> forceMigration() async {
    try {
      state = const AsyncValue.loading();
      
      final migrationResult = await _migrationService.forceMigration();
      if (migrationResult.success) {
        // 重新加载数据
        final newState = await _loadConfigData();
        state = AsyncValue.data(newState);
        AppLogger.info('✅ 强制迁移完成: ${migrationResult.migratedItems}项');
      } else {
        throw Exception(migrationResult.message);
      }
    } catch (e) {
      AppLogger.error('❌ 强制迁移失败', error: e);
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
  
  /// 添加工作人员
  Future<void> addWorker(WorkerConfig worker) async {
    try {
      await _repository.saveWorker(worker);
      await refreshConfigs();
      AppLogger.info('✅ 添加工作人员成功: ${worker.name}');
    } catch (e) {
      AppLogger.error('❌ 添加工作人员失败', error: e);
      rethrow;
    }
  }
  
  /// 更新工作人员
  Future<void> updateWorker(WorkerConfig worker) async {
    try {
      await _repository.saveWorker(worker);
      await refreshConfigs();
      AppLogger.info('✅ 更新工作人员成功: ${worker.name}');
    } catch (e) {
      AppLogger.error('❌ 更新工作人员失败', error: e);
      rethrow;
    }
  }
  
  /// 删除工作人员
  Future<void> deleteWorker(String workerId) async {
    try {
      await _repository.deleteWorker(workerId);
      await refreshConfigs();
      AppLogger.info('✅ 删除工作人员成功: $workerId');
    } catch (e) {
      AppLogger.error('❌ 删除工作人员失败', error: e);
      rethrow;
    }
  }
  
  /// 添加仓库
  Future<void> addWarehouse(WarehouseConfig warehouse) async {
    try {
      await _repository.saveWarehouse(warehouse);
      await refreshConfigs();
      AppLogger.info('✅ 添加仓库成功: ${warehouse.name}');
    } catch (e) {
      AppLogger.error('❌ 添加仓库失败', error: e);
      rethrow;
    }
  }
  
  /// 更新仓库
  Future<void> updateWarehouse(WarehouseConfig warehouse) async {
    try {
      await _repository.saveWarehouse(warehouse);
      await refreshConfigs();
      AppLogger.info('✅ 更新仓库成功: ${warehouse.name}');
    } catch (e) {
      AppLogger.error('❌ 更新仓库失败', error: e);
      rethrow;
    }
  }
  
  /// 导出配置数据
  Future<Map<String, dynamic>> exportConfigs() async {
    try {
      return await _repository.exportConfigs();
    } catch (e) {
      AppLogger.error('❌ 导出配置数据失败', error: e);
      rethrow;
    }
  }
  
  /// 导入配置数据
  Future<void> importConfigs(Map<String, dynamic> data) async {
    try {
      await _repository.importConfigs(data);
      await refreshConfigs();
      AppLogger.info('✅ 导入配置数据成功');
    } catch (e) {
      AppLogger.error('❌ 导入配置数据失败', error: e);
      rethrow;
    }
  }
}

/// 配置状态数据类
class ConfigState {
  final List<WorkerConfig> workers;
  final List<WarehouseConfig> warehouses;
  final List<GroupConfig> groups;
  final List<TemplateConfigModel> templates;
  final List<RoleConfig> roles;
  final DateTime lastUpdated;
  final bool isInitialized;
  
  const ConfigState({
    this.workers = const [],
    this.warehouses = const [],
    this.groups = const [],
    this.templates = const [],
    this.roles = const [],
    required this.lastUpdated,
    this.isInitialized = false,
  });
  
  ConfigState copyWith({
    List<WorkerConfig>? workers,
    List<WarehouseConfig>? warehouses,
    List<GroupConfig>? groups,
    List<TemplateConfigModel>? templates,
    List<RoleConfig>? roles,
    DateTime? lastUpdated,
    bool? isInitialized,
  }) {
    return ConfigState(
      workers: workers ?? this.workers,
      warehouses: warehouses ?? this.warehouses,
      groups: groups ?? this.groups,
      templates: templates ?? this.templates,
      roles: roles ?? this.roles,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }
  
  /// 获取活跃工作人员
  List<WorkerConfig> get activeWorkers => workers.where((w) => w.isActive).toList();
  
  /// 获取活跃仓库
  List<WarehouseConfig> get activeWarehouses => warehouses.where((w) => w.isActive).toList();
  
  /// 根据仓库获取工作人员
  List<WorkerConfig> getWorkersByWarehouse(String warehouse) {
    return workers.where((w) => w.warehouse == warehouse && w.isActive).toList();
  }
  
  /// 根据角色获取工作人员
  List<WorkerConfig> getWorkersByRole(String role) {
    return workers.where((w) => w.role == role && w.isActive).toList();
  }
  
  /// 根据工作组获取工作人员
  List<WorkerConfig> getWorkersByGroup(String group) {
    return workers.where((w) => w.group == group && w.isActive).toList();
  }
  
  @override
  String toString() {
    return 'ConfigState(workers: ${workers.length}, warehouses: ${warehouses.length}, groups: ${groups.length}, templates: ${templates.length}, roles: ${roles.length}, isInitialized: $isInitialized)';
  }
}

/// ConfigNotifier Provider
final configNotifierProvider = AsyncNotifierProvider<ConfigNotifier, ConfigState>(
  ConfigNotifier.new,
);

/// 便捷的Provider定义

/// 工作人员列表Provider
final workersProvider = Provider<List<WorkerConfig>>((ref) {
  final configAsync = ref.watch(configNotifierProvider);
  return configAsync.when(
    data: (config) => config.workers,
    loading: () => <WorkerConfig>[],
    error: (error, stackTrace) => <WorkerConfig>[],
  );
});

/// 活跃工作人员Provider
final activeWorkersProvider = Provider<List<WorkerConfig>>((ref) {
  final configAsync = ref.watch(configNotifierProvider);
  return configAsync.when(
    data: (config) => config.activeWorkers,
    loading: () => <WorkerConfig>[],
    error: (error, stackTrace) => <WorkerConfig>[],
  );
});

/// 仓库列表Provider
final warehousesProvider = Provider<List<WarehouseConfig>>((ref) {
  final configAsync = ref.watch(configNotifierProvider);
  return configAsync.when(
    data: (config) => config.warehouses,
    loading: () => <WarehouseConfig>[],
    error: (error, stackTrace) => <WarehouseConfig>[],
  );
});

/// 模板列表Provider
final templatesProvider = Provider<List<TemplateConfigModel>>((ref) {
  final configAsync = ref.watch(configNotifierProvider);
  return configAsync.when(
    data: (config) => config.templates,
    loading: () => <TemplateConfigModel>[],
    error: (error, stackTrace) => <TemplateConfigModel>[],
  );
});

/// 根据仓库获取工作人员Provider
final workersByWarehouseProvider = Provider.family<List<WorkerConfig>, String>((ref, warehouse) {
  final configAsync = ref.watch(configNotifierProvider);
  return configAsync.when(
    data: (config) => config.getWorkersByWarehouse(warehouse),
    loading: () => <WorkerConfig>[],
    error: (error, stackTrace) => <WorkerConfig>[],
  );
});

/// 根据角色获取工作人员Provider
final workersByRoleProvider = Provider.family<List<WorkerConfig>, String>((ref, role) {
  final configAsync = ref.watch(configNotifierProvider);
  return configAsync.when(
    data: (config) => config.getWorkersByRole(role),
    loading: () => <WorkerConfig>[],
    error: (error, stackTrace) => <WorkerConfig>[],
  );
});
