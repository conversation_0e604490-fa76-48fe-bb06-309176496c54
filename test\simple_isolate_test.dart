import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/super_fast_recognition_service.dart';
import 'package:loadguard/services/isolate_image_processor.dart';
import 'package:loadguard/utils/app_logger.dart';

void main() {
  group('🚀 简单Isolate性能验证', () {
    late SuperFastRecognitionService superService;
    late IsolateImageProcessor isolateProcessor;
    
    setUpAll(() async {
      AppLogger.info('🧪 初始化测试环境...');
      superService = SuperFastRecognitionService.instance;
      isolateProcessor = IsolateImageProcessor.instance;
    });
    
    tearDownAll(() {
      superService.dispose();
      isolateProcessor.dispose();
      AppLogger.info('🧹 测试环境已清理');
    });

    test('🚀 Isolate图像处理器初始化测试', () async {
      expect(isolateProcessor, isNotNull);
      
      // 测试初始化
      await isolateProcessor.initialize();
      expect(isolateProcessor.isInitialized, isTrue);
      
      AppLogger.info('✅ IsolateImageProcessor 初始化测试通过');
    });

    test('⚡ 超级识别服务初始化测试', () async {
      expect(superService, isNotNull);
      
      // 测试初始化
      await superService.initialize();
      expect(superService.isInitialized, isTrue);
      
      AppLogger.info('✅ SuperFastRecognitionService 初始化测试通过');
    });

    test('🔵 蓝光处理Isolate基础功能测试', () async {
      final testImagePath = 'photos/wechat_2025-08-04_083559_653.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过蓝光处理测试');
        return;
      }
      
      AppLogger.info('🔵 开始蓝光处理Isolate测试...');
      final stopwatch = Stopwatch()..start();
      
      try {
        final processedPath = await isolateProcessor.processBlueBackgroundInIsolate(testImagePath);
        stopwatch.stop();
        
        // 验证处理结果
        final processedFile = File(processedPath);
        expect(processedFile.existsSync(), isTrue, reason: '处理后的文件应该存在');
        
        AppLogger.info('✅ 蓝光处理Isolate测试成功');
        AppLogger.info('📊 处理时间: ${stopwatch.elapsedMilliseconds}ms');
        
        // 性能验证 - 应该在30秒内完成
        expect(stopwatch.elapsedMilliseconds, lessThan(30000), 
               reason: '蓝光处理应在30秒内完成');
        
        // 清理测试文件
        try {
          processedFile.deleteSync();
        } catch (e) {
          AppLogger.warning('⚠️ 清理测试文件失败: $e');
        }
        
      } catch (e) {
        stopwatch.stop();
        AppLogger.error('❌ 蓝光处理Isolate测试失败: $e');
        fail('蓝光处理Isolate应该成功: $e');
      }
    }, timeout: const Timeout(Duration(minutes: 2)));

    test('🚀 超快速模式识别测试', () async {
      final testImagePath = 'photos/wechat_2025-08-04_083613_443.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过识别测试');
        return;
      }
      
      AppLogger.info('🚀 开始超快速识别测试...');
      final stopwatch = Stopwatch()..start();
      
      try {
        final result = await superService.recognizeSuper(
          testImagePath,
          strategy: RecognitionStrategy.ultraFast,
          onProgress: (progress, status) {
            AppLogger.debug('超快速模式进度: ${(progress * 100).toInt()}% - $status');
          },
        );
        stopwatch.stop();
        
        expect(result.success, isTrue, reason: '超快速识别应该成功');
        AppLogger.info('✅ 超快速识别测试成功');
        AppLogger.info('📊 识别时间: ${stopwatch.elapsedMilliseconds}ms');
        AppLogger.info('📊 识别策略: ${result.strategy.name}');
        AppLogger.info('📊 文本结果数: ${result.textResults.length}');
        AppLogger.info('📊 二维码结果数: ${result.qrResults.length}');
        
        // 性能验证
        expect(stopwatch.elapsedMilliseconds, lessThan(15000), 
               reason: '超快速识别应在15秒内完成');
        
      } catch (e) {
        stopwatch.stop();
        AppLogger.error('❌ 超快速识别测试失败: $e');
        fail('超快速识别应该成功: $e');
      }
    }, timeout: const Timeout(Duration(minutes: 1)));

    test('🧠 智能模式识别测试', () async {
      final testImagePath = 'photos/wechat_2025-08-04_083625_280.jpg';
      final file = File(testImagePath);
      
      if (!await file.exists()) {
        AppLogger.warning('⚠️ 测试图片不存在，跳过智能识别测试');
        return;
      }
      
      AppLogger.info('🧠 开始智能识别测试...');
      final stopwatch = Stopwatch()..start();
      
      try {
        final result = await superService.recognizeSuper(
          testImagePath,
          strategy: RecognitionStrategy.smart,
          onProgress: (progress, status) {
            AppLogger.debug('智能模式进度: ${(progress * 100).toInt()}% - $status');
          },
        );
        stopwatch.stop();
        
        expect(result.success, isTrue, reason: '智能识别应该成功');
        AppLogger.info('✅ 智能识别测试成功');
        AppLogger.info('📊 识别时间: ${stopwatch.elapsedMilliseconds}ms');
        AppLogger.info('📊 识别策略: ${result.strategy.name}');
        AppLogger.info('📊 文本结果数: ${result.textResults.length}');
        AppLogger.info('📊 二维码结果数: ${result.qrResults.length}');
        AppLogger.info('📊 是否使用了预处理: ${result.preprocessedPath != null}');
        
        // 性能验证
        expect(stopwatch.elapsedMilliseconds, lessThan(45000), 
               reason: '智能识别应在45秒内完成');
        
      } catch (e) {
        stopwatch.stop();
        AppLogger.error('❌ 智能识别测试失败: $e');
        fail('智能识别应该成功: $e');
      }
    }, timeout: const Timeout(Duration(minutes: 2)));
  });
}