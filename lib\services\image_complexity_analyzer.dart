import 'dart:io';
import 'dart:math' as math;
import 'package:image/image.dart' as img;
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/utils/color_background_processor.dart';

/// 🎯 【图像复杂度分析器】
/// 
/// 【核心功能】智能分析图像复杂度，为图像处理提供最优参数
/// 【技术原理】综合背景颜色、对比度、文字大小、反光程度等多维度指标
/// 【适用场景】为不同复杂度的图像选择最适合的处理参数和算法
/// 
/// 🎯 【复杂度等级】：
/// 1. Simple - 白色背景，高对比度，大字体
/// 2. Medium - 轻微色彩背景，中等对比度
/// 3. Complex - 明显色彩背景，低对比度，小字体
/// 4. VeryComplex - 强色彩背景，反光，极小字体
class ImageComplexityAnalyzer {
  
  /// 📊 分析图像复杂度
  static Future<ImageComplexityAnalysis> analyze(String imagePath) async {
    final stopwatch = Stopwatch()..start();
    AppLogger.debug('🎯 开始图像复杂度分析: $imagePath');
    
    try {
      final bytes = await File(imagePath).readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) {
        throw Exception('无法解码图像文件');
      }
      
      // 1. 背景颜色分析
      final colorAnalysis = ColorBackgroundProcessor.analyzeBackgroundColor(image);
      
      // 2. 对比度分析
      final contrastScore = _calculateContrastScore(image);
      
      // 3. 字体大小估算
      final fontSizeScore = _estimateFontSize(image);
      
      // 4. 反光检测
      final reflectionScore = _detectReflection(image);
      
      // 5. 噪声水平评估
      final noiseLevel = _assessNoiseLevel(image);
      
      // 6. 图像清晰度评估
      final sharpnessScore = _assessSharpness(image);
      
      // 7. 综合复杂度计算
      final complexityLevel = _calculateComplexityLevel(
        colorAnalysis, 
        contrastScore, 
        fontSizeScore, 
        reflectionScore, 
        noiseLevel, 
        sharpnessScore
      );
      
      stopwatch.stop();
      
      final analysis = ImageComplexityAnalysis(
        imagePath: imagePath,
        complexityLevel: complexityLevel,
        backgroundColorAnalysis: colorAnalysis,
        contrastScore: contrastScore,
        fontSizeScore: fontSizeScore,
        reflectionScore: reflectionScore,
        noiseLevel: noiseLevel,
        sharpnessScore: sharpnessScore,
        analysisTime: stopwatch.elapsedMilliseconds,
        recommendedParameters: _getRecommendedParameters(complexityLevel, colorAnalysis),
      );
      
      AppLogger.debug('✅ 复杂度分析完成: ${analysis.complexityLevel.name}, 耗时: ${stopwatch.elapsedMilliseconds}ms');
      
      return analysis;
    } catch (e) {
      AppLogger.error('❌ 图像复杂度分析失败: $e');
      rethrow;
    }
  }
  
  /// 🔍 计算对比度评分 (0-1, 越高越好)
  static double _calculateContrastScore(img.Image image) {
    final grayImage = img.grayscale(image);
    final histogram = List<int>.filled(256, 0);
    
    // 构建直方图
    for (int y = 0; y < grayImage.height; y++) {
      for (int x = 0; x < grayImage.width; x++) {
        final pixel = grayImage.getPixel(x, y);
        histogram[pixel.r.toInt()]++;
      }
    }
    
    // 计算对比度（标准差）
    double mean = 0;
    int totalPixels = grayImage.width * grayImage.height;
    
    for (int i = 0; i < 256; i++) {
      mean += i * histogram[i];
    }
    mean /= totalPixels;
    
    double variance = 0;
    for (int i = 0; i < 256; i++) {
      variance += histogram[i] * (i - mean) * (i - mean);
    }
    variance /= totalPixels;
    
    final standardDeviation = math.sqrt(variance);
    return math.min(1.0, standardDeviation / 64.0); // 标准化到0-1
  }
  
  /// 📏 估算字体大小评分 (0-1, 越大越好)
  static double _estimateFontSize(img.Image image) {
    // 使用边缘检测来估算文字大小
    final edges = _detectEdges(image);
    
    // 计算连通组件的平均大小
    final components = _findConnectedComponents(edges);
    if (components.isEmpty) return 0.3; // 默认中等大小
    
    final avgComponentSize = components.map((c) => c.size).reduce((a, b) => a + b) / components.length;
    final imageArea = image.width * image.height;
    final relativeSize = avgComponentSize / imageArea;
    
    // 根据相对大小判断字体大小
    if (relativeSize > 0.01) return 1.0;      // 大字体
    if (relativeSize > 0.005) return 0.7;     // 中等字体
    if (relativeSize > 0.002) return 0.4;     // 小字体
    return 0.1; // 极小字体
  }
  
  /// ✨ 检测反光程度 (0-1, 越高反光越严重)
  static double _detectReflection(img.Image image) {
    int highBrightnessPixels = 0;
    int totalPixels = 0;
    
    // 采样检测（提高性能）
    for (int y = 0; y < image.height; y += 3) {
      for (int x = 0; x < image.width; x += 3) {
        final pixel = image.getPixel(x, y);
        final brightness = (pixel.r + pixel.g + pixel.b) / 3;
        
        if (brightness > 230) { // 很亮的像素
          highBrightnessPixels++;
        }
        totalPixels++;
      }
    }
    
    return highBrightnessPixels / totalPixels;
  }
  
  /// 🔊 评估噪声水平 (0-1, 越高噪声越多)
  static double _assessNoiseLevel(img.Image image) {
    final grayImage = img.grayscale(image);
    double noiseSum = 0;
    int count = 0;
    
    // 使用Laplacian算子检测噪声
    for (int y = 1; y < grayImage.height - 1; y++) {
      for (int x = 1; x < grayImage.width - 1; x++) {
        final center = grayImage.getPixel(x, y).r.toDouble();
        final neighbors = [
          grayImage.getPixel(x-1, y).r.toDouble(),
          grayImage.getPixel(x+1, y).r.toDouble(),
          grayImage.getPixel(x, y-1).r.toDouble(),
          grayImage.getPixel(x, y+1).r.toDouble(),
        ];
        
        final laplacian = (neighbors.reduce((a, b) => a + b) - 4 * center).abs();
        noiseSum += laplacian;
        count++;
      }
    }
    
    final avgNoise = noiseSum / count;
    return math.min(1.0, avgNoise / 100.0); // 标准化
  }
  
  /// 🔍 评估图像清晰度 (0-1, 越高越清晰)
  static double _assessSharpness(img.Image image) {
    final grayImage = img.grayscale(image);
    double gradientSum = 0;
    int count = 0;
    
    // 使用Sobel算子计算梯度
    for (int y = 1; y < grayImage.height - 1; y++) {
      for (int x = 1; x < grayImage.width - 1; x++) {
        final gx = _sobelX(grayImage, x, y);
        final gy = _sobelY(grayImage, x, y);
        final gradient = math.sqrt(gx * gx + gy * gy);
        
        gradientSum += gradient;
        count++;
      }
    }
    
    final avgGradient = gradientSum / count;
    return math.min(1.0, avgGradient / 50.0); // 标准化
  }
  
  /// 计算Sobel X方向梯度
  static double _sobelX(img.Image image, int x, int y) {
    return -1 * image.getPixel(x-1, y-1).r.toDouble() +
           1 * image.getPixel(x+1, y-1).r.toDouble() +
           -2 * image.getPixel(x-1, y).r.toDouble() +
           2 * image.getPixel(x+1, y).r.toDouble() +
           -1 * image.getPixel(x-1, y+1).r.toDouble() +
           1 * image.getPixel(x+1, y+1).r.toDouble();
  }
  
  /// 计算Sobel Y方向梯度
  static double _sobelY(img.Image image, int x, int y) {
    return -1 * image.getPixel(x-1, y-1).r.toDouble() +
           -2 * image.getPixel(x, y-1).r.toDouble() +
           -1 * image.getPixel(x+1, y-1).r.toDouble() +
           1 * image.getPixel(x-1, y+1).r.toDouble() +
           2 * image.getPixel(x, y+1).r.toDouble() +
           1 * image.getPixel(x+1, y+1).r.toDouble();
  }
  
  /// 边缘检测
  static img.Image _detectEdges(img.Image image) {
    final grayImage = img.grayscale(image);
    final edges = img.Image.from(grayImage);
    
    for (int y = 1; y < grayImage.height - 1; y++) {
      for (int x = 1; x < grayImage.width - 1; x++) {
        final gx = _sobelX(grayImage, x, y);
        final gy = _sobelY(grayImage, x, y);
        final magnitude = math.sqrt(gx * gx + gy * gy);
        
        final edgeValue = magnitude > 30 ? 255 : 0;
        edges.setPixel(x, y, img.ColorRgb8(edgeValue, edgeValue, edgeValue));
      }
    }
    
    return edges;
  }
  
  /// 查找连通组件（简化版）
  static List<ConnectedComponent> _findConnectedComponents(img.Image edges) {
    final visited = List.generate(
      edges.height, 
      (_) => List.filled(edges.width, false)
    );
    final components = <ConnectedComponent>[];
    
    for (int y = 0; y < edges.height; y++) {
      for (int x = 0; x < edges.width; x++) {
        if (!visited[y][x] && edges.getPixel(x, y).r > 128) {
          final component = _floodFill(edges, visited, x, y);
          if (component.size > 10) { // 忽略太小的组件
            components.add(component);
          }
        }
      }
    }
    
    return components;
  }
  
  /// 洪水填充算法
  static ConnectedComponent _floodFill(
    img.Image edges, 
    List<List<bool>> visited, 
    int startX, 
    int startY
  ) {
    final stack = <Point>[];
    final points = <Point>[];
    
    stack.add(Point(startX, startY));
    
    while (stack.isNotEmpty) {
      final point = stack.removeLast();
      final x = point.x;
      final y = point.y;
      
      if (x < 0 || x >= edges.width || y < 0 || y >= edges.height ||
          visited[y][x] || edges.getPixel(x, y).r < 128) {
        continue;
      }
      
      visited[y][x] = true;
      points.add(point);
      
      // 添加相邻像素
      stack.addAll([
        Point(x + 1, y),
        Point(x - 1, y),
        Point(x, y + 1),
        Point(x, y - 1),
      ]);
    }
    
    return ConnectedComponent(
      points: points,
      size: points.length,
    );
  }
  
  /// 🎯 计算综合复杂度等级
  static ComplexityLevel _calculateComplexityLevel(
    ColorAnalysis colorAnalysis,
    double contrastScore,
    double fontSizeScore,
    double reflectionScore,
    double noiseLevel,
    double sharpnessScore,
  ) {
    double complexity = 0;
    
    // 背景颜色影响 (0-4分)
    switch (colorAnalysis.dominantColor) {
      case BackgroundColor.white:
        complexity += 0;
        break;
      case BackgroundColor.gray:
        complexity += 1;
        break;
      case BackgroundColor.neutral:
        complexity += 1;
        break;
      case BackgroundColor.blue:
        complexity += 3;
        break;
      case BackgroundColor.green:
        complexity += 3;
        break;
      case BackgroundColor.red:
        complexity += 2.5;
        break;
      case BackgroundColor.yellow:
        complexity += 2;
        break;
      case BackgroundColor.purple:
        complexity += 3.5;
        break;
      case BackgroundColor.dark:
        complexity += 4;
        break;
    }
    
    // 对比度影响 (0-2分)
    complexity += (1.0 - contrastScore) * 2;
    
    // 字体大小影响 (0-2分)
    complexity += (1.0 - fontSizeScore) * 2;
    
    // 反光影响 (0-1分)
    complexity += reflectionScore;
    
    // 噪声影响 (0-1分)
    complexity += noiseLevel;
    
    // 清晰度影响 (0-1分)
    complexity += (1.0 - sharpnessScore);
    
    // 根据总分确定复杂度等级
    if (complexity <= 2) return ComplexityLevel.simple;
    if (complexity <= 5) return ComplexityLevel.medium;
    if (complexity <= 8) return ComplexityLevel.complex;
    return ComplexityLevel.veryComplex;
  }
  
  /// 🛠️ 获取推荐处理参数
  static ProcessingParameters _getRecommendedParameters(
    ComplexityLevel complexity,
    ColorAnalysis colorAnalysis,
  ) {
    // 根据背景颜色调整参数
    final colorAdjustment = _getColorSpecificAdjustment(colorAnalysis.dominantColor);
    
    switch (complexity) {
      case ComplexityLevel.simple:
        return ProcessingParameters(
          contrastEnhancement: 1.1 + colorAdjustment.contrastBoost,
          brightnessAdjustment: 1.05 + colorAdjustment.brightnessBoost,
          saturationReduction: 0.9 - colorAdjustment.saturationReduction,
          binarizationThresholdAdjustment: colorAdjustment.thresholdAdjustment,
          morphologyKernelSize: 1,
          scaleFactor: 1.0,
          enableReflectionSuppression: false,
          enableAdvancedFiltering: false,
          binarizationMethod: _getBinarizationMethod(complexity, colorAnalysis.dominantColor),
          adaptiveWindowSize: 15,
          adaptiveC: 2,
        );
        
      case ComplexityLevel.medium:
        return ProcessingParameters(
          contrastEnhancement: 1.3 + colorAdjustment.contrastBoost,
          brightnessAdjustment: 1.1 + colorAdjustment.brightnessBoost,
          saturationReduction: 0.8 - colorAdjustment.saturationReduction,
          binarizationThresholdAdjustment: -5 + colorAdjustment.thresholdAdjustment,
          morphologyKernelSize: 1,
          scaleFactor: 1.2,
          enableReflectionSuppression: false,
          enableAdvancedFiltering: true,
          binarizationMethod: _getBinarizationMethod(complexity, colorAnalysis.dominantColor),
          adaptiveWindowSize: 21,
          adaptiveC: 5,
        );
        
      case ComplexityLevel.complex:
        return ProcessingParameters(
          contrastEnhancement: 1.5 + colorAdjustment.contrastBoost,
          brightnessAdjustment: 1.2 + colorAdjustment.brightnessBoost,
          saturationReduction: 0.6 - colorAdjustment.saturationReduction,
          binarizationThresholdAdjustment: -10 + colorAdjustment.thresholdAdjustment,
          morphologyKernelSize: 2,
          scaleFactor: 1.5,
          enableReflectionSuppression: true,
          enableAdvancedFiltering: true,
          binarizationMethod: _getBinarizationMethod(complexity, colorAnalysis.dominantColor),
          adaptiveWindowSize: 31,
          adaptiveC: 8,
        );
        
      case ComplexityLevel.veryComplex:
        return ProcessingParameters(
          contrastEnhancement: 1.8 + colorAdjustment.contrastBoost,
          brightnessAdjustment: 1.3 + colorAdjustment.brightnessBoost,
          saturationReduction: 0.4 - colorAdjustment.saturationReduction,
          binarizationThresholdAdjustment: -20 + colorAdjustment.thresholdAdjustment,
          morphologyKernelSize: 3,
          scaleFactor: 2.0,
          enableReflectionSuppression: true,
          enableAdvancedFiltering: true,
          binarizationMethod: _getBinarizationMethod(complexity, colorAnalysis.dominantColor),
          adaptiveWindowSize: 41,
          adaptiveC: 12,
        );
    }
  }
  
  /// 🎨 获取颜色特定调整参数
  static ColorAdjustment _getColorSpecificAdjustment(BackgroundColor color) {
    switch (color) {
      case BackgroundColor.blue:
        return ColorAdjustment(
          contrastBoost: 0.3,
          brightnessBoost: 0.15,
          saturationReduction: 0.3,
          thresholdAdjustment: -15,
        );
      case BackgroundColor.green:
        return ColorAdjustment(
          contrastBoost: 0.4,
          brightnessBoost: 0.2,
          saturationReduction: 0.4,
          thresholdAdjustment: -20,
        );
      case BackgroundColor.red:
        return ColorAdjustment(
          contrastBoost: 0.2,
          brightnessBoost: 0.1,
          saturationReduction: 0.3,
          thresholdAdjustment: -10,
        );
      case BackgroundColor.yellow:
        return ColorAdjustment(
          contrastBoost: 0.25,
          brightnessBoost: 0.05,
          saturationReduction: 0.35,
          thresholdAdjustment: -12,
        );
      case BackgroundColor.purple:
        return ColorAdjustment(
          contrastBoost: 0.35,
          brightnessBoost: 0.15,
          saturationReduction: 0.4,
          thresholdAdjustment: -18,
        );
      case BackgroundColor.dark:
        return ColorAdjustment(
          contrastBoost: 0.5,
          brightnessBoost: 0.3,
          saturationReduction: 0.1,
          thresholdAdjustment: -25,
        );
      case BackgroundColor.gray:
        return ColorAdjustment(
          contrastBoost: 0.1,
          brightnessBoost: 0.05,
          saturationReduction: 0.1,
          thresholdAdjustment: -5,
        );
      case BackgroundColor.white:
        return ColorAdjustment(
          contrastBoost: 0.0,
          brightnessBoost: 0.0,
          saturationReduction: 0.0,
          thresholdAdjustment: 0,
        );
      case BackgroundColor.neutral:
        return ColorAdjustment(
          contrastBoost: 0.05,
          brightnessBoost: 0.02,
          saturationReduction: 0.05,
          thresholdAdjustment: -3,
        );
    }
  }
  
  /// 🎯 选择最佳二值化方法
  static BinarizationMethod _getBinarizationMethod(ComplexityLevel complexity, BackgroundColor color) {
    // 对于复杂背景，使用自适应方法
    if (color == BackgroundColor.blue || color == BackgroundColor.green || 
        color == BackgroundColor.purple || color == BackgroundColor.dark) {
      return BinarizationMethod.adaptiveGaussian;
    }
    
    // 对于简单背景，使用全局Otsu
    if (complexity == ComplexityLevel.simple && 
        (color == BackgroundColor.white || color == BackgroundColor.neutral)) {
      return BinarizationMethod.otsu;
    }
    
    // 其他情况使用自适应均值
    return BinarizationMethod.adaptiveMean;
  }
  
  /// 🧮 动态计算最佳二值化阈值
  static int calculateOptimalThreshold(img.Image grayImage, ProcessingParameters params) {
    switch (params.binarizationMethod) {
      case BinarizationMethod.otsu:
        return _calculateOtsuThreshold(grayImage) + params.binarizationThresholdAdjustment;
      case BinarizationMethod.adaptiveMean:
        return _calculateAdaptiveMeanThreshold(grayImage, params);
      case BinarizationMethod.adaptiveGaussian:
        return _calculateAdaptiveGaussianThreshold(grayImage, params);
      case BinarizationMethod.triangle:
        return _calculateTriangleThreshold(grayImage) + params.binarizationThresholdAdjustment;
    }
  }
  
  /// 📊 Otsu自动阈值计算
  static int _calculateOtsuThreshold(img.Image grayImage) {
    final histogram = List<int>.filled(256, 0);
    
    // 构建直方图
    for (int y = 0; y < grayImage.height; y++) {
      for (int x = 0; x < grayImage.width; x++) {
        final pixel = grayImage.getPixel(x, y);
        histogram[pixel.r.toInt()]++;
      }
    }
    
    final totalPixels = grayImage.width * grayImage.height;
    double sum = 0;
    for (int i = 0; i < 256; i++) {
      sum += i * histogram[i];
    }
    
    double sumB = 0;
    int wB = 0;
    int wF = 0;
    double varMax = 0;
    int threshold = 0;
    
    for (int t = 0; t < 256; t++) {
      wB += histogram[t];
      if (wB == 0) continue;
      
      wF = totalPixels - wB;
      if (wF == 0) break;
      
      sumB += t * histogram[t];
      
      final mB = sumB / wB;
      final mF = (sum - sumB) / wF;
      
      final varBetween = wB * wF * (mB - mF) * (mB - mF);
      
      if (varBetween > varMax) {
        varMax = varBetween;
        threshold = t;
      }
    }
    
    return threshold;
  }
  
  /// 📈 自适应均值阈值计算
  static int _calculateAdaptiveMeanThreshold(img.Image grayImage, ProcessingParameters params) {
    // 计算局部均值并减去常数C
    double totalMean = 0;
    int count = 0;
    
    for (int y = 0; y < grayImage.height; y += 5) {
      for (int x = 0; x < grayImage.width; x += 5) {
        final pixel = grayImage.getPixel(x, y);
        totalMean += pixel.r.toDouble();
        count++;
      }
    }
    
    final globalMean = totalMean / count;
    return (globalMean - params.adaptiveC + params.binarizationThresholdAdjustment).clamp(0, 255).toInt();
  }
  
  /// 🌊 自适应高斯阈值计算
  static int _calculateAdaptiveGaussianThreshold(img.Image grayImage, ProcessingParameters params) {
    // 使用高斯加权平均
    double weightedSum = 0;
    double weightSum = 0;
    
    final centerX = grayImage.width ~/ 2;
    final centerY = grayImage.height ~/ 2;
    final sigma = params.adaptiveWindowSize / 3.0;
    
    for (int y = 0; y < grayImage.height; y += 3) {
      for (int x = 0; x < grayImage.width; x += 3) {
        final pixel = grayImage.getPixel(x, y);
        final distance = math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
        final weight = math.exp(-(distance * distance) / (2 * sigma * sigma));
        
        weightedSum += pixel.r.toDouble() * weight;
        weightSum += weight;
      }
    }
    
    final gaussianMean = weightedSum / weightSum;
    return (gaussianMean - params.adaptiveC + params.binarizationThresholdAdjustment).clamp(0, 255).toInt();
  }
  
  /// 📐 三角形阈值计算
  static int _calculateTriangleThreshold(img.Image grayImage) {
    final histogram = List<int>.filled(256, 0);
    
    // 构建直方图
    for (int y = 0; y < grayImage.height; y++) {
      for (int x = 0; x < grayImage.width; x++) {
        final pixel = grayImage.getPixel(x, y);
        histogram[pixel.r.toInt()]++;
      }
    }
    
    // 找到直方图的最大值位置
    int maxIdx = 0;
    int maxVal = histogram[0];
    for (int i = 1; i < 256; i++) {
      if (histogram[i] > maxVal) {
        maxVal = histogram[i];
        maxIdx = i;
      }
    }
    
    // 使用三角形算法找到最佳阈值
    double maxDistance = 0;
    int threshold = maxIdx;
    
    for (int i = 0; i < 256; i++) {
      if (i == maxIdx) continue;
      
      final distance = (histogram[i] - maxVal).abs() / math.sqrt(1 + (i - maxIdx) * (i - maxIdx));
      if (distance > maxDistance) {
        maxDistance = distance;
        threshold = i;
      }
    }
    
    return threshold;
  }
}

/// 🎯 复杂度等级
enum ComplexityLevel {
  simple('简单'),      // 白色背景，高对比度，大字体
  medium('中等'),      // 轻微色彩背景，中等对比度
  complex('复杂'),     // 明显色彩背景，低对比度，小字体
  veryComplex('极复杂'); // 强色彩背景，反光，极小字体

  const ComplexityLevel(this.description);
  final String description;
}

/// 📊 图像复杂度分析结果
class ImageComplexityAnalysis {
  final String imagePath;
  final ComplexityLevel complexityLevel;
  final ColorAnalysis backgroundColorAnalysis;
  final double contrastScore;
  final double fontSizeScore;
  final double reflectionScore;
  final double noiseLevel;
  final double sharpnessScore;
  final int analysisTime;
  final ProcessingParameters recommendedParameters;
  
  ImageComplexityAnalysis({
    required this.imagePath,
    required this.complexityLevel,
    required this.backgroundColorAnalysis,
    required this.contrastScore,
    required this.fontSizeScore,
    required this.reflectionScore,
    required this.noiseLevel,
    required this.sharpnessScore,
    required this.analysisTime,
    required this.recommendedParameters,
  });
  
  @override
  String toString() {
    return 'ImageComplexityAnalysis('
        '复杂度: ${complexityLevel.description}, '
        '背景: ${backgroundColorAnalysis.dominantColor.description}, '
        '对比度: ${(contrastScore * 100).toStringAsFixed(1)}%, '
        '字体: ${(fontSizeScore * 100).toStringAsFixed(1)}%, '
        '反光: ${(reflectionScore * 100).toStringAsFixed(1)}%, '
        '清晰度: ${(sharpnessScore * 100).toStringAsFixed(1)}%)';
  }
}

/// 🛠️ 处理参数
class ProcessingParameters {
  final double contrastEnhancement;          // 对比度增强系数
  final double brightnessAdjustment;         // 亮度调整系数
  final double saturationReduction;          // 饱和度降低系数
  final int binarizationThresholdAdjustment; // 二值化阈值调整
  final int morphologyKernelSize;            // 形态学核心大小
  final double scaleFactor;                  // 图像放大系数
  final bool enableReflectionSuppression;    // 是否启用反光抑制
  final bool enableAdvancedFiltering;        // 是否启用高级滤波
  final BinarizationMethod binarizationMethod; // 二值化方法
  final int adaptiveWindowSize;              // 自适应窗口大小
  final int adaptiveC;                       // 自适应常数C
  
  ProcessingParameters({
    required this.contrastEnhancement,
    required this.brightnessAdjustment,
    required this.saturationReduction,
    required this.binarizationThresholdAdjustment,
    required this.morphologyKernelSize,
    required this.scaleFactor,
    required this.enableReflectionSuppression,
    required this.enableAdvancedFiltering,
    required this.binarizationMethod,
    required this.adaptiveWindowSize,
    required this.adaptiveC,
  });
}

/// 📍 点坐标
class Point {
  final int x;
  final int y;
  
  Point(this.x, this.y);
}

/// 🔗 连通组件
class ConnectedComponent {
  final List<Point> points;
  final int size;
  
  ConnectedComponent({
    required this.points,
    required this.size,
  });
}

/// 🎯 二值化方法枚举
enum BinarizationMethod {
  otsu('Otsu自动阈值'),
  adaptiveMean('自适应均值'),
  adaptiveGaussian('自适应高斯'),
  triangle('三角形阈值');

  const BinarizationMethod(this.description);
  final String description;
}

/// 🎨 颜色调整参数
class ColorAdjustment {
  final double contrastBoost;        // 对比度提升
  final double brightnessBoost;      // 亮度提升
  final double saturationReduction;  // 饱和度降低
  final int thresholdAdjustment;     // 阈值调整
  
  ColorAdjustment({
    required this.contrastBoost,
    required this.brightnessBoost,
    required this.saturationReduction,
    required this.thresholdAdjustment,
  });
}