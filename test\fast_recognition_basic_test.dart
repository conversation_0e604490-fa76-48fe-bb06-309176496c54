import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/fast_recognition_service.dart';

void main() {
  group('FastRecognitionService 基础测试', () {
    test('服务可以正常初始化', () async {
      final service = FastRecognitionService.instance;
      expect(service, isNotNull);
      expect(service.isInitialized, isFalse);
      
      // 注意：在测试环境中MLKit可能不可用，所以我们只测试基本结构
      print('✅ FastRecognitionService 基础结构正常');
    });
    
    test('FastRecognitionResult 基本功能', () {
      final result = FastRecognitionResult(
        textResults: [],
        qrResults: [],
        processingTime: 1000,
        hasText: false,
        hasQRCode: false,
      );
      
      expect(result.hasAnyResult, isFalse);
      expect(result.getBestResult(), isNull);
      expect(result.overallConfidence, equals(0.0));
      
      print('✅ FastRecognitionResult 基础功能正常');
    });
  });
}