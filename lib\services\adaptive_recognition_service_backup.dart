import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/unified_recognition_service.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🎯 自适应识别服务
/// 
/// 根据用户选择和图像特征，智能选择最适合的识别策略：
/// - 快速模式：仅MLKit，500-1000ms，准确率70%
/// - 标准模式：MLKit + 基础优化，1000-2000ms，准确率85%
/// - 高精度模式：完整统一识别，2000-5000ms，准确率95%
class AdaptiveRecognitionService {
  static AdaptiveRecognitionService? _instance;
  static AdaptiveRecognitionService get instance => _instance ??= AdaptiveRecognitionService._();
  
  AdaptiveRecognitionService._();
  
  late final MLKitTextRecognitionService _mlkitService;
  late final UnifiedRecognitionService _unifiedService;
  bool _isInitialized = false;
  
  /// 识别模式
  enum RecognitionMode {
    fast,      // 快速模式：仅MLKit
    standard,  // 标准模式：MLKit + 基础优化
    precision, // 高精度模式：完整统一识别
  }
  
  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _mlkitService = MLKitTextRecognitionService.instance;
    await _mlkitService.initialize();
    
    _unifiedService = UnifiedRecognitionService.instance;
    // 延迟初始化统一服务，仅在需要时初始化
    
    _isInitialized = true;
    AppLogger.info('✅ 自适应识别服务初始化完成');
  }
  
  /// 🚀 智能识别 - 根据模式选择最佳策略
  Future<List<RecognitionResult>> recognizeAdaptive({
    required String imagePath,
    required RecognitionMode mode,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
    Function(double progress, String status)? onProgress,
  }) async {
    if (!_isInitialized) await initialize();
    
    final startTime = DateTime.now();
    AppLogger.info('🎯 开始自适应识别，模式: ${mode.name}');
    
    try {
      switch (mode) {
        case RecognitionMode.fast:
          return await _performFastRecognition(
            imagePath, presetProductCode, presetBatchNumber, allBatches, onProgress);
            
        case RecognitionMode.standard:
          return await _performStandardRecognition(
            imagePath, presetProductCode, presetBatchNumber, allBatches, onProgress);
            
        case RecognitionMode.precision:
          return await _performPrecisionRecognition(
            imagePath, presetProductCode, presetBatchNumber, allBatches, onProgress);
      }
    } finally {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      AppLogger.info('✅ 自适应识别完成，模式: ${mode.name}，耗时: ${duration}ms');
    }
  }
  
  /// 🚀 快速模式：仅MLKit，500-1000ms
  Future<List<RecognitionResult>> _performFastRecognition(
    String imagePath,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
    Function(double progress, String status)? onProgress,
  ) async {
    onProgress?.call(0.1, '快速识别模式启动...');
    
    if (allBatches != null && allBatches.length > 1) {
      // 混合任务使用智能匹配
      onProgress?.call(0.5, '执行快速智能匹配...');
      final result = await _mlkitService.processImageWithSmartMatching(
        imagePath: imagePath,
        matchConfig: RecognitionMatchConfig(
          batches: allBatches,
          isMixedLoad: true,
          minConfidence: 0.6, // 降低置信度要求，提高速度
          enableFuzzyMatch: false, // 禁用模糊匹配，提高速度
        ),
      );
      onProgress?.call(1.0, '快速识别完成');
      return result != null ? [result] : [];
    } else {
      // 单批次使用基础MLKit
      onProgress?.call(0.5, '执行快速文本识别...');
      final results = await _mlkitService.processImage(
        imagePath,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
      );
      onProgress?.call(1.0, '快速识别完成');
      return results;
    }
  }
  
  /// 🎯 标准模式：MLKit + 基础优化，1000-2000ms
  Future<List<RecognitionResult>> _performStandardRecognition(
    String imagePath,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
    Function(double progress, String status)? onProgress,
  ) async {
    onProgress?.call(0.1, '标准识别模式启动...');
    
    // TODO: 实现基础图像优化（轻量级预处理）
    // 例如：简单的对比度增强，不包含复杂的蓝色背景处理
    
    if (allBatches != null && allBatches.length > 1) {
      onProgress?.call(0.5, '执行标准智能匹配...');
      final result = await _mlkitService.processImageWithSmartMatching(
        imagePath: imagePath,
        matchConfig: RecognitionMatchConfig(
          batches: allBatches,
          isMixedLoad: true,
          minConfidence: 0.7,
          enableFuzzyMatch: true,
        ),
      );
      onProgress?.call(1.0, '标准识别完成');
      return result != null ? [result] : [];
    } else {
      onProgress?.call(0.5, '执行标准文本识别...');
      final results = await _mlkitService.processImage(
        imagePath,
        presetProductCode: presetProductCode,
        presetBatchNumber: presetBatchNumber,
      );
      onProgress?.call(1.0, '标准识别完成');
      return results;
    }
  }
  
  /// 🎯 高精度模式：完整统一识别，2000-5000ms
  Future<List<RecognitionResult>> _performPrecisionRecognition(
    String imagePath,
    String? presetProductCode,
    String? presetBatchNumber,
    List<BatchInfo>? allBatches,
    Function(double progress, String status)? onProgress,
  ) async {
    onProgress?.call(0.1, '高精度识别模式启动...');
    
    // 初始化统一识别服务（如果还未初始化）
    if (!_unifiedService._isInitialized) {
      onProgress?.call(0.2, '初始化高精度识别引擎...');
      await _unifiedService.initialize();
    }
    
    onProgress?.call(0.3, '执行完整统一识别...');
    final unifiedResult = await _unifiedService.recognizeUnified(
      imagePath,
      enableQRCode: true,
      enableNetworkValidation: false,
      onProgress: (progress, status) {
        // 映射进度到0.3-0.9区间
        onProgress?.call(0.3 + progress * 0.6, status);
      },
    );
    
    onProgress?.call(0.95, '处理识别结果...');
    
    // 转换统一识别结果为RecognitionResult列表
    final results = <RecognitionResult>[];
    
    // 添加文字识别结果
    for (final textResult in unifiedResult.textResults) {
      final matchesPreset = _checkMatch(textResult.ocrText, presetProductCode, presetBatchNumber, allBatches);
      results.add(RecognitionResult(
        ocrText: textResult.ocrText,
        extractedProductCode: presetProductCode,
        extractedBatchNumber: presetBatchNumber,
        matchesPreset: matchesPreset,
        confidence: unifiedResult.overallConfidence,
        isQrOcrConsistent: unifiedResult.isConsistent,
        status: matchesPreset ? RecognitionStatus.completed : RecognitionStatus.failed,
        metadata: {
          'unifiedRecognition': true,
          'hasQRCode': unifiedResult.hasQRCode,
          'isConsistent': unifiedResult.isConsistent,
          'processingTime': unifiedResult.processingTime,
        },
      ));
    }
    
    // 添加二维码识别结果
    for (final qrResult in unifiedResult.qrResults) {
      final matchesPreset = _checkMatch(qrResult.ocrText, presetProductCode, presetBatchNumber, allBatches);
      results.add(RecognitionResult(
        ocrText: qrResult.ocrText,
        qrCode: qrResult.qrCode,
        extractedProductCode: presetProductCode,
        extractedBatchNumber: presetBatchNumber,
        matchesPreset: matchesPreset,
        confidence: unifiedResult.overallConfidence,
        isQrOcrConsistent: true,
        status: matchesPreset ? RecognitionStatus.completed : RecognitionStatus.failed,
        metadata: {
          'unifiedRecognition': true,
          'fromQRCode': true,
          'processingTime': unifiedResult.processingTime,
        },
      ));
    }
    
    onProgress?.call(1.0, '高精度识别完成');
    return results;
  }
  
  /// 检查匹配
  bool _checkMatch(String? ocrText, String? presetProductCode, String? presetBatchNumber, List<BatchInfo>? allBatches) {
    if (ocrText == null || ocrText.isEmpty) return false;
    
    if (allBatches != null && allBatches.isNotEmpty) {
      // 混合任务：尝试匹配任何一个批次
      for (final batch in allBatches) {
        if (_checkPresetMatch(ocrText, batch.productCode, batch.batchNumber)) {
          return true;
        }
      }
      return false;
    } else {
      // 单批次任务
      return _checkPresetMatch(ocrText, presetProductCode, presetBatchNumber);
    }
  }
  
  /// 检查预设匹配
  bool _checkPresetMatch(String ocrText, String? productCode, String? batchNumber) {
    if (productCode == null && batchNumber == null) return true;
    
    final cleanText = ocrText.replaceAll(RegExp(r'\s+'), '').toUpperCase();
    
    bool productMatch = true;
    bool batchMatch = true;
    
    if (productCode != null) {
      final cleanProductCode = productCode.replaceAll(RegExp(r'\s+'), '').toUpperCase();
      productMatch = cleanText.contains(cleanProductCode);
    }
    
    if (batchNumber != null) {
      final cleanBatchNumber = batchNumber.replaceAll(RegExp(r'\s+'), '').toUpperCase();
      batchMatch = cleanText.contains(cleanBatchNumber);
    }
    
    return productMatch && batchMatch;
  }
  
  /// 获取推荐模式
  static RecognitionMode getRecommendedMode({
    required bool hasBlueBackground,
    required bool hasReflection,
    required bool hasQRCode,
    required bool prioritizeSpeed,
  }) {
    if (prioritizeSpeed) {
      return RecognitionMode.fast;
    }
    
    if (hasBlueBackground || hasReflection || hasQRCode) {
      return RecognitionMode.precision;
    }
    
    return RecognitionMode.standard;
  }
}
