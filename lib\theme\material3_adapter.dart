import 'package:flutter/material.dart';

/// Material 3主题适配器
/// 提供完整的Material 3颜色系统、文本样式、渐变色、阴影效果等
class Material3Adapter {
  Material3Adapter._();
}

/// Material 3颜色系统
class Material3AdapterColors {
  Material3AdapterColors._();

  // 主要颜色
  static Color primary(BuildContext context) => Theme.of(context).colorScheme.primary;
  static Color onPrimary(BuildContext context) => Theme.of(context).colorScheme.onPrimary;
  static Color primaryContainer(BuildContext context) => Theme.of(context).colorScheme.primaryContainer;
  static Color onPrimaryContainer(BuildContext context) => Theme.of(context).colorScheme.onPrimaryContainer;

  // 次要颜色
  static Color secondary(BuildContext context) => Theme.of(context).colorScheme.secondary;
  static Color onSecondary(BuildContext context) => Theme.of(context).colorScheme.onSecondary;
  static Color secondaryContainer(BuildContext context) => Theme.of(context).colorScheme.secondaryContainer;
  static Color onSecondaryContainer(BuildContext context) => Theme.of(context).colorScheme.onSecondaryContainer;

  // 第三颜色
  static Color tertiary(BuildContext context) => Theme.of(context).colorScheme.tertiary;
  static Color onTertiary(BuildContext context) => Theme.of(context).colorScheme.onTertiary;
  static Color tertiaryContainer(BuildContext context) => Theme.of(context).colorScheme.tertiaryContainer;
  static Color onTertiaryContainer(BuildContext context) => Theme.of(context).colorScheme.onTertiaryContainer;

  // 表面颜色
  static Color surface(BuildContext context) => Theme.of(context).colorScheme.surface;
  static Color onSurface(BuildContext context) => Theme.of(context).colorScheme.onSurface;
  static Color surfaceVariant(BuildContext context) => Theme.of(context).colorScheme.surfaceVariant;
  static Color onSurfaceVariant(BuildContext context) => Theme.of(context).colorScheme.onSurfaceVariant;
  static Color surfaceContainer(BuildContext context) => Theme.of(context).colorScheme.surfaceContainer;
  static Color surfaceContainerHighest(BuildContext context) => Theme.of(context).colorScheme.surfaceContainerHighest;

  // 背景颜色
  static Color background(BuildContext context) => Theme.of(context).colorScheme.background;
  static Color onBackground(BuildContext context) => Theme.of(context).colorScheme.onBackground;

  // 错误颜色
  static Color error(BuildContext context) => Theme.of(context).colorScheme.error;
  static Color onError(BuildContext context) => Theme.of(context).colorScheme.onError;
  static Color errorContainer(BuildContext context) => Theme.of(context).colorScheme.errorContainer;
  static Color onErrorContainer(BuildContext context) => Theme.of(context).colorScheme.onErrorContainer;

  // 轮廓颜色
  static Color outline(BuildContext context) => Theme.of(context).colorScheme.outline;
  static Color outlineVariant(BuildContext context) => Theme.of(context).colorScheme.outlineVariant;

  // 阴影颜色
  static Color shadow(BuildContext context) => Theme.of(context).colorScheme.shadow;
  static Color scrim(BuildContext context) => Theme.of(context).colorScheme.scrim;

  // 反色
  static Color inverseSurface(BuildContext context) => Theme.of(context).colorScheme.inverseSurface;
  static Color onInverseSurface(BuildContext context) => Theme.of(context).colorScheme.onInverseSurface;
  static Color inversePrimary(BuildContext context) => Theme.of(context).colorScheme.inversePrimary;

  // 表面色调
  static Color surfaceTint(BuildContext context) => Theme.of(context).colorScheme.surfaceTint;
}

/// Material 3文本样式
class Material3AdapterTextStyles {
  Material3AdapterTextStyles._();

  // Display样式
  static TextStyle displayLarge(BuildContext context) => Theme.of(context).textTheme.displayLarge!;
  static TextStyle displayMedium(BuildContext context) => Theme.of(context).textTheme.displayMedium!;
  static TextStyle displaySmall(BuildContext context) => Theme.of(context).textTheme.displaySmall!;

  // Headline样式
  static TextStyle headlineLarge(BuildContext context) => Theme.of(context).textTheme.headlineLarge!;
  static TextStyle headlineMedium(BuildContext context) => Theme.of(context).textTheme.headlineMedium!;
  static TextStyle headlineSmall(BuildContext context) => Theme.of(context).textTheme.headlineSmall!;

  // Title样式
  static TextStyle titleLarge(BuildContext context) => Theme.of(context).textTheme.titleLarge!;
  static TextStyle titleMedium(BuildContext context) => Theme.of(context).textTheme.titleMedium!;
  static TextStyle titleSmall(BuildContext context) => Theme.of(context).textTheme.titleSmall!;

  // Body样式
  static TextStyle bodyLarge(BuildContext context) => Theme.of(context).textTheme.bodyLarge!;
  static TextStyle bodyMedium(BuildContext context) => Theme.of(context).textTheme.bodyMedium!;
  static TextStyle bodySmall(BuildContext context) => Theme.of(context).textTheme.bodySmall!;

  // Label样式
  static TextStyle labelLarge(BuildContext context) => Theme.of(context).textTheme.labelLarge!;
  static TextStyle labelMedium(BuildContext context) => Theme.of(context).textTheme.labelMedium!;
  static TextStyle labelSmall(BuildContext context) => Theme.of(context).textTheme.labelSmall!;
}

/// Material 3渐变色生成器
class Material3AdapterGradients {
  Material3AdapterGradients._();

  static LinearGradient primary(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      colors: [
        colorScheme.primary,
        colorScheme.primary.withOpacity(0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  static LinearGradient secondary(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      colors: [
        colorScheme.secondary,
        colorScheme.secondary.withOpacity(0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  static LinearGradient tertiary(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      colors: [
        colorScheme.tertiary,
        colorScheme.tertiary.withOpacity(0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  static LinearGradient surface(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      colors: [
        colorScheme.surface,
        colorScheme.surfaceVariant,
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );
  }

  static LinearGradient error(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return LinearGradient(
      colors: [
        colorScheme.error,
        colorScheme.error.withOpacity(0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}

/// Material 3阴影系统
class Material3AdapterShadows {
  Material3AdapterShadows._();

  static BoxShadow elevation1(BuildContext context) {
    return BoxShadow(
      color: Theme.of(context).colorScheme.shadow.withOpacity(0.15),
      blurRadius: 4,
      offset: const Offset(0, 1),
    );
  }

  static BoxShadow elevation2(BuildContext context) {
    return BoxShadow(
      color: Theme.of(context).colorScheme.shadow.withOpacity(0.15),
      blurRadius: 8,
      offset: const Offset(0, 2),
    );
  }

  static BoxShadow elevation3(BuildContext context) {
    return BoxShadow(
      color: Theme.of(context).colorScheme.shadow.withOpacity(0.15),
      blurRadius: 12,
      offset: const Offset(0, 4),
    );
  }

  static BoxShadow elevation4(BuildContext context) {
    return BoxShadow(
      color: Theme.of(context).colorScheme.shadow.withOpacity(0.15),
      blurRadius: 16,
      offset: const Offset(0, 6),
    );
  }

  static BoxShadow elevation5(BuildContext context) {
    return BoxShadow(
      color: Theme.of(context).colorScheme.shadow.withOpacity(0.15),
      blurRadius: 20,
      offset: const Offset(0, 8),
    );
  }
}

/// Material 3形状系统
class Material3AdapterShapes {
  Material3AdapterShapes._();

  static const double radiusExtraSmall = 4.0;
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusExtraLarge = 28.0;

  static BorderRadius extraSmall() => BorderRadius.circular(radiusExtraSmall);
  static BorderRadius small() => BorderRadius.circular(radiusSmall);
  static BorderRadius medium() => BorderRadius.circular(radiusMedium);
  static BorderRadius large() => BorderRadius.circular(radiusLarge);
  static BorderRadius extraLarge() => BorderRadius.circular(radiusExtraLarge);
}

/// Material 3间距系统
class Material3AdapterSpacing {
  Material3AdapterSpacing._();

  static const double extraSmall = 4.0;
  static const double small = 8.0;
  static const double medium = 16.0;
  static const double large = 24.0;
  static const double extraLarge = 32.0;
}

/// 便捷访问类
class Material3AdapterAccess {
  static Material3AdapterColors get Colors => Material3AdapterColors._();
  static Material3AdapterTextStyles get TextStyles => Material3AdapterTextStyles._();
  static Material3AdapterGradients get Gradients => Material3AdapterGradients._();
  static Material3AdapterShadows get Shadows => Material3AdapterShadows._();
  static Material3AdapterShapes get Shapes => Material3AdapterShapes._();
  static Material3AdapterSpacing get Spacing => Material3AdapterSpacing._();
}
