# 📚 LoadGuard API接口文档

## 📋 **文档信息**

**文档版本**: v2.0  
**更新日期**: 2025年8月4日  
**维护团队**: LoadGuard开发团队  
**适用版本**: LoadGuard v2.0+

## 🎯 **接口概述**

LoadGuard提供了完整的多引擎识别API，支持工业标签的智能识别、任务管理、数据统计等功能。

## 🚀 **多引擎识别API**

### **MultiEngineRecognitionService**

#### **1. 核心识别接口**

```dart
/// 🎯 多引擎智能识别
Future<List<RecognitionResult>> recognizeWithMultiEngine(
  String imagePath, {
  Function(double progress, String status)? onProgress,
  RecognitionStrategy strategy = RecognitionStrategy.balanced,
})
```

**参数说明**:
- `imagePath`: 图像文件路径 (必需)
- `onProgress`: 进度回调函数 (可选)
  - `progress`: 进度值 0.0-1.0
  - `status`: 当前状态描述
- `strategy`: 识别策略 (可选，默认平衡模式)

**返回值**: `List<RecognitionResult>`
```dart
class RecognitionResult {
  final String ocrText;           // 识别的文字内容
  final double confidence;        // 置信度 0.0-1.0
  final Rect boundingBox;         // 文字边界框
  final List<String> recognizedElements; // 识别元素列表
}
```

**使用示例**:
```dart
final results = await MultiEngineRecognitionService.instance
    .recognizeWithMultiEngine(
  '/path/to/image.jpg',
  strategy: RecognitionStrategy.accuracy,
  onProgress: (progress, status) {
    print('进度: ${(progress * 100).toInt()}% - $status');
  },
);

for (final result in results) {
  print('文字: ${result.ocrText}, 置信度: ${result.confidence}');
}
```

#### **2. 识别策略枚举**

```dart
enum RecognitionStrategy {
  speed,    // 速度优先: 使用ML Kit + 边缘检测引擎
  accuracy, // 精度优先: 使用所有引擎 + 高级预处理
  balanced, // 平衡模式: 智能选择引擎组合
}
```

#### **3. 引擎类型枚举**

```dart
enum RecognitionEngine {
  mlkit('ML Kit引擎'),                    // Google官方OCR
  edgeDetection('边缘检测引擎'),           // Canny边缘检测算法
  templateMatching('模板匹配引擎'),        // 工业模板匹配
  characterSegmentation('字符分割引擎'),   // 传统字符分割
}
```

### **MultiEngineRecognitionManager**

#### **1. 静态识别方法**

```dart
/// 🚀 新一代多引擎识别 (静态方法)
static Future<List<RecognitionResult>> recognizeWithMultiEngine(
  String imagePath, {
  Function(double progress, String status)? onProgress,
  RecognitionStrategy strategy = RecognitionStrategy.balanced,
})
```

#### **2. 策略推荐方法**

```dart
/// 🎯 智能策略推荐
static RecognitionStrategy recommendStrategy({
  bool prioritizeSpeed = false,     // 是否优先速度
  bool prioritizeAccuracy = false,  // 是否优先精度
  bool hasReflection = false,       // 是否有反光
  bool hasTilt = false,            // 是否有倾斜
})
```

**使用示例**:
```dart
final strategy = MultiEngineRecognitionManager.recommendStrategy(
  hasReflection: true,
  hasTilt: true,
  prioritizeAccuracy: true,
);

final results = await MultiEngineRecognitionManager.recognizeWithMultiEngine(
  imagePath,
  strategy: strategy,
);
```

#### **3. 性能统计方法**

```dart
/// 📊 获取引擎性能统计
static Map<RecognitionEngine, double> getEnginePerformanceStats()
```

**返回示例**:
```dart
{
  RecognitionEngine.mlkit: 0.95,
  RecognitionEngine.edgeDetection: 0.75,
  RecognitionEngine.templateMatching: 0.85,
  RecognitionEngine.characterSegmentation: 0.65,
}
```

## 🔥 **图像预处理API**

### **AdvancedReflectionSuppressor**

#### **反光抑制接口**

```dart
/// 🔥 高级反光抑制
static Future<String> suppressReflections(String imagePath)
```

**功能**: 检测和抑制图像中的反光区域  
**参数**: `imagePath` - 输入图像路径  
**返回**: 处理后的图像路径  

**使用示例**:
```dart
final processedPath = await AdvancedReflectionSuppressor
    .suppressReflections('/path/to/image.jpg');
```

### **AdvancedPerspectiveCorrector**

#### **透视校正接口**

```dart
/// 📐 高级透视校正
static Future<String> correctPerspective(String imagePath)
```

**功能**: 检测和校正图像中的透视变形  
**参数**: `imagePath` - 输入图像路径  
**返回**: 校正后的图像路径  

**使用示例**:
```dart
final correctedPath = await AdvancedPerspectiveCorrector
    .correctPerspective('/path/to/image.jpg');
```

## 📋 **任务管理API**

### **TaskBusinessService**

#### **1. 任务创建**

```dart
/// 创建新任务
Future<TaskModel> createTask({
  required String taskName,
  required String description,
  String? imagePath,
})
```

#### **2. 任务更新**

```dart
/// 更新任务信息
Future<void> updateTask(TaskModel task)

/// 更新任务状态
Future<void> updateTaskStatus(String taskId, TaskStatus status)
```

#### **3. 任务查询**

```dart
/// 获取所有任务
Future<List<TaskModel>> getAllTasks()

/// 根据ID获取任务
Future<TaskModel?> getTaskById(String taskId)

/// 根据状态筛选任务
Future<List<TaskModel>> getTasksByStatus(TaskStatus status)
```

#### **4. 任务删除**

```dart
/// 删除任务
Future<void> deleteTask(String taskId)

/// 批量删除任务
Future<void> deleteTasks(List<String> taskIds)
```

### **TaskValidationService**

#### **任务验证接口**

```dart
/// 验证任务数据
ValidationResult validateTask(TaskModel task)

/// 验证任务名称
bool validateTaskName(String name)

/// 验证图像路径
bool validateImagePath(String? imagePath)
```

## 📊 **数据统计API**

### **WorkloadStatisticsService**

#### **1. 基础统计**

```dart
/// 获取任务总数
Future<int> getTotalTaskCount()

/// 获取已完成任务数
Future<int> getCompletedTaskCount()

/// 获取进行中任务数
Future<int> getInProgressTaskCount()
```

#### **2. 时间统计**

```dart
/// 获取今日任务统计
Future<DailyStatistics> getTodayStatistics()

/// 获取本周任务统计
Future<WeeklyStatistics> getWeeklyStatistics()

/// 获取本月任务统计
Future<MonthlyStatistics> getMonthlyStatistics()
```

#### **3. 性能统计**

```dart
/// 获取识别性能统计
Future<RecognitionPerformanceStats> getRecognitionStats()

/// 获取平均处理时间
Future<Duration> getAverageProcessingTime()
```

## 📸 **照片管理API**

### **PhotoManagementService**

#### **1. 照片操作**

```dart
/// 拍摄照片
Future<String?> capturePhoto()

/// 从相册选择照片
Future<String?> pickPhotoFromGallery()

/// 保存照片到任务
Future<void> savePhotoToTask(String taskId, String photoPath)
```

#### **2. 照片处理**

```dart
/// 压缩照片
Future<String> compressPhoto(String photoPath, {int quality = 85})

/// 调整照片尺寸
Future<String> resizePhoto(String photoPath, {int maxWidth = 1920})
```

## 🔧 **配置管理API**

### **AppConfigService**

#### **1. 识别配置**

```dart
/// 设置默认识别策略
Future<void> setDefaultRecognitionStrategy(RecognitionStrategy strategy)

/// 获取默认识别策略
Future<RecognitionStrategy> getDefaultRecognitionStrategy()

/// 设置识别超时时间
Future<void> setRecognitionTimeout(Duration timeout)
```

#### **2. 性能配置**

```dart
/// 设置图像质量
Future<void> setImageQuality(int quality)

/// 设置并行引擎数量
Future<void> setParallelEngineCount(int count)

/// 启用/禁用缓存
Future<void> setCacheEnabled(bool enabled)
```

## 📱 **状态管理API**

### **TaskNotifier (Riverpod)**

#### **1. 状态获取**

```dart
/// 获取任务列表状态
final taskListProvider = StateNotifierProvider<TaskNotifier, TaskState>

/// 获取当前任务状态
final currentTaskProvider = StateProvider<TaskModel?>

/// 获取加载状态
final isLoadingProvider = StateProvider<bool>
```

#### **2. 状态操作**

```dart
/// 刷新任务列表
void refreshTasks()

/// 添加新任务
void addTask(TaskModel task)

/// 更新任务
void updateTask(TaskModel task)

/// 删除任务
void removeTask(String taskId)
```

## 🚨 **错误处理**

### **异常类型**

```dart
/// 识别异常
class RecognitionException implements Exception {
  final String message;
  final String? imagePath;
  final RecognitionEngine? failedEngine;
}

/// 任务异常
class TaskException implements Exception {
  final String message;
  final String? taskId;
  final TaskOperation operation;
}

/// 验证异常
class ValidationException implements Exception {
  final String message;
  final Map<String, String> fieldErrors;
}
```

### **错误处理示例**

```dart
try {
  final results = await MultiEngineRecognitionService.instance
      .recognizeWithMultiEngine(imagePath);
} on RecognitionException catch (e) {
  print('识别失败: ${e.message}');
  if (e.failedEngine != null) {
    print('失败引擎: ${e.failedEngine!.name}');
  }
} catch (e) {
  print('未知错误: $e');
}
```

## 📈 **性能监控API**

### **PerformanceMonitor**

#### **1. 性能指标**

```dart
/// 记录识别性能
void recordRecognitionPerformance({
  required Duration processingTime,
  required RecognitionEngine engine,
  required double confidence,
})

/// 获取性能报告
Future<PerformanceReport> getPerformanceReport()
```

#### **2. 内存监控**

```dart
/// 获取当前内存使用
int getCurrentMemoryUsage()

/// 获取峰值内存使用
int getPeakMemoryUsage()

/// 清理内存缓存
Future<void> clearMemoryCache()
```

## 🔄 **回调接口**

### **进度回调**

```dart
typedef ProgressCallback = void Function(double progress, String status);
```

### **结果回调**

```dart
typedef RecognitionCallback = void Function(List<RecognitionResult> results);
```

### **错误回调**

```dart
typedef ErrorCallback = void Function(Exception error);
```

## 📝 **使用最佳实践**

### **1. 性能优化**

```dart
// ✅ 推荐：根据场景选择合适的策略
final strategy = MultiEngineRecognitionManager.recommendStrategy(
  hasReflection: imageHasReflection,
  hasTilt: imageHasTilt,
  prioritizeSpeed: needFastProcessing,
);

// ✅ 推荐：使用进度回调提升用户体验
await recognizeWithMultiEngine(
  imagePath,
  strategy: strategy,
  onProgress: (progress, status) {
    updateUI(progress, status);
  },
);
```

### **2. 错误处理**

```dart
// ✅ 推荐：完整的错误处理
try {
  final results = await recognizeWithMultiEngine(imagePath);
  handleResults(results);
} on RecognitionException catch (e) {
  handleRecognitionError(e);
} on FileSystemException catch (e) {
  handleFileError(e);
} catch (e) {
  handleUnknownError(e);
}
```

### **3. 资源管理**

```dart
// ✅ 推荐：及时释放资源
final service = MultiEngineRecognitionService.instance;
try {
  final results = await service.recognizeWithMultiEngine(imagePath);
  return results;
} finally {
  // 清理临时文件
  await cleanupTempFiles();
}
```

---

**API支持**: 如有API使用问题，请参考示例代码或联系技术支持团队。
