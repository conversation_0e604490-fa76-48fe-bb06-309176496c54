import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:loadguard/services/smart_image_cache_manager.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:path/path.dart' as path;

/// 🔮 【预测性缓存服务】
/// 
/// 小范围试点功能，基于用户行为预测并预加载图片
/// 
/// 功能特性：
/// 1. 行为模式学习
/// 2. 智能预加载
/// 3. 资源控制
/// 4. 性能监控
class PredictiveCacheService {
  static PredictiveCacheService? _instance;
  static PredictiveCacheService get instance {
    _instance ??= PredictiveCacheService._();
    return _instance!;
  }
  
  PredictiveCacheService._();
  
  // 配置参数
  static const int _maxPredictiveCache = 5; // 最多预测缓存5张图片
  static const Duration _predictionWindow = Duration(minutes: 10); // 预测时间窗口
  static const double _confidenceThreshold = 0.6; // 预测置信度阈值
  
  final Map<String, List<AccessRecord>> _accessHistory = {};
  final Map<String, PredictionTask> _activePredictions = {};
  Timer? _cleanupTimer;
  bool _isEnabled = true;
  bool _isInitialized = false;
  
  /// 初始化预测性缓存服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('🔮 初始化预测性缓存服务...');
    
    try {
      // 确保底层缓存服务已初始化
      await SmartImageCacheManager.instance.initialize();
      
      // 启动定期清理
      _scheduleCleanup();
      
      _isInitialized = true;
      AppLogger.info('✅ 预测性缓存服务初始化完成');
      
    } catch (e) {
      AppLogger.error('❌ 预测性缓存服务初始化失败: $e');
      rethrow;
    }
  }
  
  /// 记录图片访问行为
  void recordAccess(String imagePath, {String? context}) async {
    if (!_isEnabled || !_isInitialized) return;
    
    try {
      final record = AccessRecord(
        imagePath: imagePath,
        timestamp: DateTime.now(),
        context: context,
      );
      
      final directory = path.dirname(imagePath);
      
      if (!_accessHistory.containsKey(directory)) {
        _accessHistory[directory] = [];
      }
      
      _accessHistory[directory]!.add(record);
      
      // 限制历史记录大小
      if (_accessHistory[directory]!.length > 50) {
        _accessHistory[directory] = _accessHistory[directory]!.skip(25).toList();
      }
      
      // 触发预测
      await _triggerPrediction(directory, imagePath);
      
      AppLogger.debug('📝 记录图片访问: $imagePath');
      
    } catch (e) {
      AppLogger.error('记录访问行为失败: $e');
    }
  }
  
  /// 触发预测
  Future<void> _triggerPrediction(String directory, String currentImage) async {
    if (!_isEnabled) return;
    
    try {
      // 防止重复预测
      if (_activePredictions.containsKey(directory)) {
        return;
      }
      
      final predictions = await _predictNextImages(directory, currentImage);
      
      if (predictions.isNotEmpty) {
        final task = PredictionTask(
          directory: directory,
          currentImage: currentImage,
          predictions: predictions,
          startTime: DateTime.now(),
        );
        
        _activePredictions[directory] = task;
        
        // 异步执行预加载
        _executePredictiveLoading(task);
      }
      
    } catch (e) {
      AppLogger.error('触发预测失败: $e');
    }
  }
  
  /// 预测下一批可能访问的图片
  Future<List<ImagePrediction>> _predictNextImages(String directory, String currentImage) async {
    final history = _accessHistory[directory];
    if (history == null || history.length < 3) {
      return []; // 历史数据不足
    }
    
    try {
      // 获取目录中的所有图片
      final allImages = await _getDirectoryImages(directory);
      if (allImages.length <= 1) {
        return []; // 目录中图片太少
      }
      
      // 分析访问模式
      final patterns = _analyzeAccessPatterns(history, currentImage);
      
      // 生成预测
      final predictions = <ImagePrediction>[];
      
      for (final imagePath in allImages) {
        if (imagePath == currentImage) continue;
        
        final confidence = _calculatePredictionConfidence(patterns, imagePath);
        
        if (confidence >= _confidenceThreshold) {
          predictions.add(ImagePrediction(
            imagePath: imagePath,
            confidence: confidence,
            reason: 'Pattern-based prediction',
          ));
        }
      }
      
      // 按置信度排序，限制数量
      predictions.sort((a, b) => b.confidence.compareTo(a.confidence));
      
      return predictions.take(_maxPredictiveCache).toList();
      
    } catch (e) {
      AppLogger.error('预测图片失败: $e');
      return [];
    }
  }
  
  /// 获取目录中的图片文件
  Future<List<String>> _getDirectoryImages(String directory) async {
    try {
      final dir = Directory(directory);
      if (!await dir.exists()) return [];
      
      final images = <String>[];
      final extensions = {'.jpg', '.jpeg', '.png', '.bmp'};
      
      await for (final entity in dir.list()) {
        if (entity is File) {
          final ext = path.extension(entity.path).toLowerCase();
          if (extensions.contains(ext)) {
            images.add(entity.path);
          }
        }
      }
      
      return images;
      
    } catch (e) {
      AppLogger.error('获取目录图片失败: $e');
      return [];
    }
  }
  
  /// 分析访问模式
  Map<String, dynamic> _analyzeAccessPatterns(List<AccessRecord> history, String currentImage) {
    final patterns = <String, dynamic>{};
    
    try {
      // 1. 序列模式分析
      final sequences = <String>[];
      for (int i = 1; i < history.length; i++) {
        final prev = path.basename(history[i-1].imagePath);
        final curr = path.basename(history[i].imagePath);
        sequences.add('$prev->$curr');
      }
      
      patterns['sequences'] = sequences;
      
      // 2. 时间间隔分析
      final intervals = <int>[];
      for (int i = 1; i < history.length; i++) {
        final interval = history[i].timestamp.difference(history[i-1].timestamp).inSeconds;
        intervals.add(interval);
      }
      
      if (intervals.isNotEmpty) {
        intervals.sort();
        patterns['avgInterval'] = intervals[intervals.length ~/ 2]; // 中位数
      }
      
      // 3. 访问频率分析
      final frequency = <String, int>{};
      for (final record in history) {
        final fileName = path.basename(record.imagePath);
        frequency[fileName] = (frequency[fileName] ?? 0) + 1;
      }
      
      patterns['frequency'] = frequency;
      
      return patterns;
      
    } catch (e) {
      AppLogger.error('分析访问模式失败: $e');
      return {};
    }
  }
  
  /// 计算预测置信度
  double _calculatePredictionConfidence(Map<String, dynamic> patterns, String imagePath) {
    double confidence = 0.0;
    
    try {
      final fileName = path.basename(imagePath);
      
      // 1. 序列模式匹配 (50%)
      final sequences = patterns['sequences'] as List<String>? ?? [];
      for (final seq in sequences) {
        if (seq.endsWith('->$fileName')) {
          confidence += 0.5;
          break;
        }
      }
      
      // 2. 访问频率 (30%)
      final frequency = patterns['frequency'] as Map<String, int>? ?? {};
      final fileFreq = frequency[fileName] ?? 0;
      final maxFreq = frequency.values.isNotEmpty ? frequency.values.reduce((a, b) => a > b ? a : b) : 1;
      
      if (maxFreq > 0) {
        confidence += (fileFreq / maxFreq) * 0.3;
      }
      
      // 3. 文件名相似度 (20%)
      // 简单的文件名模式匹配
      if (fileName.contains(RegExp(r'\d+'))) {
        confidence += 0.2;
      }
      
      return confidence.clamp(0.0, 1.0);
      
    } catch (e) {
      AppLogger.error('计算预测置信度失败: $e');
      return 0.0;
    }
  }
  
  /// 执行预测性加载
  Future<void> _executePredictiveLoading(PredictionTask task) async {
    AppLogger.info('🔮 开始预测性加载: ${task.directory} - ${task.predictions.length}张图片');
    
    try {
      int loadedCount = 0;
      
      for (final prediction in task.predictions) {
        if (!_activePredictions.containsKey(task.directory)) {
          break; // 任务已取消
        }
        
        try {
          // 检查文件是否存在
          if (!await File(prediction.imagePath).exists()) {
            continue;
          }
          
          // 预加载到缓存
          await SmartImageCacheManager.instance.getCachedImage(prediction.imagePath);
          loadedCount++;
          
          AppLogger.debug('🔮 预加载成功: ${prediction.imagePath} (置信度: ${prediction.confidence.toStringAsFixed(2)})');
          
          // 避免阻塞，添加小延迟
          await Future.delayed(const Duration(milliseconds: 50));
          
        } catch (e) {
          AppLogger.warning('预加载单张图片失败: ${prediction.imagePath} - $e');
        }
      }
      
      AppLogger.info('✅ 预测性加载完成: ${task.directory} - 成功加载${loadedCount}张图片');
      
    } catch (e) {
      AppLogger.error('执行预测性加载失败: $e');
    } finally {
      _activePredictions.remove(task.directory);
    }
  }
  
  /// 启用/禁用预测性缓存
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    
    if (!enabled) {
      // 清理活跃预测任务
      _activePredictions.clear();
    }
    
    AppLogger.info('🔮 预测性缓存已${enabled ? '启用' : '禁用'}');
  }
  
  /// 获取预测统计信息
  PredictiveStats getStats() {
    int totalRecords = 0;
    for (final history in _accessHistory.values) {
      totalRecords += history.length;
    }
    
    return PredictiveStats(
      totalAccessRecords: totalRecords,
      trackedDirectories: _accessHistory.length,
      activePredictions: _activePredictions.length,
      isEnabled: _isEnabled,
    );
  }
  
  /// 清理过期数据
  void _scheduleCleanup() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(const Duration(minutes: 30), (_) {
      _cleanupExpiredData();
    });
  }
  
  void _cleanupExpiredData() {
    try {
      final cutoffTime = DateTime.now().subtract(const Duration(hours: 2));
      int removedCount = 0;
      
      for (final directory in _accessHistory.keys.toList()) {
        final history = _accessHistory[directory]!;
        final originalLength = history.length;
        
        history.removeWhere((record) => record.timestamp.isBefore(cutoffTime));
        
        removedCount += originalLength - history.length;
        
        if (history.isEmpty) {
          _accessHistory.remove(directory);
        }
      }
      
      if (removedCount > 0) {
        AppLogger.info('🧹 预测缓存清理完成，移除${removedCount}条过期记录');
      }
      
    } catch (e) {
      AppLogger.error('预测缓存清理失败: $e');
    }
  }
  
  /// 销毁服务
  void dispose() {
    _cleanupTimer?.cancel();
    _accessHistory.clear();
    _activePredictions.clear();
    _isInitialized = false;
    AppLogger.info('🧹 预测性缓存服务已销毁');
  }
}

/// 📊 访问记录
class AccessRecord {
  final String imagePath;
  final DateTime timestamp;
  final String? context;
  
  const AccessRecord({
    required this.imagePath,
    required this.timestamp,
    this.context,
  });
}

/// 🔮 图片预测
class ImagePrediction {
  final String imagePath;
  final double confidence;
  final String reason;
  
  const ImagePrediction({
    required this.imagePath,
    required this.confidence,
    required this.reason,
  });
}

/// 📋 预测任务
class PredictionTask {
  final String directory;
  final String currentImage;
  final List<ImagePrediction> predictions;
  final DateTime startTime;
  
  const PredictionTask({
    required this.directory,
    required this.currentImage,
    required this.predictions,
    required this.startTime,
  });
}

/// 📈 预测统计
class PredictiveStats {
  final int totalAccessRecords;
  final int trackedDirectories;
  final int activePredictions;
  final bool isEnabled;
  
  const PredictiveStats({
    required this.totalAccessRecords,
    required this.trackedDirectories,
    required this.activePredictions,
    required this.isEnabled,
  });
}

/// 🔮 预测性图片组件
class PredictiveImage extends StatefulWidget {
  final String imagePath;
  final BoxFit fit;
  final double? width;
  final double? height;
  final Widget? placeholder;
  final Widget? errorWidget;
  
  const PredictiveImage({
    Key? key,
    required this.imagePath,
    this.fit = BoxFit.cover,
    this.width,
    this.height,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);
  
  @override
  State<PredictiveImage> createState() => _PredictiveImageState();
}

class _PredictiveImageState extends State<PredictiveImage> {
  @override
  void initState() {
    super.initState();
    // 记录访问行为
    PredictiveCacheService.instance.recordAccess(widget.imagePath);
  }
  
  @override
  Widget build(BuildContext context) {
    return Image.file(
      File(widget.imagePath),
      fit: widget.fit,
      width: widget.width,
      height: widget.height,
      errorBuilder: (context, error, stackTrace) {
        return widget.errorWidget ?? Container(
          color: Colors.grey[300],
          child: const Icon(Icons.error, color: Colors.red),
        );
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        return widget.placeholder ?? const Center(
          child: CircularProgressIndicator(),
        );
      },
    );
  }
}