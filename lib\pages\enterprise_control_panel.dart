import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'package:go_router/go_router.dart';
import '../utils/theme_colors.dart';
import '../widgets/themed_card.dart';
import '../widgets/industrial_logo.dart';
import '../utils/simple_navigation_helper.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

/// 🏢 企业控制面板 - 企业级管理界面
class EnterpriseControlPanel extends ConsumerStatefulWidget {
  const EnterpriseControlPanel({Key? key}) : super(key: key);

  @override
  ConsumerState<EnterpriseControlPanel> createState() => _EnterpriseControlPanelState();
}

class _EnterpriseControlPanelState extends ConsumerState<EnterpriseControlPanel>
    with WidgetsBindingObserver, LifecycleMixin<EnterpriseControlPanel> {
  bool _isLoading = false;
  Timer? _statusUpdateTimer;
  bool _isDisposed = false;

  Map<String, dynamic> _systemStatus = {
    'isOnline': true,
    'activeUsers': 12,
    'totalTasks': 156,
    'completedTasks': 142,
    'systemHealth': 98.5,
    'lastUpdate': DateTime.now(),
  };

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startStatusUpdates();
    });
  }

  @override
  void onLifecycleDispose() {
    _isDisposed = true;
    _statusUpdateTimer?.cancel();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      _startStatusUpdates();
    } else if (state == AppLifecycleState.paused) {
      _statusUpdateTimer?.cancel();
    }
  }

  /// 启动状态更新定时器
  void _startStatusUpdates() {
    _statusUpdateTimer?.cancel();
    _statusUpdateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!_isDisposed && mounted) {
        _updateSystemStatus();
      }
    });
  }

  /// 更新系统状态
  void _updateSystemStatus() {
    if (_isDisposed || !mounted) return;

    setState(() {
      _systemStatus['lastUpdate'] = DateTime.now();
      // 模拟状态更新
      _systemStatus['activeUsers'] = 12 + (DateTime.now().second % 5);
    });
  }

  @override
  Widget build(BuildContext context) {
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: () {
        HapticFeedback.lightImpact();
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        } else {
          context.go('/home');
        }
      },
      enableSwipeBack: true,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: SafeArea(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        _buildWelcomeCard(),
                        const SizedBox(height: 20),
                        _buildQuickActions(),
                        const SizedBox(height: 20),
                        _buildSystemStatus(),
                        const SizedBox(height: 20),
                        _buildManagementTools(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      margin: const EdgeInsets.all(20),
      decoration: ModernUIHelper.modernCardDecoration(
        isGlass: true,
        isNeumorphic: false,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: ModernUIHelper.modernButtonDecoration(
                type: 'primary',
                isNeumorphic: false,
              ),
              child: Icon(
                Icons.business,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '企业控制面板',
                    style: TextStyles.modernTitle.copyWith(
                      fontSize: 20,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                  Text(
                    '企业级管理中心',
                    style: TextStyles.modernSubtitle.copyWith(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
            IndustrialLogo(
              size: 32,
              primaryColor: Theme.of(context).colorScheme.onPrimaryContainer,
              accentColor: ThemeColors.primaryLight,
              showText: false,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.successButtonGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.dashboard,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '欢迎使用企业控制面板',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                    Text(
                      '管理企业级功能和系统设置',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                      '在线用户', '${_systemStatus['activeUsers']}'),
                ),
                Expanded(
                  child: _buildStatusItem(
                      '今日任务', '${_systemStatus['totalTasks']}'),
                ),
                Expanded(
                  child: _buildStatusItem(
                      '系统健康度', '${_systemStatus['systemHealth']}%'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.warningButtonGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.flash_on,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                '快速操作',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2.5,
            children: [
              _buildActionButton(
                  '用户管理', Icons.people, () => _navigateToUserManagement()),
              _buildActionButton(
                  '任务监控', Icons.task_alt, () => _navigateToTaskMonitoring()),
              _buildActionButton(
                  '系统设置', Icons.settings, () => _navigateToSystemSettings()),
              _buildActionButton(
                  '数据分析', Icons.analytics, () => _navigateToAnalytics()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String title, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
          border: Border.all(color: Colors.white.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemStatus() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.monitor_heart,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '系统状态',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _systemStatus['isOnline']
                      ? ThemeColors.success
                      : ThemeColors.danger,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _systemStatus['isOnline'] ? '在线' : '离线',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSystemMetric(
              'MLKit识别引擎', '正常运行', Icons.check_circle, ThemeColors.success),
          _buildSystemMetric('数据库连接', '稳定', Icons.storage, ThemeColors.success),
          _buildSystemMetric('网络状态', '良好', Icons.wifi, ThemeColors.success),
          _buildSystemMetric('内存使用', '正常', Icons.memory, ThemeColors.warning),
        ],
      ),
    );
  }

  Widget _buildSystemMetric(
      String title, String status, IconData icon, Color statusColor) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: statusColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementTools() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.orangeGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.admin_panel_settings,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                '管理工具',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildManagementItem('性能监控', '查看系统性能指标', Icons.speed,
              () => context.push('/performance-stats')),
          _buildManagementItem('安全管理', '权限和激活管理', Icons.security,
              () => context.push('/security-management')),
          _buildManagementItem(
              '数据备份', '系统数据备份与恢复', Icons.backup, () => _showDataBackup()),
          _buildManagementItem(
              '日志查看', '系统运行日志分析', Icons.description, () => _showSystemLogs()),
        ],
      ),
    );
  }

  Widget _buildManagementItem(
      String title, String description, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
          border: Border.all(color: Colors.white.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.6),
            ),
          ],
        ),
      ),
    );
  }

  // 导航方法
  void _navigateToUserManagement() {
    _showNotImplemented('用户管理');
  }

  void _navigateToTaskMonitoring() {
    _showNotImplemented('任务监控');
  }

  void _navigateToSystemSettings() {
    _showNotImplemented('系统设置');
  }

  void _navigateToAnalytics() {
    _showNotImplemented('数据分析');
  }

  void _showDataBackup() {
    _showNotImplemented('数据备份');
  }

  void _showSystemLogs() {
    _showNotImplemented('日志查看');
  }

  void _showNotImplemented(String feature) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature 功能正在开发中...'),
        backgroundColor: ThemeColors.info,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
