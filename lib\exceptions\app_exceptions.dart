import 'package:flutter/foundation.dart';

/// 应用异常基类
/// 提供统一的异常处理机制和用户友好的错误信息
abstract class AppException implements Exception {
  const AppException({
    required this.message,
    required this.code,
    this.details,
    this.originalError,
    this.stackTrace,
    this.timestamp,
    this.severity = ErrorSeverity.medium,
    this.category = ErrorCategory.general,
    this.userMessage,
    this.actionable = false,
    this.retryable = false,
  });

  /// 错误消息（技术性）
  final String message;
  
  /// 错误代码
  final String code;
  
  /// 错误详情
  final Map<String, dynamic>? details;
  
  /// 原始错误
  final Object? originalError;
  
  /// 堆栈跟踪
  final StackTrace? stackTrace;
  
  /// 错误发生时间
  final DateTime? timestamp;
  
  /// 错误严重程度
  final ErrorSeverity severity;
  
  /// 错误类别
  final ErrorCategory category;
  
  /// 用户友好的错误消息
  final String? userMessage;
  
  /// 是否可操作（用户可以采取行动解决）
  final bool actionable;
  
  /// 是否可重试
  final bool retryable;

  /// 获取用户显示的消息
  String get displayMessage => userMessage ?? _getDefaultUserMessage();

  /// 获取完整的错误信息
  Map<String, dynamic> toMap() {
    return {
      'type': runtimeType.toString(),
      'message': message,
      'code': code,
      'details': details,
      'originalError': originalError?.toString(),
      'stackTrace': stackTrace?.toString(),
      'timestamp': (timestamp ?? DateTime.now()).toIso8601String(),
      'severity': severity.name,
      'category': category.name,
      'userMessage': userMessage,
      'actionable': actionable,
      'retryable': retryable,
    };
  }

  /// 获取默认用户消息
  String _getDefaultUserMessage() {
    switch (category) {
      case ErrorCategory.network:
        return '网络连接异常，请检查网络设置';
      case ErrorCategory.storage:
        return '数据存储异常，请重试';
      case ErrorCategory.recognition:
        return '识别处理失败，请重新拍照';
      case ErrorCategory.validation:
        return '输入数据有误，请检查后重试';
      case ErrorCategory.permission:
        return '权限不足，请检查应用权限设置';
      case ErrorCategory.configuration:
        return '配置错误，请联系管理员';
      case ErrorCategory.business:
        return '业务处理异常，请重试';
      case ErrorCategory.system:
        return '系统异常，请稍后重试';
      case ErrorCategory.general:
      default:
        return '操作失败，请重试';
    }
  }

  @override
  String toString() {
    return '$runtimeType: $message (Code: $code)';
  }
}

/// 网络异常
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code = 'NETWORK_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = true,
  }) : super(
          category: ErrorCategory.network,
          severity: ErrorSeverity.medium,
          actionable: true,
        );

  factory NetworkException.connectionTimeout() {
    return const NetworkException(
      message: 'Network connection timeout',
      code: 'NETWORK_TIMEOUT',
      userMessage: '网络连接超时，请检查网络后重试',
    );
  }

  factory NetworkException.noConnection() {
    return const NetworkException(
      message: 'No network connection available',
      code: 'NO_CONNECTION',
      userMessage: '无网络连接，请检查网络设置',
    );
  }

  factory NetworkException.serverError(int statusCode) {
    return NetworkException(
      message: 'Server error with status code: $statusCode',
      code: 'SERVER_ERROR_$statusCode',
      userMessage: '服务器异常，请稍后重试',
      details: {'statusCode': statusCode},
    );
  }
}

/// 存储异常
class StorageException extends AppException {
  const StorageException({
    required super.message,
    super.code = 'STORAGE_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = true,
  }) : super(
          category: ErrorCategory.storage,
          severity: ErrorSeverity.medium,
          actionable: true,
        );

  factory StorageException.readFailed(String path) {
    return StorageException(
      message: 'Failed to read from storage: $path',
      code: 'STORAGE_READ_FAILED',
      userMessage: '数据读取失败，请重试',
      details: {'path': path},
    );
  }

  factory StorageException.writeFailed(String path) {
    return StorageException(
      message: 'Failed to write to storage: $path',
      code: 'STORAGE_WRITE_FAILED',
      userMessage: '数据保存失败，请重试',
      details: {'path': path},
    );
  }

  factory StorageException.insufficientSpace() {
    return const StorageException(
      message: 'Insufficient storage space',
      code: 'INSUFFICIENT_SPACE',
      userMessage: '存储空间不足，请清理后重试',
      retryable: false,
    );
  }
}

/// 识别异常
class RecognitionException extends AppException {
  const RecognitionException({
    required super.message,
    super.code = 'RECOGNITION_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = true,
  }) : super(
          category: ErrorCategory.recognition,
          severity: ErrorSeverity.medium,
          actionable: true,
        );

  factory RecognitionException.mlkitFailed() {
    return const RecognitionException(
      message: 'ML Kit text recognition failed',
      code: 'MLKIT_FAILED',
      userMessage: '文字识别失败，请重新拍照',
    );
  }

  factory RecognitionException.imageProcessingFailed() {
    return const RecognitionException(
      message: 'Image processing failed',
      code: 'IMAGE_PROCESSING_FAILED',
      userMessage: '图像处理失败，请重新拍照',
    );
  }

  factory RecognitionException.noTextFound() {
    return const RecognitionException(
      message: 'No text found in image',
      code: 'NO_TEXT_FOUND',
      userMessage: '未识别到文字，请确保图像清晰',
      retryable: false,
    );
  }

  factory RecognitionException.timeout() {
    return const RecognitionException(
      message: 'Recognition process timeout',
      code: 'RECOGNITION_TIMEOUT',
      userMessage: '识别超时，请重试',
    );
  }
}

/// 验证异常
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code = 'VALIDATION_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = false,
  }) : super(
          category: ErrorCategory.validation,
          severity: ErrorSeverity.low,
          actionable: true,
        );

  factory ValidationException.invalidInput(String field) {
    return ValidationException(
      message: 'Invalid input for field: $field',
      code: 'INVALID_INPUT',
      userMessage: '输入格式不正确，请检查后重试',
      details: {'field': field},
    );
  }

  factory ValidationException.requiredField(String field) {
    return ValidationException(
      message: 'Required field missing: $field',
      code: 'REQUIRED_FIELD',
      userMessage: '必填字段不能为空',
      details: {'field': field},
    );
  }
}

/// 权限异常
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code = 'PERMISSION_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = false,
  }) : super(
          category: ErrorCategory.permission,
          severity: ErrorSeverity.high,
          actionable: true,
        );

  factory PermissionException.cameraPermissionDenied() {
    return const PermissionException(
      message: 'Camera permission denied',
      code: 'CAMERA_PERMISSION_DENIED',
      userMessage: '需要相机权限才能拍照，请在设置中开启',
    );
  }

  factory PermissionException.storagePermissionDenied() {
    return const PermissionException(
      message: 'Storage permission denied',
      code: 'STORAGE_PERMISSION_DENIED',
      userMessage: '需要存储权限才能保存数据，请在设置中开启',
    );
  }
}

/// 配置异常
class ConfigurationException extends AppException {
  const ConfigurationException({
    required super.message,
    super.code = 'CONFIG_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = false,
  }) : super(
          category: ErrorCategory.configuration,
          severity: ErrorSeverity.high,
          actionable: false,
        );

  factory ConfigurationException.missingConfig(String configKey) {
    return ConfigurationException(
      message: 'Missing configuration: $configKey',
      code: 'MISSING_CONFIG',
      userMessage: '配置缺失，请联系管理员',
      details: {'configKey': configKey},
    );
  }

  factory ConfigurationException.invalidConfig(String configKey) {
    return ConfigurationException(
      message: 'Invalid configuration: $configKey',
      code: 'INVALID_CONFIG',
      userMessage: '配置错误，请联系管理员',
      details: {'configKey': configKey},
    );
  }
}

/// 业务异常
class BusinessException extends AppException {
  const BusinessException({
    required super.message,
    super.code = 'BUSINESS_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = false,
  }) : super(
          category: ErrorCategory.business,
          severity: ErrorSeverity.medium,
          actionable: true,
        );

  factory BusinessException.taskNotFound(String taskId) {
    return BusinessException(
      message: 'Task not found: $taskId',
      code: 'TASK_NOT_FOUND',
      userMessage: '任务不存在，请刷新后重试',
      details: {'taskId': taskId},
    );
  }

  factory BusinessException.invalidTaskState(String currentState, String expectedState) {
    return BusinessException(
      message: 'Invalid task state: $currentState, expected: $expectedState',
      code: 'INVALID_TASK_STATE',
      userMessage: '任务状态异常，请刷新后重试',
      details: {'currentState': currentState, 'expectedState': expectedState},
    );
  }
}

/// 系统异常
class SystemException extends AppException {
  const SystemException({
    required super.message,
    super.code = 'SYSTEM_ERROR',
    super.details,
    super.originalError,
    super.stackTrace,
    super.userMessage,
    super.retryable = true,
  }) : super(
          category: ErrorCategory.system,
          severity: ErrorSeverity.high,
          actionable: false,
        );

  factory SystemException.unexpectedError(Object error) {
    return SystemException(
      message: 'Unexpected system error',
      code: 'UNEXPECTED_ERROR',
      userMessage: '系统异常，请稍后重试',
      originalError: error,
    );
  }

  factory SystemException.initializationFailed() {
    return const SystemException(
      message: 'System initialization failed',
      code: 'INITIALIZATION_FAILED',
      userMessage: '系统初始化失败，请重启应用',
    );
  }
}

/// 错误严重程度
enum ErrorSeverity {
  low,      // 低：不影响核心功能
  medium,   // 中：影响部分功能
  high,     // 高：影响核心功能
  critical, // 严重：系统无法正常运行
}

/// 错误类别
enum ErrorCategory {
  general,        // 通用
  network,        // 网络
  storage,        // 存储
  recognition,    // 识别
  validation,     // 验证
  permission,     // 权限
  configuration,  // 配置
  business,       // 业务
  system,         // 系统
}
