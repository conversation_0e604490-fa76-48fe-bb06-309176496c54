import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🧠 【智能默认值管理器】
/// 
/// 功能特性：
/// 1. 基于用户历史行为学习
/// 2. 智能推荐常用选项
/// 3. 适应性学习算法
/// 4. 上下文感知推荐
class SmartDefaultsManager {
  static SmartDefaultsManager? _instance;
  static SmartDefaultsManager get instance {
    _instance ??= SmartDefaultsManager._();
    return _instance!;
  }
  
  SmartDefaultsManager._();
  
  static const String _prefsKey = 'smart_defaults_data';
  static const int _maxHistorySize = 100;
  static const double _learningRate = 0.1;
  
  final Map<String, List<UsageRecord>> _usageHistory = {};
  bool _isInitialized = false;
  
  /// 初始化智能默认值管理器
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('🧠 初始化智能默认值管理器...');
    
    try {
      await _loadUsageHistory();
      _isInitialized = true;
      AppLogger.info('✅ 智能默认值管理器初始化完成');
    } catch (e) {
      AppLogger.error('❌ 智能默认值管理器初始化失败: $e');
      rethrow;
    }
  }
  
  /// 记录用户选择行为
  Future<void> recordUsage(String category, String value, {Map<String, dynamic>? context}) async {
    if (!_isInitialized) await initialize();
    
    try {
      final record = UsageRecord(
        value: value,
        timestamp: DateTime.now(),
        context: context ?? {},
        frequency: 1,
      );
      
      if (!_usageHistory.containsKey(category)) {
        _usageHistory[category] = [];
      }
      
      final history = _usageHistory[category]!;
      
      // 查找是否已存在相同值
      final existingIndex = history.indexWhere((r) => r.value == value);
      
      if (existingIndex != -1) {
        // 更新现有记录
        final existing = history[existingIndex];
        history[existingIndex] = existing.copyWith(
          frequency: existing.frequency + 1,
          timestamp: DateTime.now(),
        );
      } else {
        // 添加新记录
        history.add(record);
      }
      
      // 限制历史记录大小
      if (history.length > _maxHistorySize) {
        history.sort((a, b) => b.timestamp.compareTo(a.timestamp));
        _usageHistory[category] = history.take(_maxHistorySize).toList();
      }
      
      // 异步保存，不阻塞UI
      _saveUsageHistory();
      
      AppLogger.debug('📝 记录用户行为: $category -> $value');
      
    } catch (e) {
      AppLogger.error('记录用户行为失败: $e');
    }
  }
  
  /// 获取智能推荐值
  String? getSmartDefault(String category, {Map<String, dynamic>? context}) {
    if (!_isInitialized || !_usageHistory.containsKey(category)) {
      return null;
    }
    
    final history = _usageHistory[category]!;
    if (history.isEmpty) return null;
    
    try {
      // 1. 按频率和时间权重排序
      final weightedHistory = history.map((record) {
        double weight = _calculateWeight(record, context);
        return WeightedRecord(record: record, weight: weight);
      }).toList();
      
      weightedHistory.sort((a, b) => b.weight.compareTo(a.weight));
      
      final bestMatch = weightedHistory.first;
      
      AppLogger.debug('🎯 智能推荐: $category -> ${bestMatch.record.value} (权重: ${bestMatch.weight.toStringAsFixed(2)})');
      
      return bestMatch.record.value;
      
    } catch (e) {
      AppLogger.error('获取智能推荐失败: $e');
      return null;
    }
  }
  
  /// 获取推荐列表（前N个）
  List<String> getSmartRecommendations(String category, {int limit = 5, Map<String, dynamic>? context}) {
    if (!_isInitialized || !_usageHistory.containsKey(category)) {
      return [];
    }
    
    final history = _usageHistory[category]!;
    if (history.isEmpty) return [];
    
    try {
      final weightedHistory = history.map((record) {
        double weight = _calculateWeight(record, context);
        return WeightedRecord(record: record, weight: weight);
      }).toList();
      
      weightedHistory.sort((a, b) => b.weight.compareTo(a.weight));
      
      final recommendations = weightedHistory
          .take(limit)
          .map((wr) => wr.record.value)
          .toList();
      
      AppLogger.debug('🎯 智能推荐列表: $category -> $recommendations');
      
      return recommendations;
      
    } catch (e) {
      AppLogger.error('获取推荐列表失败: $e');
      return [];
    }
  }
  
  /// 计算记录权重
  double _calculateWeight(UsageRecord record, Map<String, dynamic>? context) {
    double weight = 0.0;
    
    // 1. 频率权重 (40%)
    final frequencyWeight = record.frequency.toDouble() * 0.4;
    weight += frequencyWeight;
    
    // 2. 时间衰减权重 (30%)
    final daysSinceUsage = DateTime.now().difference(record.timestamp).inDays;
    final timeWeight = (1.0 / (1.0 + daysSinceUsage * 0.1)) * 0.3;
    weight += timeWeight;
    
    // 3. 上下文匹配权重 (30%)
    if (context != null && record.context.isNotEmpty) {
      double contextScore = _calculateContextSimilarity(record.context, context);
      weight += contextScore * 0.3;
    }
    
    return weight;
  }
  
  /// 计算上下文相似度
  double _calculateContextSimilarity(Map<String, dynamic> recordContext, Map<String, dynamic> currentContext) {
    if (recordContext.isEmpty || currentContext.isEmpty) return 0.0;
    
    int matchCount = 0;
    int totalKeys = currentContext.keys.length;
    
    for (final key in currentContext.keys) {
      if (recordContext.containsKey(key) && recordContext[key] == currentContext[key]) {
        matchCount++;
      }
    }
    
    return totalKeys > 0 ? matchCount / totalKeys : 0.0;
  }
  
  /// 清理过期数据
  Future<void> cleanupOldData() async {
    if (!_isInitialized) return;
    
    AppLogger.info('🧹 清理智能默认值过期数据...');
    
    try {
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      int removedCount = 0;
      
      for (final category in _usageHistory.keys.toList()) {
        final history = _usageHistory[category]!;
        final originalLength = history.length;
        
        history.removeWhere((record) => record.timestamp.isBefore(cutoffDate));
        
        removedCount += originalLength - history.length;
        
        // 如果历史记录为空，移除整个类别
        if (history.isEmpty) {
          _usageHistory.remove(category);
        }
      }
      
      if (removedCount > 0) {
        await _saveUsageHistory();
        AppLogger.info('✅ 清理完成，移除${removedCount}条过期记录');
      }
      
    } catch (e) {
      AppLogger.error('清理过期数据失败: $e');
    }
  }
  
  /// 获取使用统计
  Map<String, UsageStats> getUsageStats() {
    final stats = <String, UsageStats>{};
    
    for (final entry in _usageHistory.entries) {
      final category = entry.key;
      final history = entry.value;
      
      final totalUsages = history.fold(0, (sum, record) => sum + record.frequency);
      final uniqueValues = history.length;
      final lastUsed = history.isNotEmpty 
          ? history.map((r) => r.timestamp).reduce((a, b) => a.isAfter(b) ? a : b)
          : null;
      
      stats[category] = UsageStats(
        totalUsages: totalUsages,
        uniqueValues: uniqueValues,
        lastUsed: lastUsed,
        topValue: history.isNotEmpty 
            ? history.reduce((a, b) => a.frequency > b.frequency ? a : b).value
            : null,
      );
    }
    
    return stats;
  }
  
  /// 导出学习数据（用于备份）
  Future<Map<String, dynamic>> exportLearningData() async {
    if (!_isInitialized) await initialize();
    
    final data = <String, dynamic>{};
    
    for (final entry in _usageHistory.entries) {
      data[entry.key] = entry.value.map((record) => record.toJson()).toList();
    }
    
    return {
      'version': '1.0',
      'exportTime': DateTime.now().toIso8601String(),
      'data': data,
    };
  }
  
  /// 导入学习数据（用于恢复）
  Future<void> importLearningData(Map<String, dynamic> data) async {
    try {
      final learningData = data['data'] as Map<String, dynamic>;
      
      for (final entry in learningData.entries) {
        final category = entry.key;
        final records = (entry.value as List).map((json) => UsageRecord.fromJson(json)).toList();
        _usageHistory[category] = records;
      }
      
      await _saveUsageHistory();
      AppLogger.info('✅ 学习数据导入完成');
      
    } catch (e) {
      AppLogger.error('导入学习数据失败: $e');
      rethrow;
    }
  }
  
  /// 加载使用历史
  Future<void> _loadUsageHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_prefsKey);
      
      if (data != null) {
        final json = jsonDecode(data) as Map<String, dynamic>;
        
        for (final entry in json.entries) {
          final category = entry.key;
          final records = (entry.value as List)
              .map((item) => UsageRecord.fromJson(item))
              .toList();
          _usageHistory[category] = records;
        }
        
        AppLogger.info('📂 加载使用历史完成: ${_usageHistory.length}个类别');
      }
      
    } catch (e) {
      AppLogger.error('加载使用历史失败: $e');
      _usageHistory.clear();
    }
  }
  
  /// 保存使用历史
  Future<void> _saveUsageHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final json = <String, dynamic>{};
      
      for (final entry in _usageHistory.entries) {
        json[entry.key] = entry.value.map((record) => record.toJson()).toList();
      }
      
      await prefs.setString(_prefsKey, jsonEncode(json));
      
    } catch (e) {
      AppLogger.error('保存使用历史失败: $e');
    }
  }
  
  /// 重置所有学习数据
  Future<void> resetAllData() async {
    AppLogger.info('🗑️ 重置所有智能默认值数据...');
    
    try {
      _usageHistory.clear();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_prefsKey);
      
      AppLogger.info('✅ 所有学习数据已重置');
      
    } catch (e) {
      AppLogger.error('重置数据失败: $e');
    }
  }
  
  /// 销毁管理器
  void dispose() {
    _usageHistory.clear();
    _isInitialized = false;
    AppLogger.info('🧹 智能默认值管理器已销毁');
  }
}

/// 📊 使用记录
class UsageRecord {
  final String value;
  final DateTime timestamp;
  final Map<String, dynamic> context;
  final int frequency;
  
  const UsageRecord({
    required this.value,
    required this.timestamp,
    required this.context,
    required this.frequency,
  });
  
  UsageRecord copyWith({
    String? value,
    DateTime? timestamp,
    Map<String, dynamic>? context,
    int? frequency,
  }) {
    return UsageRecord(
      value: value ?? this.value,
      timestamp: timestamp ?? this.timestamp,
      context: context ?? this.context,
      frequency: frequency ?? this.frequency,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'context': context,
      'frequency': frequency,
    };
  }
  
  factory UsageRecord.fromJson(Map<String, dynamic> json) {
    return UsageRecord(
      value: json['value'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      context: json['context'] as Map<String, dynamic>,
      frequency: json['frequency'] as int,
    );
  }
}

/// ⚖️ 加权记录
class WeightedRecord {
  final UsageRecord record;
  final double weight;
  
  const WeightedRecord({
    required this.record,
    required this.weight,
  });
}

/// 📈 使用统计
class UsageStats {
  final int totalUsages;
  final int uniqueValues;
  final DateTime? lastUsed;
  final String? topValue;
  
  const UsageStats({
    required this.totalUsages,
    required this.uniqueValues,
    this.lastUsed,
    this.topValue,
  });
}

/// 🧠 智能表单字段组件
class SmartFormField extends StatefulWidget {
  final String category;
  final String label;
  final String? initialValue;
  final List<String>? options;
  final Function(String) onChanged;
  final Map<String, dynamic>? context;
  final bool showRecommendations;
  
  const SmartFormField({
    Key? key,
    required this.category,
    required this.label,
    this.initialValue,
    this.options,
    required this.onChanged,
    this.context,
    this.showRecommendations = true,
  }) : super(key: key);
  
  @override
  State<SmartFormField> createState() => _SmartFormFieldState();
}

class _SmartFormFieldState extends State<SmartFormField> {
  late TextEditingController _controller;
  List<String> _recommendations = [];
  
  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _loadSmartDefault();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  Future<void> _loadSmartDefault() async {
    final smartDefault = SmartDefaultsManager.instance.getSmartDefault(
      widget.category,
      context: widget.context,
    );
    
    final value = widget.initialValue ?? smartDefault ?? '';
    _controller.text = value;
    
    if (widget.showRecommendations) {
      _recommendations = SmartDefaultsManager.instance.getSmartRecommendations(
        widget.category,
        limit: 3,
        context: widget.context,
      );
      setState(() {});
    }
  }
  
  void _onValueChanged(String value) {
    widget.onChanged(value);
    
    // 记录用户行为
    if (value.isNotEmpty) {
      SmartDefaultsManager.instance.recordUsage(
        widget.category,
        value,
        context: widget.context,
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          decoration: InputDecoration(
            labelText: widget.label,
            suffixIcon: widget.options != null 
                ? PopupMenuButton<String>(
                    icon: const Icon(Icons.arrow_drop_down),
                    onSelected: (value) {
                      _controller.text = value;
                      _onValueChanged(value);
                    },
                    itemBuilder: (context) => widget.options!
                        .map((option) => PopupMenuItem(
                              value: option,
                              child: Text(option),
                            ))
                        .toList(),
                  )
                : null,
          ),
          onChanged: _onValueChanged,
        ),
        if (_recommendations.isNotEmpty && widget.showRecommendations)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Wrap(
              spacing: 8,
              children: _recommendations.map((recommendation) {
                return ActionChip(
                  label: Text(recommendation),
                  onPressed: () {
                    _controller.text = recommendation;
                    _onValueChanged(recommendation);
                  },
                );
              }).toList(),
            ),
          ),
      ],
    );
  }
}