import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'theme_colors.dart';

/// 🎨 Material 3 主题系统
/// 基于现有ThemeColors升级到Material 3设计规范
class Material3Theme {
  /// 🌙 深色主题 - 优化Material 3色彩对比度
  static ThemeData get darkTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: ThemeColors.primary,
      brightness: Brightness.dark,
      // 主色调 - 增强对比度
      primary: ThemeColors.primary,
      onPrimary: ThemeColors.textHighContrast,
      primaryContainer: ThemeColors.primaryLight,
      onPrimaryContainer: ThemeColors.textHighContrast,

      // 辅助色
      secondary: ThemeColors.secondary,
      onSecondary: ThemeColors.textOnDark,
      secondaryContainer: ThemeColors.secondary.withValues(alpha: 0.2),
      onSecondaryContainer: ThemeColors.textOnDark,

      // 第三色
      tertiary: ThemeColors.accent,
      onTertiary: ThemeColors.textOnDark,
      tertiaryContainer: ThemeColors.accent.withValues(alpha: 0.2),
      onTertiaryContainer: ThemeColors.textOnDark,

      // 表面色
      surface: ThemeColors.cardBackgroundColor,
      onSurface: ThemeColors.textDark,
      surfaceContainerHighest:
          ThemeColors.cardBackgroundColor.withValues(alpha: 0.8),
      // 保留一次onSurface参数，移除重复项

      // 错误色
      error: ThemeColors.error,
      onError: ThemeColors.textOnDark,
      errorContainer: ThemeColors.error.withValues(alpha: 0.2),
      onErrorContainer: ThemeColors.textOnDark,

      // 轮廓色
      outline: ThemeColors.border,
      outlineVariant: ThemeColors.border.withValues(alpha: 0.5),

      // 阴影色
      shadow: Colors.black.withValues(alpha: 0.3),
      scrim: Colors.black.withValues(alpha: 0.5),

      // 反色
      inverseSurface: ThemeColors.textDark,
      onInverseSurface: ThemeColors.cardBackgroundColor,
      inversePrimary: ThemeColors.accent,
    );

    return ThemeData(
      useMaterial3: true, // 启用Material 3
      brightness: Brightness.dark,

      // 色彩系统 - 基于现有ThemeColors
      colorScheme: colorScheme,

      // 应用栏主题
      appBarTheme: AppBarTheme(
        backgroundColor: ThemeColors.primary,
        foregroundColor: ThemeColors.textOnDark,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
      ),

      // 卡片主题
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 4,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        ),
      ),

      // 按钮主题 - Material 3现代化
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ThemeColors.primary,
          foregroundColor: ThemeColors.textHighContrast,
          elevation: 3,
          shadowColor: Colors.black.withValues(alpha: 0.15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48), // Material 3最小尺寸
        ),
      ),

      // Material 3新增 - FilledButton主题
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: ThemeColors.primary,
          foregroundColor: ThemeColors.textHighContrast,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48),
        ),
      ),

      // Material 3新增 - OutlinedButton主题
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: ThemeColors.primary,
          side: const BorderSide(color: ThemeColors.primary, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48),
        ),
      ),

      // Material 3新增 - TextButton主题
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ThemeColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          minimumSize: const Size(64, 40),
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white.withValues(alpha: 0.1),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          borderSide: const BorderSide(color: ThemeColors.accent, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.2)),
        ),
        labelStyle: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
        hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
      ),

      // 文本主题
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: ThemeColors.textOnDark,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: ThemeColors.textOnDark,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: ThemeColors.textOnDark,
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: ThemeColors.textOnDark,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: ThemeColors.textOnDark,
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeColors.textOnDark,
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: ThemeColors.textOnDark,
        ),
        titleMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: ThemeColors.textOnDark,
        ),
        titleSmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: ThemeColors.textOnDark,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: ThemeColors.textOnDark,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: ThemeColors.textOnDark,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          color: ThemeColors.textOnDark,
        ),
      ),

      // 图标主题
      iconTheme: const IconThemeData(
        color: ThemeColors.iconLight,
        size: 24,
      ),

      // 浮动操作按钮主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: ThemeColors.accent,
        foregroundColor: ThemeColors.textOnDark,
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusLarge),
        ),
      ),

      // 底部导航栏主题
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: ThemeColors.primary,
        selectedItemColor: ThemeColors.accent,
        unselectedItemColor: Colors.white.withValues(alpha: 0.6),
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // 对话框主题
      dialogTheme: DialogThemeData(
        backgroundColor: colorScheme.surface,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusLarge),
        ),
      ),

      // 分割线主题
      dividerTheme: DividerThemeData(
        color: Colors.white.withValues(alpha: 0.2),
        thickness: 1,
        space: 1,
      ),
    );
  }

  /// ☀️ 浅色主题 - 基于现有浅色背景色系
  static ThemeData get lightTheme {
    final colorScheme = ColorScheme.fromSeed(
      seedColor: ThemeColors.primary,
      brightness: Brightness.light,
      // 自定义色彩映射 - 更新为Material 3标准
      primary: ThemeColors.primary,
      secondary: ThemeColors.secondary,
      tertiary: ThemeColors.accent,
      surface: ThemeColors.cardBackgroundColor,
      error: ThemeColors.error,
      onPrimary: ThemeColors.textOnDark,
      onSecondary: ThemeColors.textOnDark,
      onTertiary: ThemeColors.textOnDark,
      onSurface: ThemeColors.textDark,
      onError: ThemeColors.textOnDark,
    );

    return ThemeData(
      useMaterial3: true, // 启用Material 3
      brightness: Brightness.light,

      // 色彩系统 - 基于现有ThemeColors
      colorScheme: colorScheme,

      // 应用栏主题
      appBarTheme: AppBarTheme(
        backgroundColor: ThemeColors.primary,
        foregroundColor: ThemeColors.textOnDark,
        elevation: 0,
        centerTitle: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
        ),
      ),

      // 卡片主题
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        elevation: 2,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
        ),
      ),

      // 按钮主题 - Material 3现代化
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ThemeColors.primary,
          foregroundColor: ThemeColors.textHighContrast,
          elevation: 3,
          shadowColor: Colors.black.withValues(alpha: 0.15),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48), // Material 3最小尺寸
        ),
      ),

      // Material 3新增 - FilledButton主题（浅色）
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: ThemeColors.primary,
          foregroundColor: ThemeColors.textHighContrast,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48),
        ),
      ),

      // Material 3新增 - OutlinedButton主题（浅色）
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: ThemeColors.primary,
          side: const BorderSide(color: ThemeColors.primary, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          minimumSize: const Size(88, 48),
        ),
      ),

      // Material 3新增 - TextButton主题（浅色）
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ThemeColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          minimumSize: const Size(64, 40),
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.grey[50],
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          borderSide: const BorderSide(color: ThemeColors.accent, width: 2),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusMedium),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        labelStyle: TextStyle(color: Colors.grey[700]),
        hintStyle: TextStyle(color: Colors.grey[500]),
      ),

      // 文本主题
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: ThemeColors.textDark,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: ThemeColors.textDark,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: ThemeColors.textDark,
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: ThemeColors.textDark,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: ThemeColors.textDark,
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: ThemeColors.textDark,
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: ThemeColors.textDark,
        ),
        titleMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: ThemeColors.textDark,
        ),
        titleSmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: ThemeColors.textDark,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: ThemeColors.textDark,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: ThemeColors.textDark,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          color: ThemeColors.textDark,
        ),
      ),

      // 图标主题
      iconTheme: const IconThemeData(
        color: ThemeColors.iconDark,
        size: 24,
      ),

      // 浮动操作按钮主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: ThemeColors.accent,
        foregroundColor: ThemeColors.textOnDark,
        elevation: 6,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusLarge),
        ),
      ),

      // 底部导航栏主题
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: ThemeColors.primary,
        unselectedItemColor: Colors.grey[600],
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // 对话框主题
      dialogTheme: DialogThemeData(
        backgroundColor: Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ThemeColors.modernRadiusLarge),
        ),
      ),

      // 分割线主题
      dividerTheme: DividerThemeData(
        color: Colors.grey[300],
        thickness: 1,
        space: 1,
      ),
    );
  }

  /// 🎯 获取当前主题（根据系统设置自动选择）
  static ThemeData getTheme(Brightness brightness) {
    return brightness == Brightness.dark ? darkTheme : lightTheme;
  }

  /// 🌈 获取动态主题（支持实时切换）
  static ThemeData getDynamicTheme({
    required Brightness brightness,
    Color? seedColor,
  }) {
    final baseTheme = getTheme(brightness);

    if (seedColor != null) {
      return baseTheme.copyWith(
        colorScheme: ColorScheme.fromSeed(
          seedColor: seedColor,
          brightness: brightness,
        ),
      );
    }

    return baseTheme;
  }
}
