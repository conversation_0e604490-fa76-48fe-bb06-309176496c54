/// 🎯 任务核心服务
/// 
/// 专门负责任务的核心业务逻辑，包括：
/// - 任务初始化和生命周期管理
/// - Repository集成和数据监听
/// - 批量更新机制
/// - 资源管理
library task_core_service;

import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 任务核心服务
class TaskCoreService {
  static const String _tag = 'TaskCoreService';
  
  final TaskRepository _repository;
  
  TaskModel? _currentTask;
  List<TaskModel> _tasks = [];
  
  // 🚀 批量更新机制
  Timer? _updateTimer;
  bool _pendingUpdate = false;
  static const Duration _batchUpdateDelay = Duration(milliseconds: 100);
  
  // 🚀 Repository监听器
  StreamSubscription<List<TaskModel>>? _tasksSubscription;
  StreamSubscription<TaskModel?>? _currentTaskSubscription;
  
  // Getters
  TaskModel? get currentTask => _currentTask;
  List<TaskModel> get tasks => _tasks;
  
  /// 构造函数 - 注入Repository依赖
  TaskCoreService({TaskRepository? repository})
      : _repository = repository ?? TaskRepositoryImpl();
  
  /// 初始化服务
  Future<void> initialize() async {
    try {
      AppLogger.info('🚀 初始化任务核心服务...', tag: _tag);
      
      // 初始化Repository
      await _repository.initialize();
      
      // 设置Repository数据监听
      _setupRepositoryListeners();
      
      // 加载初始数据
      await _loadTasks();
      
      AppLogger.info('✅ 任务核心服务初始化完成', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 任务核心服务初始化失败: $e', tag: _tag);
      rethrow;
    }
  }
  
  /// 设置Repository数据监听
  void _setupRepositoryListeners() {
    // 监听任务列表变化
    if (_repository is TaskRepositoryImpl) {
      _tasksSubscription = _repository.watchTasks().listen((tasks) {
        _tasks = tasks;
        _scheduleUpdate();
      });
      
      _currentTaskSubscription = _repository.watchCurrentTask().listen((task) {
        _currentTask = task;
        _scheduleUpdate();
      });
    }
  }
  
  /// 🚀 批量更新机制
  void _scheduleUpdate() {
    if (_pendingUpdate) return;
    
    _pendingUpdate = true;
    _updateTimer?.cancel();
    _updateTimer = Timer(_batchUpdateDelay, () {
      _pendingUpdate = false;
      AppLogger.debug('📝 TaskCoreService批量更新完成', tag: _tag);
    });
  }
  
  /// 加载任务数据
  Future<void> _loadTasks() async {
    try {
      // 从Repository加载任务
      _tasks = await _repository.getAllTasks();
      
      // 加载当前任务
      _currentTask = await _repository.getCurrentTask();
      
      // 🚀 使用批量更新机制
      _scheduleUpdate();
      
      AppLogger.info('📚 任务加载完成: ${_tasks.length}个任务', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 任务加载失败: $e', tag: _tag);
      _tasks = [];
      _currentTask = null;
      _scheduleUpdate();
    }
  }
  
  /// 刷新任务数据
  Future<void> refreshData() async {
    try {
      await _loadTasks();
      AppLogger.info('✅ 任务数据刷新完成，共${_tasks.length}个任务', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 刷新任务数据失败: $e', tag: _tag);
      rethrow;
    }
  }
  
  /// 🔧 强制保存所有数据
  Future<void> forceSaveAllData() async {
    try {
      // 使用Repository批量保存所有任务
      await _repository.saveTasks(_tasks);
      
      // 保存当前任务状态
      await _repository.setCurrentTask(_currentTask);
      
      AppLogger.info('✅ 强制保存所有数据完成', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 强制保存数据失败: $e', tag: _tag);
      rethrow;
    }
  }
  
  /// 保存任务（新增或更新）
  Future<void> saveTask(TaskModel task) async {
    try {
      // 使用Repository保存任务
      await _repository.saveTask(task);
      
      // 更新本地缓存
      final existingIndex = _tasks.indexWhere((t) => t.id == task.id);
      if (existingIndex >= 0) {
        _tasks[existingIndex] = task;
        AppLogger.info('📝 更新任务: ${task.id}', tag: _tag);
      } else {
        _tasks.insert(0, task); // 新任务插入到开头
        AppLogger.info('➕ 添加新任务: ${task.id}', tag: _tag);
      }
      
      // 通知监听器
      _scheduleUpdate();
    } catch (e) {
      AppLogger.error('❌ 保存任务失败: ${task.id} - $e', tag: _tag);
      rethrow;
    }
  }
  
  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      // 使用Repository删除任务
      await _repository.deleteTask(taskId);
      
      // 更新本地缓存
      _tasks.removeWhere((task) => task.id == taskId);
      
      // 如果删除的是当前任务，清除当前任务
      if (_currentTask?.id == taskId) {
        _currentTask = null;
      }
      
      // 🚀 使用批量更新机制
      _scheduleUpdate();
      
      AppLogger.info('🗑️ 删除任务成功: $taskId', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $taskId - $e', tag: _tag);
      rethrow;
    }
  }
  
  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task) async {
    try {
      // 使用Repository设置当前任务
      await _repository.setCurrentTask(task);
      
      // 更新本地缓存
      _currentTask = task;
      
      // 🚀 使用批量更新机制
      _scheduleUpdate();
      
      AppLogger.info('📌 当前任务已设置: ${task?.id ?? 'null'}', tag: _tag);
    } catch (e) {
      AppLogger.error('❌ 设置当前任务失败: $e', tag: _tag);
      rethrow;
    }
  }
  
  /// 更新任务
  Future<void> updateTask(TaskModel task) async {
    // 查找并更新任务列表中的任务
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      _tasks[index] = task;
    }
    
    // 如果是当前任务，也更新当前任务引用
    if (_currentTask?.id == task.id) {
      _currentTask = task;
    }
    
    // 保存到本地存储
    await _repository.saveTask(task);
    
    // 异步通知监听器
    _scheduleUpdate();
  }
  
  /// 清除当前任务
  void clearCurrentTask() {
    _currentTask = null;
    _scheduleUpdate();
  }
  
  /// 获取任务通过ID
  TaskModel? getTaskById(String taskId) {
    try {
      return _tasks.firstWhere((task) => task.id == taskId);
    } catch (e) {
      return null;
    }
  }
  
  /// 设置任务列表
  void setTasks(List<TaskModel> tasks) {
    _tasks = tasks;
    _scheduleUpdate();
  }
  
  /// 清理资源
  void dispose() {
    // 🚀 清理批量更新Timer
    _updateTimer?.cancel();
    
    // 🚀 清理Repository监听器
    _tasksSubscription?.cancel();
    _currentTaskSubscription?.cancel();
    
    // 🚀 清理Repository资源
    if (_repository is TaskRepositoryImpl) {
      _repository.dispose();
    }
    
    AppLogger.info('🧹 TaskCoreService资源已清理', tag: _tag);
  }
}
