/// 📸 【照片管理服务】
///
/// 【核心职责】专门负责任务照片的全生命周期管理
/// 【设计目标】从TaskService中提取照片相关逻辑，减少服务复杂度
/// 【架构优势】单一职责，高内聚低耦合
///
/// 🔧 【主要功能】：
/// - 📷 照片路径更新 - 管理照片文件的存储路径
/// - 🔍 识别结果管理 - 处理多引擎识别结果的存储和更新
/// - 📊 识别状态控制 - 跟踪和管理照片的识别状态
/// - 🗂️ 照片元数据管理 - 维护照片的创建时间、大小等信息
///
/// 🎯 【适用场景】：
/// - 工业标签照片的拍摄和管理
/// - 多引擎识别结果的统一管理
/// - 照片质量评估和优化建议
library photo_management_service;

import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 照片管理服务
class PhotoManagementService {
  
  /// 更新照片路径
  Future<void> updatePhotoPath(
    TaskModel task,
    String photoId, 
    String imagePath,
  ) async {
    try {
      AppLogger.info('📸 更新照片路径: $photoId -> $imagePath', tag: 'PhotoMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      // 更新照片路径
      task.photos[photoIndex] = task.photos[photoIndex].copyWith(
        imagePath: imagePath,
      );
      
      AppLogger.info('✅ 照片路径更新成功', tag: 'PhotoMgmt');
    } catch (e) {
      AppLogger.error('❌ 更新照片路径失败: $e', tag: 'PhotoMgmt');
      rethrow;
    }
  }

  /// 根据标签更新照片路径
  Future<void> updatePhotoPathByLabel(
    TaskModel task,
    String photoLabel,
    String newPath,
  ) async {
    try {
      AppLogger.info('📸 根据标签更新照片: $photoLabel -> $newPath', tag: 'PhotoMgmt');
      
      final photo = task.photos.firstWhere(
        (p) => p.label == photoLabel,
        orElse: () => throw Exception('照片不存在: $photoLabel'),
      );
      
      await updatePhotoPath(task, photo.id, newPath);
    } catch (e) {
      AppLogger.error('❌ 根据标签更新照片失败: $e', tag: 'PhotoMgmt');
      rethrow;
    }
  }

  /// 更新识别结果
  Future<void> updateRecognitionResult(
    TaskModel task,
    String photoId,
    RecognitionResult result,
  ) async {
    try {
      AppLogger.info('🔍 更新识别结果: $photoId', tag: 'PhotoMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      // 更新识别结果
      task.photos[photoIndex] = task.photos[photoIndex].copyWith(
        recognitionResult: result,
        recognitionStatus: RecognitionStatus.completed,
      );
      
      AppLogger.info('✅ 识别结果更新成功', tag: 'PhotoMgmt');
    } catch (e) {
      AppLogger.error('❌ 更新识别结果失败: $e', tag: 'PhotoMgmt');
      rethrow;
    }
  }

  /// 开始照片识别
  Future<void> startRecognition(TaskModel task, String photoId) async {
    try {
      AppLogger.info('🔍 开始照片识别: $photoId', tag: 'PhotoMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      // 更新识别状态
      task.photos[photoIndex] = task.photos[photoIndex].copyWith(
        recognitionStatus: RecognitionStatus.processing,
      );
      
      AppLogger.info('✅ 照片识别已开始', tag: 'PhotoMgmt');
    } catch (e) {
      AppLogger.error('❌ 开始照片识别失败: $e', tag: 'PhotoMgmt');
      rethrow;
    }
  }

  /// 取消照片识别
  Future<void> cancelRecognition(TaskModel task, String photoId) async {
    try {
      AppLogger.info('❌ 取消照片识别: $photoId', tag: 'PhotoMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      // 重置识别状态
      task.photos[photoIndex] = task.photos[photoIndex].copyWith(
        recognitionStatus: RecognitionStatus.pending,
      );
      
      AppLogger.info('✅ 照片识别已取消', tag: 'PhotoMgmt');
    } catch (e) {
      AppLogger.error('❌ 取消照片识别失败: $e', tag: 'PhotoMgmt');
      rethrow;
    }
  }

  /// 重置照片识别状态
  Future<void> resetPhotoRecognition(TaskModel task, String photoId) async {
    try {
      AppLogger.info('🔄 重置照片识别: $photoId', tag: 'PhotoMgmt');
      
      final photoIndex = task.photos.indexWhere((p) => p.id == photoId);
      if (photoIndex == -1) {
        throw Exception('照片不存在: $photoId');
      }

      // 重置识别状态和结果
      task.photos[photoIndex] = task.photos[photoIndex].copyWith(
        recognitionStatus: RecognitionStatus.pending,
        recognitionResult: null,
      );
      
      AppLogger.info('✅ 照片识别状态已重置', tag: 'PhotoMgmt');
    } catch (e) {
      AppLogger.error('❌ 重置照片识别失败: $e', tag: 'PhotoMgmt');
      rethrow;
    }
  }

  /// 获取照片识别进度
  Map<String, dynamic> getRecognitionProgress(TaskModel task) {
    try {
      final totalPhotos = task.photos.length;
      final completedPhotos = task.photos
          .where((p) => p.recognitionStatus == RecognitionStatus.completed)
          .length;
      final processingPhotos = task.photos
          .where((p) => p.recognitionStatus == RecognitionStatus.processing)
          .length;
      
      return {
        'total': totalPhotos,
        'completed': completedPhotos,
        'processing': processingPhotos,
        'pending': totalPhotos - completedPhotos - processingPhotos,
        'progress': totalPhotos > 0 ? (completedPhotos / totalPhotos * 100) : 0.0,
      };
    } catch (e) {
      AppLogger.error('❌ 获取识别进度失败: $e', tag: 'PhotoMgmt');
      return {
        'total': 0,
        'completed': 0,
        'processing': 0,
        'pending': 0,
        'progress': 0.0,
      };
    }
  }

  /// 验证照片完整性
  bool validatePhotosCompleteness(TaskModel task) {
    try {
      // 检查必需照片是否已上传
      final requiredPhotos = task.photos.where((p) => p.isRequired).toList();
      final missingPhotos = requiredPhotos
          .where((p) => p.imagePath == null || p.imagePath!.isEmpty)
          .toList();
      
      if (missingPhotos.isNotEmpty) {
        AppLogger.warning('⚠️ 还有${missingPhotos.length}张必需照片未上传', tag: 'PhotoMgmt');
        return false;
      }
      
      // 检查需要识别的照片是否已识别
      final recognitionPhotos = task.photos.where((p) => p.needRecognition).toList();
      final unrecognizedPhotos = recognitionPhotos
          .where((p) => p.recognitionResult == null)
          .toList();
      
      if (unrecognizedPhotos.isNotEmpty) {
        AppLogger.warning('⚠️ 还有${unrecognizedPhotos.length}张照片未完成识别', tag: 'PhotoMgmt');
        return false;
      }
      
      AppLogger.info('✅ 照片完整性验证通过', tag: 'PhotoMgmt');
      return true;
    } catch (e) {
      AppLogger.error('❌ 验证照片完整性失败: $e', tag: 'PhotoMgmt');
      return false;
    }
  }

  /// 获取照片统计信息
  Map<String, dynamic> getPhotoStatistics(TaskModel task) {
    try {
      final totalPhotos = task.photos.length;
      final uploadedPhotos = task.photos
          .where((p) => p.imagePath != null && p.imagePath!.isNotEmpty)
          .length;
      final recognizedPhotos = task.photos
          .where((p) => p.recognitionResult != null)
          .length;
      
      return {
        'total': totalPhotos,
        'uploaded': uploadedPhotos,
        'recognized': recognizedPhotos,
        'uploadProgress': totalPhotos > 0 ? (uploadedPhotos / totalPhotos * 100) : 0.0,
        'recognitionProgress': totalPhotos > 0 ? (recognizedPhotos / totalPhotos * 100) : 0.0,
      };
    } catch (e) {
      AppLogger.error('❌ 获取照片统计失败: $e', tag: 'PhotoMgmt');
      return {
        'total': 0,
        'uploaded': 0,
        'recognized': 0,
        'uploadProgress': 0.0,
        'recognitionProgress': 0.0,
      };
    }
  }
}
