import 'package:loadguard/models/config_models.dart';

/// 配置数据仓库接口
/// 管理人员、仓库、模板等配置数据的CRUD操作
abstract class ConfigRepository {
  // ==================== 工作人员配置 ====================
  
  /// 获取所有工作人员配置
  Future<List<WorkerConfig>> getAllWorkers();
  
  /// 根据ID获取工作人员配置
  Future<WorkerConfig?> getWorkerById(String id);
  
  /// 保存工作人员配置
  Future<void> saveWorker(WorkerConfig worker);
  
  /// 批量保存工作人员配置
  Future<void> saveWorkers(List<WorkerConfig> workers);
  
  /// 删除工作人员配置
  Future<void> deleteWorker(String id);
  
  /// 根据条件查询工作人员
  Future<List<WorkerConfig>> queryWorkers({
    String? warehouse,
    String? group,
    String? role,
    bool? isActive,
  });

  // ==================== 仓库配置 ====================
  
  /// 获取所有仓库配置
  Future<List<WarehouseConfig>> getAllWarehouses();
  
  /// 根据ID获取仓库配置
  Future<WarehouseConfig?> getWarehouseById(String id);
  
  /// 保存仓库配置
  Future<void> saveWarehouse(WarehouseConfig warehouse);
  
  /// 删除仓库配置
  Future<void> deleteWarehouse(String id);

  // ==================== 工作组配置 ====================
  
  /// 获取所有工作组配置
  Future<List<GroupConfig>> getAllGroups();
  
  /// 根据ID获取工作组配置
  Future<GroupConfig?> getGroupById(String id);
  
  /// 保存工作组配置
  Future<void> saveGroup(GroupConfig group);
  
  /// 删除工作组配置
  Future<void> deleteGroup(String id);
  
  /// 根据仓库ID获取工作组
  Future<List<GroupConfig>> getGroupsByWarehouse(String warehouseId);

  // ==================== 模板配置 ====================

  /// 获取所有模板配置
  Future<List<TemplateConfigModel>> getAllTemplates();

  /// 根据ID获取模板配置
  Future<TemplateConfigModel?> getTemplateById(String id);

  /// 保存模板配置
  Future<void> saveTemplate(TemplateConfigModel template);

  /// 删除模板配置
  Future<void> deleteTemplate(String id);

  // ==================== 照片配置 ====================

  /// 获取模板的照片配置
  Future<List<PhotoConfigModel>> getPhotoConfigsByTemplate(String templateId);

  /// 获取模板的照片组配置
  Future<List<PhotoGroupModel>> getPhotoGroupsByTemplate(String templateId);

  /// 保存照片配置
  Future<void> savePhotoConfig(PhotoConfigModel photoConfig);

  /// 保存照片组配置
  Future<void> savePhotoGroup(PhotoGroupModel photoGroup);

  // ==================== 角色配置 ====================

  /// 获取所有角色配置
  Future<List<RoleConfig>> getAllRoles();

  /// 根据ID获取角色配置
  Future<RoleConfig?> getRoleById(String id);

  /// 保存角色配置
  Future<void> saveRole(RoleConfig role);

  /// 删除角色配置
  Future<void> deleteRole(String id);

  // ==================== 系统配置 ====================

  /// 获取所有系统配置
  Future<List<SystemConfig>> getAllSystemConfigs();

  /// 根据键获取系统配置
  Future<SystemConfig?> getSystemConfigByKey(String key);

  /// 保存系统配置
  Future<void> saveSystemConfig(SystemConfig config);

  /// 根据分类获取系统配置
  Future<List<SystemConfig>> getSystemConfigsByCategory(String category);

  // ==================== 数据迁移和备份 ====================
  
  /// 从硬编码数据迁移
  Future<void> migrateFromLegacyData();
  
  /// 导出配置数据
  Future<Map<String, dynamic>> exportConfigs({
    bool includeWorkers = true,
    bool includeWarehouses = true,
    bool includeGroups = true,
    bool includeTemplates = true,
    bool includeRoles = true,
    bool includeSystemConfigs = true,
  });

  /// 导入配置数据
  Future<void> importConfigs(Map<String, dynamic> configExport, {
    bool overwriteExisting = false,
  });
  
  /// 备份配置数据
  Future<void> backupConfigs();
  
  /// 恢复配置数据
  Future<void> restoreConfigs(String backupId);

  // ==================== 版本管理 ====================
  
  /// 保存配置版本
  Future<void> saveConfigVersion(Map<String, dynamic> version);

  /// 获取配置版本历史
  Future<List<Map<String, dynamic>>> getConfigVersionHistory(String configType);

  /// 回滚到指定版本
  Future<void> rollbackToVersion(String configType, int version);

  // ==================== 统计和监控 ====================

  /// 获取配置统计信息
  Future<Map<String, dynamic>> getConfigStatistics();
  
  /// 验证配置数据完整性
  Future<Map<String, dynamic>> validateConfigIntegrity();
  
  /// 清理无效配置
  Future<void> cleanupInvalidConfigs();

  // ==================== 初始化和关闭 ====================
  
  /// 初始化配置仓库
  Future<void> initialize();
  
  /// 关闭配置仓库
  Future<void> close();
}

/// 配置数据源接口
/// 定义底层配置存储的抽象
abstract class ConfigDataSource {
  /// 初始化数据源
  Future<void> initialize();
  
  /// 保存配置数据
  Future<void> saveConfig(String key, Map<String, dynamic> data);
  
  /// 获取配置数据
  Future<Map<String, dynamic>?> getConfig(String key);
  
  /// 删除配置数据
  Future<void> deleteConfig(String key);
  
  /// 获取所有配置键
  Future<List<String>> getAllConfigKeys();
  
  /// 批量保存配置
  Future<void> saveConfigs(Map<String, Map<String, dynamic>> configs);
  
  /// 清空所有配置
  Future<void> clearAllConfigs();
  
  /// 检查数据源是否可用
  Future<bool> isAvailable();
  
  /// 关闭数据源
  Future<void> close();
}

/// 配置Repository异常类
class ConfigRepositoryException implements Exception {
  final String message;
  final dynamic originalError;
  
  const ConfigRepositoryException(this.message, {this.originalError});
  
  @override
  String toString() => 'ConfigRepositoryException: $message';
}

/// 配置数据迁移异常
class ConfigMigrationException extends ConfigRepositoryException {
  const ConfigMigrationException(super.message, {super.originalError});
}

/// 配置数据验证异常
class ConfigValidationException extends ConfigRepositoryException {
  const ConfigValidationException(super.message, {super.originalError});
}
