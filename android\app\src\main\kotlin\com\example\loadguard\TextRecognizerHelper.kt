package com.example.loadguard

import android.content.Context
import androidx.core.content.ContextCompat
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import com.google.mlkit.vision.text.Text
import kotlinx.coroutines.*
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import java.io.File
import java.io.IOException

/**
 * 🚀 ML Kit Text Recognition v2 官方实现
 * 支持本地模型，无需网络连接
 * 专为工业场景优化
 */
class TextRecognizerHelper(
    private val context: Context,
    private val modelPath: String = ""
) {
    private var mlKitRecognizer: com.google.mlkit.vision.text.TextRecognizer? = null
    private var isInitialized = false
    
    companion object {
        private const val TAG = "TextRecognizerHelper"
        private const val RECOGNITION_TIMEOUT = 30000L // 30 seconds
    }

    init {
        setupTextRecognizer()
    }

    /**
     * 🔧 设置ML Kit Text Recognition v2识别器 - 优化中文识别
     */
    private fun setupTextRecognizer() {
        try {
            Log.i(TAG, "🚀 初始化ML Kit Text Recognition v2 (通用识别)...")
            // 🔧 修复：使用0.15.0兼容的API
            mlKitRecognizer = TextRecognition.getClient(
                TextRecognizerOptions.DEFAULT_OPTIONS
            )
            isInitialized = true
            Log.i(TAG, "✅ ML Kit通用文本识别器初始化成功")
        } catch (e: Exception) {
            Log.e(TAG, "❌ ML Kit初始化失败: ${e.message}", e)
            isInitialized = false
        }
    }

    /**
     * 🎯 执行文本识别 - ML Kit v2官方API
     */
    suspend fun recognizeText(imagePath: String): Map<String, Any> = withContext(Dispatchers.IO) {
        Log.d(TAG, "🔍 开始识别图片: $imagePath")
        
        if (!isInitialized || mlKitRecognizer == null) {
            throw IllegalStateException("ML Kit Text Recognition v2未初始化")
        }

        // 验证文件存在
        val imageFile = File(imagePath)
        if (!imageFile.exists()) {
            throw IOException("图片文件不存在: $imagePath")
        }

        try {
            // 创建输入图像
            val inputImage = InputImage.fromFilePath(context, android.net.Uri.fromFile(imageFile))
            Log.d(TAG, "✅ InputImage创建成功")

            // 执行识别 - 使用suspendCancellableCoroutine处理异步回调
            val recognizedText = suspendCancellableCoroutine<Text> { continuation ->
                mlKitRecognizer!!.process(inputImage)
                    .addOnSuccessListener { visionText ->
                        Log.d(TAG, "✅ ML Kit识别成功")
                        continuation.resume(visionText)
                    }
                    .addOnFailureListener { exception ->
                        Log.e(TAG, "❌ ML Kit识别失败: ${exception.message}", exception)
                        continuation.resumeWithException(exception)
                    }
            }

            // 处理识别结果
            val resultText = recognizedText.text
            Log.i(TAG, "📊 识别结果: \"$resultText\" | 文本块数量: ${recognizedText.textBlocks.size}")
            
            // 构建结果映射
            val resultMap = mutableMapOf<String, Any>()
            resultMap["text"] = resultText
            resultMap["processingTime"] = System.currentTimeMillis()
            resultMap["engineUsed"] = "ML Kit Text Recognition v2"
            
            // 处理文本块
            val blocks = mutableListOf<Map<String, Any>>()
            for (block in recognizedText.textBlocks) {
                val blockMap = mutableMapOf<String, Any>()
                blockMap["text"] = block.text
                blockMap["confidence"] = 0.9 // ML Kit不提供置信度，使用默认值
                
                // 处理边界框
                val boundingBox = block.boundingBox
                if (boundingBox != null) {
                    blockMap["boundingBox"] = mapOf(
                        "left" to boundingBox.left,
                        "top" to boundingBox.top,
                        "right" to boundingBox.right,
                        "bottom" to boundingBox.bottom
                    )
                }
                
                blocks.add(blockMap)
            }
            resultMap["blocks"] = blocks
            
            resultMap
        } catch (e: Exception) {
            Log.e(TAG, "❌ 识别过程出错: ${e.message}", e)
            throw e
        }
    }

    /**
     * 🔧 释放资源
     */
    fun dispose() {
        try {
            mlKitRecognizer?.close()
            mlKitRecognizer = null
            isInitialized = false
            Log.i(TAG, "✅ ML Kit资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 释放ML Kit资源时出错: ${e.message}", e)
        }
    }

    /**
     * 📊 获取服务信息
     */
    fun getServiceInfo(): Map<String, Any> {
        return mapOf(
            "isInitialized" to isInitialized,
            "engineVersion" to "ML Kit Text Recognition v2",
            "modelType" to "通用识别模型",
            "supportsOffline" to true,
            "timestamp" to System.currentTimeMillis()
        )
    }

    /**
     * ✅ 检查服务是否就绪
     */
    fun isReady(): Boolean {
        return isInitialized && mlKitRecognizer != null
    }

    /**
     * 🔧 关闭服务（别名方法，兼容性）
     */
    fun close() {
        dispose()
    }
}