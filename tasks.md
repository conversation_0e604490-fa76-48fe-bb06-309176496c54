# Flutter项目优化实施计划

## 任务概述

基于需求分析和设计方案，将优化工作分解为具体的编码任务。每个任务都经过精心设计，确保渐进式实施、及时编译测试，并保持现有功能的完整性。特别强调保留ML Kit V2 0.15.0核心识别能力，对12种算法进行智能优化。

## 实施任务

### 阶段1：基础架构优化和数据一致性修复

- [ ] 1. 创建Repository层基础架构
  - 实现TaskRepository接口和基础实现类
  - 创建HiveDataSource和SharedPreferencesDataSource
  - 建立统一的数据访问模式，解决当前TaskService中的数据同步问题
  - 编写单元测试验证Repository功能
  - _需求: 1.1, 2.1, 2.2_

- [x] 1.1 实现TaskRepository接口


  - 定义TaskRepository抽象接口，包含所有任务数据操作方法
  - 创建TaskRepositoryImpl具体实现类
  - 实现单一数据源缓存机制，确保_currentTask和_tasks列表的一致性
  - _需求: 1.1, 2.1_



- [ ] 1.2 创建数据源抽象层
  - 实现HiveDataSource作为主要存储
  - 实现SharedPreferencesDataSource作为备份存储
  - 建立异步备份机制，避免双重写入的性能问题


  - _需求: 1.1, 2.2_

- [ ] 1.3 集成Repository到现有TaskService
  - 重构TaskService使用Repository进行数据访问
  - 移除TaskService中的直接存储操作代码
  - 保持现有API接口不变，确保UI层无需修改
  - 编译测试确保功能正常
  - _需求: 1.1, 2.1_

- [ ] 2. 统一状态管理迁移
  - 创建TaskNotifier替换TaskService的ChangeNotifier部分
  - 实现Riverpod状态管理，解决状态更新混乱问题
  - 保持向后兼容性，确保现有UI组件正常工作
  - _需求: 1.1, 2.2_

- [ ] 2.1 创建TaskNotifier状态管理
  - 使用@riverpod注解创建TaskNotifier类
  - 实现AsyncValue状态管理，提供loading、error、data状态
  - 实现乐观更新机制，提升用户体验
  - _需求: 2.2_

- [ ] 2.2 重构TaskService为纯业务逻辑
  - 移除TaskService中的ChangeNotifier继承
  - 将状态管理职责转移到TaskNotifier
  - 保留业务逻辑方法，通过Repository进行数据操作
  - _需求: 1.1, 2.2_

- [ ] 2.3 更新UI组件使用新状态管理
  - 逐步更新页面组件使用ref.watch(taskNotifierProvider)
  - 保持现有UI交互逻辑不变
  - 编译测试每个更新的页面
  - _需求: 2.2_

### 阶段2：工作量统计系统重构

- [ ] 3. 修复工作量统计数据显示问题
  - 重构WorkloadAssignmentService，解决数据获取失败问题
  - 实现WorkloadCalculationService统一计算逻辑
  - 创建WorkloadNotifier状态管理
  - 确保工作量统计页面能正确显示数据和个人明细
  - _需求: 1.1, 1.2, 1.3_

- [ ] 3.1 创建工作量数据模型
  - 定义WorkloadStatistics和WorkloadDetail数据模型
  - 使用@freezed注解确保数据不可变性和类型安全
  - 实现JSON序列化支持数据持久化
  - _需求: 1.1, 1.2_

- [ ] 3.2 实现WorkloadCalculationService
  - 创建统一的工作量计算逻辑
  - 实现calculateWorkerStatistics方法，确保数据准确性
  - 处理任务分配和工作量分配的关联计算
  - 编写单元测试验证计算逻辑正确性
  - _需求: 1.1, 1.2, 1.3_

- [ ] 3.3 创建WorkloadRepository
  - 实现工作量数据的存储和检索
  - 建立与TaskRepository的数据关联
  - 确保工作量数据与任务数据的一致性
  - _需求: 1.1, 1.3_

- [ ] 3.4 实现WorkloadNotifier状态管理
  - 创建工作量统计的Riverpod状态管理
  - 实现loadWorkloadStatistics和getWorkerStatistics方法
  - 提供实时数据更新机制
  - _需求: 1.1, 1.2_

- [ ] 3.5 修复工作量统计页面显示
  - 更新工作量统计页面使用新的状态管理
  - 实现个人工作量明细显示功能
  - 确保数据准确性和实时更新
  - 编译测试页面功能完整性
  - _需求: 1.1, 1.2, 1.3_

### 阶段3：硬编码数据动态化

- [ ] 4. 实现配置数据动态管理
  - 创建ConfigRepository管理人员、仓库、模板配置
  - 实现数据迁移，将硬编码的88名工作人员信息迁移到Hive存储
  - 建立向后兼容机制，确保现有页面调用逻辑不受影响
  - _需求: 5.1, 5.2, 5.3_

- [ ] 4.1 创建配置数据模型
  - 定义WorkerConfig、WarehouseConfig、GroupConfig、TemplateConfig模型
  - 使用@freezed确保类型安全和不可变性
  - 实现JSON序列化支持配置导入导出
  - _需求: 5.1, 5.2_

- [ ] 4.2 实现ConfigRepository
  - 创建配置数据的Repository接口和实现
  - 使用Hive存储配置数据，支持CRUD操作
  - 实现配置数据的导入导出功能
  - _需求: 5.1, 5.2_

- [ ] 4.3 实现数据迁移服务
  - 创建ConfigMigrationService处理硬编码数据迁移
  - 将worker_info_data.dart中的88名工作人员数据迁移到Hive
  - 将template_config.dart中的模板配置迁移到动态存储
  - 实现版本控制，支持未来的数据迁移
  - _需求: 5.1, 5.2, 5.3_

- [ ] 4.4 创建向后兼容适配器
  - 实现WorkerInfoAdapter保持现有API兼容性
  - 实现TemplateConfigAdapter支持现有模板调用
  - 确保现有页面的人员选择和模板使用功能不受影响
  - _需求: 5.2, 5.3_

- [ ] 4.5 实现ConfigNotifier状态管理
  - 创建配置数据的Riverpod状态管理
  - 实现配置数据的加载、更新、删除操作
  - 提供实时配置更新通知
  - _需求: 5.2_

- [ ] 4.6 创建配置管理UI
  - 实现ConfigManagementPage配置管理界面
  - 创建人员管理、仓库管理、模板管理标签页
  - 支持配置数据的增删改查操作
  - 编译测试配置管理功能
  - _需求: 5.2, 5.3_

### 阶段4：本地应用API预留和业务流程优化

- [ ] 4.7 预留API抽象层和服务器对接接口
  - 创建TaskApiClient、WorkloadApiClient、ConfigApiClient接口
  - 实现本地优先的HybridRepository模式
  - 建立数据同步管理器SyncManager
  - 预留设备间协作和实时通信接口
  - _需求: 本地应用扩展性_

- [ ] 4.8 优化业务流程（参考同类应用最佳实践）
  - 实现增强的任务状态机TaskLifecycleManager
  - 创建智能任务分配算法IntelligentTaskAssignmentService
  - 建立质量控制流程QualityControlService
  - 参考工业级WMS系统优化工作流程
  - _需求: 业务流程优化_

### 阶段5：ML Kit V2识别算法优化

- [ ] 5. 保留并优化ML Kit V2 0.15.0核心识别
  - 保持Google ML Kit Text Recognition V2 0.15.0的核心识别能力不变
  - 对现有12种识别算法进行评估和优化
  - 实现智能调用策略，根据图像质量选择最优算法
  - 确保识别准确率和处理速度不降低
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [ ] 5.1 评估现有12种识别算法
  - 分析ML Kit核心OCR识别算法的性能表现
  - 评估字符标准化、批号提取、产品代码匹配等算法效果
  - 测试逐行匹配、全文匹配、模糊匹配三种策略的准确率
  - 分析智能后处理、多批次匹配等算法的实际价值
  - 制定算法保留、优化、简化的具体方案
  - _需求: 6.1, 6.2, 6.4_

- [ ] 5.2 实现图像质量评估器
  - 创建ImageQualityAssessor评估图像亮度、对比度、清晰度
  - 实现图像质量分级（高质量、中等质量、低质量）
  - 为不同质量等级定义最优识别策略
  - 编写单元测试验证质量评估准确性
  - _需求: 6.2, 6.3_

- [ ] 5.3 创建智能策略选择器
  - 实现IntelligentRecognitionStrategy策略选择逻辑
  - 根据图像质量智能选择precise、balanced、tolerant策略
  - 定义策略优先级和置信度权重
  - 确保策略选择的准确性和效率
  - _需求: 6.2, 6.3_

- [ ] 5.4 优化保留的核心算法
  - 保留并增强ML Kit核心OCR识别（EnhancedMLKitRecognizer）
  - 优化字符标准化算法，简化复杂的映射逻辑
  - 升级批号提取算法，支持多种格式和动态配置
  - 整合智能匹配引擎，统一多种匹配策略
  - _需求: 6.1, 6.2, 6.3_

- [ ] 5.5 简化或移除低效算法
  - 移除过度复杂的硬编码字符映射逻辑
  - 简化文本清理算法，保留基本功能
  - 优化置信度计算，减少不必要的复杂度
  - 确保简化后功能不受影响
  - _需求: 6.2, 6.4_

- [ ] 5.6 实现智能识别协调器
  - 创建IntelligentRecognitionCoordinator统一调度识别流程
  - 集成图像质量评估、策略选择、ML Kit识别、智能匹配
  - 实现降级策略，识别失败时回退到基础模式
  - 提供详细的进度回调和错误处理
  - _需求: 6.1, 6.2, 6.3_

- [ ] 5.7 编写识别算法测试用例
  - 创建标准测试数据集，包含不同质量的图像样本
  - 测试ML Kit V2 0.15.0核心功能保持不变
  - 验证智能策略选择的准确性
  - 确保优化后识别准确率不低于85%
  - 验证识别速度保持在3秒内
  - _需求: 6.1, 6.3, 6.4_

### 阶段5：错误处理和安全性增强

- [ ] 6. 实现统一错误处理系统
  - 创建AppException异常体系
  - 实现GlobalErrorHandler全局错误处理
  - 提供用户友好的错误提示
  - _需求: 2.4, 7.1, 7.2_

- [ ] 6.1 创建异常类型体系
  - 定义AppException基类和具体异常类型
  - 实现DataConsistencyException、WorkloadCalculationException等
  - 提供用户友好的错误消息
  - _需求: 2.4, 7.1_

- [ ] 6.2 实现GlobalErrorHandler
  - 创建全局错误处理器
  - 实现错误日志记录和用户通知
  - 集成到所有关键业务操作中
  - _需求: 2.4, 7.1_

- [ ] 6.3 添加输入验证机制
  - 实现InputValidator验证用户输入
  - 添加产品代码、批号、数量等验证规则
  - 防止无效数据进入系统
  - _需求: 7.2, 8.2_

- [ ] 7. 增强数据安全性
  - 实现SecureDataManager加密存储敏感数据
  - 加密激活码、设备信息等敏感信息
  - 确保数据完整性和安全性
  - _需求: 7.1, 7.2_

- [ ] 7.1 实现数据加密存储
  - 创建SecureDataManager处理敏感数据加密
  - 使用AES加密算法保护数据
  - 实现安全的密钥管理机制
  - _需求: 7.1_

- [ ] 7.2 更新敏感数据存储
  - 重构激活码和许可证信息的存储方式
  - 使用加密存储替换明文存储
  - 编译测试激活验证功能
  - _需求: 7.1, 7.2_

### 阶段6：性能优化和测试

- [ ] 8. 性能优化和内存管理
  - 修复潜在的内存泄漏问题
  - 优化UI性能和响应速度
  - 实现资源管理和清理机制
  - _需求: 3.1, 3.2, 7.3_

- [ ] 8.1 修复内存泄漏问题
  - 检查和修复Timer资源泄漏
  - 优化图像处理的内存使用
  - 实现proper dispose机制
  - _需求: 3.1_

- [ ] 8.2 优化UI性能
  - 实现精细化状态更新，避免不必要的Widget重建
  - 优化长列表的渲染性能
  - 添加加载状态和进度指示
  - _需求: 3.2, 7.3_

- [ ] 8.3 实现资源管理
  - 创建统一的资源清理机制
  - 优化异步操作的生命周期管理
  - 添加内存使用监控
  - _需求: 3.1, 3.2_

- [ ] 9. 编写测试用例
  - 为关键业务逻辑编写单元测试
  - 创建集成测试验证数据一致性
  - 实现性能测试确保优化效果
  - 特别关注ML Kit识别算法的测试覆盖
  - _需求: 所有需求的验证_

- [ ] 9.1 单元测试覆盖
  - 为Repository、Service、Calculator等核心类编写单元测试
  - 重点测试ML Kit识别算法的准确性和性能
  - 测试覆盖率达到80%以上
  - 重点测试数据一致性和计算准确性
  - _需求: 1.1, 1.2, 2.1, 6.1_

- [ ] 9.2 集成测试实现
  - 创建端到端的业务流程测试
  - 验证任务创建、工作量分配、数据统计的完整流程
  - 测试数据迁移和配置管理功能
  - 验证识别算法在实际业务场景中的表现
  - _需求: 1.1, 1.2, 5.1, 6.1_

- [ ] 9.3 性能测试验证
  - 测试大数据量场景下的性能表现
  - 验证内存使用优化效果
  - 测试图像处理性能改进
  - 确保识别速度保持在合理范围内
  - _需求: 3.1, 3.2, 6.3_

### 阶段7：最终集成和验证

- [ ] 10. 系统集成和最终测试
  - 集成所有优化模块
  - 进行全面的功能测试
  - 验证中文字符编码正确性
  - 确保所有现有功能正常工作
  - 特别验证ML Kit V2识别功能的完整性
  - _需求: 所有需求的最终验证_

- [ ] 10.1 模块集成测试
  - 集成所有重构的模块
  - 验证模块间的数据流和交互
  - 确保没有功能回归
  - 特别关注识别算法与业务逻辑的集成
  - _需求: 1.1, 2.1, 5.1, 6.1_

- [ ] 10.2 中文编码验证
  - 测试中文字符在所有模块中的正确处理
  - 验证数据存储和显示的编码一致性
  - 确保没有编码相关的问题
  - _需求: 8.1, 8.2, 8.3_

- [ ] 10.3 完整功能验证
  - 测试任务创建、执行、完成的完整流程
  - 验证工作量统计和报告生成功能
  - 测试配置管理和数据迁移功能
  - 验证ML Kit识别在各种场景下的准确性
  - 确保所有业务场景正常工作
  - _需求: 所有需求_

- [ ] 10.4 性能和稳定性测试
  - 进行长时间运行测试
  - 验证内存使用稳定性
  - 测试异常情况的处理
  - 确保识别算法的稳定性和可靠性
  - 确保应用稳定可靠
  - _需求: 3.1, 3.2, 6.3, 7.1_

## 实施注意事项

### ML Kit V2识别算法优化原则
- **保留核心能力**：绝不降级或删减ML Kit Text Recognition V2 0.15.0的核心功能
- **智能策略选择**：根据图像质量动态选择最优识别策略
- **性能保证**：确保识别准确率不低于85%，处理速度保持在3秒内
- **算法评估**：基于实际测试数据决定算法的保留、优化或简化

### 编译测试策略
- 每完成一个子任务后立即进行编译测试
- 发现问题立即修复，避免问题累积
- 保持代码随时可运行的状态
- 特别关注识别功能的回归测试

### 向后兼容性
- 所有重构都要保持现有API的兼容性
- 通过适配器模式确保现有UI组件正常工作
- 渐进式迁移，避免大范围的破坏性变更
- 确保硬编码数据迁移不影响现有页面调用

### 数据安全性
- 所有数据操作都要有备份机制
- 实现数据迁移的回滚功能
- 确保敏感数据的加密存储

### 中文字符处理
- 所有文本处理都使用UTF-8编码
- 避免使用可能破坏编码的工具（如PowerShell批量替换）
- 测试中文字符在各个环节的正确处理

### 性能监控
- 在关键操作中添加性能监控
- 及时发现和解决性能问题
- 确保优化后的性能提升
- 特别监控识别算法的性能表现

通过这个详细的任务计划，可以系统性地解决项目中的所有关键问题，同时保持应用的稳定性和现有功能的完整性，特别是ML Kit V2识别功能的核心能力。