import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';

void main() {
  group('ML Kit Text Recognition Tests', () {
    late MLKitTextRecognitionService recognitionService;

    setUp(() {
      recognitionService = MLKitTextRecognitionService();
    });

    tearDown(() {
      recognitionService.dispose();
    });

    group('Service Initialization', () {
      test('should create service instance correctly', () {
        expect(recognitionService, isNotNull);
      });

      test('should be singleton', () {
        final instance1 = MLKitTextRecognitionService();
        final instance2 = MLKitTextRecognitionService();
        expect(instance1, same(instance2));
      });
    });

    group('Basic Functionality', () {
      test('should handle service lifecycle correctly', () async {
        // 测试初始化
        await recognitionService.initialize();

        // 测试基本功能存在
        expect(recognitionService, isNotNull);

        // 测试清理
        recognitionService.dispose();
      });

      test('should provide basic service information', () {
        // 测试服务基本信息
        expect(recognitionService.toString(), contains('MLKitTextRecognitionService'));
      });
    });

    group('Service Robustness', () {
      test('should handle multiple initialization calls safely', () async {
        await recognitionService.initialize();
        await recognitionService.initialize(); // 第二次调用应该安全
        await recognitionService.initialize(); // 第三次调用应该安全

        expect(recognitionService, isNotNull);
      });

      test('should handle disposal safely', () {
        recognitionService.dispose();
        recognitionService.dispose(); // 多次调用应该安全

        expect(recognitionService, isNotNull);
      });
    });
  });
}
