import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/local_data_manager.dart';
import 'package:loadguard/services/secure_storage_service.dart';
import 'package:loadguard/services/global_error_handler.dart';
import 'package:loadguard/exceptions/app_exceptions.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 同步服务
/// 为本地应用预留的服务器同步接口
/// 特点：
/// 1. 本地优先 - 本地应用可完全离线工作
/// 2. 可选同步 - 同步功能完全可选，不影响核心功能
/// 3. 冲突解决 - 智能处理本地和服务器数据冲突
/// 4. 增量同步 - 只同步变更的数据，节省带宽
/// 5. 安全传输 - 所有同步数据都经过加密
class SyncService {
  final LocalDataManager _localDataManager;
  final SecureStorageService _secureStorage;
  final GlobalErrorHandler _errorHandler;
  
  // 同步状态
  bool _syncEnabled = false;
  bool _syncing = false;
  DateTime? _lastSyncTime;
  
  // 同步配置
  String? _serverEndpoint;
  String? _apiKey;
  Duration _syncInterval = const Duration(minutes: 30);
  
  // 同步事件流
  final StreamController<SyncEvent> _syncEventStream = StreamController.broadcast();
  Timer? _syncTimer;
  
  SyncService(this._localDataManager, this._secureStorage, this._errorHandler);
  
  /// 同步事件流
  Stream<SyncEvent> get syncEventStream => _syncEventStream.stream;
  
  /// 是否启用同步
  bool get isSyncEnabled => _syncEnabled;
  
  /// 是否正在同步
  bool get isSyncing => _syncing;
  
  /// 最后同步时间
  DateTime? get lastSyncTime => _lastSyncTime;
  
  /// 初始化同步服务
  Future<void> initialize() async {
    try {
      AppLogger.info('🔄 初始化同步服务...');
      
      // 加载同步配置
      await _loadSyncConfig();
      
      // 如果启用了同步，启动定时同步
      if (_syncEnabled && _serverEndpoint != null) {
        await _startPeriodicSync();
      }
      
      AppLogger.info('✅ 同步服务初始化完成 (启用: $_syncEnabled)');
    } catch (e) {
      AppLogger.error('❌ 同步服务初始化失败', error: e);
      await _errorHandler.handleException(e, operation: 'SyncService.initialize');
    }
  }
  
  /// 配置同步服务
  Future<void> configureSyncService({
    required String serverEndpoint,
    required String apiKey,
    Duration? syncInterval,
    bool enableAutoSync = true,
  }) async {
    try {
      _serverEndpoint = serverEndpoint;
      _apiKey = apiKey;
      _syncInterval = syncInterval ?? _syncInterval;
      _syncEnabled = enableAutoSync;
      
      // 保存配置到安全存储
      await _saveSyncConfig();
      
      // 启动或停止定时同步
      if (_syncEnabled) {
        await _startPeriodicSync();
      } else {
        await _stopPeriodicSync();
      }
      
      _emitSyncEvent(SyncEventType.configurationChanged, 'Sync service configured');
      AppLogger.info('⚙️ 同步服务已配置: $serverEndpoint');
    } catch (e) {
      await _errorHandler.handleException(e, operation: 'configureSyncService');
      rethrow;
    }
  }
  
  /// 手动执行同步
  Future<SyncResult> performSync({bool force = false}) async {
    if (_syncing && !force) {
      return SyncResult.failure('Sync already in progress');
    }
    
    if (!_syncEnabled || _serverEndpoint == null || _apiKey == null) {
      return SyncResult.failure('Sync not configured');
    }
    
    _syncing = true;
    _emitSyncEvent(SyncEventType.syncStarted, 'Manual sync started');
    
    try {
      AppLogger.info('🔄 开始手动同步...');
      
      // 1. 检查服务器连接
      final serverStatus = await _checkServerConnection();
      if (!serverStatus.isConnected) {
        throw NetworkException(
          message: 'Cannot connect to sync server',
          userMessage: '无法连接到同步服务器，请检查网络连接',
        );
      }
      
      // 2. 获取本地数据变更
      final localChanges = await _getLocalChanges();
      
      // 3. 获取服务器数据变更
      final serverChanges = await _getServerChanges();
      
      // 4. 解决冲突
      final resolvedChanges = await _resolveConflicts(localChanges, serverChanges);
      
      // 5. 应用变更
      final syncStats = await _applyChanges(resolvedChanges);
      
      // 6. 更新同步状态
      _lastSyncTime = DateTime.now();
      await _updateSyncStatus();
      
      _emitSyncEvent(SyncEventType.syncCompleted, 'Sync completed successfully');
      AppLogger.info('✅ 同步完成: ${syncStats.toString()}');
      
      return SyncResult.success(
        message: 'Sync completed successfully',
        syncStats: syncStats,
      );
    } catch (e) {
      _emitSyncEvent(SyncEventType.syncFailed, 'Sync failed: $e');
      AppLogger.error('❌ 同步失败', error: e);
      
      return SyncResult.failure('Sync failed: $e');
    } finally {
      _syncing = false;
    }
  }
  
  /// 启用/禁用同步
  Future<void> setSyncEnabled(bool enabled) async {
    try {
      _syncEnabled = enabled;
      await _saveSyncConfig();
      
      if (enabled && _serverEndpoint != null) {
        await _startPeriodicSync();
      } else {
        await _stopPeriodicSync();
      }
      
      _emitSyncEvent(
        SyncEventType.configurationChanged,
        'Sync ${enabled ? 'enabled' : 'disabled'}',
      );
      
      AppLogger.info('🔄 同步已${enabled ? '启用' : '禁用'}');
    } catch (e) {
      await _errorHandler.handleException(e, operation: 'setSyncEnabled');
      rethrow;
    }
  }
  
  /// 导出数据用于手动同步
  Future<String> exportForSync() async {
    try {
      AppLogger.info('📤 导出数据用于手动同步...');
      
      final exportPath = await _localDataManager.exportData(
        includeTasks: true,
        includeConfig: true,
        includeStatistics: true,
      );
      
      AppLogger.info('✅ 同步数据导出完成: $exportPath');
      return exportPath;
    } catch (e) {
      await _errorHandler.handleException(e, operation: 'exportForSync');
      rethrow;
    }
  }
  
  /// 导入同步数据
  Future<void> importFromSync(String filePath) async {
    try {
      AppLogger.info('📥 导入同步数据: $filePath');
      
      await _localDataManager.importData(filePath, overwrite: false);
      
      _emitSyncEvent(SyncEventType.dataImported, 'Data imported from sync file');
      AppLogger.info('✅ 同步数据导入完成');
    } catch (e) {
      await _errorHandler.handleException(e, operation: 'importFromSync');
      rethrow;
    }
  }
  
  /// 获取同步状态
  SyncStatus getSyncStatus() {
    return SyncStatus(
      enabled: _syncEnabled,
      syncing: _syncing,
      lastSyncTime: _lastSyncTime,
      serverEndpoint: _serverEndpoint,
      syncInterval: _syncInterval,
      hasConfiguration: _serverEndpoint != null && _apiKey != null,
    );
  }
  
  /// 检查服务器连接（模拟实现）
  Future<ServerConnectionStatus> _checkServerConnection() async {
    // 这里是模拟实现，实际应该发送HTTP请求检查服务器状态
    await Future.delayed(const Duration(milliseconds: 500));
    
    if (_serverEndpoint == null) {
      return ServerConnectionStatus(isConnected: false, message: 'No server endpoint configured');
    }
    
    // 模拟网络检查
    try {
      // 实际实现中应该使用 http 包发送请求
      // final response = await http.get(Uri.parse('$_serverEndpoint/health'));
      // return ServerConnectionStatus(isConnected: response.statusCode == 200);
      
      return ServerConnectionStatus(isConnected: true, message: 'Connected to server');
    } catch (e) {
      return ServerConnectionStatus(isConnected: false, message: 'Connection failed: $e');
    }
  }
  
  /// 获取本地数据变更
  Future<List<DataChange>> _getLocalChanges() async {
    // 这里应该实现获取本地数据变更的逻辑
    // 例如：比较当前数据与上次同步时的快照
    
    final changes = <DataChange>[];
    
    // 模拟一些变更
    changes.add(DataChange(
      type: DataChangeType.taskUpdated,
      id: 'task_123',
      timestamp: DateTime.now(),
      data: {'status': 'completed'},
    ));
    
    return changes;
  }
  
  /// 获取服务器数据变更
  Future<List<DataChange>> _getServerChanges() async {
    // 这里应该实现从服务器获取数据变更的逻辑
    // 例如：调用服务器API获取增量更新
    
    final changes = <DataChange>[];
    
    // 模拟服务器返回的变更
    changes.add(DataChange(
      type: DataChangeType.configUpdated,
      id: 'config_456',
      timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      data: {'setting': 'new_value'},
    ));
    
    return changes;
  }
  
  /// 解决数据冲突
  Future<List<DataChange>> _resolveConflicts(
    List<DataChange> localChanges,
    List<DataChange> serverChanges,
  ) async {
    final resolvedChanges = <DataChange>[];
    
    // 简单的冲突解决策略：本地优先
    // 实际应用中可能需要更复杂的冲突解决逻辑
    
    resolvedChanges.addAll(localChanges);
    
    for (final serverChange in serverChanges) {
      final hasLocalConflict = localChanges.any((local) => 
        local.id == serverChange.id && local.type == serverChange.type);
      
      if (!hasLocalConflict) {
        resolvedChanges.add(serverChange);
      } else {
        AppLogger.warning('⚠️ 数据冲突已解决: ${serverChange.id} (本地优先)');
      }
    }
    
    return resolvedChanges;
  }
  
  /// 应用数据变更
  Future<SyncStatistics> _applyChanges(List<DataChange> changes) async {
    int appliedChanges = 0;
    int failedChanges = 0;
    
    for (final change in changes) {
      try {
        // 这里应该实现具体的数据变更应用逻辑
        // 例如：更新本地数据库、调用服务器API等
        
        appliedChanges++;
      } catch (e) {
        failedChanges++;
        AppLogger.warning('⚠️ 应用数据变更失败: ${change.id} - $e');
      }
    }
    
    return SyncStatistics(
      totalChanges: changes.length,
      appliedChanges: appliedChanges,
      failedChanges: failedChanges,
      syncTime: DateTime.now(),
    );
  }
  
  /// 启动定时同步
  Future<void> _startPeriodicSync() async {
    await _stopPeriodicSync(); // 先停止现有的定时器
    
    _syncTimer = Timer.periodic(_syncInterval, (timer) async {
      if (!_syncing) {
        await performSync();
      }
    });
    
    AppLogger.debug('⏰ 定时同步已启动 (间隔: ${_syncInterval.inMinutes}分钟)');
  }
  
  /// 停止定时同步
  Future<void> _stopPeriodicSync() async {
    _syncTimer?.cancel();
    _syncTimer = null;
    AppLogger.debug('⏰ 定时同步已停止');
  }
  
  /// 加载同步配置
  Future<void> _loadSyncConfig() async {
    try {
      final config = await _secureStorage.getSecureData<Map<String, dynamic>>('sync_config');
      if (config != null) {
        _syncEnabled = config['enabled'] as bool? ?? false;
        _serverEndpoint = config['serverEndpoint'] as String?;
        _apiKey = config['apiKey'] as String?;
        
        final intervalMinutes = config['syncIntervalMinutes'] as int?;
        if (intervalMinutes != null) {
          _syncInterval = Duration(minutes: intervalMinutes);
        }
        
        final lastSyncString = config['lastSyncTime'] as String?;
        if (lastSyncString != null) {
          _lastSyncTime = DateTime.parse(lastSyncString);
        }
      }
    } catch (e) {
      AppLogger.warning('⚠️ 加载同步配置失败: $e');
    }
  }
  
  /// 保存同步配置
  Future<void> _saveSyncConfig() async {
    try {
      final config = {
        'enabled': _syncEnabled,
        'serverEndpoint': _serverEndpoint,
        'apiKey': _apiKey,
        'syncIntervalMinutes': _syncInterval.inMinutes,
        'lastSyncTime': _lastSyncTime?.toIso8601String(),
      };
      
      await _secureStorage.storeSecureData('sync_config', config);
    } catch (e) {
      AppLogger.warning('⚠️ 保存同步配置失败: $e');
    }
  }
  
  /// 更新同步状态
  Future<void> _updateSyncStatus() async {
    await _saveSyncConfig();
  }
  
  /// 发送同步事件
  void _emitSyncEvent(SyncEventType type, String message) {
    final event = SyncEvent(
      type: type,
      message: message,
      timestamp: DateTime.now(),
    );
    
    _syncEventStream.add(event);
    
    switch (type) {
      case SyncEventType.syncFailed:
        AppLogger.warning('🔄 同步事件: $message');
        break;
      default:
        AppLogger.debug('🔄 同步事件: $message');
        break;
    }
  }
  
  /// 释放资源
  Future<void> dispose() async {
    await _stopPeriodicSync();
    _syncEventStream.close();
    AppLogger.info('🔄 同步服务已释放');
  }
}

/// 同步事件类型
enum SyncEventType {
  configurationChanged,
  syncStarted,
  syncCompleted,
  syncFailed,
  dataImported,
  dataExported,
  conflictResolved,
}

/// 数据变更类型
enum DataChangeType {
  taskCreated,
  taskUpdated,
  taskDeleted,
  configUpdated,
  statisticsUpdated,
}

/// 同步事件
class SyncEvent {
  final SyncEventType type;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? details;

  const SyncEvent({
    required this.type,
    required this.message,
    required this.timestamp,
    this.details,
  });

  @override
  String toString() {
    return 'SyncEvent(${type.name}: $message)';
  }
}

/// 同步结果
class SyncResult {
  final bool success;
  final String message;
  final SyncStatistics? syncStats;
  final Object? error;

  const SyncResult._({
    required this.success,
    required this.message,
    this.syncStats,
    this.error,
  });

  factory SyncResult.success({
    required String message,
    SyncStatistics? syncStats,
  }) {
    return SyncResult._(
      success: true,
      message: message,
      syncStats: syncStats,
    );
  }

  factory SyncResult.failure(String message, {Object? error}) {
    return SyncResult._(
      success: false,
      message: message,
      error: error,
    );
  }

  @override
  String toString() {
    return 'SyncResult(success: $success, message: $message)';
  }
}

/// 同步状态
class SyncStatus {
  final bool enabled;
  final bool syncing;
  final DateTime? lastSyncTime;
  final String? serverEndpoint;
  final Duration syncInterval;
  final bool hasConfiguration;

  const SyncStatus({
    required this.enabled,
    required this.syncing,
    this.lastSyncTime,
    this.serverEndpoint,
    required this.syncInterval,
    required this.hasConfiguration,
  });

  Map<String, dynamic> toMap() {
    return {
      'enabled': enabled,
      'syncing': syncing,
      'lastSyncTime': lastSyncTime?.toIso8601String(),
      'serverEndpoint': serverEndpoint,
      'syncIntervalMinutes': syncInterval.inMinutes,
      'hasConfiguration': hasConfiguration,
    };
  }

  @override
  String toString() {
    return 'SyncStatus(enabled: $enabled, syncing: $syncing, configured: $hasConfiguration)';
  }
}

/// 服务器连接状态
class ServerConnectionStatus {
  final bool isConnected;
  final String message;
  final int? statusCode;

  const ServerConnectionStatus({
    required this.isConnected,
    required this.message,
    this.statusCode,
  });

  @override
  String toString() {
    return 'ServerConnectionStatus(connected: $isConnected, message: $message)';
  }
}

/// 数据变更
class DataChange {
  final DataChangeType type;
  final String id;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  const DataChange({
    required this.type,
    required this.id,
    required this.timestamp,
    required this.data,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.name,
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }

  factory DataChange.fromMap(Map<String, dynamic> map) {
    return DataChange(
      type: DataChangeType.values.firstWhere((e) => e.name == map['type']),
      id: map['id'] as String,
      timestamp: DateTime.parse(map['timestamp'] as String),
      data: map['data'] as Map<String, dynamic>,
    );
  }

  @override
  String toString() {
    return 'DataChange(${type.name}: $id)';
  }
}

/// 同步统计
class SyncStatistics {
  final int totalChanges;
  final int appliedChanges;
  final int failedChanges;
  final DateTime syncTime;

  const SyncStatistics({
    required this.totalChanges,
    required this.appliedChanges,
    required this.failedChanges,
    required this.syncTime,
  });

  double get successRate => totalChanges > 0 ? appliedChanges / totalChanges : 0.0;

  Map<String, dynamic> toMap() {
    return {
      'totalChanges': totalChanges,
      'appliedChanges': appliedChanges,
      'failedChanges': failedChanges,
      'successRate': successRate,
      'syncTime': syncTime.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'SyncStatistics(total: $totalChanges, applied: $appliedChanges, failed: $failedChanges)';
  }
}

/// 同步服务Provider
final syncServiceProvider = Provider<SyncService>((ref) {
  final localDataManager = ref.read(localDataManagerProvider);
  final secureStorage = ref.read(secureStorageServiceProvider);
  final errorHandler = ref.read(globalErrorHandlerProvider);
  final service = SyncService(localDataManager, secureStorage, errorHandler);
  ref.onDispose(() => service.dispose());
  return service;
});
