import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/task_model_extensions.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/services/workload_calculation_service.dart';

/// Mock TaskRepository for testing
class MockTaskRepository implements TaskRepository {
  final List<TaskModel> _tasks = [];

  void addTask(TaskModel task) {
    _tasks.add(task);
  }

  @override
  Future<List<TaskModel>> getAllTasks() async {
    return List.from(_tasks);
  }

  @override
  Future<TaskModel?> getTaskById(String id) async {
    return _tasks.where((task) => task.id == id).firstOrNull;
  }

  @override
  Future<void> saveTask(TaskModel task) async {
    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index >= 0) {
      _tasks[index] = task;
    } else {
      _tasks.add(task);
    }
  }

  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    _tasks.clear();
    _tasks.addAll(tasks);
  }

  @override
  Future<void> deleteTask(String id) async {
    _tasks.removeWhere((task) => task.id == id);
  }

  @override
  Future<void> clearAllTasks() async {
    _tasks.clear();
  }

  @override
  Future<TaskModel?> getCurrentTask() async {
    return _tasks.isNotEmpty ? _tasks.first : null;
  }

  @override
  Future<void> setCurrentTask(TaskModel? task) async {
    // Mock implementation
  }

  @override
  Future<List<TaskModel>> queryTasks({
    DateTime? startDate,
    DateTime? endDate,
    String? template,
    bool? isCompleted,
  }) async {
    return _tasks.where((task) {
      if (startDate != null && task.createTime.isBefore(startDate)) return false;
      if (endDate != null && task.createTime.isAfter(endDate)) return false;
      if (template != null && task.template != template) return false;
      if (isCompleted != null && task.isCompleted != isCompleted) return false;
      return true;
    }).toList();
  }

  @override
  Future<Map<String, dynamic>> getTaskStatistics() async {
    return {
      'totalTasks': _tasks.length,
      'completedTasks': _tasks.where((t) => t.isCompleted).length,
    };
  }

  @override
  Future<void> migrateData() async {}

  @override
  Future<void> backupData() async {}

  @override
  Future<void> restoreData() async {}
}

void main() {
  group('WorkloadCalculationService Tests', () {
    late MockTaskRepository mockRepository;
    late WorkloadCalculationService calculationService;

    setUp(() {
      mockRepository = MockTaskRepository();
      calculationService = WorkloadCalculationService(mockRepository);
    });

    test('should calculate worker statistics correctly', () async {
      // 创建测试任务
      final task = TaskModel(
        id: 'test-task-1',
        template: '平板车',
        productCode: 'TEST-001',
        batchNumber: '240101A12345',
        quantity: 10,
        createTime: DateTime.now(),
        photos: [],
      );

      // 添加工作量分配数据
      final workloadAssignment = WorkloadAssignment(
        records: [
          WorkloadRecord(
            workerId: '1',
            workerName: '张三',
            role: '叉车',
            warehouse: '1号库',
            group: '1组',
            allocatedTonnage: 7.5, // 10托 * 1.5吨 / 2人 = 7.5吨
            assignedAt: DateTime.now(),
          ),
          WorkloadRecord(
            workerId: '2',
            workerName: '李四',
            role: '仓管',
            warehouse: '1号库',
            group: '1组',
            allocatedTonnage: 7.5,
            assignedAt: DateTime.now(),
          ),
        ],
        totalTonnage: 15.0,
        palletCount: 10,
        assignedAt: DateTime.now(),
        assignedBy: 'test',
      );

      task.recognitionMetadata = {'workload': workloadAssignment.toMap()};
      mockRepository.addTask(task);

      // 计算工作量统计
      final stats = await calculationService.calculateWorkerStatistics();

      // 验证结果
      expect(stats, isNotEmpty);
      expect(stats.containsKey('1'), isTrue);
      expect(stats.containsKey('2'), isTrue);

      final worker1Stats = stats['1']!;
      // 使用真实的工作人员姓名（来自worker_info_data.dart）
      expect(worker1Stats['workerName'], equals('葛峰')); // ID为1的工作人员
      expect(worker1Stats['assignedTonnage'], equals(7.5));
      expect(worker1Stats['totalTasks'], equals(1));
    });

    test('should calculate workload overview correctly', () async {
      // 创建多个测试任务
      final task1 = TaskModel(
        id: 'test-task-1',
        template: '平板车',
        productCode: 'TEST-001',
        batchNumber: '240101A12345',
        quantity: 10,
        createTime: DateTime.now(),
        photos: [],
      );

      final task2 = TaskModel(
        id: 'test-task-2',
        template: '集装箱',
        productCode: 'TEST-002',
        batchNumber: '240102B12345',
        quantity: 20,
        createTime: DateTime.now(),
        photos: [],
      );

      // 设置第一个任务为已完成（添加必需照片并设置为已拍摄）
      task1.photos.add(PhotoItem(
        id: 'photo1',
        label: '测试照片',
        configId: 'config1',
        isRequired: true,
        needRecognition: false,
        imagePath: '/test/path/photo1.jpg', // 设置照片路径表示已拍摄
      ));
      task1.completedAt = DateTime.now();

      mockRepository.addTask(task1);
      mockRepository.addTask(task2);

      // 计算概览
      final overview = await calculationService.calculateWorkloadOverview();

      // 验证结果
      expect(overview['totalTasks'], equals(2));
      expect(overview['completedTasks'], equals(1)); // 只有task1完成
      expect(overview['totalTonnage'], equals(45.0)); // (10+20) * 1.5
      expect(overview['completedTonnage'], equals(15.0)); // 10 * 1.5
    });

    test('should validate workload consistency', () async {
      // 创建有问题的任务（工作量分配不一致）
      final task = TaskModel(
        id: 'test-task-1',
        template: '平板车',
        productCode: 'TEST-001',
        batchNumber: '240101A12345',
        quantity: 10, // 期望15吨
        createTime: DateTime.now(),
        photos: [],
      );

      // 添加不一致的工作量分配（总计20吨，但期望15吨）
      final workloadAssignment = WorkloadAssignment(
        records: [
          WorkloadRecord(
            workerId: '1',
            workerName: '张三',
            role: '叉车',
            warehouse: '1号库',
            group: '1组',
            allocatedTonnage: 20.0, // 错误的分配
            assignedAt: DateTime.now(),
          ),
        ],
        totalTonnage: 20.0,
        palletCount: 10,
        assignedAt: DateTime.now(),
        assignedBy: 'test',
      );

      task.recognitionMetadata = {'workload': workloadAssignment.toMap()};
      mockRepository.addTask(task);

      // 验证一致性
      final validation = await calculationService.validateWorkloadConsistency();

      // 验证结果
      expect(validation['isConsistent'], isFalse);
      expect(validation['issues'], isNotEmpty);
      expect(validation['tasksWithWorkload'], equals(1));
      expect(validation['tasksWithoutWorkload'], equals(0));
    });

    test('should handle empty task list', () async {
      // 计算空任务列表的统计
      final stats = await calculationService.calculateWorkerStatistics();
      final overview = await calculationService.calculateWorkloadOverview();

      // 验证结果
      expect(stats, isNotEmpty); // 应该包含所有已知工作人员的初始化数据
      expect(overview['totalTasks'], equals(0));
      expect(overview['totalTonnage'], equals(0.0));
    });

    test('should filter tasks by date range', () async {
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(days: 1));
      final tomorrow = now.add(const Duration(days: 1));

      // 创建不同日期的任务
      final oldTask = TaskModel(
        id: 'old-task',
        template: '平板车',
        productCode: 'OLD-001',
        batchNumber: '240101A12345',
        quantity: 10,
        createTime: yesterday,
        photos: [],
      );

      final newTask = TaskModel(
        id: 'new-task',
        template: '平板车',
        productCode: 'NEW-001',
        batchNumber: '240102A12345',
        quantity: 10,
        createTime: tomorrow,
        photos: [],
      );

      mockRepository.addTask(oldTask);
      mockRepository.addTask(newTask);

      // 只统计今天之后的任务
      final stats = await calculationService.calculateWorkerStatistics(
        startDate: now,
      );

      // 验证只包含新任务的统计
      final overview = await calculationService.calculateWorkloadOverview(
        startDate: now,
      );

      expect(overview['totalTasks'], equals(1)); // 只有newTask
    });
  });
}
