import 'package:flutter/material.dart';
import '../models/product_database.dart';
import '../utils/theme_colors.dart';

/// 🎨 产品选择器弹窗 - 独立StatefulWidget支持搜索和分组
class ProductSelectorModal extends StatefulWidget {
  final String? selectedProductCode;
  final Function(String) onProductSelected;

  const ProductSelectorModal({
    super.key,
    this.selectedProductCode,
    required this.onProductSelected,
  });

  @override
  State<ProductSelectorModal> createState() => _ProductSelectorModalState();
}

class _ProductSelectorModalState extends State<ProductSelectorModal> {
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        gradient: ThemeColors.primaryGradient,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // 顶部把手和标题
          _buildHeader(),

          // 搜索框
          _buildSearchBox(),

          // 分组产品列表
          Expanded(
            child: _buildGroupedProductList(),
          ),
        ],
      ),
    );
  }

  /// 🎨 顶部标题
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 20),
      child: Column(
        children: [
          // 顶部把手
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 标题
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.category,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '选择产品牌号',
                      style: TextStyles.cardTitle,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '按类别分组，便于快速查找',
                      style: TextStyles.cardSubtitle,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 🔍 搜索框
  Widget _buildSearchBox() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
      ),
      child: TextField(
        style: TextStyles.body,
        decoration: InputDecoration(
          hintText: '搜索牌号或类别...',
          hintStyle: TextStyles.description.copyWith(
            color: Colors.white.withValues(alpha: 0.6),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: Colors.white.withValues(alpha: 0.7),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  /// 📋 分组产品列表
  Widget _buildGroupedProductList() {
    // 获取所有产品并应用搜索过滤
    List<ProductInfo> allProducts = ProductDatabase.getAllProducts();

    // 如果有搜索查询，过滤产品
    if (_searchQuery.isNotEmpty) {
      allProducts = allProducts.where((product) {
        return product.code.toLowerCase().contains(_searchQuery) ||
            product.category.toLowerCase().contains(_searchQuery) ||
            product.name.toLowerCase().contains(_searchQuery);
      }).toList();
    }

    // 按类别分组产品
    final Map<String, List<ProductInfo>> groupedProducts = {};
    for (final product in allProducts) {
      if (!groupedProducts.containsKey(product.category)) {
        groupedProducts[product.category] = [];
      }
      groupedProducts[product.category]!.add(product);
    }

    // 如果搜索后没有结果
    if (groupedProducts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.white.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              '未找到匹配的产品',
              style: TextStyles.subtitle.copyWith(
                color: Colors.white.withValues(alpha: 0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请尝试其他关键词',
              style: TextStyles.description.copyWith(
                color: Colors.white.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      );
    }

    // 类别排序（热门类别优先）
    final sortedCategories = groupedProducts.keys.toList()
      ..sort((a, b) {
        const priority = ['LLDPE', 'HDPE', 'PP', 'mPE', 'SAN', 'PS'];
        final aIndex = priority.indexOf(a);
        final bIndex = priority.indexOf(b);
        if (aIndex != -1 && bIndex != -1) return aIndex.compareTo(bIndex);
        if (aIndex != -1) return -1;
        if (bIndex != -1) return 1;
        return a.compareTo(b);
      });

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: sortedCategories.length,
      itemBuilder: (context, categoryIndex) {
        final category = sortedCategories[categoryIndex];
        final products = groupedProducts[category]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 类别标题
            _buildCategoryHeader(category, products.length),

            // 该类别下的所有产品
            ...products.map((product) => _buildProductItem(product)),

            // 分组间距
            if (categoryIndex < sortedCategories.length - 1)
              const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  /// 🏷️ 类别标题
  Widget _buildCategoryHeader(String category, int count) {
    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: ProductDatabase.getCategoryColor(category).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ProductDatabase.getCategoryColor(category).withValues(alpha: 0.4),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: ProductDatabase.getCategoryColor(category),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: ProductDatabase.getCategoryColor(category)
                      .withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              category,
              style: TextStyles.button.copyWith(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '$count 个牌号',
            style: TextStyles.description.copyWith(
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
          const Spacer(),
          Icon(
            Icons.expand_more,
            color: Colors.white.withValues(alpha: 0.6),
            size: 20,
          ),
        ],
      ),
    );
  }

  /// 📦 产品项
  Widget _buildProductItem(ProductInfo product) {
    final isSelected = widget.selectedProductCode == product.code;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? Colors.white.withValues(alpha: 0.25)
            : Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? Colors.white.withValues(alpha: 0.6)
              : Colors.white.withValues(alpha: 0.2),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            widget.onProductSelected(product.code);
            Navigator.pop(context);
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 类别色块（小号）
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: ProductDatabase.getCategoryColor(product.category),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    product.category,
                    style: TextStyles.caption.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // 牌号名称
                Expanded(
                  child: Text(
                    product.code,
                    style: TextStyles.body.copyWith(
                      fontWeight:
                          isSelected ? FontWeight.w700 : FontWeight.w600,
                      color: isSelected
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ),

                // 选中状态
                if (isSelected) ...[
                  const SizedBox(width: 12),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: ThemeColors.success,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '已选',
                          style: TextStyles.caption.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  const SizedBox(width: 12),
                  Icon(
                    Icons.radio_button_unchecked,
                    color: Colors.white.withValues(alpha: 0.4),
                    size: 20,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
