import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/utils/qr_data_parser.dart';
import 'package:loadguard/services/unified_recognition_service.dart';

/// 🧪 【二维码URL格式专用测试】
/// 
/// 【测试目标】：验证对您实际二维码格式的支持
/// 【实际格式】：qiyulongoc.com.cn
/// 【预期结果】：正确解析并支持网络验证
void main() {
  group('QR URL Format Tests - 针对实际二维码格式', () {
    
    test('应该正确解析域名格式的二维码', () {
      // 您的实际二维码内容
      const qrContent = 'qiyulongoc.com.cn';
      
      // 解析二维码数据
      final parsedData = IntelligentQRDataParser.parseQRData(qrContent);
      
      // 验证解析结果
      expect(parsedData.format, equals(QRDataFormat.url));
      expect(parsedData.url, equals('https://qiyulongoc.com.cn'));
      expect(parsedData.rawData, equals(qrContent));
      
      print('✅ 域名格式解析测试通过');
      print('   原始数据: $qrContent');
      print('   解析格式: ${parsedData.format.description}');
      print('   构建URL: ${parsedData.url}');
    });
    
    test('应该支持带路径的域名格式', () {
      const qrContent = 'qiyulongoc.com.cn/product/LLD-7042';
      
      final parsedData = IntelligentQRDataParser.parseQRData(qrContent);
      
      expect(parsedData.format, equals(QRDataFormat.url));
      expect(parsedData.url, equals('https://qiyulongoc.com.cn/product/LLD-7042'));
      expect(parsedData.productCode, equals('LLD-7042')); // 从路径中提取
      
      print('✅ 带路径域名格式解析测试通过');
      print('   提取的产品代码: ${parsedData.productCode}');
    });
    
    test('应该支持带查询参数的URL格式', () {
      const qrContent = 'qiyulongoc.com.cn?code=LLD-7042&batch=250712F20440-2C';
      
      final parsedData = IntelligentQRDataParser.parseQRData(qrContent);
      
      expect(parsedData.format, equals(QRDataFormat.url));
      expect(parsedData.productCode, equals('LLD-7042'));
      expect(parsedData.batchNumber, equals('250712F20440-2C'));
      
      print('✅ 带查询参数URL格式解析测试通过');
      print('   产品代码: ${parsedData.productCode}');
      print('   批次号: ${parsedData.batchNumber}');
    });
    
    test('应该正确验证域名格式', () {
      const testCases = [
        'qiyulongoc.com.cn',
        'example.com',
        'subdomain.example.org',
        'test-site.co.uk',
      ];
      
      for (final testCase in testCases) {
        final parsedData = IntelligentQRDataParser.parseQRData(testCase);
        expect(parsedData.format, equals(QRDataFormat.url));
        expect(parsedData.url, startsWith('https://'));
        
        print('✅ 域名验证通过: $testCase → ${parsedData.url}');
      }
    });
    
    test('应该正确处理无效的域名格式', () {
      const invalidCases = [
        'not-a-domain',
        '123.456.789',
        'invalid..domain.com',
        '.invalid.com',
      ];
      
      for (final testCase in invalidCases) {
        final parsedData = IntelligentQRDataParser.parseQRData(testCase);
        // 无效域名应该被识别为原始数据格式
        expect(parsedData.format, equals(QRDataFormat.raw));
        
        print('✅ 无效域名正确处理: $testCase → ${parsedData.format.description}');
      }
    });
    
    test('数据验证器应该正确处理URL格式', () {
      const qrContent = 'qiyulongoc.com.cn';
      final parsedData = IntelligentQRDataParser.parseQRData(qrContent);
      final validation = QRDataValidator.validate(parsedData);
      
      // URL格式的数据即使没有产品代码也应该被认为是有效的
      expect(validation.isValid, isTrue);
      expect(validation.confidence, greaterThan(0.5));
      
      print('✅ URL格式数据验证通过');
      print('   验证结果: ${validation.isValid}');
      print('   置信度: ${validation.confidence.toStringAsFixed(2)}');
    });
    
    test('统一识别服务应该正确处理域名格式二维码', () async {
      // 模拟二维码识别结果
      final mockQRResult = createMockQRResult('qiyulongoc.com.cn');
      
      // 测试信息提取
      final service = UnifiedRecognitionService.instance;
      final extractedInfo = service._extractInfoFromQRContent('qiyulongoc.com.cn');
      
      expect(extractedInfo['url'], equals('https://qiyulongoc.com.cn'));
      
      print('✅ 统一识别服务处理域名格式测试通过');
      print('   提取的URL: ${extractedInfo['url']}');
    });
    
    test('网络验证应该支持域名格式', () async {
      // 注意：这个测试需要网络连接，在实际环境中可能需要mock
      const qrContent = 'example.com'; // 使用可靠的测试域名
      
      try {
        final service = UnifiedRecognitionService.instance;
        await service.initialize();
        
        // 创建模拟的二维码识别结果
        final mockResult = createMockQRResult(qrContent);
        
        // 执行网络验证
        final validation = await service._performNetworkValidation(mockResult);
        
        if (validation != null) {
          expect(validation.url, startsWith('https://'));
          print('✅ 网络验证测试完成');
          print('   验证URL: ${validation.url}');
          print('   验证结果: ${validation.isValid}');
          print('   状态码: ${validation.statusCode}');
        }
      } catch (e) {
        print('⚠️ 网络验证测试跳过（需要网络连接）: $e');
      }
    });
    
    test('性能测试 - 域名格式解析速度', () {
      const qrContent = 'qiyulongoc.com.cn';
      final stopwatch = Stopwatch()..start();
      
      // 执行100次解析测试
      for (int i = 0; i < 100; i++) {
        final parsedData = IntelligentQRDataParser.parseQRData(qrContent);
        expect(parsedData.format, equals(QRDataFormat.url));
      }
      
      stopwatch.stop();
      final avgTime = stopwatch.elapsedMicroseconds / 100;
      
      // 解析时间应该小于1毫秒
      expect(avgTime, lessThan(1000)); // 1000微秒 = 1毫秒
      
      print('✅ 性能测试通过');
      print('   平均解析时间: ${avgTime.toStringAsFixed(1)}微秒');
      print('   100次解析总时间: ${stopwatch.elapsedMilliseconds}毫秒');
    });
  });
}

// 辅助函数：创建模拟的二维码识别结果
dynamic createMockQRResult(String content) {
  return {
    'qrCode': content,
    'confidence': 95.0,
    'boundingBox': {
      'left': 100.0,
      'top': 100.0,
      'right': 200.0,
      'bottom': 200.0,
    },
  };
}

// 扩展测试：验证完整的识别流程
void runIntegrationTest() async {
  print('\n🧪 === 集成测试：完整识别流程 ===');
  
  try {
    final service = UnifiedRecognitionService.instance;
    await service.initialize();
    
    // 模拟包含您的二维码格式的图像识别
    // 注意：这需要实际的图像文件
    // final result = await service.recognizeUnified(
    //   'path/to/your/qr_image.jpg',
    //   enableQRCode: true,
    //   enableNetworkValidation: true,
    // );
    
    print('✅ 集成测试准备完成');
    print('💡 要运行完整测试，请提供包含二维码的实际图像文件');
    
  } catch (e) {
    print('❌ 集成测试失败: $e');
  }
}
