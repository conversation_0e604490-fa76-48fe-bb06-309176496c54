# 🚀 LoadGuard 部署运维文档

## 📋 **文档信息**

**文档版本**: v2.0  
**更新日期**: 2025年8月4日  
**运维团队**: LoadGuard DevOps团队  
**适用环境**: 生产环境、测试环境

## 🎯 **部署概述**

LoadGuard采用Flutter跨平台架构，支持Android和iOS双平台部署。本文档详细说明了多引擎识别系统的部署流程、配置要求和运维监控。

## 📱 **系统要求**

### **Android平台**
```yaml
最低要求:
  - Android版本: 6.0 (API Level 23)
  - RAM内存: 3GB
  - 存储空间: 500MB
  - 摄像头: 800万像素以上

推荐配置:
  - Android版本: 8.0+ (API Level 26+)
  - RAM内存: 6GB+
  - 存储空间: 2GB+
  - 摄像头: 1300万像素以上
  - 处理器: 骁龙660+或同等性能
```

### **iOS平台**
```yaml
最低要求:
  - iOS版本: 12.0
  - 设备: iPhone 7/iPad (6th generation)
  - RAM内存: 2GB
  - 存储空间: 500MB

推荐配置:
  - iOS版本: 14.0+
  - 设备: iPhone 11+/iPad Air 3+
  - RAM内存: 4GB+
  - 存储空间: 2GB+
```

## 🔧 **开发环境配置**

### **1. Flutter环境**

```bash
# 安装Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"

# 验证安装
flutter doctor

# 要求版本
Flutter 3.16.0+
Dart 3.2.0+
```

### **2. 依赖包配置**

```yaml
# pubspec.yaml 核心依赖
dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  flutter_riverpod: ^2.4.9
  
  # 本地存储
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # 图像处理
  image: ^4.1.3
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  
  # ML Kit OCR
  google_mlkit_text_recognition: ^0.15.0
  
  # 路径和文件
  path_provider: ^2.1.1
  path: ^1.8.3
  
  # UI组件
  cupertino_icons: ^1.0.6
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
```

### **3. 平台特定配置**

#### **Android配置**
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 权限配置 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    
    <!-- 相机特性 -->
    <uses-feature android:name="android.hardware.camera" android:required="true" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    
    <application
        android:label="LoadGuard"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher">
        
        <!-- ML Kit元数据 -->
        <meta-data
            android:name="com.google.mlkit.vision.DEPENDENCIES"
            android:value="ocr" />
            
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
        </activity>
    </application>
</manifest>
```

#### **iOS配置**
```xml
<!-- ios/Runner/Info.plist -->
<dict>
    <!-- 相机权限 -->
    <key>NSCameraUsageDescription</key>
    <string>LoadGuard需要使用相机拍摄工业标签进行识别</string>
    
    <!-- 相册权限 -->
    <key>NSPhotoLibraryUsageDescription</key>
    <string>LoadGuard需要访问相册选择图片进行识别</string>
    
    <!-- 文件访问权限 -->
    <key>NSDocumentsFolderUsageDescription</key>
    <string>LoadGuard需要访问文档文件夹保存识别结果</string>
    
    <!-- 最低iOS版本 -->
    <key>MinimumOSVersion</key>
    <string>12.0</string>
</dict>
```

## 🏗️ **构建部署流程**

### **1. 代码构建**

```bash
# 清理项目
flutter clean

# 获取依赖
flutter pub get

# 生成Hive适配器
flutter packages pub run build_runner build

# 代码分析
flutter analyze

# 运行测试
flutter test
```

### **2. Android构建**

```bash
# Debug构建
flutter build apk --debug

# Release构建
flutter build apk --release

# AAB构建 (Google Play)
flutter build appbundle --release

# 指定架构构建
flutter build apk --split-per-abi --release
```

### **3. iOS构建**

```bash
# 清理iOS构建缓存
cd ios && rm -rf Pods Podfile.lock && cd ..

# 安装CocoaPods依赖
cd ios && pod install && cd ..

# Debug构建
flutter build ios --debug

# Release构建
flutter build ios --release

# 生成IPA文件
flutter build ipa --release
```

## 📦 **应用打包配置**

### **Android签名配置**

```bash
# 生成签名密钥
keytool -genkey -v -keystore ~/loadguard-release-key.jks \
  -keyalg RSA -keysize 2048 -validity 10000 \
  -alias loadguard

# android/key.properties
storePassword=your_store_password
keyPassword=your_key_password
keyAlias=loadguard
storeFile=/path/to/loadguard-release-key.jks
```

```gradle
// android/app/build.gradle
android {
    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

### **iOS证书配置**

```bash
# 在Xcode中配置
1. 打开 ios/Runner.xcworkspace
2. 选择 Runner target
3. 配置 Signing & Capabilities
4. 选择正确的 Team 和 Bundle Identifier
5. 配置 Provisioning Profile
```

## 🔧 **环境配置管理**

### **1. 环境变量配置**

```dart
// lib/config/app_config.dart
class AppConfig {
  static const String appName = 'LoadGuard';
  static const String version = '2.0.0';
  
  // 识别配置
  static const int maxImageSize = 1920;
  static const int imageQuality = 85;
  static const Duration recognitionTimeout = Duration(seconds: 30);
  
  // 性能配置
  static const int maxCacheSize = 100; // MB
  static const int maxParallelEngines = 4;
  static const bool enablePerformanceLogging = true;
  
  // 调试配置
  static const bool isDebugMode = bool.fromEnvironment('DEBUG', defaultValue: false);
  static const bool enableDetailedLogging = bool.fromEnvironment('DETAILED_LOG', defaultValue: false);
}
```

### **2. 构建配置**

```bash
# 开发环境构建
flutter build apk --debug --dart-define=DEBUG=true --dart-define=DETAILED_LOG=true

# 测试环境构建
flutter build apk --release --dart-define=DEBUG=false --dart-define=DETAILED_LOG=true

# 生产环境构建
flutter build apk --release --dart-define=DEBUG=false --dart-define=DETAILED_LOG=false
```

## 📊 **性能监控配置**

### **1. 性能指标收集**

```dart
// lib/utils/performance_monitor.dart
class PerformanceMonitor {
  static final Map<String, Stopwatch> _timers = {};
  static final List<PerformanceMetric> _metrics = [];
  
  // 开始计时
  static void startTimer(String operation) {
    _timers[operation] = Stopwatch()..start();
  }
  
  // 结束计时
  static void endTimer(String operation) {
    final timer = _timers[operation];
    if (timer != null) {
      timer.stop();
      _recordMetric(operation, timer.elapsedMilliseconds);
      _timers.remove(operation);
    }
  }
  
  // 记录指标
  static void _recordMetric(String operation, int duration) {
    _metrics.add(PerformanceMetric(
      operation: operation,
      duration: duration,
      timestamp: DateTime.now(),
    ));
    
    // 保持最近1000条记录
    if (_metrics.length > 1000) {
      _metrics.removeAt(0);
    }
  }
  
  // 获取性能报告
  static PerformanceReport getReport() {
    return PerformanceReport(_metrics);
  }
}
```

### **2. 内存监控**

```dart
// lib/utils/memory_monitor.dart
class MemoryMonitor {
  static Timer? _monitorTimer;
  static int _peakMemoryUsage = 0;
  
  // 开始监控
  static void startMonitoring() {
    _monitorTimer = Timer.periodic(Duration(seconds: 10), (timer) {
      _checkMemoryUsage();
    });
  }
  
  // 检查内存使用
  static void _checkMemoryUsage() {
    // 获取当前内存使用情况
    final currentUsage = _getCurrentMemoryUsage();
    
    if (currentUsage > _peakMemoryUsage) {
      _peakMemoryUsage = currentUsage;
    }
    
    // 内存使用过高时清理缓存
    if (currentUsage > 200 * 1024 * 1024) { // 200MB
      _clearCache();
    }
  }
  
  // 停止监控
  static void stopMonitoring() {
    _monitorTimer?.cancel();
    _monitorTimer = null;
  }
}
```

## 🚨 **错误监控和日志**

### **1. 日志配置**

```dart
// lib/utils/app_logger.dart
class AppLogger {
  static late Logger _logger;
  
  static void initialize() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
    );
  }
  
  // 不同级别的日志
  static void debug(String message) {
    if (AppConfig.enableDetailedLogging) {
      _logger.d(message);
    }
  }
  
  static void info(String message) {
    _logger.i(message);
  }
  
  static void warning(String message) {
    _logger.w(message);
  }
  
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error, stackTrace);
    
    // 生产环境中发送错误报告
    if (!AppConfig.isDebugMode) {
      _sendErrorReport(message, error, stackTrace);
    }
  }
}
```

### **2. 崩溃监控**

```dart
// lib/main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 设置全局错误处理
  FlutterError.onError = (FlutterErrorDetails details) {
    AppLogger.error('Flutter错误', details.exception, details.stack);
  };
  
  // 设置Zone错误处理
  runZonedGuarded(() {
    runApp(MyApp());
  }, (error, stackTrace) {
    AppLogger.error('Zone错误', error, stackTrace);
  });
}
```

## 🔄 **更新部署策略**

### **1. 热更新配置**

```dart
// 检查应用更新
class UpdateChecker {
  static Future<bool> checkForUpdates() async {
    try {
      // 检查应用商店版本
      final storeVersion = await _getStoreVersion();
      final currentVersion = AppConfig.version;
      
      return _isNewerVersion(storeVersion, currentVersion);
    } catch (e) {
      AppLogger.error('检查更新失败', e);
      return false;
    }
  }
  
  static Future<void> promptUpdate() async {
    // 显示更新提示对话框
    // 引导用户到应用商店更新
  }
}
```

### **2. 渐进式部署**

```bash
# 分阶段部署策略
阶段1: 内部测试 (10个设备)
阶段2: Beta测试 (100个用户)
阶段3: 小规模发布 (10%用户)
阶段4: 全量发布 (100%用户)
```

## 📈 **运维监控指标**

### **1. 关键指标**

```yaml
性能指标:
  - 应用启动时间: < 3秒
  - 识别处理时间: < 5秒
  - 内存使用峰值: < 300MB
  - 崩溃率: < 0.1%

业务指标:
  - 识别成功率: > 90%
  - 用户活跃度: 日活跃用户数
  - 任务完成率: > 95%
  - 用户满意度: > 4.5/5.0
```

### **2. 监控告警**

```dart
// 监控告警配置
class MonitoringAlerts {
  // 性能告警
  static void checkPerformanceAlerts() {
    final report = PerformanceMonitor.getReport();
    
    if (report.averageRecognitionTime > 10000) { // 10秒
      _sendAlert('识别时间过长', '平均识别时间: ${report.averageRecognitionTime}ms');
    }
    
    if (MemoryMonitor.getCurrentUsage() > 250 * 1024 * 1024) { // 250MB
      _sendAlert('内存使用过高', '当前内存使用: ${MemoryMonitor.getCurrentUsage()}');
    }
  }
  
  // 业务告警
  static void checkBusinessAlerts() {
    final stats = WorkloadStatisticsService.getTodayStats();
    
    if (stats.failureRate > 0.1) { // 失败率超过10%
      _sendAlert('识别失败率过高', '今日失败率: ${stats.failureRate * 100}%');
    }
  }
}
```

## 🛠️ **故障排查指南**

### **1. 常见问题**

```yaml
问题: 识别速度慢
原因: 
  - 设备性能不足
  - 图像分辨率过高
  - 多引擎并行数量过多
解决:
  - 降低图像质量
  - 减少并行引擎数量
  - 使用速度优先策略

问题: 识别准确率低
原因:
  - 图像质量差
  - 反光或倾斜严重
  - 引擎选择不当
解决:
  - 启用预处理算法
  - 使用精度优先策略
  - 检查图像拍摄条件

问题: 应用崩溃
原因:
  - 内存不足
  - 图像文件损坏
  - 并发处理冲突
解决:
  - 清理内存缓存
  - 验证图像文件
  - 限制并发数量
```

### **2. 调试工具**

```dart
// 调试工具类
class DebugTools {
  // 导出日志
  static Future<String> exportLogs() async {
    final logs = AppLogger.getAllLogs();
    final file = await _writeLogsToFile(logs);
    return file.path;
  }
  
  // 导出性能报告
  static Future<String> exportPerformanceReport() async {
    final report = PerformanceMonitor.getReport();
    final file = await _writeReportToFile(report);
    return file.path;
  }
  
  // 系统信息
  static Future<Map<String, dynamic>> getSystemInfo() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'memory': await _getAvailableMemory(),
      'storage': await _getAvailableStorage(),
    };
  }
}
```

---

**运维支持**: 如有部署或运维问题，请联系LoadGuard运维团队获取技术支持。
