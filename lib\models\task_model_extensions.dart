/// 工作量记录模型
/// 记录具体的工作分配信息
class WorkloadRecord {
  final String workerId;
  final String workerName;
  final String role;
  final String warehouse;
  final String group;
  final double allocatedTonnage;
  final DateTime assignedAt;
  final bool isCompleted;
  final DateTime? completedAt;

  const WorkloadRecord({
    required this.workerId,
    required this.workerName,
    required this.role,
    required this.warehouse,
    required this.group,
    required this.allocatedTonnage,
    required this.assignedAt,
    this.isCompleted = false,
    this.completedAt,
  });

  /// 从Map创建WorkloadRecord
  factory WorkloadRecord.fromMap(Map<String, dynamic> map) {
    return WorkloadRecord(
      workerId: map['workerId'] ?? '',
      workerName: map['workerName'] ?? '',
      role: map['role'] ?? '',
      warehouse: map['warehouse'] ?? '',
      group: map['group'] ?? '',
      allocatedTonnage: (map['allocatedTonnage'] ?? 0.0).toDouble(),
      assignedAt:
          DateTime.parse(map['assignedAt'] ?? DateTime.now().toIso8601String()),
      isCompleted: map['isCompleted'] ?? false,
      completedAt: map['completedAt'] != null
          ? DateTime.parse(map['completedAt'])
          : null,
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'workerId': workerId,
      'workerName': workerName,
      'role': role,
      'warehouse': warehouse,
      'group': group,
      'allocatedTonnage': allocatedTonnage,
      'assignedAt': assignedAt.toIso8601String(),
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  /// 标记为完成
  WorkloadRecord markCompleted() {
    return WorkloadRecord(
      workerId: workerId,
      workerName: workerName,
      role: role,
      warehouse: warehouse,
      group: group,
      allocatedTonnage: allocatedTonnage,
      assignedAt: assignedAt,
      isCompleted: true,
      completedAt: DateTime.now(),
    );
  }
}

/// 工作量统计数据模型
/// 用于汇总和分析工作量数据
class WorkloadStatistics {
  final String entityId; // 实体ID（可以是工人ID、库区名称等）
  final String entityName; // 实体名称
  final String entityType; // 实体类型：worker, warehouse, role, group
  final double totalAssignedTonnage; // 总分配吨数
  final double totalCompletedTonnage; // 总完成吨数
  final int totalTasks; // 总任务数
  final int completedTasks; // 完成任务数
  final double completionRate; // 完成率
  final DateTime periodStart; // 统计周期开始
  final DateTime periodEnd; // 统计周期结束
  final Map<String, dynamic> additionalData; // 附加数据

  const WorkloadStatistics({
    required this.entityId,
    required this.entityName,
    required this.entityType,
    required this.totalAssignedTonnage,
    required this.totalCompletedTonnage,
    required this.totalTasks,
    required this.completedTasks,
    required this.completionRate,
    required this.periodStart,
    required this.periodEnd,
    this.additionalData = const {},
  });

  /// 从Map创建WorkloadStatistics
  factory WorkloadStatistics.fromMap(Map<String, dynamic> map) {
    return WorkloadStatistics(
      entityId: map['entityId'] ?? '',
      entityName: map['entityName'] ?? '',
      entityType: map['entityType'] ?? '',
      totalAssignedTonnage: (map['totalAssignedTonnage'] ?? 0.0).toDouble(),
      totalCompletedTonnage: (map['totalCompletedTonnage'] ?? 0.0).toDouble(),
      totalTasks: map['totalTasks'] ?? 0,
      completedTasks: map['completedTasks'] ?? 0,
      completionRate: (map['completionRate'] ?? 0.0).toDouble(),
      periodStart: DateTime.parse(
          map['periodStart'] ?? DateTime.now().toIso8601String()),
      periodEnd:
          DateTime.parse(map['periodEnd'] ?? DateTime.now().toIso8601String()),
      additionalData: map['additionalData'] ?? {},
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'entityId': entityId,
      'entityName': entityName,
      'entityType': entityType,
      'totalAssignedTonnage': totalAssignedTonnage,
      'totalCompletedTonnage': totalCompletedTonnage,
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'completionRate': completionRate,
      'periodStart': periodStart.toIso8601String(),
      'periodEnd': periodEnd.toIso8601String(),
      'additionalData': additionalData,
    };
  }

  /// 计算效率评级
  String get efficiencyRating {
    if (completionRate >= 0.95) return 'A+';
    if (completionRate >= 0.85) return 'A';
    if (completionRate >= 0.75) return 'B+';
    if (completionRate >= 0.65) return 'B';
    if (completionRate >= 0.50) return 'C';
    return 'D';
  }

  /// 计算平均吨数
  double get averageTonnagePerTask {
    return totalTasks > 0 ? totalAssignedTonnage / totalTasks : 0.0;
  }
}

/// 工作量分配信息
/// 存储在任务metadata中的工作量分配数据
class WorkloadAssignment {
  final List<WorkloadRecord> records;
  final double totalTonnage;
  final int palletCount;
  final DateTime assignedAt;
  final String assignedBy;

  const WorkloadAssignment({
    required this.records,
    required this.totalTonnage,
    required this.palletCount,
    required this.assignedAt,
    this.assignedBy = 'system',
  });

  /// 从Map创建WorkloadAssignment
  factory WorkloadAssignment.fromMap(Map<String, dynamic> map) {
    final recordsList = (map['records'] as List<dynamic>? ?? [])
        .map((r) => WorkloadRecord.fromMap(r as Map<String, dynamic>))
        .toList();

    return WorkloadAssignment(
      records: recordsList,
      totalTonnage: (map['totalTonnage'] ?? 0.0).toDouble(),
      palletCount: map['palletCount'] ?? 0,
      assignedAt:
          DateTime.parse(map['assignedAt'] ?? DateTime.now().toIso8601String()),
      assignedBy: map['assignedBy'] ?? 'system',
    );
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'records': records.map((r) => r.toMap()).toList(),
      'totalTonnage': totalTonnage,
      'palletCount': palletCount,
      'assignedAt': assignedAt.toIso8601String(),
      'assignedBy': assignedBy,
    };
  }

  /// 获取参与人员数量
  int get participantCount => records.length;

  /// 获取已完成人员数量
  int get completedParticipants => records.where((r) => r.isCompleted).length;

  /// 获取完成率
  double get completionRate =>
      participantCount > 0 ? completedParticipants / participantCount : 0.0;

  /// 按角色分组
  Map<String, List<WorkloadRecord>> get recordsByRole {
    final Map<String, List<WorkloadRecord>> grouped = {};
    for (final record in records) {
      grouped.putIfAbsent(record.role, () => []).add(record);
    }
    return grouped;
  }

  /// 按库区分组
  Map<String, List<WorkloadRecord>> get recordsByWarehouse {
    final Map<String, List<WorkloadRecord>> grouped = {};
    for (final record in records) {
      grouped.putIfAbsent(record.warehouse, () => []).add(record);
    }
    return grouped;
  }
}
