import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/app_initialization_service.dart';
import 'package:loadguard/pages/config_management_page.dart';

/// 应用初始化页面
/// 展示应用启动时的初始化过程和配置数据迁移状态
class AppInitializationPage extends ConsumerWidget {
  const AppInitializationPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final initializationAsync = ref.watch(initializationProvider);
    
    return Scaffold(
      backgroundColor: Colors.blue[50],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 应用Logo和标题
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.blue[600],
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.local_shipping,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              
              Text(
                'LoadGuard',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
              const SizedBox(height: 8),
              
              Text(
                '智能装车监管系统',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.blue[600],
                ),
              ),
              const SizedBox(height: 48),
              
              // 初始化状态
              initializationAsync.when(
                data: (result) => _buildInitializationSuccess(context, ref, result),
                loading: () => _buildInitializationLoading(),
                error: (error, stackTrace) => _buildInitializationError(context, ref, error),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInitializationLoading() {
    return Column(
      children: [
        SizedBox(
          width: 60,
          height: 60,
          child: CircularProgressIndicator(
            strokeWidth: 4,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
          ),
        ),
        const SizedBox(height: 24),
        
        Text(
          '正在初始化应用...',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Colors.blue[800],
          ),
        ),
        const SizedBox(height: 12),
        
        Text(
          '正在检查和迁移配置数据',
          style: TextStyle(
            fontSize: 14,
            color: Colors.blue[600],
          ),
        ),
        const SizedBox(height: 32),
        
        // 初始化步骤指示器
        _buildInitializationSteps(),
      ],
    );
  }

  Widget _buildInitializationSteps() {
    final steps = [
      '初始化数据存储',
      '检查配置数据',
      '执行数据迁移',
      '验证核心配置',
      '启动应用服务',
    ];
    
    return Column(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  step,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.blue[700],
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildInitializationSuccess(BuildContext context, WidgetRef ref, InitializationResult result) {
    return Column(
      children: [
        // 成功图标
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.green[100],
            borderRadius: BorderRadius.circular(40),
          ),
          child: Icon(
            Icons.check_circle,
            size: 50,
            color: Colors.green[600],
          ),
        ),
        const SizedBox(height: 24),
        
        Text(
          '初始化完成',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.green[800],
          ),
        ),
        const SizedBox(height: 12),
        
        Text(
          '耗时: ${result.duration}ms',
          style: TextStyle(
            fontSize: 14,
            color: Colors.green[600],
          ),
        ),
        const SizedBox(height: 24),
        
        // 初始化步骤列表
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '初始化步骤:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              ...result.steps.map((step) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.green[600],
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        step,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ),
        const SizedBox(height: 32),
        
        // 操作按钮
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const ConfigManagementPage(),
                    ),
                  );
                },
                icon: const Icon(Icons.settings),
                label: const Text('查看配置'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // 这里可以导航到主页面
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('进入主页面功能待实现')),
                  );
                },
                icon: const Icon(Icons.home),
                label: const Text('进入主页'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // 初始化摘要
        _buildInitializationSummary(ref),
      ],
    );
  }

  Widget _buildInitializationError(BuildContext context, WidgetRef ref, Object error) {
    return Column(
      children: [
        // 错误图标
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.red[100],
            borderRadius: BorderRadius.circular(40),
          ),
          child: Icon(
            Icons.error,
            size: 50,
            color: Colors.red[600],
          ),
        ),
        const SizedBox(height: 24),
        
        Text(
          '初始化失败',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.red[800],
          ),
        ),
        const SizedBox(height: 12),
        
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Text(
            error.toString(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.red[800],
            ),
          ),
        ),
        const SizedBox(height: 32),
        
        // 重试按钮
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              ref.invalidate(initializationProvider);
            },
            icon: const Icon(Icons.refresh),
            label: const Text('重试初始化'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // 跳过初始化按钮
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const ConfigManagementPage(),
                ),
              );
            },
            icon: const Icon(Icons.skip_next),
            label: const Text('跳过初始化'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey[600],
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInitializationSummary(WidgetRef ref) {
    final summaryAsync = ref.watch(initializationSummaryProvider);
    
    return summaryAsync.when(
      data: (summary) => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '配置摘要:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.blue[800],
              ),
            ),
            const SizedBox(height: 8),
            if (summary.containsKey('workers'))
              Text(
                '工作人员: ${summary['workers']['active']}/${summary['workers']['total']} 活跃',
                style: TextStyle(fontSize: 12, color: Colors.blue[700]),
              ),
            if (summary.containsKey('warehouses'))
              Text(
                '仓库: ${summary['warehouses']['active']}/${summary['warehouses']['total']} 活跃',
                style: TextStyle(fontSize: 12, color: Colors.blue[700]),
              ),
            if (summary.containsKey('templates'))
              Text(
                '模板: ${summary['templates']['active']}/${summary['templates']['total']} 活跃',
                style: TextStyle(fontSize: 12, color: Colors.blue[700]),
              ),
          ],
        ),
      ),
      loading: () => const SizedBox.shrink(),
      error: (error, stackTrace) => const SizedBox.shrink(),
    );
  }
}
