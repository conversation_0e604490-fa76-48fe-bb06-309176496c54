import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/product_database.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/task_service.dart';
import 'package:loadguard/services/workload_assignment_service.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/worker_model.dart';

void main() {
  group('🚀 LoadGuard系统最终集成测试', () {
    late TaskService taskService;
    late MLKitTextRecognitionService mlkitService;
    late WorkloadAssignmentService workloadService;
    
    setUp(() {
      taskService = TaskService();
      mlkitService = MLKitTextRecognitionService();
      workloadService = WorkloadAssignmentService();
    });
    
    tearDown(() {
      taskService.dispose();
      mlkitService.dispose();
    });
    
    group('📊 产品数据库集成测试', () {
      test('验证新增的PP-1100N系列牌号', () {
        // 验证PP-1100N
        expect(ProductDatabase.hasProduct('PP-1100N'), true);
        final pp1100n = ProductDatabase.presetProducts['PP-1100N']!;
        expect(pp1100n.code, 'PP-1100N');
        expect(pp1100n.name, '聚丙烯注塑料');
        expect(pp1100n.category, 'PP');
        expect(pp1100n.priority, 7);
        
        // 验证PP-1100N-GD
        expect(ProductDatabase.hasProduct('PP-1100N-GD'), true);
        final pp1100nGd = ProductDatabase.presetProducts['PP-1100N-GD']!;
        expect(pp1100nGd.code, 'PP-1100N-GD');
        expect(pp1100nGd.name, '聚丙烯注塑料(过渡料)');
        expect(pp1100nGd.category, 'PP');
        expect(pp1100nGd.priority, 7);
      });
      
      test('验证新增的HD-5502S系列牌号', () {
        // 验证HD-5502S
        expect(ProductDatabase.hasProduct('HD-5502S'), true);
        final hd5502s = ProductDatabase.presetProducts['HD-5502S']!;
        expect(hd5502s.code, 'HD-5502S');
        expect(hd5502s.name, '高密度聚乙烯');
        expect(hd5502s.category, 'HDPE');
        expect(hd5502s.priority, 7);
        
        // 验证HD-5502S-GD
        expect(ProductDatabase.hasProduct('HD-5502S-GD'), true);
        final hd5502sGd = ProductDatabase.presetProducts['HD-5502S-GD']!;
        expect(hd5502sGd.code, 'HD-5502S-GD');
        expect(hd5502sGd.name, '高密度聚乙烯(过渡料)');
        expect(hd5502sGd.category, 'HDPE');
        expect(hd5502sGd.priority, 7);
      });
      
      test('验证产品总数正确', () {
        final totalProducts = ProductDatabase.presetProducts.length;
        // 原来34个 + PP-1100N系列2个 + HD-5502S系列2个 = 38个
        expect(totalProducts, 38);
      });
      
      test('验证热门产品列表包含新增牌号', () {
        final popularProducts = ProductDatabase.getPopularProducts();
        final popularCodes = popularProducts.map((p) => p.code).toList();
        
        // 新增的牌号都是优先级7，应该在热门产品中
        expect(popularCodes, contains('PP-1100N'));
        expect(popularCodes, contains('PP-1100N-GD'));
        expect(popularCodes, contains('HD-5502S'));
        expect(popularCodes, contains('HD-5502S-GD'));
      });
    });
    
    group('🤖 ML Kit V2 0.15.0集成测试', () {
      test('ML Kit服务初始化正常', () async {
        await mlkitService.initialize();
        expect(mlkitService, isNotNull);
      });
      
      test('验证12种算法可用', () async {
        await mlkitService.initialize();
        
        final algorithms = mlkitService.getAvailableAlgorithms();
        expect(algorithms.length, 12);
        
        // 验证关键算法存在
        expect(algorithms, contains(RecognitionAlgorithm.standardText));
        expect(algorithms, contains(RecognitionAlgorithm.chineseOptimized));
        expect(algorithms, contains(RecognitionAlgorithm.numberFocused));
        expect(algorithms, contains(RecognitionAlgorithm.enhancedAccuracy));
      });
      
      test('算法性能指标正常', () async {
        await mlkitService.initialize();
        
        for (final algorithm in RecognitionAlgorithm.values) {
          final performance = mlkitService.getAlgorithmPerformance(algorithm);
          expect(performance.accuracy, greaterThan(0.0));
          expect(performance.accuracy, lessThanOrEqualTo(1.0));
          expect(performance.speed, greaterThan(0.0));
          expect(performance.speed, lessThanOrEqualTo(1.0));
        }
      });
      
      test('性能统计功能正常', () async {
        await mlkitService.initialize();
        
        final stats = mlkitService.getMLKitPerformanceStats();
        expect(stats, isNotNull);
        expect(stats.totalRecognitions, greaterThanOrEqualTo(0));
        expect(stats.averageRecognitionTime, greaterThanOrEqualTo(0.0));
        expect(stats.cacheHitRate, greaterThanOrEqualTo(0.0));
      });
    });
    
    group('📋 任务管理集成测试', () {
      test('TaskService基本功能正常', () {
        expect(taskService.tasks, isNotNull);
        expect(taskService.tasks, isEmpty); // 初始状态为空
      });
      
      test('创建任务功能正常', () {
        final task = TaskModel(
          id: 'test-task-001',
          templateName: '测试模板',
          warehouseName: '测试仓库',
          workerName: '测试工人',
          batches: [
            BatchInfo(
              id: 'batch-001',
              productCode: 'PP-1100N', // 使用新增的牌号
              batchNumber: '250615A12345',
              plannedQuantity: 100,
            ),
          ],
          createdAt: DateTime.now(),
        );
        
        taskService.addTask(task);
        expect(taskService.tasks.length, 1);
        expect(taskService.tasks.first.id, 'test-task-001');
        expect(taskService.tasks.first.batches.first.productCode, 'PP-1100N');
      });
      
      test('任务状态管理正常', () {
        final task = TaskModel(
          id: 'test-task-002',
          templateName: '测试模板',
          warehouseName: '测试仓库',
          workerName: '测试工人',
          batches: [
            BatchInfo(
              id: 'batch-002',
              productCode: 'HD-5502S', // 使用新增的牌号
              batchNumber: '250618B67890',
              plannedQuantity: 200,
            ),
          ],
          createdAt: DateTime.now(),
        );
        
        taskService.addTask(task);
        
        // 更新任务状态
        taskService.updateTaskStatus(task.id, TaskStatus.inProgress);
        final updatedTask = taskService.getTaskById(task.id);
        expect(updatedTask?.status, TaskStatus.inProgress);
      });
    });
    
    group('👥 工作量统计集成测试', () {
      test('WorkloadAssignmentService基本功能', () {
        expect(workloadService, isNotNull);
      });
      
      test('工作人员数据正常', () {
        final workers = workloadService.getAllWorkers();
        expect(workers, isNotEmpty);
        
        // 验证工作人员数据结构
        for (final worker in workers.take(5)) {
          expect(worker.name, isNotEmpty);
          expect(worker.id, isNotEmpty);
          expect(worker.department, isNotEmpty);
        }
      });
      
      test('工作量计算功能正常', () {
        // 创建测试任务
        final task = TaskModel(
          id: 'workload-test-001',
          templateName: '工作量测试',
          warehouseName: '测试仓库',
          workerName: '张三',
          batches: [
            BatchInfo(
              id: 'batch-workload-001',
              productCode: 'PP-1100N',
              batchNumber: '250615C11111',
              plannedQuantity: 150,
              actualQuantity: 150,
            ),
          ],
          status: TaskStatus.completed,
          createdAt: DateTime.now(),
          completedAt: DateTime.now(),
        );
        
        taskService.addTask(task);
        
        // 计算工作量
        final workload = workloadService.calculateWorkerWorkload('张三', DateTime.now());
        expect(workload, isNotNull);
      });
    });
    
    group('🔤 中文字符编码测试', () {
      test('产品名称中文显示正常', () {
        final products = [
          ProductDatabase.presetProducts['PP-1100N']!,
          ProductDatabase.presetProducts['HD-5502S']!,
          ProductDatabase.presetProducts['LLD-7042']!,
        ];
        
        for (final product in products) {
          expect(product.name, isNotEmpty);
          // 验证中文字符正常
          expect(product.name.contains('聚'), true);
        }
      });
      
      test('工作人员姓名中文显示正常', () {
        final workers = workloadService.getAllWorkers();
        final chineseWorkers = workers.where((w) => 
            w.name.contains('张') || 
            w.name.contains('李') || 
            w.name.contains('王')).toList();
        
        expect(chineseWorkers, isNotEmpty);
        
        for (final worker in chineseWorkers.take(3)) {
          expect(worker.name, isNotEmpty);
          expect(worker.name.length, greaterThan(1));
        }
      });
      
      test('任务模板中文显示正常', () {
        final task = TaskModel(
          id: 'chinese-test-001',
          templateName: '中文模板测试',
          warehouseName: '北京仓库',
          workerName: '李明',
          batches: [
            BatchInfo(
              id: 'chinese-batch-001',
              productCode: 'PP-1100N',
              batchNumber: '250615D22222',
              plannedQuantity: 100,
            ),
          ],
          createdAt: DateTime.now(),
        );
        
        taskService.addTask(task);
        
        final savedTask = taskService.getTaskById(task.id);
        expect(savedTask?.templateName, '中文模板测试');
        expect(savedTask?.warehouseName, '北京仓库');
        expect(savedTask?.workerName, '李明');
      });
    });
    
    group('🔄 数据一致性测试', () {
      test('任务数据持久化一致性', () {
        final initialCount = taskService.tasks.length;
        
        final task = TaskModel(
          id: 'consistency-test-001',
          templateName: '一致性测试',
          warehouseName: '测试仓库',
          workerName: '测试工人',
          batches: [
            BatchInfo(
              id: 'consistency-batch-001',
              productCode: 'HD-5502S-GD',
              batchNumber: '250620E33333',
              plannedQuantity: 300,
            ),
          ],
          createdAt: DateTime.now(),
        );
        
        taskService.addTask(task);
        expect(taskService.tasks.length, initialCount + 1);
        
        // 验证数据完整性
        final savedTask = taskService.getTaskById(task.id);
        expect(savedTask, isNotNull);
        expect(savedTask!.id, task.id);
        expect(savedTask.batches.length, 1);
        expect(savedTask.batches.first.productCode, 'HD-5502S-GD');
      });
      
      test('产品数据库一致性', () {
        // 验证所有产品都有完整信息
        for (final product in ProductDatabase.presetProducts.values) {
          expect(product.code, isNotEmpty);
          expect(product.name, isNotEmpty);
          expect(product.category, isNotEmpty);
          expect(product.priority, greaterThan(0));
          expect(product.priority, lessThanOrEqualTo(10));
        }
      });
    });
    
    group('⚡ 性能验证测试', () {
      test('大量任务处理性能', () {
        final stopwatch = Stopwatch()..start();
        
        // 创建100个测试任务
        for (int i = 0; i < 100; i++) {
          final task = TaskModel(
            id: 'perf-test-$i',
            templateName: '性能测试$i',
            warehouseName: '测试仓库',
            workerName: '测试工人$i',
            batches: [
              BatchInfo(
                id: 'perf-batch-$i',
                productCode: i % 2 == 0 ? 'PP-1100N' : 'HD-5502S',
                batchNumber: '25061${i.toString().padLeft(2, '0')}A${i.toString().padLeft(5, '0')}',
                plannedQuantity: 100 + i,
              ),
            ],
            createdAt: DateTime.now(),
          );
          
          taskService.addTask(task);
        }
        
        stopwatch.stop();
        
        expect(taskService.tasks.length, greaterThanOrEqualTo(100));
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // 应该在1秒内完成
      });
      
      test('产品查询性能', () {
        final stopwatch = Stopwatch()..start();
        
        // 执行1000次产品查询
        for (int i = 0; i < 1000; i++) {
          final productCodes = ['PP-1100N', 'HD-5502S', 'LLD-7042', 'HD-5000S'];
          final code = productCodes[i % productCodes.length];
          
          final hasProduct = ProductDatabase.hasProduct(code);
          expect(hasProduct, true);
        }
        
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // 应该在100ms内完成
      });
    });
  });
}
