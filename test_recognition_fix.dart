import 'dart:io';
import 'package:loadguard/services/unified_recognition_service.dart';
import 'package:loadguard/utils/blue_background_processor.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🧪 测试修复后的识别效果
/// 
/// 这个脚本将测试样本照片的识别情况，验证：
/// 1. 蓝色背景检测是否正常工作
/// 2. 图像预处理是否被正确调用
/// 3. 识别准确率是否提升
void main() async {
  print('🧪 开始测试修复后的识别效果...\n');
  
  // 测试照片列表（基于用户提供的样本）
  final testImages = [
    'D:\\weishi\\photos\\wechat_2025-08-04_165936_949.png', // 蓝色背景
    'D:\\weishi\\photos\\wechat_2025-08-04_165948_105.png', // 蓝色背景
    'D:\\weishi\\photos\\wechat_2025-08-04_170153_138.png', // 灰色背景
    'D:\\weishi\\photos\\wechat_2025-08-04_170240_913.png', // 白色标签
    'D:\\weishi\\photos\\wechat_2025-08-04_170251_156.png', // 蓝色背景
  ];
  
  final recognitionService = UnifiedRecognitionService.instance;
  await recognitionService.initialize();
  
  int successCount = 0;
  int totalCount = testImages.length;
  
  for (int i = 0; i < testImages.length; i++) {
    final imagePath = testImages[i];
    final fileName = imagePath.split('\\').last;
    
    print('📸 测试图片 ${i + 1}/$totalCount: $fileName');
    
    if (!await File(imagePath).exists()) {
      print('❌ 文件不存在，跳过');
      continue;
    }
    
    try {
      // 1. 测试蓝色背景检测
      final isBlue = await BlueBackgroundProcessor.isBlueBackground(imagePath);
      print('   🔵 蓝色背景检测: ${isBlue ? "是" : "否"}');
      
      // 2. 执行统一识别
      final result = await recognitionService.recognizeUnified(
        imagePath,
        enableQRCode: true,
        enableNetworkValidation: false,
        onProgress: (progress, status) {
          if (progress == 1.0) {
            print('   ⚡ $status (${(progress * 100).toInt()}%)');
          }
        },
      );
      
      // 3. 分析识别结果
      print('   📊 识别结果:');
      print('     - 文字识别: ${result.hasText ? "成功" : "失败"}');
      print('     - 二维码识别: ${result.hasQRCode ? "成功" : "失败"}');
      print('     - 综合置信度: ${result.overallConfidence.toStringAsFixed(1)}%');
      print('     - 处理时间: ${result.processingTime}ms');
      
      if (result.hasText) {
        print('   📝 识别到的文字:');
        for (final textResult in result.textResults) {
          if (textResult.ocrText != null && textResult.ocrText!.isNotEmpty) {
            print('     "${textResult.ocrText}" (置信度: ${textResult.confidence?.toStringAsFixed(1) ?? "N/A"}%)');
          }
        }
      }
      
      if (result.hasQRCode) {
        print('   📱 识别到的二维码:');
        for (final qrResult in result.qrResults) {
          if (qrResult.qrCode != null && qrResult.qrCode!.isNotEmpty) {
            print('     "${qrResult.qrCode}"');
          }
        }
      }
      
      // 4. 检查是否成功识别关键信息（LLD-7042, 250712F20440）
      final bestInfo = result.getBestProductInfo();
      final hasProductCode = bestInfo.containsKey('productCode') || 
                           result.textResults.any((r) => r.ocrText?.contains('LLD-7042') == true);
      final hasBatchNumber = bestInfo.containsKey('batchNumber') || 
                           result.textResults.any((r) => r.ocrText?.contains('250712F20440') == true);
      
      if (hasProductCode || hasBatchNumber) {
        successCount++;
        print('   ✅ 成功识别关键信息');
      } else {
        print('   ❌ 未能识别关键信息');
      }
      
      // 显示预处理后的图像路径
      if (result.preprocessedImagePath != imagePath) {
        print('   🔧 已应用图像预处理: ${result.preprocessedImagePath.split('\\').last}');
      }
      
    } catch (e) {
      print('   ❌ 识别失败: $e');
    }
    
    print(''); // 空行分割
  }
  
  // 最终统计
  print('📈 测试完成！');
  print('总体识别成功率: ${successCount}/$totalCount (${(successCount / totalCount * 100).toStringAsFixed(1)}%)');
  
  if (successCount >= totalCount * 0.8) {
    print('🎉 修复效果良好！识别率显著提升');
  } else if (successCount >= totalCount * 0.6) {
    print('👍 修复有效，但仍有改进空间');
  } else {
    print('⚠️ 需要进一步优化算法参数');
  }
}