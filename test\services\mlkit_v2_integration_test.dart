import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/services/mlkit_text_recognition_service.dart';
import 'package:loadguard/services/mlkit_performance_optimizer.dart';
import 'package:loadguard/services/mlkit_algorithm_manager.dart';
// ✅ 更新：使用统一的性能管理器
import 'package:loadguard/services/performance_manager.dart';

void main() {
  group('ML Kit V2 0.15.0 Integration Tests', () {
    late MLKitTextRecognitionService recognitionService;
    late MLKitPerformanceOptimizer performanceOptimizer;
    late MLKitAlgorithmManager algorithmManager;
    
    setUp(() {
      recognitionService = MLKitTextRecognitionService();
      performanceOptimizer = MLKitPerformanceOptimizer.instance;
      algorithmManager = MLKitAlgorithmManager.instance;
    });
    
    tearDown(() {
      recognitionService.dispose();
    });
    
    group('🚀 Performance Optimizer Tests', () {
      test('should initialize performance optimizer correctly', () {
        performanceOptimizer.initialize();
        
        final stats = performanceOptimizer.getPerformanceStats();
        expect(stats, isNotNull);
        expect(stats.totalRecognitions, 0);
        expect(stats.cacheSize, 0);
        expect(stats.recognizerPoolSize, 0);
      });
      
      test('should provide performance statistics', () {
        performanceOptimizer.initialize();
        
        final stats = performanceOptimizer.getPerformanceStats();
        expect(stats.totalRecognitions, greaterThanOrEqualTo(0));
        expect(stats.averageRecognitionTime, greaterThanOrEqualTo(0.0));
        expect(stats.cacheHitRate, greaterThanOrEqualTo(0.0));
        expect(stats.memoryUsage, greaterThanOrEqualTo(0));
      });
      
      test('should optimize memory correctly', () async {
        performanceOptimizer.initialize();
        
        // 执行内存优化
        await performanceOptimizer.optimizeMemory();
        
        // 验证优化后的状态
        final stats = performanceOptimizer.getPerformanceStats();
        expect(stats, isNotNull);
      });
      
      test('should handle semaphore correctly', () async {
        final semaphore = Semaphore(2);
        
        // 测试获取和释放
        await semaphore.acquire();
        await semaphore.acquire();
        
        // 第三次获取应该等待
        bool completed = false;
        semaphore.acquire().then((_) => completed = true);
        
        await Future.delayed(const Duration(milliseconds: 10));
        expect(completed, false);
        
        // 释放一个，第三次获取应该完成
        semaphore.release();
        await Future.delayed(const Duration(milliseconds: 10));
        expect(completed, true);
      });
    });
    
    group('🤖 Algorithm Manager Tests', () {
      test('should initialize algorithm manager correctly', () {
        algorithmManager.initialize();
        
        final algorithms = algorithmManager.getAvailableAlgorithms();
        expect(algorithms.length, 12);
        expect(algorithms, contains(RecognitionAlgorithm.standardText));
        expect(algorithms, contains(RecognitionAlgorithm.chineseOptimized));
        expect(algorithms, contains(RecognitionAlgorithm.numberFocused));
      });
      
      test('should provide algorithm descriptions', () {
        algorithmManager.initialize();
        
        for (final algorithm in RecognitionAlgorithm.values) {
          final description = algorithmManager.getAlgorithmDescription(algorithm);
          expect(description, isNotEmpty);
          expect(description.length, greaterThan(20));
        }
      });
      
      test('should provide algorithm performance metrics', () {
        algorithmManager.initialize();
        
        for (final algorithm in RecognitionAlgorithm.values) {
          final performance = algorithmManager.getAlgorithmPerformance(algorithm);
          expect(performance.accuracy, greaterThan(0.0));
          expect(performance.accuracy, lessThanOrEqualTo(1.0));
          expect(performance.speed, greaterThan(0.0));
          expect(performance.speed, lessThanOrEqualTo(1.0));
          expect(performance.memoryUsage, greaterThan(0.0));
          expect(performance.memoryUsage, lessThanOrEqualTo(1.0));
        }
      });
      
      test('should rank algorithms correctly', () {
        algorithmManager.initialize();
        
        final byAccuracy = algorithmManager.getAlgorithmsByAccuracy();
        expect(byAccuracy.length, 12);
        
        // 验证排序正确性
        for (int i = 0; i < byAccuracy.length - 1; i++) {
          final current = algorithmManager.getAlgorithmPerformance(byAccuracy[i]);
          final next = algorithmManager.getAlgorithmPerformance(byAccuracy[i + 1]);
          expect(current.accuracy, greaterThanOrEqualTo(next.accuracy));
        }
        
        final bySpeed = algorithmManager.getAlgorithmsBySpeed();
        expect(bySpeed.length, 12);
        
        for (int i = 0; i < bySpeed.length - 1; i++) {
          final current = algorithmManager.getAlgorithmPerformance(bySpeed[i]);
          final next = algorithmManager.getAlgorithmPerformance(bySpeed[i + 1]);
          expect(current.speed, greaterThanOrEqualTo(next.speed));
        }
      });
      
      test('should select optimal algorithm based on characteristics', () {
        algorithmManager.initialize();
        
        // 测试数字内容
        final numberImage = ImageCharacteristics(
          quality: ImageQuality.high,
          lighting: LightingCondition.good,
          contentType: ContentType.numbers,
          language: LanguageHint.chinese,
        );
        
        final numberAlgorithm = algorithmManager.selectOptimalAlgorithm(numberImage);
        expect(numberAlgorithm, RecognitionAlgorithm.numberFocused);
        
        // 测试手写文本
        final handwrittenImage = ImageCharacteristics(
          quality: ImageQuality.medium,
          lighting: LightingCondition.good,
          contentType: ContentType.handwritten,
          language: LanguageHint.chinese,
        );
        
        final handwrittenAlgorithm = algorithmManager.selectOptimalAlgorithm(handwrittenImage);
        expect(handwrittenAlgorithm, RecognitionAlgorithm.handwrittenText);
        
        // 测试低光照
        final lowLightImage = ImageCharacteristics(
          quality: ImageQuality.medium,
          lighting: LightingCondition.poor,
          contentType: ContentType.text,
          language: LanguageHint.chinese,
        );
        
        final lowLightAlgorithm = algorithmManager.selectOptimalAlgorithm(lowLightImage);
        expect(lowLightAlgorithm, RecognitionAlgorithm.lowLight);
      });
      
      test('should provide fallback algorithms', () {
        algorithmManager.initialize();
        
        for (final algorithm in RecognitionAlgorithm.values) {
          final fallbacks = algorithmManager.getFallbackAlgorithms(algorithm);
          expect(fallbacks, isNotEmpty);
          expect(fallbacks.length, lessThanOrEqualTo(3));
          expect(fallbacks, isNot(contains(algorithm)));
        }
      });
      
      test('should create optimized recognizers', () {
        algorithmManager.initialize();
        
        for (final algorithm in RecognitionAlgorithm.values) {
          final recognizer = algorithmManager.createOptimizedRecognizer(algorithm);
          expect(recognizer, isNotNull);
          
          // 清理资源
          recognizer.close();
        }
      });
    });
    
    group('🔗 Service Integration Tests', () {
      test('should integrate all components correctly', () async {
        await recognitionService.initialize();
        
        // 验证服务已初始化
        expect(recognitionService, isNotNull);
        
        // 验证可以获取算法列表
        final algorithms = recognitionService.getAvailableAlgorithms();
        expect(algorithms.length, 12);
        
        // 验证可以获取性能统计
        final stats = recognitionService.getMLKitPerformanceStats();
        expect(stats, isNotNull);
      });
      
      test('should provide algorithm information through service', () async {
        await recognitionService.initialize();
        
        for (final algorithm in RecognitionAlgorithm.values) {
          final description = recognitionService.getAlgorithmDescription(algorithm);
          expect(description, isNotEmpty);
          
          final performance = recognitionService.getAlgorithmPerformance(algorithm);
          expect(performance.accuracy, greaterThan(0.0));
        }
      });
      
      test('should handle memory optimization through service', () async {
        await recognitionService.initialize();
        
        // 执行内存优化
        await recognitionService.optimizeMemory();
        
        // 验证优化完成
        final stats = recognitionService.getMLKitPerformanceStats();
        expect(stats, isNotNull);
      });
    });
    
    group('📊 Performance Metrics Tests', () {
      test('should track performance correctly', () {
        final stats = MLKitPerformanceStats(
          totalRecognitions: 100,
          averageRecognitionTime: 450.0,
          cacheHitRate: 0.85,
          memoryUsage: 45,
          recognizerPoolSize: 3,
          cacheSize: 25,
        );
        
        expect(stats.totalRecognitions, 100);
        expect(stats.averageRecognitionTime, 450.0);
        expect(stats.cacheHitRate, 0.85);
        expect(stats.memoryUsage, 45);
        expect(stats.recognizerPoolSize, 3);
        expect(stats.cacheSize, 25);
        
        final string = stats.toString();
        expect(string, contains('100'));
        expect(string, contains('450.0ms'));
        expect(string, contains('85.0%'));
        expect(string, contains('45MB'));
      });
      
      test('should create empty performance stats', () {
        final emptyStats = MLKitPerformanceStats.empty();
        
        expect(emptyStats.totalRecognitions, 0);
        expect(emptyStats.averageRecognitionTime, 0.0);
        expect(emptyStats.cacheHitRate, 0.0);
        expect(emptyStats.memoryUsage, 0);
        expect(emptyStats.recognizerPoolSize, 0);
        expect(emptyStats.cacheSize, 0);
      });
    });
    
    group('🎯 Algorithm Configuration Tests', () {
      test('should create algorithm configurations correctly', () {
        final config = AlgorithmConfig(
          algorithm: RecognitionAlgorithm.enhancedAccuracy,
          timeout: const Duration(seconds: 5),
          maxRetries: 3,
          confidenceThreshold: 0.9,
        );
        
        expect(config.algorithm, RecognitionAlgorithm.enhancedAccuracy);
        expect(config.timeout, const Duration(seconds: 5));
        expect(config.maxRetries, 3);
        expect(config.confidenceThreshold, 0.9);
      });
      
      test('should create default algorithm configuration', () {
        final defaultConfig = AlgorithmConfig.defaultConfig();
        
        expect(defaultConfig.algorithm, RecognitionAlgorithm.standardText);
        expect(defaultConfig.timeout, const Duration(seconds: 3));
        expect(defaultConfig.maxRetries, 2);
        expect(defaultConfig.confidenceThreshold, 0.8);
      });
      
      test('should create algorithm performance metrics correctly', () {
        final performance = AlgorithmPerformance(
          accuracy: 0.95,
          speed: 0.85,
          memoryUsage: 0.7,
        );
        
        expect(performance.accuracy, 0.95);
        expect(performance.speed, 0.85);
        expect(performance.memoryUsage, 0.7);
      });
      
      test('should create default algorithm performance', () {
        final defaultPerformance = AlgorithmPerformance.defaultPerformance();
        
        expect(defaultPerformance.accuracy, 0.9);
        expect(defaultPerformance.speed, 0.8);
        expect(defaultPerformance.memoryUsage, 0.6);
      });
    });
    
    group('🔍 Image Characteristics Tests', () {
      test('should create image characteristics correctly', () {
        final characteristics = ImageCharacteristics(
          quality: ImageQuality.high,
          lighting: LightingCondition.good,
          contentType: ContentType.mixed,
          language: LanguageHint.chinese,
        );
        
        expect(characteristics.quality, ImageQuality.high);
        expect(characteristics.lighting, LightingCondition.good);
        expect(characteristics.contentType, ContentType.mixed);
        expect(characteristics.language, LanguageHint.chinese);
      });
      
      test('should have all expected enum values', () {
        expect(ImageQuality.values.length, 3);
        expect(LightingCondition.values.length, 3);
        expect(ContentType.values.length, 6);
        expect(LanguageHint.values.length, 3);
        expect(RecognitionAlgorithm.values.length, 12);
      });
    });
  });
}
