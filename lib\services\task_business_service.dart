import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/models/template_config.dart';
import 'package:loadguard/models/worker_info_data.dart';
import 'package:loadguard/models/task_model_extensions.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/services/task_data_service.dart';
import 'package:loadguard/services/task_validation_service.dart';
import 'package:loadguard/services/task_event_service.dart';

/// 🎯 【任务业务逻辑服务】
///
/// 【核心职责】专注于纯业务逻辑处理，不涉及数据存储和UI状态管理
/// 【设计原则】遵循单一职责原则，只处理业务规则和流程
/// 【架构位置】位于业务逻辑层，连接表现层和数据访问层
///
/// 🔧 【主要功能】：
/// 1. 任务创建和验证 - 确保任务数据的完整性和有效性
/// 2. 任务状态管理 - 处理任务生命周期中的状态转换
/// 3. 业务规则执行 - 实施工业标签识别的业务逻辑
/// 4. 事件通知处理 - 协调任务相关的事件和通知
///
/// 📊 【依赖服务】：
/// - TaskDataService: 数据访问服务
/// - TaskValidationService: 数据验证服务
/// - TaskEventService: 事件通知服务
class TaskBusinessService {
  final TaskDataService _dataService;
  final TaskValidationService _validationService;
  final TaskEventService _eventService;

  TaskBusinessService({
    TaskDataService? dataService,
    TaskValidationService? validationService,
    TaskEventService? eventService,
  }) : _dataService = dataService ?? TaskDataService(),
       _validationService = validationService ?? TaskValidationService(),
       _eventService = eventService ?? TaskEventService();

  /// 初始化服务
  Future<void> initialize() async {
    await _dataService.initialize();
  }

  /// 创建单批次任务
  Future<TaskModel> createSingleBatchTask({
    required String template,
    required String productCode,
    required String batchNumber,
    required int quantity,
    List<String>? participants,
  }) async {
    try {
      // 创建任务
      final task = TaskModel(
        template: template,
        productCode: productCode,
        batchNumber: batchNumber,
        quantity: quantity,
        participants: participants ?? [],
      );

      // 初始化照片配置
      await _initializeTaskPhotos(task);

      // 附加工作量分配数据
      await _attachWorkloadData(task, participants);

      // 保存任务
      await _dataService.saveTask(task);

      AppLogger.info('✅ 创建单批次任务成功: ${task.id}');
      return task;
    } catch (e) {
      AppLogger.error('❌ 创建单批次任务失败: $e');
      rethrow;
    }
  }

  /// 创建混装任务
  Future<TaskModel> createMixedLoadTask({
    required String template,
    required List<BatchInfo> batches,
    List<String>? participants,
  }) async {
    try {
      // 创建混装任务
      final task = TaskModel(
        template: template,
        batches: batches,
        participants: participants ?? [],
      );

      // 初始化照片配置
      await _initializeTaskPhotos(task);

      // 附加工作量分配数据
      await _attachWorkloadData(task, participants);

      // 保存任务
      await _dataService.saveTask(task);

      AppLogger.info('✅ 创建混装任务成功: ${task.id}');
      return task;
    } catch (e) {
      AppLogger.error('❌ 创建混装任务失败: $e');
      rethrow;
    }
  }

  /// 完成任务
  Future<void> completeTask(TaskModel task) async {
    try {
      // 设置完成时间
      task.completedAt = DateTime.now();

      // 记录个人工作量历史
      await _recordPersonalWorkloadHistory(task);

      // 保存任务
      await _dataService.saveTask(task);

      AppLogger.info('✅ 任务完成: ${task.id}');
    } catch (e) {
      AppLogger.error('❌ 完成任务失败: $e');
      rethrow;
    }
  }

  /// 更新任务照片
  Future<void> updateTaskPhoto(TaskModel task, PhotoItem photo) async {
    try {
      // 查找并更新照片
      final photoIndex = task.photos.indexWhere((p) => p.id == photo.id);
      if (photoIndex >= 0) {
        task.photos[photoIndex] = photo;
      } else {
        task.photos.add(photo);
      }

      // 保存任务
      await _dataService.saveTask(task);

      AppLogger.info('✅ 更新任务照片成功: ${task.id} - ${photo.id}');
    } catch (e) {
      AppLogger.error('❌ 更新任务照片失败: $e');
      rethrow;
    }
  }

  /// 获取任务的必拍照片数量
  int getRequiredPhotosCount(TaskModel task) {
    return task.photos.where((p) => p.isRequired).length;
  }

  /// 获取任务的已拍必拍照片数量
  int getCompletedRequiredPhotosCount(TaskModel task) {
    return task.photos.where((p) => p.isRequired && p.imagePath != null).length;
  }

  /// 检查任务是否可以完成
  bool canCompleteTask(TaskModel task) {
    final requiredPhotos = task.photos.where((p) => p.isRequired);
    final completedPhotos = requiredPhotos.where((p) => p.imagePath != null && p.imagePath!.isNotEmpty);
    return requiredPhotos.isNotEmpty && completedPhotos.length >= requiredPhotos.length;
  }

  /// 获取任务进度
  double getTaskProgress(TaskModel task) {
    final requiredPhotos = task.photos.where((p) => p.isRequired);
    if (requiredPhotos.isEmpty) return 0.0;
    
    final completedPhotos = requiredPhotos.where((p) => p.imagePath != null && p.imagePath!.isNotEmpty);
    return completedPhotos.length / requiredPhotos.length;
  }

  /// 验证任务数据
  bool validateTaskData(TaskModel task) {
    // 基本验证
    if (task.template.isEmpty) return false;
    if (task.batches.isEmpty) return false;
    
    // 批次验证
    for (final batch in task.batches) {
      if (batch.productCode.isEmpty || batch.batchNumber.isEmpty) {
        return false;
      }
      if (batch.plannedQuantity <= 0) return false;
    }
    
    return true;
  }

  /// 初始化任务照片配置
  Future<void> _initializeTaskPhotos(TaskModel task) async {
    try {
      final photoConfigs = TemplateConfig.getPhotoConfigs(task.template);
      
      for (final config in photoConfigs) {
        final photo = PhotoItem(
          id: config.id,
          label: config.label,
          configId: config.id,
          isRequired: config.isRequired,
          needRecognition: config.needRecognition,
        );
        task.photos.add(photo);
      }
      
      AppLogger.info('📸 初始化任务照片配置: ${photoConfigs.length}张');
    } catch (e) {
      AppLogger.error('❌ 初始化任务照片配置失败: $e');
    }
  }

  /// 附加工作量分配数据
  Future<void> _attachWorkloadData(TaskModel task, List<String>? participants) async {
    try {
      // TODO: 实现工作量分配服务
      // final assignment = await WorkloadAssignmentService.loadCurrentAssignment();
      // if (assignment != null) {
      //   task.recognitionMetadata ??= {};
      //   task.recognitionMetadata!['workload'] = assignment.toMap();
      //   task.recognitionMetadata!['workloadAssignedAt'] = DateTime.now().toIso8601String();
      //   AppLogger.info('📋 工作量数据已附加到任务: ${assignment.records.length}人');
      // } else
      if (participants != null && participants.isNotEmpty) {
        // 创建默认工作量分配
        await _createDefaultWorkloadAssignment(task, participants);
      } else {
        AppLogger.warning('⚠️ 创建任务时没有找到工作量分配数据');
      }
    } catch (e) {
      AppLogger.error('❌ 附加工作量数据失败: $e');
    }
  }

  /// 创建默认工作量分配
  Future<void> _createDefaultWorkloadAssignment(TaskModel task, List<String> participants) async {
    try {
      final totalTonnage = task.quantity * 1.5; // 每托1.5吨
      final perPersonTonnage = participants.isNotEmpty ? totalTonnage / participants.length : 0.0;
      
      final records = participants.map((id) => WorkloadRecord(
        workerId: id,
        workerName: id, // 临时使用ID作为姓名
        role: 'worker',
        warehouse: '1号库',
        group: '默认组',
        allocatedTonnage: perPersonTonnage,
        assignedAt: DateTime.now(),
      )).toList();
      
      final defaultAssignment = WorkloadAssignment(
        records: records,
        totalTonnage: totalTonnage,
        palletCount: task.quantity,
        assignedAt: DateTime.now(),
        assignedBy: 'system',
      );
      
      task.recognitionMetadata ??= {};
      task.recognitionMetadata!['workload'] = defaultAssignment.toMap();
      task.recognitionMetadata!['workloadAssignedAt'] = DateTime.now().toIso8601String();
      
      AppLogger.info('📋 已创建默认工作量分配: ${participants.length}人, $totalTonnage吨');
    } catch (e) {
      AppLogger.error('❌ 创建默认工作量分配失败: $e');
    }
  }

  /// 记录个人工作量历史
  Future<void> _recordPersonalWorkloadHistory(TaskModel task) async {
    try {
      final workload = task.recognitionMetadata?['workload'];
      if (workload != null) {
        final assignment = WorkloadAssignment.fromMap(workload as Map<String, dynamic>);

        // 转换为WorkerInfo列表
        final participants = assignment.records.map((record) => WorkerInfo(
          id: record.workerId,
          name: record.workerName,
          role: record.role,
          warehouse: record.warehouse,
          group: record.group,
        )).toList();

        // TODO: 实现个人工作量历史服务
        // await PersonalWorkloadHistoryService.saveTaskCompletionWorkload(
        //   task: task,
        //   participants: participants,
        // );

        AppLogger.info('📊 个人工作量历史记录完成: ${participants.length}人');
      }
    } catch (e) {
      AppLogger.error('❌ 记录个人工作量历史失败: $e');
    }
  }

  /// 关闭服务
  Future<void> close() async {
    // 释放数据服务资源
    _dataService.dispose();
  }
}
