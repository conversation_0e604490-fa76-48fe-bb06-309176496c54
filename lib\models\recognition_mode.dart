/// 📱 识别模式枚举
/// 
/// 让用户根据需求选择速度和准确率的平衡
enum RecognitionMode {
  /// 🚀 快速模式
  /// - 识别时间: 500-1000ms
  /// - 准确率: 70-80%
  /// - 适用场景: 实时识别、快速批量处理
  fast('快速模式', '500ms', '70-80%'),
  
  /// ⚖️ 标准模式
  /// - 识别时间: 1000-2000ms  
  /// - 准确率: 85-90%
  /// - 适用场景: 日常使用，平衡速度和准确率
  standard('标准模式', '1-2s', '85-90%'),
  
  /// 🎯 精确模式
  /// - 识别时间: 3000-5000ms
  /// - 准确率: 95%+
  /// - 适用场景: 重要文档、困难图像
  precision('精确模式', '3-5s', '95%+');
  
  const RecognitionMode(this.displayName, this.speed, this.accuracy);
  
  final String displayName;
  final String speed;
  final String accuracy;
  
  String get description => '$displayName - 速度: $speed, 准确率: $accuracy';
}