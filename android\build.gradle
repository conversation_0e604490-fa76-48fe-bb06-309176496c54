// Top-level build file where you can add configuration options common to all sub-projects/modules.

// 配置本地Kotlin依赖路径
def localKotlinDeps = file("gradle/kotlin-deps/1.9.24")

// 强制所有项目使用国内镜像源和本地依赖
allprojects {
    repositories {
        // 本地Kotlin依赖（最高优先级）
        flatDir {
            dirs localKotlinDeps
        }
        
        // 阿里云镜像源
        maven { 
            url 'https://maven.aliyun.com/repository/public'
            allowInsecureProtocol false
        }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        
        // 华为云镜像源
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        
        // 腾讯云镜像源
        maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
        
        // 清华大学镜像源
        maven { url 'https://mirrors.tuna.tsinghua.edu.cn/maven/' }

        // 官方源作为最后备用
        google()
        mavenCentral()
    }
}

// 为所有子项目配置镜像源和本地依赖
subprojects {
    repositories {
        // 本地Kotlin依赖（最高优先级）
        flatDir {
            dirs localKotlinDeps
        }
        
        // 阿里云镜像源
        maven { 
            url 'https://maven.aliyun.com/repository/public'
            allowInsecureProtocol false
        }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        
        // 华为云镜像源
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        
        // 腾讯云镜像源
        maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public/' }
        
        // 清华大学镜像源
        maven { url 'https://mirrors.tuna.tsinghua.edu.cn/maven/' }

        // 官方源作为最后备用
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
