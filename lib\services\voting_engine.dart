import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';
import 'package:loadguard/services/multi_engine_recognition_service.dart';

/// 🗳️ 投票融合引擎
/// 
/// 将多个识别引擎的结果进行智能融合
/// 通过加权投票和置信度分析得出最终结果
/// 
/// 🎯 核心算法：
/// 1. 空间重叠分析
/// 2. 置信度加权投票
/// 3. 字符相似度匹配
/// 4. 上下文一致性检查
/// 5. 结果质量评估
class VotingEngine {
  
  /// 🗳️ 融合多引擎识别结果
  Future<List<RecognitionResult>> fuseResults(
    Map<RecognitionEngine, List<RecognitionResult>> engineResults,
    ImageAnalysis imageAnalysis,
  ) async {
    AppLogger.debug('🗳️ 开始融合${engineResults.length}个引擎的识别结果...');
    
    try {
      // 1. 预处理结果
      final preprocessedResults = _preprocessResults(engineResults);
      
      // 2. 空间聚类
      final clusters = _spatialClustering(preprocessedResults);
      
      // 3. 投票融合
      final fusedResults = _voteAndFuse(clusters, imageAnalysis);
      
      // 4. 后处理优化
      final finalResults = _postProcessResults(fusedResults);
      
      AppLogger.debug('✅ 融合完成，最终结果数: ${finalResults.length}');
      return finalResults;
      
    } catch (e) {
      AppLogger.error('❌ 结果融合失败: $e');
      // 返回置信度最高的引擎结果
      return _getFallbackResults(engineResults);
    }
  }
  
  /// 预处理结果
  Map<RecognitionEngine, List<EnhancedRecognitionResult>> _preprocessResults(
    Map<RecognitionEngine, List<RecognitionResult>> engineResults
  ) {
    final enhanced = <RecognitionEngine, List<EnhancedRecognitionResult>>{};
    
    for (final entry in engineResults.entries) {
      final engine = entry.key;
      final results = entry.value;
      
      enhanced[engine] = results.map((result) => EnhancedRecognitionResult(
        original: result,
        engine: engine,
        engineWeight: _getEngineWeight(engine),
        normalizedConfidence: _normalizeConfidence(result.confidence ?? 0.0, engine),
      )).toList();
    }
    
    return enhanced;
  }
  
  /// 获取引擎权重
  double _getEngineWeight(RecognitionEngine engine) {
    switch (engine) {
      case RecognitionEngine.mlkit:
        return 1.0; // ML Kit权重最高
      case RecognitionEngine.templateMatching:
        return 0.8; // 模板匹配次之
      case RecognitionEngine.edgeDetection:
        return 0.6; // 边缘检测中等
      case RecognitionEngine.characterSegmentation:
        return 0.4; // 字符分割最低
    }
  }
  
  /// 标准化置信度
  double _normalizeConfidence(double confidence, RecognitionEngine engine) {
    // 不同引擎的置信度范围可能不同，需要标准化
    switch (engine) {
      case RecognitionEngine.mlkit:
        return confidence; // ML Kit置信度已经标准化
      case RecognitionEngine.templateMatching:
        return confidence * 0.9; // 模板匹配稍微降低
      case RecognitionEngine.edgeDetection:
        return confidence * 0.8; // 边缘检测降低更多
      case RecognitionEngine.characterSegmentation:
        return confidence * 0.7; // 字符分割置信度最低
    }
  }
  
  /// 空间聚类
  List<ResultCluster> _spatialClustering(
    Map<RecognitionEngine, List<EnhancedRecognitionResult>> results
  ) {
    final allResults = <EnhancedRecognitionResult>[];
    
    // 收集所有结果
    for (final engineResults in results.values) {
      allResults.addAll(engineResults);
    }
    
    // 按空间位置聚类
    final clusters = <ResultCluster>[];
    final used = <bool>[...List.filled(allResults.length, false)];
    
    for (int i = 0; i < allResults.length; i++) {
      if (used[i]) continue;
      
      final cluster = ResultCluster();
      cluster.results.add(allResults[i]);
      used[i] = true;
      
      // 查找空间重叠的结果
      for (int j = i + 1; j < allResults.length; j++) {
        if (used[j]) continue;
        
        if (_isOverlapping(allResults[i].original.boundingBox, allResults[j].original.boundingBox)) {
          cluster.results.add(allResults[j]);
          used[j] = true;
        }
      }
      
      if (cluster.results.isNotEmpty) {
        clusters.add(cluster);
      }
    }
    
    return clusters;
  }
  
  /// 判断两个边界框是否重叠
  bool _isOverlapping(Map<String, double>? box1, Map<String, double>? box2) {
    if (box1 == null || box2 == null) return false;

    // 计算重叠面积
    final overlapLeft = math.max(box1['left'] ?? 0.0, box2['left'] ?? 0.0);
    final overlapTop = math.max(box1['top'] ?? 0.0, box2['top'] ?? 0.0);
    final overlapRight = math.min(box1['right'] ?? 0.0, box2['right'] ?? 0.0);
    final overlapBottom = math.min(box1['bottom'] ?? 0.0, box2['bottom'] ?? 0.0);
    
    if (overlapLeft >= overlapRight || overlapTop >= overlapBottom) {
      return false; // 没有重叠
    }
    
    final overlapArea = (overlapRight - overlapLeft) * (overlapBottom - overlapTop);
    final width1 = (box1['right'] ?? 0.0) - (box1['left'] ?? 0.0);
    final height1 = (box1['bottom'] ?? 0.0) - (box1['top'] ?? 0.0);
    final width2 = (box2['right'] ?? 0.0) - (box2['left'] ?? 0.0);
    final height2 = (box2['bottom'] ?? 0.0) - (box2['top'] ?? 0.0);
    final area1 = width1 * height1;
    final area2 = width2 * height2;
    final minArea = math.min(area1, area2);

    // 重叠面积超过较小矩形的50%则认为重叠
    return minArea > 0 && overlapArea / minArea > 0.5;
  }
  
  /// 投票融合
  List<RecognitionResult> _voteAndFuse(List<ResultCluster> clusters, ImageAnalysis imageAnalysis) {
    final fusedResults = <RecognitionResult>[];
    
    for (final cluster in clusters) {
      final fusedResult = _fuseCluster(cluster, imageAnalysis);
      if (fusedResult != null) {
        fusedResults.add(fusedResult);
      }
    }
    
    return fusedResults;
  }
  
  /// 融合单个聚类
  RecognitionResult? _fuseCluster(ResultCluster cluster, ImageAnalysis imageAnalysis) {
    if (cluster.results.isEmpty) return null;
    
    // 1. 收集所有候选文本
    final candidates = <String, CandidateVote>{};
    
    for (final result in cluster.results) {
      final text = result.original.ocrText?.trim() ?? '';
      if (text.isEmpty) continue;
      
      if (!candidates.containsKey(text)) {
        candidates[text] = CandidateVote(text: text);
      }
      
      final vote = candidates[text]!;
      vote.votes += result.engineWeight;
      vote.totalConfidence += result.normalizedConfidence * result.engineWeight;
      vote.engines.add(result.engine);
    }
    
    if (candidates.isEmpty) return null;
    
    // 2. 计算最终分数
    for (final vote in candidates.values) {
      vote.finalScore = _calculateFinalScore(vote, cluster, imageAnalysis);
    }
    
    // 3. 选择最佳候选
    final bestCandidate = candidates.values.reduce((a, b) => 
      a.finalScore > b.finalScore ? a : b
    );
    
    // 4. 计算融合边界框
    final fusedBoundingBox = _calculateFusedBoundingBox(cluster.results);
    
    // 5. 计算最终置信度
    final finalConfidence = _calculateFinalConfidence(bestCandidate, cluster);
    
    return RecognitionResult(
      ocrText: bestCandidate.text,
      confidence: finalConfidence,
      boundingBox: fusedBoundingBox,
      isQrOcrConsistent: false, // 必需参数
      matchesPreset: false, // 必需参数
      metadata: {
        'recognizedElements': [bestCandidate.text],
        'clusterSize': cluster.results.length,
        'engines': cluster.results.map((r) => r.engine.name).toSet().toList(),
      },
    );
  }
  
  /// 计算最终分数
  double _calculateFinalScore(CandidateVote vote, ResultCluster cluster, ImageAnalysis imageAnalysis) {
    double score = 0.0;
    
    // 1. 投票权重 (40%)
    final voteScore = vote.votes / cluster.results.length;
    score += voteScore * 0.4;
    
    // 2. 平均置信度 (30%)
    final confidenceScore = vote.totalConfidence / vote.votes;
    score += confidenceScore * 0.3;
    
    // 3. 引擎多样性奖励 (20%)
    final diversityScore = vote.engines.length / RecognitionEngine.values.length;
    score += diversityScore * 0.2;
    
    // 4. 文本质量评估 (10%)
    final qualityScore = _assessTextQuality(vote.text);
    score += qualityScore * 0.1;
    
    return score;
  }
  
  /// 评估文本质量
  double _assessTextQuality(String text) {
    double score = 1.0;
    
    // 长度合理性
    if (text.length < 1 || text.length > 50) {
      score *= 0.5;
    }
    
    // 字符合理性
    final validChars = RegExp(r'^[A-Za-z0-9\-/.]+$');
    if (!validChars.hasMatch(text)) {
      score *= 0.7;
    }
    
    // 连续性检查
    if (text.contains('??') || text.contains('  ')) {
      score *= 0.6;
    }
    
    return score;
  }
  
  /// 计算融合边界框
  Map<String, double> _calculateFusedBoundingBox(List<EnhancedRecognitionResult> results) {
    if (results.isEmpty) return {'left': 0.0, 'top': 0.0, 'right': 0.0, 'bottom': 0.0};
    
    double minLeft = double.infinity;
    double minTop = double.infinity;
    double maxRight = double.negativeInfinity;
    double maxBottom = double.negativeInfinity;
    
    for (final result in results) {
      final rect = result.original.boundingBox;
      if (rect != null) {
        minLeft = math.min(minLeft, rect['left'] ?? 0.0);
        minTop = math.min(minTop, rect['top'] ?? 0.0);
        maxRight = math.max(maxRight, rect['right'] ?? 0.0);
        maxBottom = math.max(maxBottom, rect['bottom'] ?? 0.0);
      }
    }
    
    return {
      'left': minLeft,
      'top': minTop,
      'right': maxRight,
      'bottom': maxBottom,
    };
  }
  
  /// 计算最终置信度
  double _calculateFinalConfidence(CandidateVote vote, ResultCluster cluster) {
    // 基于投票权重和置信度的加权平均
    final baseConfidence = vote.totalConfidence / vote.votes;
    
    // 引擎一致性奖励
    final consistencyBonus = vote.engines.length > 1 ? 0.1 : 0.0;
    
    // 投票强度奖励
    final voteStrengthBonus = (vote.votes / cluster.results.length - 0.5) * 0.2;
    
    return (baseConfidence + consistencyBonus + voteStrengthBonus).clamp(0.0, 1.0);
  }
  
  /// 后处理优化
  List<RecognitionResult> _postProcessResults(List<RecognitionResult> results) {
    // 1. 按位置排序
    results.sort((a, b) {
      final aBox = a.boundingBox;
      final bBox = b.boundingBox;

      if (aBox == null || bBox == null) return 0;

      final aCenterY = (aBox['top']! + aBox['bottom']!) / 2;
      final bCenterY = (bBox['top']! + bBox['bottom']!) / 2;
      final aCenterX = (aBox['left']! + aBox['right']!) / 2;
      final bCenterX = (bBox['left']! + bBox['right']!) / 2;

      // 先按Y坐标排序（行），再按X坐标排序（列）
      final yDiff = aCenterY.compareTo(bCenterY);
      if (yDiff != 0) return yDiff;
      return aCenterX.compareTo(bCenterX);
    });
    
    // 2. 过滤低质量结果
    final filtered = results.where((result) =>
      (result.confidence ?? 0.0) > 0.3 &&
      (result.ocrText?.trim().isNotEmpty ?? false)
    ).toList();
    
    // 3. 合并相邻的相似结果
    final merged = _mergeAdjacentResults(filtered);
    
    return merged;
  }
  
  /// 合并相邻的相似结果
  List<RecognitionResult> _mergeAdjacentResults(List<RecognitionResult> results) {
    if (results.length <= 1) return results;
    
    final merged = <RecognitionResult>[];
    RecognitionResult? current;
    
    for (final result in results) {
      if (current == null) {
        current = result;
        continue;
      }
      
      // 检查是否应该合并
      if (_shouldMergeResults(current, result)) {
        current = _mergeResults(current, result);
      } else {
        merged.add(current);
        current = result;
      }
    }
    
    if (current != null) {
      merged.add(current);
    }
    
    return merged;
  }
  
  /// 判断是否应该合并结果
  bool _shouldMergeResults(RecognitionResult a, RecognitionResult b) {
    // TODO: 重新实现边界框合并逻辑
    return false; // 暂时禁用合并功能
  }
  
  /// 合并两个结果
  RecognitionResult _mergeResults(RecognitionResult a, RecognitionResult b) {
    // TODO: 重新实现结果合并逻辑
    return a; // 暂时返回第一个结果
  }
  
  /// 获取备用结果
  List<RecognitionResult> _getFallbackResults(
    Map<RecognitionEngine, List<RecognitionResult>> engineResults
  ) {
    // 返回ML Kit的结果作为备用
    if (engineResults.containsKey(RecognitionEngine.mlkit)) {
      return engineResults[RecognitionEngine.mlkit]!;
    }
    
    // 如果没有ML Kit结果，返回第一个非空结果
    for (final results in engineResults.values) {
      if (results.isNotEmpty) {
        return results;
      }
    }
    
    return [];
  }
}

/// 增强的识别结果
class EnhancedRecognitionResult {
  final RecognitionResult original;
  final RecognitionEngine engine;
  final double engineWeight;
  final double normalizedConfidence;
  
  EnhancedRecognitionResult({
    required this.original,
    required this.engine,
    required this.engineWeight,
    required this.normalizedConfidence,
  });
}

/// 结果聚类
class ResultCluster {
  final List<EnhancedRecognitionResult> results = [];
}

/// 候选投票
class CandidateVote {
  final String text;
  double votes = 0.0;
  double totalConfidence = 0.0;
  double finalScore = 0.0;
  final Set<RecognitionEngine> engines = {};
  
  CandidateVote({required this.text});
}
