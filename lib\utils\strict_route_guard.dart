import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../services/strict_security_service.dart';
import '../services/enterprise_license_service.dart';
import '../utils/app_logger.dart';

/// 🔐 严格的路由守卫 - 防止绕过激活页面
class StrictRouteGuard {
  static bool _isInitialized = false;
  static Map<String, dynamic>? _cachedSecurityStatus;
  static DateTime? _lastSecurityCheck;
  
  /// 初始化路由守卫
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      await StrictSecurityService.initialize();
      await _updateSecurityStatus();
      _isInitialized = true;
      AppLogger.info('✅ 严格路由守卫初始化完成', tag: 'StrictRouteGuard');
    } catch (e) {
      AppLogger.error('❌ 严格路由守卫初始化失败', error: e, tag: 'StrictRouteGuard');
      rethrow;
    }
  }

  /// 🔧 修复：正确的全局路由重定向逻辑
  static String? globalRedirect(BuildContext context, GoRouterState state) {
    try {
      // 确保初始化
      if (!_isInitialized) {
        AppLogger.warning('路由守卫未初始化，允许通过', tag: 'StrictRouteGuard');
        return null;
      }

      final currentPath = state.uri.path;
      AppLogger.info('🔍 路由守卫检查: $currentPath', tag: 'StrictRouteGuard');

      // 启动页面和激活页面始终允许访问
      if (currentPath == '/' || currentPath == '/activation') {
        AppLogger.info('✅ 允许访问系统页面: $currentPath', tag: 'StrictRouteGuard');
        return null;
      }

      // 🔧 关键修复：检查许可证状态，只有真正过期才重定向
      if (_cachedSecurityStatus != null && _isSecurityStatusValid()) {
        final canLaunch = _cachedSecurityStatus!['canLaunch'] as bool;
        final shouldRedirect = _cachedSecurityStatus!['shouldRedirectToActivation'] as bool;
        final userRole = _cachedSecurityStatus!['userRole'];

        AppLogger.info('📊 安全状态检查: canLaunch=$canLaunch, shouldRedirect=$shouldRedirect, userRole=$userRole', tag: 'StrictRouteGuard');

        // 🔧 修复：只有真正需要激活时才重定向
        if (!canLaunch && shouldRedirect) {
          AppLogger.warning('❌ 许可证已过期，重定向到激活页面', tag: 'StrictRouteGuard');
          return '/activation'; // 使用统一的激活页面
        } else {
          AppLogger.info('✅ 许可证有效，允许访问: $currentPath', tag: 'StrictRouteGuard');
          return null;
        }
      } else {
        // 🔧 优化：缓存过期时，异步更新但允许通过
        AppLogger.info('⚠️ 安全状态缓存过期，异步更新中...', tag: 'StrictRouteGuard');

        // 异步更新安全状态，不阻塞当前请求
        _updateSecurityStatus().then((_) {
          AppLogger.info('🔄 安全状态后台更新完成', tag: 'StrictRouteGuard');
        }).catchError((e) {
          AppLogger.error('❌ 安全状态后台更新失败', error: e, tag: 'StrictRouteGuard');
        });

        // 🔧 关键修复：缓存过期时允许通过，不中断用户操作
        AppLogger.info('✅ 缓存过期但允许访问: $currentPath', tag: 'StrictRouteGuard');
        return null;
      }
    } catch (e) {
      AppLogger.error('❌ 路由守卫检查失败', error: e, tag: 'StrictRouteGuard');
      // 🔧 修复：异常时允许通过，不阻塞用户
      AppLogger.warning('⚠️ 异常情况下允许访问: ${state.uri.path}', tag: 'StrictRouteGuard');
      return null;
    }
  }

  /// 创建受保护的页面构建器
  static Widget Function(BuildContext, GoRouterState) createProtectedPageBuilder(
    Widget Function(BuildContext, GoRouterState) builder,
  ) {
    return (context, state) {
      return FutureBuilder<Map<String, dynamic>>(
        future: _checkPageAccess(state.uri.path),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingPage();
          }

          if (snapshot.hasError) {
            AppLogger.error('页面访问检查失败', error: snapshot.error, tag: 'StrictRouteGuard');
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.go('/activation');
            });
            return _buildErrorPage();
          }

          final result = snapshot.data!;
          final canAccess = result['canAccess'] as bool;

          if (!canAccess) {
            AppLogger.info('页面访问被拒绝，重定向到激活页面', tag: 'StrictRouteGuard');
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.go('/activation');
            });
            return _buildAccessDeniedPage();
          }

          return builder(context, state);
        },
      );
    };
  }

  /// 检查页面访问权限
  static Future<Map<String, dynamic>> _checkPageAccess(String path) async {
    try {
      await _updateSecurityStatus();
      
      if (_cachedSecurityStatus == null) {
        return {'canAccess': false, 'reason': '安全状态未知'};
      }

      final canLaunch = _cachedSecurityStatus!['canLaunch'] as bool;
      final shouldRedirect = _cachedSecurityStatus!['shouldRedirectToActivation'] as bool;

      if (!canLaunch || shouldRedirect) {
        return {'canAccess': false, 'reason': '需要激活'};
      }

      // 检查特定页面的权限
      final hasPermission = await _checkPagePermission(path);
      if (!hasPermission) {
        return {'canAccess': false, 'reason': '权限不足'};
      }

      return {'canAccess': true};
    } catch (e) {
      AppLogger.error('检查页面访问权限失败', error: e, tag: 'StrictRouteGuard');
      return {'canAccess': false, 'reason': '检查失败'};
    }
  }

  /// 检查特定页面权限
  static Future<bool> _checkPagePermission(String path) async {
    try {
      // 根据路径映射到功能权限
      String featureName = 'basicRecognition'; // 默认权限
      
      if (path.startsWith('/task-detail') || path.startsWith('/result')) {
        featureName = 'taskManagement';
      } else if (path.startsWith('/security-management')) {
        featureName = 'systemSettings';
      } else if (path.startsWith('/enterprise-control')) {
        featureName = 'deviceManagement';
      }

      return await StrictSecurityService.hasPermission(featureName);
    } catch (e) {
      AppLogger.error('检查页面权限失败', error: e, tag: 'StrictRouteGuard');
      return false;
    }
  }

  /// 更新安全状态缓存
  static Future<void> _updateSecurityStatus() async {
    try {
      _cachedSecurityStatus = await StrictSecurityService.checkAppLaunchPermission();
      _lastSecurityCheck = DateTime.now();
      AppLogger.info('安全状态已更新: $_cachedSecurityStatus', tag: 'StrictRouteGuard');
    } catch (e) {
      AppLogger.error('更新安全状态失败', error: e, tag: 'StrictRouteGuard');
      _cachedSecurityStatus = null;
    }
  }

  /// 检查安全状态缓存是否有效
  static bool _isSecurityStatusValid() {
    if (_lastSecurityCheck == null) return false;

    final now = DateTime.now();
    final age = now.difference(_lastSecurityCheck!);

    // 🔧 优化：延长缓存有效期到30分钟，大幅减少不必要的重定向
    return age.inMinutes < 30;
  }

  /// 强制刷新安全状态
  static Future<void> refreshSecurityStatus() async {
    await _updateSecurityStatus();
  }

  /// 🔧 新增：智能重试机制
  static int _retryCount = 0;
  static const int _maxRetries = 3;

  /// 智能重试安全状态检查
  static Future<bool> _retrySecurityCheck() async {
    if (_retryCount >= _maxRetries) {
      AppLogger.warning('⚠️ 安全检查重试次数已达上限，允许通过', tag: 'StrictRouteGuard');
      _retryCount = 0; // 重置重试计数
      return true; // 允许通过
    }

    try {
      _retryCount++;
      await _updateSecurityStatus();

      if (_cachedSecurityStatus != null) {
        AppLogger.info('✅ 重试安全检查成功 (第$_retryCount次)', tag: 'StrictRouteGuard');
        _retryCount = 0; // 重置重试计数
        return true;
      }
    } catch (e) {
      AppLogger.error('❌ 重试安全检查失败 (第$_retryCount次): $e', tag: 'StrictRouteGuard');
    }

    return false;
  }

  /// 启动定期检查激活状态
  static void startPeriodicSecurityCheck() {
    // 每分钟检查一次激活状态
    Timer.periodic(Duration(minutes: 1), (timer) async {
      try {
        await _updateSecurityStatus();
        
        if (_cachedSecurityStatus != null) {
          final shouldRedirect = _cachedSecurityStatus!['shouldRedirectToActivation'] as bool;
          if (shouldRedirect) {
            AppLogger.warning('检测到许可证过期，需要重新激活', tag: 'StrictRouteGuard');
            // 这里可以发出通知或执行其他处理
          }
        }
      } catch (e) {
        AppLogger.error('定期安全检查失败', error: e, tag: 'StrictRouteGuard');
      }
    });
  }

  /// 构建加载页面
  static Widget _buildLoadingPage() {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在验证访问权限...'),
          ],
        ),
      ),
    );
  }

  /// 构建错误页面
  static Widget _buildErrorPage() {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text('权限验证失败'),
            SizedBox(height: 8),
            Text('正在重定向到激活页面...'),
          ],
        ),
      ),
    );
  }

  /// 构建访问拒绝页面
  static Widget _buildAccessDeniedPage() {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.block, size: 64, color: Colors.orange),
            SizedBox(height: 16),
            Text('访问被拒绝'),
            SizedBox(height: 8),
            Text('正在重定向到激活页面...'),
          ],
        ),
      ),
    );
  }

  /// 检查是否需要重定向到激活页面
  static Future<bool> shouldRedirectToActivation() async {
    try {
      await _updateSecurityStatus();
      
      if (_cachedSecurityStatus == null) {
        return true;
      }

      final canLaunch = _cachedSecurityStatus!['canLaunch'] as bool;
      final shouldRedirect = _cachedSecurityStatus!['shouldRedirectToActivation'] as bool;

      return !canLaunch || shouldRedirect;
    } catch (e) {
      AppLogger.error('检查重定向需求失败', error: e, tag: 'StrictRouteGuard');
      return true; // 出错时默认需要重定向
    }
  }

  /// 获取当前用户角色
  static Future<UserRole> getCurrentUserRole() async {
    try {
      final status = await StrictSecurityService.getLicenseStatus();
      return status['userRole'] as UserRole;
    } catch (e) {
      AppLogger.error('获取用户角色失败', error: e, tag: 'StrictRouteGuard');
      return UserRole.trial;
    }
  }

  /// 清理资源
  static void dispose() {
    _cachedSecurityStatus = null;
    _lastSecurityCheck = null;
    _isInitialized = false;
    AppLogger.info('路由守卫资源已清理', tag: 'StrictRouteGuard');
  }
}
