/// 📊 任务状态管理服务
///
/// 专门负责任务状态的管理，包括：
/// - 当前任务管理
/// - 任务状态切换
/// - 任务完成逻辑
/// - 任务列表管理
/// 从TaskService中提取，减少其复杂度
library task_state_management_service;

import 'dart:async';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 任务状态变更事件
class TaskStateChangeEvent {
  final String taskId;
  final TaskModel? oldTask;
  final TaskModel? newTask;
  final String changeType;
  final DateTime timestamp;

  TaskStateChangeEvent({
    required this.taskId,
    this.oldTask,
    this.newTask,
    required this.changeType,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// 任务状态变更事件
class TaskStateChangeEvent {
  final String taskId;
  final TaskModel? oldTask;
  final TaskModel? newTask;
  final String changeType;
  final DateTime timestamp;

  TaskStateChangeEvent({
    required this.taskId,
    this.oldTask,
    this.newTask,
    required this.changeType,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// 任务状态管理服务
class TaskStateManagementService {
  TaskModel? _currentTask;
  List<TaskModel> _tasks = [];
  
  // 状态变更事件流
  final StreamController<TaskStateChangeEvent> _stateChangeController = 
      StreamController<TaskStateChangeEvent>.broadcast();

  /// 获取当前任务
  TaskModel? get currentTask => _currentTask;
  
  /// 获取任务列表
  List<TaskModel> get tasks => List.unmodifiable(_tasks);
  
  /// 状态变更事件流
  Stream<TaskStateChangeEvent> get stateChangeStream => _stateChangeController.stream;

  /// 设置当前任务
  Future<void> setCurrentTask(TaskModel? task) async {
    try {
      AppLogger.info('📋 设置当前任务: ${task?.id ?? 'null'}', tag: 'TaskStateMgmt');
      
      final oldTask = _currentTask;
      _currentTask = task;
      
      // 发送状态变更事件
      _stateChangeController.add(TaskStateChangeEvent(
        taskId: task?.id ?? 'null',
        oldTask: oldTask,
        newTask: task,
        changeType: 'currentTaskChanged',
      ));
      
      AppLogger.info('✅ 当前任务设置成功', tag: 'TaskStateMgmt');
    } catch (e) {
      AppLogger.error('❌ 设置当前任务失败: $e', tag: 'TaskStateMgmt');
      rethrow;
    }
  }

  /// 清除当前任务
  void clearCurrentTask() {
    try {
      AppLogger.info('🗑️ 清除当前任务', tag: 'TaskStateMgmt');
      
      final oldTask = _currentTask;
      _currentTask = null;
      
      // 发送状态变更事件
      _stateChangeController.add(TaskStateChangeEvent(
        taskId: oldTask?.id ?? 'null',
        oldTask: oldTask,
        newTask: null,
        changeType: 'currentTaskCleared',
      ));
      
      AppLogger.info('✅ 当前任务已清除', tag: 'TaskStateMgmt');
    } catch (e) {
      AppLogger.error('❌ 清除当前任务失败: $e', tag: 'TaskStateMgmt');
    }
  }

  /// 设置任务列表
  void setTasks(List<TaskModel> tasks) {
    try {
      AppLogger.info('📋 设置任务列表: ${tasks.length}个任务', tag: 'TaskStateMgmt');
      
      _tasks = List<TaskModel>.from(tasks);
      
      // 发送状态变更事件
      _stateChangeController.add(TaskStateChangeEvent(
        taskId: 'all',
        changeType: 'tasksListChanged',
      ));
      
      AppLogger.info('✅ 任务列表设置成功', tag: 'TaskStateMgmt');
    } catch (e) {
      AppLogger.error('❌ 设置任务列表失败: $e', tag: 'TaskStateMgmt');
    }
  }

  /// 添加任务到列表
  void addTask(TaskModel task) {
    try {
      AppLogger.info('➕ 添加任务: ${task.id}', tag: 'TaskStateMgmt');
      
      // 检查任务是否已存在
      final existingIndex = _tasks.indexWhere((t) => t.id == task.id);
      if (existingIndex != -1) {
        AppLogger.warning('⚠️ 任务已存在，将替换: ${task.id}', tag: 'TaskStateMgmt');
        _tasks[existingIndex] = task;
      } else {
        _tasks.insert(0, task); // 新任务插入到列表开头
      }
      
      // 发送状态变更事件
      _stateChangeController.add(TaskStateChangeEvent(
        taskId: task.id,
        newTask: task,
        changeType: 'taskAdded',
      ));
      
      AppLogger.info('✅ 任务添加成功', tag: 'TaskStateMgmt');
    } catch (e) {
      AppLogger.error('❌ 添加任务失败: $e', tag: 'TaskStateMgmt');
    }
  }

  /// 更新任务
  void updateTask(TaskModel task) {
    try {
      AppLogger.info('🔄 更新任务: ${task.id}', tag: 'TaskStateMgmt');
      
      final index = _tasks.indexWhere((t) => t.id == task.id);
      if (index == -1) {
        AppLogger.warning('⚠️ 任务不存在，将添加: ${task.id}', tag: 'TaskStateMgmt');
        addTask(task);
        return;
      }
      
      final oldTask = _tasks[index];
      _tasks[index] = task;
      
      // 如果是当前任务，也要更新当前任务引用
      if (_currentTask?.id == task.id) {
        _currentTask = task;
      }
      
      // 发送状态变更事件
      _stateChangeController.add(TaskStateChangeEvent(
        taskId: task.id,
        oldTask: oldTask,
        newTask: task,
        changeType: 'taskUpdated',
      ));
      
      AppLogger.info('✅ 任务更新成功', tag: 'TaskStateMgmt');
    } catch (e) {
      AppLogger.error('❌ 更新任务失败: $e', tag: 'TaskStateMgmt');
    }
  }

  /// 删除任务
  void removeTask(String taskId) {
    try {
      AppLogger.info('🗑️ 删除任务: $taskId', tag: 'TaskStateMgmt');
      
      final index = _tasks.indexWhere((t) => t.id == taskId);
      if (index == -1) {
        AppLogger.warning('⚠️ 任务不存在: $taskId', tag: 'TaskStateMgmt');
        return;
      }
      
      final removedTask = _tasks.removeAt(index);
      
      // 如果删除的是当前任务，清除当前任务
      if (_currentTask?.id == taskId) {
        clearCurrentTask();
      }
      
      // 发送状态变更事件
      _stateChangeController.add(TaskStateChangeEvent(
        taskId: taskId,
        oldTask: removedTask,
        changeType: 'taskRemoved',
      ));
      
      AppLogger.info('✅ 任务删除成功', tag: 'TaskStateMgmt');
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $e', tag: 'TaskStateMgmt');
    }
  }

  /// 完成任务
  Future<void> completeTask(TaskModel task) async {
    try {
      AppLogger.info('🎯 完成任务: ${task.id}', tag: 'TaskStateMgmt');
      
      // 设置完成时间
      final completedTask = task.copyWith(
        completedAt: DateTime.now(),
      );
      
      // 更新任务
      updateTask(completedTask);
      
      AppLogger.info('✅ 任务完成: ${task.id}', tag: 'TaskStateMgmt');
    } catch (e) {
      AppLogger.error('❌ 完成任务失败: $e', tag: 'TaskStateMgmt');
      rethrow;
    }
  }

  /// 根据ID获取任务
  TaskModel? getTaskById(String taskId) {
    try {
      return _tasks.firstWhere(
        (task) => task.id == taskId,
        orElse: () => throw Exception('任务不存在'),
      );
    } catch (e) {
      AppLogger.warning('⚠️ 获取任务失败: $taskId', tag: 'TaskStateMgmt');
      return null;
    }
  }

  /// 获取任务统计信息
  Map<String, dynamic> getTaskStatistics() {
    try {
      final total = _tasks.length;
      final completed = _tasks.where((t) => t.isCompleted).length;
      final inProgress = _tasks.where((t) => !t.isCompleted).length;
      
      return {
        'total': total,
        'completed': completed,
        'inProgress': inProgress,
        'completionRate': total > 0 ? (completed / total * 100) : 0.0,
        'hasCurrentTask': _currentTask != null,
        'currentTaskId': _currentTask?.id,
      };
    } catch (e) {
      AppLogger.error('❌ 获取任务统计失败: $e', tag: 'TaskStateMgmt');
      return {
        'total': 0,
        'completed': 0,
        'inProgress': 0,
        'completionRate': 0.0,
        'hasCurrentTask': false,
        'currentTaskId': null,
      };
    }
  }

  /// 查询任务
  List<TaskModel> queryTasks({
    String? template,
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
    String? productCode,
  }) {
    try {
      var filteredTasks = _tasks.where((task) {
        // 模板过滤
        if (template != null && task.template != template) {
          return false;
        }
        
        // 完成状态过滤
        if (isCompleted != null && task.isCompleted != isCompleted) {
          return false;
        }
        
        // 开始日期过滤
        if (startDate != null && task.createdAt.isBefore(startDate)) {
          return false;
        }
        
        // 结束日期过滤
        if (endDate != null && task.createdAt.isAfter(endDate)) {
          return false;
        }
        
        // 产品代码过滤
        if (productCode != null && task.productCode != productCode) {
          return false;
        }
        
        return true;
      }).toList();
      
      AppLogger.info('🔍 查询任务结果: ${filteredTasks.length}个', tag: 'TaskStateMgmt');
      return filteredTasks;
    } catch (e) {
      AppLogger.error('❌ 查询任务失败: $e', tag: 'TaskStateMgmt');
      return [];
    }
  }

  /// 获取最近的任务
  List<TaskModel> getRecentTasks({int limit = 10}) {
    try {
      final recentTasks = List<TaskModel>.from(_tasks)
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return recentTasks.take(limit).toList();
    } catch (e) {
      AppLogger.error('❌ 获取最近任务失败: $e', tag: 'TaskStateMgmt');
      return [];
    }
  }

  /// 释放资源
  void dispose() {
    _stateChangeController.close();
    AppLogger.info('🔄 任务状态管理服务已释放', tag: 'TaskStateMgmt');
  }
}
