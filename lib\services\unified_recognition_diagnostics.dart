import 'dart:io';
import 'package:loadguard/services/unified_recognition_service.dart';
import 'package:loadguard/services/multi_engine_recognition_service.dart';
import 'package:loadguard/services/qr_code_recognition_engine.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🔍 统一识别服务诊断工具
/// 用于测试和验证统一识别服务的各个组件是否正常工作
class UnifiedRecognitionDiagnostics {
  
  /// 🔧 完整诊断统一识别服务
  static Future<Map<String, dynamic>> diagnoseUnifiedRecognition(String imagePath) async {
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'imagePath': imagePath,
      'imageExists': false,
      'unifiedServiceInit': false,
      'multiEngineInit': false,
      'qrEngineInit': false,
      'recognitionResult': null,
      'errors': <String>[],
      'warnings': <String>[],
      'performance': <String, int>{},
    };
    
    try {
      AppLogger.info('🔍 开始统一识别服务诊断...');
      
      // 1. 检查图像文件是否存在
      final imageFile = File(imagePath);
      results['imageExists'] = await imageFile.exists();
      if (!results['imageExists']) {
        results['errors'].add('图像文件不存在: $imagePath');
        return results;
      }
      
      final imageSize = await imageFile.length();
      results['imageSize'] = imageSize;
      AppLogger.info('✅ 图像文件存在，大小: ${imageSize}字节');
      
      // 2. 测试MultiEngineRecognitionService初始化
      try {
        final startTime = DateTime.now();
        final multiEngineService = MultiEngineRecognitionService.instance;
        await multiEngineService.initialize();
        results['multiEngineInit'] = true;
        results['performance']['multiEngineInitTime'] = DateTime.now().difference(startTime).inMilliseconds;
        AppLogger.info('✅ MultiEngineRecognitionService初始化成功');
      } catch (e) {
        results['errors'].add('MultiEngineRecognitionService初始化失败: $e');
        AppLogger.error('❌ MultiEngineRecognitionService初始化失败: $e');
      }
      
      // 3. 测试QRCodeRecognitionEngine初始化
      try {
        final startTime = DateTime.now();
        final qrEngine = QRCodeRecognitionEngine();
        await qrEngine.initialize();
        results['qrEngineInit'] = true;
        results['performance']['qrEngineInitTime'] = DateTime.now().difference(startTime).inMilliseconds;
        AppLogger.info('✅ QRCodeRecognitionEngine初始化成功');
      } catch (e) {
        results['errors'].add('QRCodeRecognitionEngine初始化失败: $e');
        AppLogger.error('❌ QRCodeRecognitionEngine初始化失败: $e');
      }
      
      // 4. 测试UnifiedRecognitionService初始化
      try {
        final startTime = DateTime.now();
        final unifiedService = UnifiedRecognitionService.instance;
        await unifiedService.initialize();
        results['unifiedServiceInit'] = true;
        results['performance']['unifiedServiceInitTime'] = DateTime.now().difference(startTime).inMilliseconds;
        AppLogger.info('✅ UnifiedRecognitionService初始化成功');
      } catch (e) {
        results['errors'].add('UnifiedRecognitionService初始化失败: $e');
        AppLogger.error('❌ UnifiedRecognitionService初始化失败: $e');
        return results;
      }
      
      // 5. 执行实际识别测试
      if (results['unifiedServiceInit']) {
        try {
          AppLogger.info('🔍 开始执行统一识别测试...');
          final startTime = DateTime.now();
          
          final unifiedService = UnifiedRecognitionService.instance;
          final recognitionResult = await unifiedService.recognizeUnified(
            imagePath,
            enableQRCode: true,
            enableNetworkValidation: false,
            onProgress: (progress, status) {
              AppLogger.debug('🔍 诊断进度: ${(progress * 100).toInt()}% - $status');
            },
          );
          
          results['performance']['recognitionTime'] = DateTime.now().difference(startTime).inMilliseconds;
          
          // 记录识别结果详情
          results['recognitionResult'] = {
            'hasText': recognitionResult.hasText,
            'hasQRCode': recognitionResult.hasQRCode,
            'isConsistent': recognitionResult.isConsistent,
            'overallConfidence': recognitionResult.overallConfidence,
            'textResultsCount': recognitionResult.textResults.length,
            'qrResultsCount': recognitionResult.qrResults.length,
            'processingTime': recognitionResult.processingTime,
          };
          
          if (recognitionResult.textResults.isNotEmpty) {
            results['recognitionResult']['firstTextResult'] = {
              'ocrText': recognitionResult.textResults.first.ocrText,
              'confidence': recognitionResult.textResults.first.confidence,
            };
          }
          
          if (recognitionResult.qrResults.isNotEmpty) {
            results['recognitionResult']['firstQRResult'] = {
              'qrCode': recognitionResult.qrResults.first.qrCode,
              'ocrText': recognitionResult.qrResults.first.ocrText,
            };
          }
          
          AppLogger.info('✅ 统一识别测试完成');
          AppLogger.info('📊 识别结果: 文字${recognitionResult.textResults.length}个, 二维码${recognitionResult.qrResults.length}个');
          
        } catch (e, stackTrace) {
          results['errors'].add('统一识别执行失败: $e');
          AppLogger.error('❌ 统一识别执行失败: $e');
          AppLogger.error('堆栈跟踪: $stackTrace');
        }
      }
      
      // 6. 生成诊断报告
      final totalErrors = (results['errors'] as List).length;
      final totalWarnings = (results['warnings'] as List).length;
      
      if (totalErrors == 0) {
        AppLogger.info('🎉 统一识别服务诊断完成 - 所有组件正常工作');
      } else {
        AppLogger.warning('⚠️ 统一识别服务诊断完成 - 发现${totalErrors}个错误，${totalWarnings}个警告');
      }
      
    } catch (e, stackTrace) {
      results['errors'].add('诊断过程异常: $e');
      AppLogger.error('❌ 诊断过程异常: $e');
      AppLogger.error('堆栈跟踪: $stackTrace');
    }
    
    return results;
  }
  
  /// 🔧 简化版识别测试（仅测试基础功能）
  static Future<bool> quickRecognitionTest(String imagePath) async {
    try {
      AppLogger.info('🚀 开始快速识别测试...');
      
      final unifiedService = UnifiedRecognitionService.instance;
      await unifiedService.initialize();
      
      final result = await unifiedService.recognizeUnified(
        imagePath,
        enableQRCode: true,
        enableNetworkValidation: false,
      );
      
      final hasResults = result.hasText || result.hasQRCode;
      AppLogger.info('✅ 快速识别测试完成: ${hasResults ? '有结果' : '无结果'}');
      
      return hasResults;
    } catch (e) {
      AppLogger.error('❌ 快速识别测试失败: $e');
      return false;
    }
  }
  
  /// 📊 打印诊断报告
  static void printDiagnosticReport(Map<String, dynamic> results) {
    AppLogger.info('📊 ===== 统一识别服务诊断报告 =====');
    AppLogger.info('🕐 时间: ${results['timestamp']}');
    AppLogger.info('📁 图像: ${results['imagePath']}');
    AppLogger.info('📏 大小: ${results['imageSize'] ?? 'N/A'}字节');
    AppLogger.info('');
    
    AppLogger.info('🔧 组件状态:');
    AppLogger.info('  - 图像文件: ${results['imageExists'] ? '✅' : '❌'}');
    AppLogger.info('  - 多引擎服务: ${results['multiEngineInit'] ? '✅' : '❌'}');
    AppLogger.info('  - 二维码引擎: ${results['qrEngineInit'] ? '✅' : '❌'}');
    AppLogger.info('  - 统一识别服务: ${results['unifiedServiceInit'] ? '✅' : '❌'}');
    AppLogger.info('');
    
    if (results['recognitionResult'] != null) {
      final result = results['recognitionResult'] as Map<String, dynamic>;
      AppLogger.info('📊 识别结果:');
      AppLogger.info('  - 文字识别: ${result['hasText'] ? '✅' : '❌'} (${result['textResultsCount']}个结果)');
      AppLogger.info('  - 二维码识别: ${result['hasQRCode'] ? '✅' : '❌'} (${result['qrResultsCount']}个结果)');
      AppLogger.info('  - 结果一致性: ${result['isConsistent'] ? '✅' : '❌'}');
      AppLogger.info('  - 整体置信度: ${result['overallConfidence']}');
      AppLogger.info('');
    }
    
    final performance = results['performance'] as Map<String, int>;
    if (performance.isNotEmpty) {
      AppLogger.info('⚡ 性能指标:');
      performance.forEach((key, value) {
        AppLogger.info('  - $key: ${value}ms');
      });
      AppLogger.info('');
    }
    
    final errors = results['errors'] as List<String>;
    if (errors.isNotEmpty) {
      AppLogger.info('❌ 错误列表:');
      for (int i = 0; i < errors.length; i++) {
        AppLogger.info('  ${i + 1}. ${errors[i]}');
      }
      AppLogger.info('');
    }
    
    final warnings = results['warnings'] as List<String>;
    if (warnings.isNotEmpty) {
      AppLogger.info('⚠️ 警告列表:');
      for (int i = 0; i < warnings.length; i++) {
        AppLogger.info('  ${i + 1}. ${warnings[i]}');
      }
    }
    
    AppLogger.info('📊 ===== 诊断报告结束 =====');
  }
}
