import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../utils/app_logger.dart';
// ✅ 移除过时的日志服务引用，统一使用AppLogger
import '../services/app_security_service.dart';
import '../services/enterprise_license_service.dart';

/// 🔐 严格的安全管理服务 - 专为本地应用设计
///
/// 功能特点：
/// 1. 严格的权限控制，无法绕过
/// 2. 试用期只能激活一次
/// 3. 超级管理员可以为其他设备生成激活码
/// 4. 每次激活码都不同
/// 5. 到期后强制跳转到激活页面
class StrictSecurityService {
  static const String _keyPrefix = 'strict_security_';
  static const String _licenseKey = '${_keyPrefix}license_info';
  static const String _deviceIdKey = 'device_id'; // 统一使用AppSecurityService的key
  static const String _trialActivatedKey = '${_keyPrefix}trial_activated';
  static const String _firstInstallKey = '${_keyPrefix}first_install';
  static const String _activationHistoryKey = '${_keyPrefix}activation_history';
  static const String _masterDeviceKey = '${_keyPrefix}master_device';
  static const String _securityStateKey = '${_keyPrefix}security_state';

  /// 安全状态
  static Map<String, dynamic> _securityState = {};
  static bool _isInitialized = false;

  /// 🚀 初始化安全服务
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('🔐 初始化严格安全服务', tag: 'StrictSecurity');

      final prefs = await SharedPreferences.getInstance();

      // 🚀 优化：并行执行独立的初始化任务
      await Future.wait([
        _ensureDeviceId(prefs),
        _recordFirstInstall(prefs),
        _loadSecurityState(prefs),
      ]);

      // 检查许可证状态（依赖前面的结果）
      await _validateLicenseStatus(prefs);

      _isInitialized = true;
      AppLogger.info('✅ 严格安全服务初始化完成', tag: 'StrictSecurity');
    } catch (e, stackTrace) {
      AppLogger.error('❌ 严格安全服务初始化失败',
          error: e, stackTrace: stackTrace, tag: 'StrictSecurity');
      rethrow;
    }
  }

  /// 🔍 检查应用是否可以启动 - 优化强制激活逻辑
  static Future<Map<String, dynamic>> checkAppLaunchPermission() async {
    await _ensureInitialized();

    try {
      AppLogger.info('🔍 开始检查应用启动权限');

      // 首先检查是否需要强制激活
      final shouldForce = await AppSecurityService.shouldForceActivation();
      if (shouldForce) {
        AppLogger.info('❌ 检测到强制激活要求');
        return {
          'canLaunch': false,
          'shouldRedirectToActivation': true,
          'message': '需要重新激活',
          'userRole': UserRole.trial,
        };
      }

      final licenseStatus = await getLicenseStatus();
      final isValid = licenseStatus['isValid'] as bool;
      final userRole = licenseStatus['userRole'] as UserRole;
      final remainingDays = licenseStatus['remainingDays'] as int;

      AppLogger.info('📊 许可证状态: isValid=$isValid, userRole=$userRole, remainingDays=$remainingDays');

      // 超级管理员永远可以启动
      if (userRole == UserRole.superAdmin) {
        AppLogger.info('✅ 超级管理员权限，允许启动');
        return {
          'canLaunch': true,
          'shouldRedirectToActivation': false,
          'message': '超级管理员权限',
          'userRole': userRole,
        };
      }

      // 🔧 修复：正确的许可证状态检查逻辑
      if (!isValid || remainingDays <= 0) {
        AppLogger.warning('❌ 许可证已过期: isValid=$isValid, remainingDays=$remainingDays, userRole=$userRole');
        return {
          'canLaunch': false,
          'shouldRedirectToActivation': true,
          'message': userRole == UserRole.trial ? '试用期已过期，请激活' : '许可证已过期，请重新激活',
          'userRole': userRole,
        };
      }

      // 🔧 修复：有效期内正常使用，不需要激活
      AppLogger.info('✅ 许可证有效，正常使用: userRole=$userRole, remainingDays=$remainingDays');
      return {
        'canLaunch': true,
        'shouldRedirectToActivation': false,
        'message': userRole == UserRole.trial
            ? '试用期有效，剩余${remainingDays}天'
            : '许可证有效，剩余${remainingDays}天',
        'userRole': userRole,
      };
    } catch (e) {
      AppLogger.error('❌ 检查启动权限失败: $e', error: e);
      return {
        'canLaunch': false,
        'shouldRedirectToActivation': true,
        'message': '安全检查失败',
        'userRole': UserRole.trial,
      };
    }
  }

  /// 📱 获取许可证状态
  static Future<Map<String, dynamic>> getLicenseStatus() async {
    await _ensureInitialized();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final licenseData = prefs.getString(_licenseKey);
      
      if (licenseData == null) {
        return _createEmptyLicenseStatus();
      }
      
      final license = jsonDecode(licenseData) as Map<String, dynamic>;
      final expiryTime = license['expiryTime'] as int;
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final remainingTime = expiryTime - currentTime;
      final remainingDays = (remainingTime / (24 * 60 * 60 * 1000)).ceil();
      
      final userRoleStr = license['userRole'] as String;
      final userRole = UserRole.values.firstWhere(
        (role) => role.name == userRoleStr,
        orElse: () => UserRole.trial,
      );
      
      final licenseTypeStr = license['licenseType'] as String;
      final licenseType = LicenseType.values.firstWhere(
        (type) => type.name == licenseTypeStr,
        orElse: () => LicenseType.trial,
      );
      
      return {
        'isValid': remainingDays > 0 || userRole == UserRole.superAdmin,
        'userRole': userRole,
        'licenseType': licenseType,
        'remainingDays': userRole == UserRole.superAdmin ? -1 : remainingDays,
        'expiryDate': DateTime.fromMillisecondsSinceEpoch(expiryTime),
        'activatedAt': DateTime.fromMillisecondsSinceEpoch(license['activatedAt'] as int),
      };
    } catch (e) {
      AppLogger.error('获取许可证状态失败: $e', error: e);
      return _createEmptyLicenseStatus();
    }
  }

  /// 🎯 激活试用期
  static Future<Map<String, dynamic>> activateTrial() async {
    await _ensureInitialized();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 检查是否已经激活过试用期
      final hasTrialActivated = prefs.getBool(_trialActivatedKey) ?? false;
      if (hasTrialActivated) {
        return {
          'success': false,
          'message': '试用期只能激活一次',
          'shouldNavigateHome': false,
        };
      }
      
      // 检查当前状态
      final currentStatus = await getLicenseStatus();
      final currentRole = currentStatus['userRole'] as UserRole;
      
      if (currentRole == UserRole.activated || currentRole == UserRole.superAdmin) {
        return {
          'success': false,
          'message': '当前账户已激活，无需试用',
          'shouldNavigateHome': false,
        };
      }
      
      // 激活7天试用期
      final activatedAt = DateTime.now().millisecondsSinceEpoch;
      final expiryTime = activatedAt + (7 * 24 * 60 * 60 * 1000); // 7天
      
      final licenseData = {
        'licenseType': LicenseType.trial.name,
        'userRole': UserRole.trial.name,
        'activatedAt': activatedAt,
        'expiryTime': expiryTime,
        'deviceId': await _getDeviceId(),
        'activationId': _generateActivationId(),
      };
      
      await prefs.setString(_licenseKey, jsonEncode(licenseData));
      await prefs.setBool(_trialActivatedKey, true);
      
      // 记录激活历史
      await _recordActivationHistory('trial', 'self_activated');

      // 🔐 激活成功后清除强制激活标记
      await AppSecurityService.clearForceActivationFlag();

      AppLogger.info('✅ 试用期激活成功');

      return {
        'success': true,
        'message': '试用期激活成功，有效期7天',
        'shouldNavigateHome': true,
      };
    } catch (e) {
      AppLogger.error('试用期激活失败: $e', error: e);
      return {
        'success': false,
        'message': '激活失败: $e',
        'shouldNavigateHome': false,
      };
    }
  }

  /// 🔑 验证激活码
  static Future<Map<String, dynamic>> validateActivationCode(String code) async {
    await _ensureInitialized();
    
    try {
      // 检查是否为超级管理员激活码
      if (await _validateSuperAdminCode(code)) {
        await _activateAsSuperAdmin();
        // 🔐 激活成功后清除强制激活标记
        await AppSecurityService.clearForceActivationFlag();
        return {
          'success': true,
          'message': '超级管理员激活成功',
          'shouldNavigateHome': true,
          'userRole': UserRole.superAdmin,
        };
      }

      // 检查是否为标准激活码
      final validationResult = await _validateStandardActivationCode(code);
      if (validationResult['isValid'] as bool) {
        await _activateWithCode(code, validationResult);
        // 🔐 激活成功后清除强制激活标记
        await AppSecurityService.clearForceActivationFlag();
        return {
          'success': true,
          'message': '激活成功',
          'shouldNavigateHome': true,
          'userRole': UserRole.activated,
        };
      }
      
      return {
        'success': false,
        'message': '激活码无效或已过期',
        'shouldNavigateHome': false,
      };
    } catch (e) {
      AppLogger.error('验证激活码失败: $e', error: e);
      return {
        'success': false,
        'message': '验证失败: $e',
        'shouldNavigateHome': false,
      };
    }
  }

  /// 🔧 私有方法：确保初始化
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// 🔧 私有方法：确保设备ID存在（统一使用AppSecurityService）
  static Future<void> _ensureDeviceId(SharedPreferences prefs) async {
    if (!prefs.containsKey(_deviceIdKey)) {
      final deviceId = await AppSecurityService.getDeviceId();
      await prefs.setString(_deviceIdKey, deviceId);
      AppLogger.info('使用统一设备ID: $deviceId');
    }
  }

  /// 🔧 私有方法：记录首次安装
  static Future<void> _recordFirstInstall(SharedPreferences prefs) async {
    if (!prefs.containsKey(_firstInstallKey)) {
      await prefs.setInt(_firstInstallKey, DateTime.now().millisecondsSinceEpoch);
      AppLogger.info('记录首次安装时间');
    }
  }

  /// 🔧 私有方法：加载安全状态
  static Future<void> _loadSecurityState(SharedPreferences prefs) async {
    final stateData = prefs.getString(_securityStateKey);
    if (stateData != null) {
      _securityState = jsonDecode(stateData) as Map<String, dynamic>;
    } else {
      _securityState = {
        'initialized': true,
        'lastCheck': DateTime.now().millisecondsSinceEpoch,
      };
      await _saveSecurityState(prefs);
    }
  }

  /// 🔧 私有方法：保存安全状态
  static Future<void> _saveSecurityState(SharedPreferences prefs) async {
    await prefs.setString(_securityStateKey, jsonEncode(_securityState));
  }

  /// 🔧 私有方法：验证许可证状态
  static Future<void> _validateLicenseStatus(SharedPreferences prefs) async {
    // 这里可以添加额外的许可证验证逻辑
    // 例如：检查时间是否被篡改、验证许可证完整性等
  }

  /// 🔧 私有方法：创建空许可证状态
  static Map<String, dynamic> _createEmptyLicenseStatus() {
    return {
      'isValid': false,
      'userRole': UserRole.trial,
      'licenseType': LicenseType.trial,
      'remainingDays': 0,
      'expiryDate': null,
      'activatedAt': null,
    };
  }

  /// 🔧 私有方法：获取设备ID（统一使用AppSecurityService）
  static Future<String> _getDeviceId() async {
    return await AppSecurityService.getDeviceId();
  }

  /// 🔧 私有方法：生成激活ID
  static String _generateActivationId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomPart = List.generate(8, (index) => random.nextInt(16).toRadixString(16)).join();
    return '${timestamp.toRadixString(16)}-$randomPart'.toUpperCase();
  }

  /// 🔧 私有方法：记录激活历史
  static Future<void> _recordActivationHistory(String type, String method) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyData = prefs.getString(_activationHistoryKey);
      List<Map<String, dynamic>> history = [];

      if (historyData != null) {
        history = (jsonDecode(historyData) as List).cast<Map<String, dynamic>>();
      }

      history.add({
        'type': type,
        'method': method,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'deviceId': await _getDeviceId(),
      });

      // 只保留最近50条记录
      if (history.length > 50) {
        history = history.sublist(history.length - 50);
      }

      await prefs.setString(_activationHistoryKey, jsonEncode(history));
    } catch (e) {
      AppLogger.error('记录激活历史失败: $e', error: e);
    }
  }

  /// 🔐 私有方法：验证超级管理员激活码
  static Future<bool> _validateSuperAdminCode(String code) async {
    try {
      // 方法1：基于时间的动态码（每小时变化）
      final timeBasedCode = _generateTimeBasedAdminCode();
      if (code.toUpperCase().replaceAll('-', '') == timeBasedCode) {
        return true;
      }

      // 方法2：基于设备指纹的固定码
      final deviceCode = await _generateDeviceBasedAdminCode();
      if (code.toUpperCase().replaceAll('-', '') == deviceCode) {
        return true;
      }

      // 方法3：主密码（固定的超级管理员密码）
      const masterPassword = 'LOADGUARD2024ADMIN';
      if (code.toUpperCase().replaceAll('-', '') == masterPassword) {
        return true;
      }

      return false;
    } catch (e) {
      AppLogger.error('验证超级管理员码失败: $e', error: e);
      return false;
    }
  }

  /// 🔐 私有方法：生成基于时间的管理员码
  static String _generateTimeBasedAdminCode() {
    final now = DateTime.now();
    final hourKey = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}${now.hour.toString().padLeft(2, '0')}';
    final bytes = utf8.encode('LOADGUARD_ADMIN_$hourKey');
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 12).toUpperCase();
  }

  /// 🔐 私有方法：生成基于设备的管理员码
  static Future<String> _generateDeviceBasedAdminCode() async {
    final deviceId = await _getDeviceId();
    final today = DateTime.now();
    final dateKey = '${today.year}${today.month.toString().padLeft(2, '0')}${today.day.toString().padLeft(2, '0')}';
    final bytes = utf8.encode('LOADGUARD_DEVICE_${deviceId}_$dateKey');
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 12).toUpperCase();
  }

  /// 🔐 私有方法：激活为超级管理员
  static Future<void> _activateAsSuperAdmin() async {
    final prefs = await SharedPreferences.getInstance();
    final activatedAt = DateTime.now().millisecondsSinceEpoch;

    final licenseData = {
      'licenseType': LicenseType.permanent.name,
      'userRole': UserRole.superAdmin.name,
      'activatedAt': activatedAt,
      'expiryTime': activatedAt + (365 * 24 * 60 * 60 * 1000), // 1年，但管理员永不过期
      'deviceId': await _getDeviceId(),
      'activationId': _generateActivationId(),
      'isMasterDevice': true,
    };

    await prefs.setString(_licenseKey, jsonEncode(licenseData));
    await prefs.setString(_masterDeviceKey, await _getDeviceId());

    // 记录激活历史
    await _recordActivationHistory('super_admin', 'admin_code');

    AppLogger.info('✅ 超级管理员激活成功');
  }

  /// 🔐 私有方法：验证标准激活码
  static Future<Map<String, dynamic>> _validateStandardActivationCode(String code) async {
    try {
      // 这里实现标准激活码的验证逻辑
      // 激活码格式：PROF-XXXX-XXXX-XXXX-XXXX
      if (!code.toUpperCase().startsWith('PROF-')) {
        return {'isValid': false, 'reason': '激活码格式错误'};
      }

      // 简单的校验逻辑（实际应用中应该更复杂）
      final parts = code.toUpperCase().split('-');
      if (parts.length != 5) {
        return {'isValid': false, 'reason': '激活码格式错误'};
      }

      // 验证校验码（最后一部分）
      final checksum = _calculateActivationCodeChecksum(parts.sublist(0, 4).join(''));
      if (parts[4] != checksum) {
        return {'isValid': false, 'reason': '激活码校验失败'};
      }

      return {
        'isValid': true,
        'licenseType': LicenseType.permanent,
        'validityDays': 365, // 1年有效期
      };
    } catch (e) {
      AppLogger.error('验证标准激活码失败: $e', error: e);
      return {'isValid': false, 'reason': '验证过程出错'};
    }
  }

  /// 🔐 私有方法：使用激活码激活
  static Future<void> _activateWithCode(String code, Map<String, dynamic> validationResult) async {
    final prefs = await SharedPreferences.getInstance();
    final activatedAt = DateTime.now().millisecondsSinceEpoch;
    final validityDays = validationResult['validityDays'] as int;
    final expiryTime = activatedAt + (validityDays * 24 * 60 * 60 * 1000);

    final licenseData = {
      'licenseType': (validationResult['licenseType'] as LicenseType).name,
      'userRole': UserRole.activated.name,
      'activatedAt': activatedAt,
      'expiryTime': expiryTime,
      'deviceId': await _getDeviceId(),
      'activationId': _generateActivationId(),
      'activationCode': code,
    };

    await prefs.setString(_licenseKey, jsonEncode(licenseData));

    // 记录激活历史
    await _recordActivationHistory('standard', 'activation_code');

    AppLogger.info('✅ 标准激活成功');
  }

  /// 🔐 私有方法：计算激活码校验和
  static String _calculateActivationCodeChecksum(String codeWithoutChecksum) {
    final bytes = utf8.encode(codeWithoutChecksum);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 4).toUpperCase();
  }

  /// 🎯 超级管理员：为其他设备生成激活码
  static Future<Map<String, dynamic>> generateActivationCodeForDevice({
    required String targetDeviceId,
    required LicenseType licenseType,
    required int validityDays,
  }) async {
    await _ensureInitialized();

    try {
      // 检查当前用户是否为超级管理员
      final currentStatus = await getLicenseStatus();
      if (currentStatus['userRole'] != UserRole.superAdmin) {
        return {
          'success': false,
          'message': '只有超级管理员才能生成激活码',
        };
      }

      // 生成唯一的激活码
      final activationCode = _generateUniqueActivationCode(targetDeviceId, licenseType, validityDays);

      // 记录生成的激活码（用于验证）
      await _recordGeneratedActivationCode(activationCode, targetDeviceId, licenseType, validityDays);

      return {
        'success': true,
        'activationCode': activationCode,
        'targetDeviceId': targetDeviceId,
        'licenseType': licenseType.name,
        'validityDays': validityDays,
        'generatedAt': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('生成激活码失败: $e', error: e);
      return {
        'success': false,
        'message': '生成失败: $e',
      };
    }
  }

  /// 🔐 私有方法：生成唯一激活码
  static String _generateUniqueActivationCode(String deviceId, LicenseType licenseType, int validityDays) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random();

    // 生成基础码
    final baseCode = '${licenseType.name.toUpperCase().substring(0, 4)}-${timestamp.toRadixString(16).toUpperCase().substring(0, 4)}-${deviceId.substring(0, 4)}-${validityDays.toRadixString(16).toUpperCase().padLeft(4, '0')}';

    // 添加随机部分确保每次都不同
    final randomPart = List.generate(4, (index) => random.nextInt(16).toRadixString(16)).join().toUpperCase();

    // 计算校验码
    final checksum = _calculateActivationCodeChecksum('$baseCode-$randomPart');

    return '$baseCode-$randomPart-$checksum';
  }

  /// 🔐 私有方法：记录生成的激活码
  static Future<void> _recordGeneratedActivationCode(String code, String deviceId, LicenseType licenseType, int validityDays) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      const key = '${_keyPrefix}generated_codes';
      final codesData = prefs.getString(key);
      List<Map<String, dynamic>> codes = [];

      if (codesData != null) {
        codes = (jsonDecode(codesData) as List).cast<Map<String, dynamic>>();
      }

      codes.add({
        'code': code,
        'targetDeviceId': deviceId,
        'licenseType': licenseType.name,
        'validityDays': validityDays,
        'generatedAt': DateTime.now().millisecondsSinceEpoch,
        'generatedBy': await _getDeviceId(),
        'used': false,
      });

      // 只保留最近100条记录
      if (codes.length > 100) {
        codes = codes.sublist(codes.length - 100);
      }

      await prefs.setString(key, jsonEncode(codes));
    } catch (e) {
      AppLogger.error('记录生成的激活码失败: $e', error: e);
    }
  }

  /// 🔍 检查功能权限
  static Future<bool> hasPermission(String featureName) async {
    await _ensureInitialized();

    try {
      final launchPermission = await checkAppLaunchPermission();
      if (!(launchPermission['canLaunch'] as bool)) {
        return false;
      }

      final licenseStatus = await getLicenseStatus();
      final userRole = licenseStatus['userRole'] as UserRole;
      final licenseType = licenseStatus['licenseType'] as LicenseType;

      // 超级管理员拥有所有权限
      if (userRole == UserRole.superAdmin) {
        return true;
      }

      // 根据许可证类型和功能名称检查权限
      return _checkFeaturePermission(featureName, licenseType, userRole);
    } catch (e) {
      AppLogger.error('检查功能权限失败: $e', error: e);
      return false;
    }
  }

  /// 🔐 私有方法：检查功能权限
  static bool _checkFeaturePermission(String featureName, LicenseType licenseType, UserRole userRole) {
    // 基础功能权限映射
    const basicFeatures = ['basicRecognition', 'taskManagement'];
    const advancedFeatures = ['advancedRecognition', 'batchProcessing', 'dataExport'];
    const adminFeatures = ['deviceManagement', 'codeGeneration', 'systemSettings'];

    switch (licenseType) {
      case LicenseType.trial:
        return basicFeatures.contains(featureName) || advancedFeatures.contains(featureName);
      case LicenseType.monthly:
        return basicFeatures.contains(featureName);
      case LicenseType.quarterly:
        return basicFeatures.contains(featureName) || advancedFeatures.contains(featureName);
      case LicenseType.annual:
        return basicFeatures.contains(featureName) || advancedFeatures.contains(featureName);
      case LicenseType.permanent:
        return basicFeatures.contains(featureName) ||
               advancedFeatures.contains(featureName) ||
               (userRole == UserRole.superAdmin && adminFeatures.contains(featureName));
      default:
        return false;
    }
  }

  /// 🧹 清理过期数据
  static Future<void> cleanup() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清理过期的生成码记录
      const key = '${_keyPrefix}generated_codes';
      final codesData = prefs.getString(key);
      if (codesData != null) {
        final codes = (jsonDecode(codesData) as List).cast<Map<String, dynamic>>();
        final now = DateTime.now().millisecondsSinceEpoch;
        final validCodes = codes.where((code) {
          final generatedAt = code['generatedAt'] as int;
          final age = now - generatedAt;
          return age < (30 * 24 * 60 * 60 * 1000); // 保留30天内的记录
        }).toList();

        await prefs.setString(key, jsonEncode(validCodes));
      }

      AppLogger.info('✅ 安全数据清理完成');
    } catch (e) {
      AppLogger.error('安全数据清理失败: $e', error: e);
    }
  }
}
