<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>装运卫士</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>loadguard</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	
	<!-- 明确最低支持iOS版本 -->
	<key>MinimumOSVersion</key>
	<string>11.0</string>
	
	<!-- 支持的设备方向 -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- iPad支持 -->
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	
	<!-- 相机权限 -->
	<key>NSCameraUsageDescription</key>
	<string>装运卫士需要访问相机来拍摄货物照片进行识别</string>
	
	<!-- 相册权限 -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>装运卫士需要访问相册来选择货物照片进行识别</string>
	
	<!-- 网络权限 -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	
	<!-- 后台处理 -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>background-fetch</string>
	</array>
	
	<!-- 应用图标 -->
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundlePrimaryIcon</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon</string>
			</array>
			<key>CFBundleIconName</key>
			<string>AppIcon</string>
		</dict>
	</dict>
	
	<!-- 启动屏幕 -->
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	
	<!-- 状态栏样式 -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	
	<!-- 隐藏状态栏 -->
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	
	<!-- 支持的文件类型 -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Images</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.image</string>
				<string>public.jpeg</string>
				<string>public.png</string>
			</array>
		</dict>
	</array>
	
	<!-- 导入文件支持 -->
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	
	<!-- 本地化支持 -->
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh_CN</string>
		<string>en</string>
	</array>
	
	<!-- 应用内购买 -->
	<key>SKAdNetworkItems</key>
	<array>
		<dict>
			<key>SKAdNetworkIdentifier</key>
			<string>your.skadnetwork.identifier</string>
		</dict>
	</array>
	
	<!-- 隐私政策 -->
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	
	<!-- 应用商店信息 -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>https</string>
		<string>http</string>
	</array>
</dict>
</plist>
