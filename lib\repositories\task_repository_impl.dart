import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/data/datasources/hive_task_data_source.dart';
import 'package:loadguard/data/datasources/shared_preferences_task_data_source.dart';
import 'package:loadguard/data/datasources/task_data_source.dart';
import 'package:loadguard/utils/app_logger.dart';

/// ✅ 简化的TaskRepository实现
/// 主要使用Hive存储，SharedPreferences仅用于数据迁移
class TaskRepositoryImpl implements TaskRepository {
  final TaskDataSource _primaryDataSource;
  final TaskDataSource? _migrationDataSource;

  // 内存缓存，确保数据一致性
  List<TaskModel>? _cachedTasks;
  TaskModel? _cachedCurrentTask;
  bool _cacheValid = false;
  bool _migrationCompleted = false;

  TaskRepositoryImpl({
    TaskDataSource? primaryDataSource,
    TaskDataSource? migrationDataSource,
  }) : _primaryDataSource = primaryDataSource ?? HiveTaskDataSource(),
       _migrationDataSource = migrationDataSource ?? SharedPreferencesTaskDataSource();

  /// 初始化Repository
  @override
  Future<void> initialize() async {
    try {
      // 新的数据源不需要显式初始化
      // 检查是否需要数据迁移
      await _checkAndMigrateData();

      AppLogger.info('✅ TaskRepository 初始化成功');
    } catch (e) {
      AppLogger.error('❌ TaskRepository 初始化失败: $e');
      throw RepositoryException('Repository初始化失败', originalError: e);
    }
  }

  @override
  Future<List<TaskModel>> getAllTasks() async {
    try {
      // 如果缓存有效，直接返回缓存
      if (_cacheValid && _cachedTasks != null) {
        return List.from(_cachedTasks!);
      }
      
      // 从主数据源加载
      final tasks = await _primaryDataSource.getAllTasks();
      
      // 更新缓存
      _cachedTasks = List.from(tasks);
      _cacheValid = true;
      
      AppLogger.info('📚 获取所有任务: ${tasks.length}个');
      return List.from(tasks);
    } catch (e) {
      AppLogger.error('❌ 获取所有任务失败: $e');
      throw RepositoryException('获取任务列表失败', originalError: e);
    }
  }

  @override
  Future<TaskModel?> getTaskById(String id) async {
    try {
      final tasks = await getAllTasks();
      return tasks.where((task) => task.id == id).firstOrNull;
    } catch (e) {
      AppLogger.error('❌ 根据ID获取任务失败: $e');
      throw RepositoryException('获取任务失败', originalError: e);
    }
  }

  @override
  Future<void> saveTask(TaskModel task) async {
    try {
      // 保存到主数据源
      await _primaryDataSource.saveTask(task);
      
      // ✅ 简化：移除自动备份逻辑
      
      // 更新缓存
      await _updateTaskInCache(task);
      
      AppLogger.info('💾 保存任务成功: ${task.id}');
    } catch (e) {
      AppLogger.error('❌ 保存任务失败: $e');
      throw RepositoryException('保存任务失败', originalError: e);
    }
  }

  @override
  Future<void> saveTasks(List<TaskModel> tasks) async {
    try {
      // 保存到主数据源
      await _primaryDataSource.saveTasks(tasks);
      
      // ✅ 简化：移除自动备份逻辑
      
      // 更新缓存
      _cachedTasks = List.from(tasks);
      _cacheValid = true;
      
      AppLogger.info('💾 批量保存任务成功: ${tasks.length}个');
    } catch (e) {
      AppLogger.error('❌ 批量保存任务失败: $e');
      throw RepositoryException('批量保存任务失败', originalError: e);
    }
  }

  @override
  Future<void> deleteTask(String id) async {
    try {
      // 从主数据源删除
      await _primaryDataSource.deleteTask(id);
      
      // ✅ 简化：移除自动备份逻辑
      
      // 更新缓存
      if (_cachedTasks != null) {
        _cachedTasks!.removeWhere((task) => task.id == id);
      }
      
      // 如果删除的是当前任务，清除当前任务
      if (_cachedCurrentTask?.id == id) {
        _cachedCurrentTask = null;
        await _primaryDataSource.setCurrentTaskId(null);
      }
      
      AppLogger.info('🗑️ 删除任务成功: $id');
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $e');
      throw RepositoryException('删除任务失败', originalError: e);
    }
  }

  @override
  Future<void> clearAllTasks() async {
    try {
      // 清空主数据源
      await _primaryDataSource.clearAllTasks();
      
      // ✅ 简化：移除自动备份逻辑
      
      // 清空缓存
      _cachedTasks = [];
      _cachedCurrentTask = null;
      _cacheValid = true;
      
      AppLogger.info('🧹 清空所有任务成功');
    } catch (e) {
      AppLogger.error('❌ 清空所有任务失败: $e');
      throw RepositoryException('清空任务失败', originalError: e);
    }
  }

  @override
  Future<TaskModel?> getCurrentTask() async {
    try {
      // 如果缓存有效，直接返回缓存
      if (_cacheValid && _cachedCurrentTask != null) {
        return _cachedCurrentTask;
      }
      
      // 从数据源获取当前任务ID
      final currentTaskId = await _primaryDataSource.getCurrentTaskId();
      if (currentTaskId == null) {
        _cachedCurrentTask = null;
        return null;
      }
      
      // 根据ID获取任务
      final currentTask = await getTaskById(currentTaskId);
      _cachedCurrentTask = currentTask;
      
      return currentTask;
    } catch (e) {
      AppLogger.error('❌ 获取当前任务失败: $e');
      throw RepositoryException('获取当前任务失败', originalError: e);
    }
  }

  @override
  Future<void> setCurrentTask(TaskModel? task) async {
    try {
      // 设置当前任务ID
      await _primaryDataSource.setCurrentTaskId(task?.id);
      
      // ✅ 简化：移除自动备份逻辑
      
      // 更新缓存
      _cachedCurrentTask = task;
      
      AppLogger.info('📌 设置当前任务: ${task?.id}');
    } catch (e) {
      AppLogger.error('❌ 设置当前任务失败: $e');
      throw RepositoryException('设置当前任务失败', originalError: e);
    }
  }

  @override
  Future<List<TaskModel>> queryTasks({
    DateTime? startDate,
    DateTime? endDate,
    String? template,
    bool? isCompleted,
  }) async {
    try {
      final allTasks = await getAllTasks();
      
      return allTasks.where((task) {
        // 日期过滤
        if (startDate != null && task.createTime.isBefore(startDate)) {
          return false;
        }
        if (endDate != null && task.createTime.isAfter(endDate)) {
          return false;
        }
        
        // 模板过滤
        if (template != null && task.template != template) {
          return false;
        }
        
        // 完成状态过滤
        if (isCompleted != null && task.isCompleted != isCompleted) {
          return false;
        }
        
        return true;
      }).toList();
    } catch (e) {
      AppLogger.error('❌ 查询任务失败: $e');
      throw RepositoryException('查询任务失败', originalError: e);
    }
  }

  @override
  Future<Map<String, dynamic>> getTaskStatistics() async {
    try {
      final tasks = await getAllTasks();
      
      final totalTasks = tasks.length;
      final completedTasks = tasks.where((task) => task.isCompleted).length;
      final pendingTasks = totalTasks - completedTasks;
      
      final totalQuantity = tasks.fold<int>(0, (sum, task) => sum + task.quantity);
      final completedQuantity = tasks
          .where((task) => task.isCompleted)
          .fold<int>(0, (sum, task) => sum + task.quantity);
      
      return {
        'totalTasks': totalTasks,
        'completedTasks': completedTasks,
        'pendingTasks': pendingTasks,
        'completionRate': totalTasks > 0 ? completedTasks / totalTasks : 0.0,
        'totalQuantity': totalQuantity,
        'completedQuantity': completedQuantity,
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.error('❌ 获取任务统计失败: $e');
      throw RepositoryException('获取任务统计失败', originalError: e);
    }
  }

  @override
  Future<void> migrateData() async {
    await _checkAndMigrateData();
  }

  @override
  Future<void> backupData() async {
    if (_migrationDataSource == null) {
      throw RepositoryException('备份数据源不可用');
    }

    try {
      final tasks = await _primaryDataSource.getAllTasks();
      await _migrationDataSource!.saveTasks(tasks);

      final currentTaskId = await _primaryDataSource.getCurrentTaskId();
      await _migrationDataSource!.setCurrentTaskId(currentTaskId);

      AppLogger.info('📦 数据备份成功');
    } catch (e) {
      AppLogger.error('❌ 数据备份失败: $e');
      throw RepositoryException('数据备份失败', originalError: e);
    }
  }

  @override
  Future<void> restoreData() async {
    if (_migrationDataSource == null) {
      throw RepositoryException('恢复数据源不可用');
    }

    try {
      final tasks = await _migrationDataSource!.getAllTasks();
      await _primaryDataSource.saveTasks(tasks);

      final currentTaskId = await _migrationDataSource!.getCurrentTaskId();
      await _primaryDataSource.setCurrentTaskId(currentTaskId);

      // 清空缓存，强制重新加载
      _invalidateCache();

      AppLogger.info('🔄 数据恢复成功');
    } catch (e) {
      AppLogger.error('❌ 数据恢复失败: $e');
      throw RepositoryException('数据恢复失败', originalError: e);
    }
  }

  /// ✅ 简化的数据迁移逻辑
  Future<void> _checkAndMigrateData() async {
    if (_migrationCompleted || _migrationDataSource == null) {
      return;
    }

    try {
      // 检查主数据源是否有数据
      final primaryTasks = await _primaryDataSource.getAllTasks();

      // 只在主数据源为空且迁移数据源有数据时才迁移
      if (primaryTasks.isEmpty) {
        AppLogger.info('🔄 检查数据迁移需求...');

        final migrationTasks = await _migrationDataSource!.getAllTasks();
        if (migrationTasks.isNotEmpty) {
          AppLogger.info('🔄 开始一次性数据迁移: ${migrationTasks.length}个任务');

          await _primaryDataSource.saveTasks(migrationTasks);

          final currentTaskId = await _migrationDataSource!.getCurrentTaskId();
          if (currentTaskId != null) {
            await _primaryDataSource.setCurrentTaskId(currentTaskId);
          }

          AppLogger.info('✅ 数据迁移完成，后续将仅使用Hive存储');
        }
      }

      _migrationCompleted = true;
    } catch (e) {
      AppLogger.error('❌ 数据迁移失败: $e');
      // 迁移失败不应该阻止Repository初始化
      _migrationCompleted = true; // 标记为完成，避免重复尝试
    }
  }

  /// 更新缓存中的任务
  Future<void> _updateTaskInCache(TaskModel task) async {
    if (_cachedTasks != null) {
      final index = _cachedTasks!.indexWhere((t) => t.id == task.id);
      if (index >= 0) {
        _cachedTasks![index] = task;
      } else {
        _cachedTasks!.add(task);
      }
    }
  }

  /// ✅ 简化：移除自动备份逻辑，专注于Hive存储
  /// 备份功能仅在手动调用backupData()时执行

  /// ✅ 简化：移除备用数据源操作方法

  /// 使缓存失效
  void _invalidateCache() {
    _cacheValid = false;
    _cachedTasks = null;
    _cachedCurrentTask = null;
  }

  /// 监听任务列表变化（用于状态管理）
  Stream<List<TaskModel>> watchTasks() async* {
    // 初始数据
    yield await getAllTasks();

    // 这里可以添加实时监听逻辑
    // 目前返回初始数据，后续可以扩展为实时流
  }

  /// 监听当前任务变化（用于状态管理）
  Stream<TaskModel?> watchCurrentTask() async* {
    // 初始数据
    yield await getCurrentTask();

    // 这里可以添加实时监听逻辑
    // 目前返回初始数据，后续可以扩展为实时流
  }

  /// 释放资源（别名方法）
  void dispose() {
    close();
  }

  /// 关闭Repository
  Future<void> close() async {
    // 新的数据源不需要显式关闭
    _invalidateCache();
    AppLogger.info('🔒 TaskRepository 已关闭');
  }
}
