import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/repositories/hive_task_data_source.dart';
import 'package:loadguard/repositories/shared_prefs_task_data_source.dart';

void main() {
  group('TaskRepository Tests', () {
    late TaskRepositoryImpl repository;
    
    setUp(() async {
      // 注意：在实际测试中，我们需要使用Mock数据源
      // 这里只是基本的结构测试
      repository = TaskRepositoryImpl();
    });

    test('should create repository instance', () {
      expect(repository, isNotNull);
      expect(repository, isA<TaskRepositoryImpl>());
    });

    test('should handle empty task list', () async {
      // 这个测试需要Mock数据源才能正常运行
      // 在实际实现中，我们会使用mockito或类似的库
      // 暂时跳过这个测试，因为它需要实际的数据源初始化
      expect(repository, isNotNull);
    });

    test('should validate task model structure', () {
      // 测试TaskModel的基本结构
      final task = TaskModel(
        id: 'test-id',
        template: '平板车',
        productCode: 'TEST-001',
        batchNumber: '240101A12345',
        quantity: 10,
        createTime: DateTime.now(),
        photos: [],
      );

      expect(task.id, equals('test-id'));
      expect(task.template, equals('平板车'));
      expect(task.productCode, equals('TEST-001'));
      expect(task.batchNumber, equals('240101A12345'));
      expect(task.quantity, equals(10));
      expect(task.photos, isEmpty);
      expect(task.batches, isNotEmpty); // 应该有一个批次
    });

    test('should handle task serialization', () {
      final task = TaskModel(
        id: 'test-id',
        template: '平板车',
        productCode: 'TEST-001',
        batchNumber: '240101A12345',
        quantity: 10,
        createTime: DateTime.now(),
        photos: [],
      );

      // 测试序列化和反序列化
      final json = task.toJson();
      expect(json, isA<Map<String, dynamic>>());
      expect(json['id'], equals('test-id'));
      expect(json['template'], equals('平板车'));

      final deserializedTask = TaskModel.fromJson(json);
      expect(deserializedTask.id, equals(task.id));
      expect(deserializedTask.template, equals(task.template));
      // 由于TaskModel现在使用batches，productCode是计算属性
      expect(deserializedTask.productCode, equals(task.productCode));
    });

    tearDown(() async {
      // 清理资源
      await repository.close();
    });
  });

  group('Repository Exception Tests', () {
    test('should create repository exceptions', () {
      const exception = RepositoryException('Test error');
      expect(exception.message, equals('Test error'));
      expect(exception.toString(), contains('RepositoryException: Test error'));
    });

    test('should create data consistency exceptions', () {
      const exception = DataConsistencyException('Data inconsistent');
      expect(exception.message, equals('Data inconsistent'));
      expect(exception, isA<RepositoryException>());
    });

    test('should create data source unavailable exceptions', () {
      const exception = DataSourceUnavailableException('Source unavailable');
      expect(exception.message, equals('Source unavailable'));
      expect(exception, isA<RepositoryException>());
    });
  });
}
