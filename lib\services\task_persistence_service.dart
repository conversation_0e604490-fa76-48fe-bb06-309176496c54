/// 💾 任务持久化服务
///
/// 专门负责任务数据的持久化，包括：
/// - 任务保存和加载
/// - 数据同步管理
/// - 缓存策略
/// - 备份和恢复
/// 从TaskService中提取，减少其复杂度
library task_persistence_service;

import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 持久化策略
enum PersistenceStrategy {
  repositoryOnly,    // 仅使用Repository
  preferencesOnly,   // 仅使用SharedPreferences
  dual,             // 双重保存
  repositoryFirst,  // Repository优先，失败时使用SharedPreferences
}

/// 任务持久化服务
class TaskPersistenceService {
  final TaskRepository _repository;
  SharedPreferences? _prefs;
  
  // 持久化配置
  PersistenceStrategy _strategy = PersistenceStrategy.dual;
  bool _autoSave = true;
  Duration _autoSaveInterval = const Duration(seconds: 30);
  
  // 自动保存定时器
  Timer? _autoSaveTimer;
  bool _hasPendingChanges = false;
  
  // 数据监听
  final StreamController<List<TaskModel>> _tasksController = StreamController.broadcast();
  final StreamController<TaskModel?> _currentTaskController = StreamController.broadcast();

  TaskPersistenceService({TaskRepository? repository})
      : _repository = repository ?? TaskRepositoryImpl();

  /// 初始化持久化服务
  Future<void> initialize() async {
    try {
      AppLogger.info('💾 初始化持久化服务...', tag: 'TaskPersistence');
      
      // 初始化SharedPreferences
      _prefs = await SharedPreferences.getInstance();
      
      // 初始化Repository
      await _repository.initialize();
      
      // 启动自动保存
      if (_autoSave) {
        _startAutoSave();
      }
      
      AppLogger.info('✅ 持久化服务初始化完成', tag: 'TaskPersistence');
    } catch (e) {
      AppLogger.error('❌ 持久化服务初始化失败: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 保存任务
  Future<void> saveTask(TaskModel task) async {
    try {
      AppLogger.info('💾 保存任务: ${task.id}', tag: 'TaskPersistence');
      
      switch (_strategy) {
        case PersistenceStrategy.repositoryOnly:
          await _repository.saveTask(task);
          break;
        case PersistenceStrategy.preferencesOnly:
          await _saveToPreferences(task);
          break;
        case PersistenceStrategy.dual:
          await Future.wait([
            _repository.saveTask(task),
            _saveToPreferences(task),
          ]);
          break;
        case PersistenceStrategy.repositoryFirst:
          try {
            await _repository.saveTask(task);
          } catch (e) {
            AppLogger.warning('Repository保存失败，使用SharedPreferences: $e', tag: 'TaskPersistence');
            await _saveToPreferences(task);
          }
          break;
      }
      
      AppLogger.info('✅ 任务保存成功: ${task.id}', tag: 'TaskPersistence');
    } catch (e) {
      AppLogger.error('❌ 保存任务失败: ${task.id}, 错误: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 批量保存任务
  Future<void> saveTasks(List<TaskModel> tasks) async {
    try {
      AppLogger.info('💾 批量保存任务: ${tasks.length}个', tag: 'TaskPersistence');
      
      switch (_strategy) {
        case PersistenceStrategy.repositoryOnly:
          await _repository.saveTasks(tasks);
          break;
        case PersistenceStrategy.preferencesOnly:
          await _saveTasksToPreferences(tasks);
          break;
        case PersistenceStrategy.dual:
          await Future.wait([
            _repository.saveTasks(tasks),
            _saveTasksToPreferences(tasks),
          ]);
          break;
        case PersistenceStrategy.repositoryFirst:
          try {
            await _repository.saveTasks(tasks);
          } catch (e) {
            AppLogger.warning('Repository批量保存失败，使用SharedPreferences: $e', tag: 'TaskPersistence');
            await _saveTasksToPreferences(tasks);
          }
          break;
      }
      
      // 通知监听者
      _tasksController.add(tasks);
      
      AppLogger.info('✅ 批量保存任务成功: ${tasks.length}个', tag: 'TaskPersistence');
    } catch (e) {
      AppLogger.error('❌ 批量保存任务失败: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 加载所有任务
  Future<List<TaskModel>> loadAllTasks() async {
    try {
      AppLogger.info('📖 加载所有任务...', tag: 'TaskPersistence');
      
      List<TaskModel> tasks = [];
      
      // 优先从Repository加载
      try {
        tasks = await _repository.getAllTasks();
        AppLogger.info('📖 从Repository加载任务: ${tasks.length}个', tag: 'TaskPersistence');
      } catch (e) {
        AppLogger.warning('Repository加载失败，尝试从SharedPreferences加载: $e', tag: 'TaskPersistence');
        tasks = await _loadTasksFromPreferences();
        AppLogger.info('📖 从SharedPreferences加载任务: ${tasks.length}个', tag: 'TaskPersistence');
      }
      
      // 通知监听者
      _tasksController.add(tasks);
      
      AppLogger.info('✅ 任务加载完成: ${tasks.length}个', tag: 'TaskPersistence');
      return tasks;
    } catch (e) {
      AppLogger.error('❌ 加载任务失败: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 保存当前任务
  Future<void> saveCurrentTask(TaskModel? task) async {
    try {
      AppLogger.info('💾 保存当前任务: ${task?.id ?? 'null'}', tag: 'TaskPersistence');
      
      await _repository.setCurrentTask(task);
      
      // 通知监听者
      _currentTaskController.add(task);
      
      AppLogger.info('✅ 当前任务保存成功', tag: 'TaskPersistence');
    } catch (e) {
      AppLogger.error('❌ 保存当前任务失败: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 加载当前任务
  Future<TaskModel?> loadCurrentTask() async {
    try {
      AppLogger.info('📖 加载当前任务...', tag: 'TaskPersistence');
      
      final task = await _repository.getCurrentTask();
      
      // 通知监听者
      _currentTaskController.add(task);
      
      AppLogger.info('✅ 当前任务加载完成: ${task?.id ?? 'null'}', tag: 'TaskPersistence');
      return task;
    } catch (e) {
      AppLogger.error('❌ 加载当前任务失败: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    try {
      AppLogger.info('🗑️ 删除任务: $taskId', tag: 'TaskPersistence');
      
      await _repository.deleteTask(taskId);
      
      AppLogger.info('✅ 任务删除成功: $taskId', tag: 'TaskPersistence');
    } catch (e) {
      AppLogger.error('❌ 删除任务失败: $taskId, 错误: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 强制保存所有数据
  Future<void> forceSaveAllData() async {
    try {
      AppLogger.info('💾 强制保存所有数据...', tag: 'TaskPersistence');
      
      // 这里可以实现强制保存逻辑
      // 例如：保存所有缓存的数据、同步到云端等
      
      _hasPendingChanges = false;
      
      AppLogger.info('✅ 强制保存完成', tag: 'TaskPersistence');
    } catch (e) {
      AppLogger.error('❌ 强制保存失败: $e', tag: 'TaskPersistence');
      rethrow;
    }
  }

  /// 标记有待保存的更改
  void markPendingChanges() {
    _hasPendingChanges = true;
  }

  /// 配置持久化策略
  void configurePersistence({
    PersistenceStrategy? strategy,
    bool? autoSave,
    Duration? autoSaveInterval,
  }) {
    if (strategy != null) {
      _strategy = strategy;
      AppLogger.info('📝 持久化策略已更新: $strategy', tag: 'TaskPersistence');
    }
    
    if (autoSave != null) {
      _autoSave = autoSave;
      if (_autoSave) {
        _startAutoSave();
      } else {
        _stopAutoSave();
      }
      AppLogger.info('📝 自动保存已${_autoSave ? '启用' : '禁用'}', tag: 'TaskPersistence');
    }
    
    if (autoSaveInterval != null) {
      _autoSaveInterval = autoSaveInterval;
      if (_autoSave) {
        _startAutoSave(); // 重启定时器
      }
      AppLogger.info('📝 自动保存间隔已更新: ${_autoSaveInterval.inSeconds}秒', tag: 'TaskPersistence');
    }
  }

  /// 获取数据流
  Stream<List<TaskModel>> get tasksStream => _tasksController.stream;
  Stream<TaskModel?> get currentTaskStream => _currentTaskController.stream;

  /// 释放资源
  void dispose() {
    _stopAutoSave();
    _tasksController.close();
    _currentTaskController.close();
    AppLogger.info('🔄 持久化服务已释放', tag: 'TaskPersistence');
  }

  /// 私有方法：保存到SharedPreferences
  Future<void> _saveToPreferences(TaskModel task) async {
    if (_prefs == null) return;
    
    // 这里需要实现具体的SharedPreferences保存逻辑
    // 暂时简化实现
    final taskJson = jsonEncode(task.toJson());
    await _prefs!.setString('task_${task.id}', taskJson);
  }

  /// 私有方法：批量保存到SharedPreferences
  Future<void> _saveTasksToPreferences(List<TaskModel> tasks) async {
    if (_prefs == null) return;
    
    final tasksJson = jsonEncode(tasks.map((t) => t.toJson()).toList());
    await _prefs!.setString('tasks', tasksJson);
  }

  /// 私有方法：从SharedPreferences加载任务
  Future<List<TaskModel>> _loadTasksFromPreferences() async {
    if (_prefs == null) return [];
    
    final tasksJson = _prefs!.getString('tasks');
    if (tasksJson == null) return [];
    
    try {
      final tasksList = jsonDecode(tasksJson) as List;
      return tasksList.map((json) => TaskModel.fromJson(json)).toList();
    } catch (e) {
      AppLogger.error('解析SharedPreferences任务数据失败: $e', tag: 'TaskPersistence');
      return [];
    }
  }

  /// 私有方法：启动自动保存
  void _startAutoSave() {
    _stopAutoSave();
    _autoSaveTimer = Timer.periodic(_autoSaveInterval, (_) {
      if (_hasPendingChanges) {
        forceSaveAllData();
      }
    });
  }

  /// 私有方法：停止自动保存
  void _stopAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
  }
}
