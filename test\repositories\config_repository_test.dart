import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/config_models.dart';
import 'package:loadguard/repositories/config_repository_impl.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';

/// 模拟的HiveConfigDataSource用于测试
class MockHiveConfigDataSource extends HiveConfigDataSource {
  final Map<String, Map<String, dynamic>> _storage = {};

  @override
  Future<void> saveConfig(String key, Map<String, dynamic> data) async {
    _storage[key] = Map<String, dynamic>.from(data);
  }

  @override
  Future<Map<String, dynamic>?> getConfig(String key) async {
    return _storage[key];
  }

  @override
  Future<void> deleteConfig(String key) async {
    _storage.remove(key);
  }

  @override
  Future<List<String>> getAllConfigKeys() async {
    return _storage.keys.toList();
  }

  @override
  Future<void> clearAllConfigs() async {
    _storage.clear();
  }
}

void main() {
  group('ConfigRepository Tests', () {
    late ConfigRepositoryImpl repository;
    late MockHiveConfigDataSource mockDataSource;

    setUp(() {
      mockDataSource = MockHiveConfigDataSource();
      repository = ConfigRepositoryImpl(dataSource: mockDataSource);
    });

    group('WorkerConfig Tests', () {
      test('should save and retrieve worker config', () async {
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
          phone: '13800138000',
          email: '<EMAIL>',
        );

        await repository.saveWorker(worker);
        final retrieved = await repository.getWorkerById('worker1');

        expect(retrieved, isNotNull);
        expect(retrieved!.id, 'worker1');
        expect(retrieved.name, '张三');
        expect(retrieved.role, '操作员');
        expect(retrieved.phone, '13800138000');
      });

      test('should get all workers', () async {
        final worker1 = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        final worker2 = WorkerConfig(
          id: 'worker2',
          name: '李四',
          role: '管理员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        await repository.saveWorker(worker1);
        await repository.saveWorker(worker2);

        final workers = await repository.getAllWorkers();
        expect(workers.length, 2);
        expect(workers.map((w) => w.name), containsAll(['张三', '李四']));
      });

      test('should delete worker', () async {
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        await repository.saveWorker(worker);
        await repository.deleteWorker('worker1');

        final retrieved = await repository.getWorkerById('worker1');
        expect(retrieved, isNull);
      });
    });

    group('WarehouseConfig Tests', () {
      test('should save and retrieve warehouse config', () async {
        final warehouse = WarehouseConfig(
          id: 'warehouse1',
          name: '主仓库',
          location: '北京市朝阳区',
          description: '主要仓库',
          supportedTemplates: ['平板车', '集装箱'],
        );

        await repository.saveWarehouse(warehouse);
        final retrieved = await repository.getWarehouseById('warehouse1');

        expect(retrieved, isNotNull);
        expect(retrieved!.id, 'warehouse1');
        expect(retrieved.name, '主仓库');
        expect(retrieved.location, '北京市朝阳区');
        expect(retrieved.supportedTemplates, ['平板车', '集装箱']);
      });
    });

    group('GroupConfig Tests', () {
      test('should save and retrieve group config', () async {
        final group = GroupConfig(
          id: 'group1',
          name: '第一组',
          warehouseId: 'warehouse1',
          description: '主要工作组',
          memberIds: ['worker1', 'worker2'],
        );

        await repository.saveGroup(group);
        final retrieved = await repository.getGroupById('group1');

        expect(retrieved, isNotNull);
        expect(retrieved!.id, 'group1');
        expect(retrieved.name, '第一组');
        expect(retrieved.warehouseId, 'warehouse1');
        expect(retrieved.memberIds, ['worker1', 'worker2']);
      });
    });

    group('TemplateConfig Tests', () {
      test('should save and retrieve template config', () async {
        final template = TemplateConfigModel(
          id: 'template1',
          name: '平板车模板',
          type: '平板车',
          description: '平板车拍照模板',
          totalPhotos: 10,
          requiredPhotos: 8,
          recognitionPhotos: 5,
        );

        await repository.saveTemplate(template);
        final retrieved = await repository.getTemplateById('template1');

        expect(retrieved, isNotNull);
        expect(retrieved!.id, 'template1');
        expect(retrieved.name, '平板车模板');
        expect(retrieved.type, '平板车');
        expect(retrieved.totalPhotos, 10);
        expect(retrieved.requiredPhotos, 8);
        expect(retrieved.recognitionPhotos, 5);
      });
    });

    group('Export/Import Tests', () {
      test('should export and import configs', () async {
        // 准备测试数据
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        final warehouse = WarehouseConfig(
          id: 'warehouse1',
          name: '主仓库',
          location: '北京市朝阳区',
        );

        await repository.saveWorker(worker);
        await repository.saveWarehouse(warehouse);

        // 导出配置
        final exported = await repository.exportConfigs();
        expect(exported['workers'], isNotNull);
        expect(exported['warehouses'], isNotNull);
        expect(exported['version'], '1.0.0');

        // 清空数据
        await mockDataSource.clearAllConfigs();

        // 导入配置
        await repository.importConfigs(exported);

        // 验证导入结果
        final importedWorker = await repository.getWorkerById('worker1');
        final importedWarehouse = await repository.getWarehouseById('warehouse1');

        expect(importedWorker, isNotNull);
        expect(importedWorker!.name, '张三');
        expect(importedWarehouse, isNotNull);
        expect(importedWarehouse!.name, '主仓库');
      });
    });
  });
}
