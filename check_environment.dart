#!/usr/bin/env dart

/// 环境检查脚本
/// 验证开发环境是否正确配置
/// 
/// 使用方法: dart check_environment.dart

import 'dart:io';

void main() async {
  print('🔍 检查开发环境...\n');
  
  bool allChecksPass = true;
  
  // 检查Flutter
  allChecksPass &= await checkFlutter();
  
  // 检查Dart
  allChecksPass &= await checkDart();
  
  // 检查Java
  allChecksPass &= await checkJava();
  
  // 检查Android SDK
  allChecksPass &= await checkAndroidSdk();
  
  // 检查项目依赖
  allChecksPass &= await checkProjectDependencies();
  
  // 检查网络连接
  allChecksPass &= await checkNetworkConnectivity();
  
  print('\n' + '='*50);
  if (allChecksPass) {
    print('✅ 所有检查通过！环境配置正确。');
    print('🚀 可以开始开发了！');
  } else {
    print('❌ 发现环境问题，请根据上述提示进行修复。');
    exit(1);
  }
}

Future<bool> checkFlutter() async {
  print('📱 检查Flutter SDK...');
  
  try {
    final result = await Process.run('flutter', ['--version']);
    if (result.exitCode == 0) {
      final output = result.stdout.toString();
      print('  ✅ Flutter已安装');
      
      // 提取版本信息
      final versionMatch = RegExp(r'Flutter (\d+\.\d+\.\d+)').firstMatch(output);
      if (versionMatch != null) {
        final version = versionMatch.group(1)!;
        print('  📦 版本: $version');
        
        // 检查版本是否满足要求
        if (_compareVersions(version, '3.24.0') >= 0) {
          print('  ✅ 版本满足要求 (>=3.24.0)');
          return true;
        } else {
          print('  ⚠️  版本过低，建议升级到3.24.0或更高版本');
          return false;
        }
      }
      return true;
    } else {
      print('  ❌ Flutter命令执行失败');
      return false;
    }
  } catch (e) {
    print('  ❌ Flutter未安装或不在PATH中');
    print('  💡 请访问 https://flutter.dev/docs/get-started/install');
    return false;
  }
}

Future<bool> checkDart() async {
  print('\n🎯 检查Dart SDK...');
  
  try {
    final result = await Process.run('dart', ['--version']);
    if (result.exitCode == 0) {
      final output = result.stdout.toString();
      print('  ✅ Dart已安装');
      
      // 提取版本信息
      final versionMatch = RegExp(r'Dart SDK version: (\d+\.\d+\.\d+)').firstMatch(output);
      if (versionMatch != null) {
        final version = versionMatch.group(1)!;
        print('  📦 版本: $version');
        
        // 检查版本是否满足要求
        if (_compareVersions(version, '3.5.0') >= 0) {
          print('  ✅ 版本满足要求 (>=3.5.0)');
          return true;
        } else {
          print('  ⚠️  版本过低，建议升级');
          return false;
        }
      }
      return true;
    } else {
      print('  ❌ Dart命令执行失败');
      return false;
    }
  } catch (e) {
    print('  ❌ Dart未安装或不在PATH中');
    return false;
  }
}

Future<bool> checkJava() async {
  print('\n☕ 检查Java JDK...');
  
  try {
    final result = await Process.run('java', ['--version']);
    if (result.exitCode == 0) {
      final output = result.stdout.toString();
      print('  ✅ Java已安装');
      
      // 提取版本信息
      final versionMatch = RegExp(r'java (\d+)').firstMatch(output);
      if (versionMatch != null) {
        final majorVersion = int.parse(versionMatch.group(1)!);
        print('  📦 主版本: $majorVersion');
        
        if (majorVersion >= 17) {
          print('  ✅ 版本满足要求 (>=17)');
          return true;
        } else {
          print('  ⚠️  版本过低，建议使用JDK 17或更高版本');
          return false;
        }
      }
      return true;
    } else {
      print('  ❌ Java命令执行失败');
      return false;
    }
  } catch (e) {
    print('  ❌ Java未安装或不在PATH中');
    print('  💡 请安装JDK 17或更高版本');
    return false;
  }
}

Future<bool> checkAndroidSdk() async {
  print('\n🤖 检查Android SDK...');
  
  // 检查ANDROID_HOME环境变量
  final androidHome = Platform.environment['ANDROID_HOME'];
  if (androidHome == null) {
    print('  ⚠️  ANDROID_HOME环境变量未设置');
    return false;
  }
  
  print('  ✅ ANDROID_HOME: $androidHome');
  
  // 检查SDK目录是否存在
  final sdkDir = Directory(androidHome);
  if (!sdkDir.existsSync()) {
    print('  ❌ Android SDK目录不存在');
    return false;
  }
  
  print('  ✅ Android SDK目录存在');
  return true;
}

Future<bool> checkProjectDependencies() async {
  print('\n📦 检查项目依赖...');
  
  // 检查pubspec.yaml
  final pubspecFile = File('pubspec.yaml');
  if (!pubspecFile.existsSync()) {
    print('  ❌ pubspec.yaml文件不存在');
    return false;
  }
  
  print('  ✅ pubspec.yaml存在');
  
  // 检查.dart_tool目录
  final dartToolDir = Directory('.dart_tool');
  if (!dartToolDir.existsSync()) {
    print('  ⚠️  .dart_tool目录不存在，需要运行 flutter pub get');
    return false;
  }
  
  print('  ✅ 依赖已安装');
  return true;
}

Future<bool> checkNetworkConnectivity() async {
  print('\n🌐 检查网络连接...');
  
  try {
    // 测试连接到pub.dev
    final client = HttpClient();
    final request = await client.getUrl(Uri.parse('https://pub.dev'));
    final response = await request.close();
    await response.drain();
    client.close();
    
    print('  ✅ 网络连接正常');
    return true;
  } catch (e) {
    print('  ⚠️  网络连接可能有问题');
    print('  💡 如果在中国大陆，项目已配置国内镜像源');
    return false;
  }
}

int _compareVersions(String version1, String version2) {
  final v1Parts = version1.split('.').map(int.parse).toList();
  final v2Parts = version2.split('.').map(int.parse).toList();
  
  for (int i = 0; i < 3; i++) {
    final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
    final v2Part = i < v2Parts.length ? v2Parts[i] : 0;
    
    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }
  
  return 0;
}
