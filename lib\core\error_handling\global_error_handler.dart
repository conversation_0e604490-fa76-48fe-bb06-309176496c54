import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:loadguard/core/exceptions/app_exceptions.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🚨 全局错误处理器
/// 
/// 统一处理应用中的所有错误，提供一致的用户体验
/// 支持错误分类、日志记录、用户通知和恢复策略
class GlobalErrorHandler {
  static final GlobalErrorHandler _instance = GlobalErrorHandler._internal();
  factory GlobalErrorHandler() => _instance;
  GlobalErrorHandler._internal();

  /// 错误处理策略映射
  final Map<Type, ErrorHandlingStrategy> _strategies = {};
  
  /// 错误监听器
  final List<ErrorListener> _listeners = [];
  
  /// 错误统计
  final Map<String, int> _errorStats = {};
  
  /// 是否已初始化
  bool _isInitialized = false;

  /// 初始化全局错误处理器
  static Future<void> initialize() async {
    final handler = GlobalErrorHandler();
    await handler._initialize();
  }

  /// 内部初始化方法
  Future<void> _initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('🚨 初始化全局错误处理器...', tag: 'ErrorHandler');

      // 注册默认错误处理策略
      _registerDefaultStrategies();

      // 设置Flutter错误处理
      _setupFlutterErrorHandling();

      // 设置Zone错误处理
      _setupZoneErrorHandling();

      _isInitialized = true;
      AppLogger.info('✅ 全局错误处理器初始化完成', tag: 'ErrorHandler');
    } catch (e) {
      AppLogger.error('❌ 全局错误处理器初始化失败: $e', tag: 'ErrorHandler');
      rethrow;
    }
  }

  /// 处理错误
  static Future<void> handleError(
    Object error, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
    bool showToUser = true,
  }) async {
    final handler = GlobalErrorHandler();
    await handler._handleError(
      error,
      stackTrace: stackTrace,
      context: context,
      additionalInfo: additionalInfo,
      showToUser: showToUser,
    );
  }

  /// 注册错误处理策略
  static void registerStrategy<T extends Exception>(ErrorHandlingStrategy strategy) {
    final handler = GlobalErrorHandler();
    handler._strategies[T] = strategy;
    AppLogger.debug('🚨 注册错误处理策略: $T', tag: 'ErrorHandler');
  }

  /// 添加错误监听器
  static void addListener(ErrorListener listener) {
    final handler = GlobalErrorHandler();
    handler._listeners.add(listener);
    AppLogger.debug('🚨 添加错误监听器', tag: 'ErrorHandler');
  }

  /// 移除错误监听器
  static void removeListener(ErrorListener listener) {
    final handler = GlobalErrorHandler();
    handler._listeners.remove(listener);
    AppLogger.debug('🚨 移除错误监听器', tag: 'ErrorHandler');
  }

  /// 获取错误统计
  static Map<String, int> getErrorStats() {
    return Map.from(GlobalErrorHandler()._errorStats);
  }

  /// 清除错误统计
  static void clearErrorStats() {
    GlobalErrorHandler()._errorStats.clear();
    AppLogger.info('🚨 清除错误统计', tag: 'ErrorHandler');
  }

  // 私有方法

  /// 内部错误处理方法
  Future<void> _handleError(
    Object error, {
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
    bool showToUser = true,
  }) async {
    try {
      // 1. 记录错误统计
      _recordErrorStats(error);

      // 2. 记录错误日志
      _logError(error, stackTrace, context, additionalInfo);

      // 3. 通知监听器
      await _notifyListeners(error, stackTrace, context);

      // 4. 执行错误处理策略
      final strategy = _getStrategy(error);
      if (strategy != null) {
        await strategy.handle(error, stackTrace, context, additionalInfo);
      }

      // 5. 显示用户通知
      if (showToUser) {
        await _showUserNotification(error, context);
      }

    } catch (e) {
      // 错误处理器本身出错，记录但不再递归处理
      AppLogger.error('❌ 错误处理器内部错误: $e', tag: 'ErrorHandler');
    }
  }

  /// 注册默认错误处理策略
  void _registerDefaultStrategies() {
    // 业务异常策略
    _strategies[BusinessException] = BusinessErrorStrategy();
    
    // 数据异常策略
    _strategies[DataException] = DataErrorStrategy();
    
    // 网络异常策略
    _strategies[NetworkException] = NetworkErrorStrategy();
    
    // 权限异常策略
    _strategies[PermissionException] = PermissionErrorStrategy();
    
    // 系统异常策略
    _strategies[SystemException] = SystemErrorStrategy();
    
    // 默认异常策略
    _strategies[Exception] = DefaultErrorStrategy();
  }

  /// 设置Flutter错误处理
  void _setupFlutterErrorHandling() {
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleError(
        details.exception,
        stackTrace: details.stack,
        context: 'Flutter Framework',
        additionalInfo: {
          'library': details.library,
          'context': details.context?.toString(),
        },
        showToUser: !kDebugMode,
      );
    };
  }

  /// 设置Zone错误处理
  void _setupZoneErrorHandling() {
    // 在runApp之前设置Zone错误处理
    // 这通常在main.dart中调用
  }

  /// 获取错误处理策略
  ErrorHandlingStrategy? _getStrategy(Object error) {
    // 精确匹配
    final exactStrategy = _strategies[error.runtimeType];
    if (exactStrategy != null) return exactStrategy;

    // 类型匹配
    for (final entry in _strategies.entries) {
      if (entry.key.toString() == error.runtimeType.toString()) {
        return entry.value;
      }
    }

    // 继承匹配
    for (final entry in _strategies.entries) {
      if (error.runtimeType.toString().contains(entry.key.toString())) {
        return entry.value;
      }
    }

    // 默认策略
    return _strategies[Exception];
  }

  /// 记录错误统计
  void _recordErrorStats(Object error) {
    final errorType = error.runtimeType.toString();
    _errorStats[errorType] = (_errorStats[errorType] ?? 0) + 1;
  }

  /// 记录错误日志
  void _logError(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  ) {
    final buffer = StringBuffer();
    buffer.writeln('🚨 全局错误处理');
    buffer.writeln('错误类型: ${error.runtimeType}');
    buffer.writeln('错误信息: $error');
    
    if (context != null) {
      buffer.writeln('错误上下文: $context');
    }
    
    if (additionalInfo != null && additionalInfo.isNotEmpty) {
      buffer.writeln('附加信息: $additionalInfo');
    }
    
    if (stackTrace != null) {
      buffer.writeln('堆栈跟踪:');
      buffer.writeln(stackTrace.toString());
    }

    AppLogger.error(buffer.toString(), tag: 'ErrorHandler');
  }

  /// 通知监听器
  Future<void> _notifyListeners(
    Object error,
    StackTrace? stackTrace,
    String? context,
  ) async {
    for (final listener in _listeners) {
      try {
        await listener(error, stackTrace, context);
      } catch (e) {
        AppLogger.error('❌ 错误监听器执行失败: $e', tag: 'ErrorHandler');
      }
    }
  }

  /// 显示用户通知
  Future<void> _showUserNotification(Object error, String? context) async {
    String message = '操作失败，请重试';
    String title = '错误';
    
    if (error is AppException) {
      message = error.getUserFriendlyMessage();
      title = _getErrorTitle(error.level);
    }

    // 这里可以集成具体的UI通知组件
    AppLogger.info('🚨 用户通知: $title - $message', tag: 'ErrorHandler');
  }

  /// 获取错误标题
  String _getErrorTitle(ErrorLevel level) {
    switch (level) {
      case ErrorLevel.info:
        return '提示';
      case ErrorLevel.warning:
        return '警告';
      case ErrorLevel.error:
        return '错误';
      case ErrorLevel.critical:
        return '严重错误';
    }
  }
}

/// 错误处理策略接口
abstract class ErrorHandlingStrategy {
  Future<void> handle(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  );
}

/// 业务错误策略
class BusinessErrorStrategy implements ErrorHandlingStrategy {
  @override
  Future<void> handle(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  ) async {
    if (error is BusinessException) {
      AppLogger.warning('🔔 业务异常: ${error.message}', tag: 'BusinessError');
      
      // 业务异常通常不需要特殊处理，只需要通知用户
      if (error.isRecoverable && error.recoverySuggestions.isNotEmpty) {
        AppLogger.info('💡 恢复建议: ${error.recoverySuggestions.join(', ')}', tag: 'BusinessError');
      }
    }
  }
}

/// 数据错误策略
class DataErrorStrategy implements ErrorHandlingStrategy {
  @override
  Future<void> handle(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  ) async {
    if (error is DataException) {
      AppLogger.error('💾 数据异常: ${error.message}', tag: 'DataError');
      
      // 数据异常可能需要数据同步或修复
      if (error.code == 'DATA_INCONSISTENCY') {
        AppLogger.info('🔄 尝试数据同步...', tag: 'DataError');
        // 这里可以触发数据一致性管理器
      }
    }
  }
}

/// 网络错误策略
class NetworkErrorStrategy implements ErrorHandlingStrategy {
  @override
  Future<void> handle(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  ) async {
    if (error is NetworkException) {
      AppLogger.warning('🌐 网络异常: ${error.message}', tag: 'NetworkError');
      
      // 网络异常可能需要重试机制
      if (error.isRecoverable) {
        AppLogger.info('🔄 网络错误，建议重试', tag: 'NetworkError');
      }
    }
  }
}

/// 权限错误策略
class PermissionErrorStrategy implements ErrorHandlingStrategy {
  @override
  Future<void> handle(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  ) async {
    if (error is PermissionException) {
      AppLogger.warning('🔒 权限异常: ${error.message}', tag: 'PermissionError');
      
      // 权限异常可能需要重新认证或权限申请
    }
  }
}

/// 系统错误策略
class SystemErrorStrategy implements ErrorHandlingStrategy {
  @override
  Future<void> handle(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  ) async {
    if (error is SystemException) {
      AppLogger.error('⚠️ 系统异常: ${error.message}', tag: 'SystemError');
      
      // 系统异常可能需要重启或重新初始化
      if (error.code == 'INITIALIZATION_FAILED') {
        AppLogger.info('🔄 系统初始化失败，建议重启应用', tag: 'SystemError');
      }
    }
  }
}

/// 默认错误策略
class DefaultErrorStrategy implements ErrorHandlingStrategy {
  @override
  Future<void> handle(
    Object error,
    StackTrace? stackTrace,
    String? context,
    Map<String, dynamic>? additionalInfo,
  ) async {
    AppLogger.error('❓ 未知错误: $error', tag: 'DefaultError');
  }
}

/// 错误监听器类型定义
typedef ErrorListener = Future<void> Function(
  Object error,
  StackTrace? stackTrace,
  String? context,
);
