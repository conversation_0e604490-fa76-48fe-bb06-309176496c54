// 最小化测试 - 检查基本导入是否工作
import 'dart:io';

void main() {
  print('🧪 开始最小化测试...');
  
  // 测试基本Dart功能
  final now = DateTime.now();
  print('⏰ 当前时间: $now');
  
  // 测试文件系统访问
  try {
    final currentDir = Directory.current;
    print('📁 当前目录: ${currentDir.path}');
    
    // 检查pubspec.yaml是否存在
    final pubspecFile = File('pubspec.yaml');
    if (pubspecFile.existsSync()) {
      print('✅ pubspec.yaml 存在');
    } else {
      print('❌ pubspec.yaml 不存在');
    }
    
    // 检查lib目录是否存在
    final libDir = Directory('lib');
    if (libDir.existsSync()) {
      print('✅ lib目录存在');
      
      // 列出lib目录下的文件
      final files = libDir.listSync().take(5);
      print('📂 lib目录内容 (前5项):');
      for (final file in files) {
        print('  - ${file.path}');
      }
    } else {
      print('❌ lib目录不存在');
    }
    
  } catch (e) {
    print('❌ 文件系统测试失败: $e');
  }
  
  print('🎉 最小化测试完成！');
}
