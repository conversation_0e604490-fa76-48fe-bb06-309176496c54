import 'package:flutter/material.dart';
import '../../models/worker_info_data.dart';
import '../../utils/theme_colors.dart';

/// 📋 **全屏人员选择页面** - 更大的操作空间，更直观的选择体验
class WorkerSelectionPage extends StatefulWidget {
  final Set<String> selectedWarehouses;
  final Set<String> selectedWorkerIds;
  final int quantity;

  const WorkerSelectionPage({
    Key? key,
    required this.selectedWarehouses,
    required this.selectedWorkerIds,
    required this.quantity,
  }) : super(key: key);

  @override
  State<WorkerSelectionPage> createState() => _WorkerSelectionPageState();
}

class _WorkerSelectionPageState extends State<WorkerSelectionPage> {
  late Set<String> _selectedWarehouses;
  late Set<String> _selectedWorkerIds;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedWarehouses = Set<String>.from(widget.selectedWarehouses);
    _selectedWorkerIds = Set<String>.from(widget.selectedWorkerIds);

    // 如果没有选择库区，默认选择1号库
    if (_selectedWarehouses.isEmpty) {
      _selectedWarehouses.add('1号库');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: ThemeColors.primary,
        title: const Text('选择参与人员', style: TextStyle(color: Colors.white)),
        // 移除冗余的返回按钮，使用手势导航
        automaticallyImplyLeading: false,
        actions: [
          TextButton(
            onPressed: _confirmSelection,
            child: const Text(
              '确定',
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: ThemeColors.primaryGradient,
        ),
        child: Column(
          children: [
            // 顶部统计和快捷操作
            _buildTopSection(),
            const SizedBox(height: 16),

            // 仓库选择器
            _buildWarehouseSelector(),
            const SizedBox(height: 16),

            // 智能快捷按钮
            _buildQuickActions(),
            const SizedBox(height: 16),

            // 搜索框
            _buildSearchBar(),
            const SizedBox(height: 16),

            // 人员列表 - 占据剩余空间
            Expanded(
              child: _buildWorkerList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 **顶部统计区域**
  Widget _buildTopSection() {
    final totalTonnage = widget.quantity * 1.5;
    final perPersonTonnage = _selectedWorkerIds.isNotEmpty
        ? totalTonnage / _selectedWorkerIds.length
        : 0.0;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Text(
                  '任务数量',
                  style: TextStyle(
                      color: Colors.white.withOpacity(0.8), fontSize: 12),
                ),
                Text(
                  '${widget.quantity} 托',
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          Container(width: 1, height: 30, color: Colors.white.withOpacity(0.3)),
          Expanded(
            child: Column(
              children: [
                Text(
                  '总吨数',
                  style: TextStyle(
                      color: Colors.white.withOpacity(0.8), fontSize: 12),
                ),
                Text(
                  '${totalTonnage.toStringAsFixed(1)} 吨',
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          Container(width: 1, height: 30, color: Colors.white.withOpacity(0.3)),
          Expanded(
            child: Column(
              children: [
                Text(
                  '已选人数',
                  style: TextStyle(
                      color: Colors.white.withOpacity(0.8), fontSize: 12),
                ),
                Text(
                  '${_selectedWorkerIds.length} 人',
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          if (_selectedWorkerIds.isNotEmpty) ...[
            Container(
                width: 1, height: 30, color: Colors.white.withOpacity(0.3)),
            Expanded(
              child: Column(
                children: [
                  Text(
                    '人均分配',
                    style: TextStyle(
                        color: Colors.white.withOpacity(0.8), fontSize: 12),
                  ),
                  Text(
                    '${perPersonTonnage.toStringAsFixed(1)} 吨',
                    style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 🏭 **仓库选择器**
  Widget _buildWarehouseSelector() {
    final allWarehouses = allWorkers.map((w) => w.warehouse).toSet().toList()
      ..sort();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '选择库区',
            style: TextStyle(
                color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: allWarehouses.map((warehouse) {
              final isSelected = _selectedWarehouses.contains(warehouse);
              final color = _getWarehouseColor(warehouse);

              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      _selectedWarehouses.remove(warehouse);
                    } else {
                      _selectedWarehouses.add(warehouse);
                    }
                  });
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? color.withOpacity(0.8)
                        : Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected ? color : Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Text(
                    warehouse,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 🚀 **智能快捷操作**
  Widget _buildQuickActions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildQuickButton(
                '智能推荐', Icons.auto_awesome, _selectRecommended),
          ),
          const SizedBox(width: 8),
          Expanded(
            child:
                _buildQuickButton('全选当前', Icons.select_all, _selectAllVisible),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildQuickButton('清空选择', Icons.clear_all, _clearSelection),
          ),
        ],
      ),
    );
  }

  /// 🔍 **搜索框**
  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: Colors.white, fontSize: 16),
        decoration: const InputDecoration(
          hintText: '搜索姓名、小组、角色...',
          hintStyle: TextStyle(color: Colors.white54),
          border: InputBorder.none,
          prefixIcon: Icon(Icons.search, color: Colors.white54),
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.trim();
          });
        },
      ),
    );
  }

  /// 📋 **人员列表**
  Widget _buildWorkerList() {
    final filteredWorkers = _getFilteredWorkers();
    final groupedWorkers = _groupWorkersByTeam(filteredWorkers);

    if (groupedWorkers.isEmpty) {
      return const Center(
        child: Text(
          '没有找到符合条件的人员',
          style: TextStyle(color: Colors.white70, fontSize: 16),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        children: groupedWorkers.entries.map((entry) {
          final group = entry.key;
          final workers = entry.value;
          final groupColor = _getGroupColor(group);

          return Column(
            children: [
              // 小组标题
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: groupColor,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        group.isEmpty ? '未分组' : group,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${workers.length}人',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                    const Spacer(),
                    // 小组全选按钮
                    GestureDetector(
                      onTap: () => _toggleGroupSelection(workers),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: groupColor.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _isGroupSelected(workers) ? '取消全选' : '全选',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 人员列表
              ...workers.map((worker) => _buildWorkerItem(worker, groupColor)),
              const SizedBox(height: 12),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// 👤 **人员项目**
  Widget _buildWorkerItem(WorkerInfo worker, Color groupColor) {
    final isSelected = _selectedWorkerIds.contains(worker.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _toggleWorkerSelection(worker.id),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected
                  ? groupColor.withOpacity(0.3)
                  : Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? groupColor : Colors.white.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: groupColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    worker.group.isEmpty ? '未分组' : worker.group,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        worker.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${worker.role} | ${worker.warehouse}',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                // 选择状态圆圈
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isSelected ? groupColor : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? groupColor
                          : Colors.white.withOpacity(0.5),
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 辅助方法
  Widget _buildQuickButton(String text, IconData icon, VoidCallback onPressed) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16, color: Colors.white),
      label:
          Text(text, style: const TextStyle(color: Colors.white, fontSize: 12)),
      style: OutlinedButton.styleFrom(
        foregroundColor: Colors.white,
        side: BorderSide(color: Colors.white.withOpacity(0.3)),
        backgroundColor: Colors.white.withOpacity(0.1),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      ),
    );
  }

  List<WorkerInfo> _getFilteredWorkers() {
    var workers = allWorkers.where((w) => w.role != '无').toList();

    // 按选中仓库过滤
    if (_selectedWarehouses.isNotEmpty) {
      workers = workers
          .where((w) => _selectedWarehouses.contains(w.warehouse))
          .toList();
    }

    // 搜索过滤
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      workers = workers
          .where((w) =>
              w.name.toLowerCase().contains(query) ||
              w.warehouse.toLowerCase().contains(query) ||
              w.group.toLowerCase().contains(query) ||
              w.role.toLowerCase().contains(query))
          .toList();
    }

    return workers;
  }

  Map<String, List<WorkerInfo>> _groupWorkersByTeam(List<WorkerInfo> workers) {
    final groups = <String, List<WorkerInfo>>{};
    for (final worker in workers) {
      final groupKey = worker.group.isEmpty ? "未分组" : worker.group;
      groups.putIfAbsent(groupKey, () => []);
      groups[groupKey]!.add(worker);
    }
    return groups;
  }

  Color _getGroupColor(String group) {
    final colors = [
      Colors.green,
      Colors.blue,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
    ];
    return colors[group.hashCode.abs() % colors.length];
  }

  Color _getWarehouseColor(String warehouse) {
    final colors = [
      ThemeColors.primary,
      Colors.green,
      Colors.orange,
      Colors.blue,
      Colors.purple,
      Colors.teal,
    ];
    return colors[warehouse.hashCode.abs() % colors.length];
  }

  void _toggleWorkerSelection(String workerId) {
    setState(() {
      if (_selectedWorkerIds.contains(workerId)) {
        _selectedWorkerIds.remove(workerId);
      } else {
        _selectedWorkerIds.add(workerId);
      }
    });
  }

  bool _isGroupSelected(List<WorkerInfo> workers) {
    return workers.every((w) => _selectedWorkerIds.contains(w.id));
  }

  void _toggleGroupSelection(List<WorkerInfo> workers) {
    final isSelected = _isGroupSelected(workers);
    setState(() {
      if (isSelected) {
        for (final worker in workers) {
          _selectedWorkerIds.remove(worker.id);
        }
      } else {
        for (final worker in workers) {
          _selectedWorkerIds.add(worker.id);
        }
      }
    });
  }

  void _selectRecommended() {
    final recommendedCount = (widget.quantity / 10).ceil().clamp(2, 6);
    final availableWorkers = _getFilteredWorkers();

    setState(() {
      _selectedWorkerIds.clear();
      // 优先选择叉车角色
      final forklifts = availableWorkers
          .where((w) => w.role.contains('叉车'))
          .take(recommendedCount ~/ 2)
          .toList();
      final others = availableWorkers
          .where((w) => !w.role.contains('叉车'))
          .take(recommendedCount - forklifts.length)
          .toList();

      for (final worker in [...forklifts, ...others]) {
        _selectedWorkerIds.add(worker.id);
      }
    });
  }

  void _selectAllVisible() {
    final visibleWorkers = _getFilteredWorkers();
    setState(() {
      for (final worker in visibleWorkers) {
        _selectedWorkerIds.add(worker.id);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedWorkerIds.clear();
    });
  }

  void _confirmSelection() {
    Navigator.of(context).pop({
      'warehouses': _selectedWarehouses,
      'workerIds': _selectedWorkerIds,
    });
  }
}
