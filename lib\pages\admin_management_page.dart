import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/app_security_service.dart' as security;
import '../services/enterprise_license_service.dart';
import '../utils/theme_colors.dart';
import '../widgets/themed_card.dart';
import '../widgets/themed_button.dart';
import '../widgets/industrial_logo.dart';
import 'package:go_router/go_router.dart';
import '../utils/simple_navigation_helper.dart';
import '../core/lifecycle_mixin.dart';
import '../core/providers/app_providers.dart';

/// 管理员管理页面
/// 用于生成激活码、管理设备等管理员功能
class AdminManagementPage extends ConsumerStatefulWidget {
  const AdminManagementPage({super.key});

  @override
  ConsumerState<AdminManagementPage> createState() => _AdminManagementPageState();
}

class _AdminManagementPageState extends ConsumerState<AdminManagementPage>
    with LifecycleMixin<AdminManagementPage> {
  final _masterPasswordController = TextEditingController();
  final _deviceIdController = TextEditingController();
  final _batchDeviceIdsController = TextEditingController();

  bool _isAuthenticated = false;
  String _currentDeviceId = '';
  Map<String, String> _deviceInfo = {};
  LicenseType _selectedLicenseType = LicenseType.annual;
  UserRole _selectedUserRole = UserRole.activated;
  String _generatedCode = '';
  Map<String, String> _batchCodes = {};

  // 当前用户权限信息
  UserRole _currentUserRole = UserRole.trial;
  Map<String, dynamic> _licenseStatus = {};
  List<Map<String, dynamic>> _activationHistory = [];
  List<String> _authorizedDevices = [];
  bool _isMasterPasswordValid = false;

  @override
  void initState() {
    super.initState();
    _loadDeviceInfo();
    _loadCurrentUserInfo();
  }

  @override
  void onLifecycleDispose() {
    _masterPasswordController.dispose();
    _deviceIdController.dispose();
    _batchDeviceIdsController.dispose();
  }

  Future<void> _loadDeviceInfo() async {
    try {
      final deviceId = await security.AppSecurityService.getDeviceId();
      final deviceInfo = await security.AppSecurityService.getDeviceInfo();
      setState(() {
        _currentDeviceId = deviceId;
        _deviceInfo = deviceInfo;
      });
    } catch (e) {
      setState(() {
        _currentDeviceId = 'Error loading device ID';
        _deviceInfo = {'Error': 'Failed to load device info: $e'};
      });
    }
  }

  Future<void> _loadCurrentUserInfo() async {
    try {
      final role = await security.AppSecurityService.getUserRole();
      final licenseStatus =
          await security.AppSecurityService.checkLicenseStatus();

      setState(() {
        _currentUserRole = role;
        _licenseStatus = licenseStatus;
      });

      // 如果是超级管理员，加载额外信息
      if (role == UserRole.superAdmin) {
        _loadAdminData();
      }
    } catch (e) {
      // print('加载用户信息失败: $e');
    }
  }

  Future<void> _loadAdminData() async {
    try {
      final history = await security.AppSecurityService.getActivationHistory();
      final devices = await security.AppSecurityService.getAuthorizedDevices();

      setState(() {
        _activationHistory = history;
        _authorizedDevices = devices;
      });
    } catch (e) {
      // print('加载管理员数据失败: $e');
    }
  }

  void _validateMasterPassword() async {
    final isValid = await security.AppSecurityService.validateMasterPassword(
        _masterPasswordController.text);
    setState(() {
      _isAuthenticated = isValid;
      _isMasterPasswordValid = isValid;
    });

    if (isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text('主密码验证成功！'),
            ],
          ),
          backgroundColor: ThemeColors.success,
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text('主密码错误！'),
            ],
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _generateSingleActivationCode() async {
    if (!_isAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先验证主密码')),
      );
      return;
    }

    final deviceId = _deviceIdController.text.trim();
    if (deviceId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入设备ID')),
      );
      return;
    }

    try {
      final activationCode =
          await security.AppSecurityService.generateActivationCodeWithMaster(
        deviceId,
        _selectedLicenseType,
        _masterPasswordController.text,
        role: _selectedUserRole,
      );
      setState(() {
        _generatedCode = activationCode;
      });

      // 重新加载管理员数据以更新历史记录
      _loadAdminData();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              '激活码生成成功！角色：${_getUserRoleName(_selectedUserRole.toString().split('.').last)}'),
          backgroundColor: ThemeColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('生成失败：$e'), backgroundColor: Colors.red),
      );
    }
  }

  Future<void> _generateBatchActivationCodes() async {
    if (!_isAuthenticated) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先验证主密码')),
      );
      return;
    }

    final deviceIdsText = _batchDeviceIdsController.text.trim();
    if (deviceIdsText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入设备ID列表')),
      );
      return;
    }

    final deviceIds = deviceIdsText
        .split('\n')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();
    try {
      final batchCodes =
          await security.AppSecurityService.generateBatchActivationCodes(
        deviceIds,
        _selectedLicenseType,
        role: _selectedUserRole,
      );
      setState(() {
        _batchCodes = batchCodes;
      });
      // 重新加载管理员数据以更新历史记录
      _loadAdminData();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              '成功生成${batchCodes.length}个激活码！角色：${_getUserRoleName(_selectedUserRole.toString().split('.').last)}'),
          backgroundColor: ThemeColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('批量生成失败：$e'), backgroundColor: Colors.red),
      );
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check, color: Colors.white),
            const SizedBox(width: 8),
            Text('已复制到剪贴板'),
          ],
        ),
        backgroundColor: ThemeColors.success,
      ),
    );
  }

  String _getLicenseTypeDescription(LicenseType type) {
    switch (type) {
      case LicenseType.trial:
        return '试用版 (7天)';
      case LicenseType.monthly:
        return '月度版 (30天)';
      case LicenseType.quarterly:
        return '季度版 (90天)';
      case LicenseType.annual:
        return '年度版 (365天)';
      case LicenseType.permanent:
        return '永久版';
      default:
        return '未知类型';
    }
  }

  String _getUserRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.activated:
        return '激活用户 (完整功能)';
      case UserRole.trial:
        return '试用用户 (7天试用)';
      case UserRole.superAdmin:
        return '超级管理员 (完全权限)';
      default:
        return '未知角色';
    }
  }

  String _getUserRoleName(String roleStr) {
    switch (roleStr.toLowerCase()) {
      case 'superadmin':
        return '超级管理员';
      case 'user':
        return '普通用户';
      case 'guest':
        return '访客';
      default:
        return '未知角色';
    }
  }

  // 检查当前用户是否有权限访问管理功能
  bool _canAccessAdminFeatures() {
    return _currentUserRole == UserRole.superAdmin;
  }

  // 检查当前用户是否有超级管理员权限

  void _handleBackNavigation() async {
    // 如果有未保存的更改，提示用户确认
    if (_masterPasswordController.text.isNotEmpty ||
        _deviceIdController.text.isNotEmpty) {
      final shouldLeave = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('确认退出'),
          content: const Text('您有未保存的输入内容，确定要退出管理页面吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('继续编辑'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('退出'),
            ),
          ],
        ),
      );

      if (shouldLeave != true) return;
    }

    // 执行导航返回
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      context.go('/home');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SimpleNavigationHelper.buildStandardPage(
      onBackPressed: _handleBackNavigation,
      enableSwipeBack: true,
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: ThemeColors.primaryGradient,
          ),
          child: SafeArea(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // 显示当前用户权限信息
                        _buildCurrentUserSection(),
                        const SizedBox(height: 16),

                        // 根据权限显示不同功能
                        if (!_canAccessAdminFeatures()) ...[
                          // 普通用户只能看到自己的信息
                          _buildUserInfoSection(),
                        ] else ...[
                          // 超级管理员功能
                          _buildAdminFeatures(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 新增：当前用户信息显示
  Widget _buildCurrentUserSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: _canAccessAdminFeatures()
                      ? ThemeColors.successButtonGradient
                      : ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  _canAccessAdminFeatures()
                      ? Icons.admin_panel_settings
                      : Icons.person,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前用户权限',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    Text(
                      _getUserRoleName(
                          _currentUserRole.toString().split('.').last),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: _canAccessAdminFeatures()
                            ? ThemeColors.success
                            : ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (_licenseStatus.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
                border: Border.all(
                  color: _licenseStatus['isValid'] == true
                      ? Colors.green.withOpacity(0.3)
                      : Colors.orange.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _licenseStatus['message'] as String? ?? '未知状态',
                    style: TextStyle(
                      color: ThemeColors.textOnGradient,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '许可证类型: ${_getLicenseTypeDescription(LicenseType.values.firstWhere((e) => e.name == _licenseStatus['licenseType'], orElse: () => LicenseType.trial))}',
                    style: TextStyle(
                      fontSize: 12,
                      color: ThemeColors.textOnGradient.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
          if (!_canAccessAdminFeatures()) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ThemeColors.warning.withOpacity(0.2),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
                border: Border.all(color: ThemeColors.warning.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline,
                      color: ThemeColors.warning, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '普通用户只能查看自己的激活信息。如需管理功能，请联系超级管理员升级权限。',
                      style: TextStyle(
                        fontSize: 12,
                        color: ThemeColors.textOnGradient.withOpacity(0.9),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 新增：普通用户信息显示区域
  Widget _buildUserInfoSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.devices,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '我的设备信息',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '设备ID: ',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        _currentDeviceId,
                        style: TextStyle(
                          fontFamily: 'monospace',
                          color: ThemeColors.textOnGradient.withOpacity(0.9),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _copyToClipboard(_currentDeviceId),
                      icon: Icon(
                        Icons.copy,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '请将此设备ID提供给管理员以获取激活码',
                  style: TextStyle(
                    fontSize: 12,
                    color: ThemeColors.textOnGradient.withOpacity(0.7),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 新增：用户角色选择区域
  Widget _buildUserRoleSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.purpleGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.group,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '用户角色',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: DropdownButtonFormField<UserRole>(
              value: _selectedUserRole,
              dropdownColor: ThemeColors.primary,
              style: TextStyle(color: ThemeColors.textOnGradient),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              items: UserRole.values.map((role) {
                return DropdownMenuItem(
                  value: role,
                  child: Text(
                    _getUserRoleDescription(role),
                    style: TextStyle(color: ThemeColors.textOnGradient),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedUserRole = value;
                  });
                }
              },
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '💡 选择要为新设备分配的用户角色权限',
            style: TextStyle(
              fontSize: 12,
              color: ThemeColors.textOnGradient.withOpacity(0.7),
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  // 新增：激活历史记录区域
  Widget _buildActivationHistorySection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.tealGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.history,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '激活历史记录',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
              IconButton(
                onPressed: _loadAdminData,
                icon: Icon(
                  Icons.refresh,
                  color: ThemeColors.textOnGradient.withOpacity(0.8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_activationHistory.isEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              ),
              child: Text(
                '暂无激活记录',
                style: TextStyle(
                  color: ThemeColors.textOnGradient.withOpacity(0.7),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ] else ...[
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
                border: Border.all(color: Colors.white.withOpacity(0.2)),
              ),
              child: ListView.separated(
                padding: const EdgeInsets.all(8),
                itemCount: _activationHistory.length,
                separatorBuilder: (context, index) => Divider(
                  color: Colors.white.withOpacity(0.2),
                  height: 1,
                ),
                itemBuilder: (context, index) {
                  final record = _activationHistory[index];
                  if (record.containsKey('error')) {
                    return ListTile(
                      leading:
                          const Icon(Icons.error, color: Colors.red, size: 20),
                      title: Text(
                        record['error'],
                        style: TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    );
                  }

                  return ListTile(
                    dense: true,
                    leading: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: ThemeColors.success.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Icon(
                        Icons.vpn_key,
                        color: ThemeColors.success,
                        size: 16,
                      ),
                    ),
                    title: Text(
                      '设备: ${record['deviceId']?.toString().substring(0, 8) ?? 'Unknown'}...',
                      style: TextStyle(
                        color: ThemeColors.textOnGradient,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: Text(
                      '${_getLicenseTypeDescription(LicenseType.values.firstWhere((e) => e.name == record['licenseType'], orElse: () => LicenseType.trial))} | ${_getUserRoleName(record['role'])}',
                      style: TextStyle(
                        color: ThemeColors.textOnGradient.withOpacity(0.7),
                        fontSize: 11,
                      ),
                    ),
                    trailing: Text(
                      DateTime.tryParse(record['timestamp'] ?? '')
                              ?.toString()
                              .split(' ')[0] ??
                          '未知日期',
                      style: TextStyle(
                        color: ThemeColors.textOnGradient.withOpacity(0.6),
                        fontSize: 10,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return ThemedCard(
      type: CardType.glass,
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '管理员控制台',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w800,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
                Text(
                  'Google ML Kit专业版管理',
                  style: TextStyle(
                    fontSize: 14,
                    color: ThemeColors.textOnGradient.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          IndustrialLogo(
            size: 32,
            primaryColor: ThemeColors.textOnGradient,
            accentColor: ThemeColors.primaryLight,
            showText: false,
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: _isAuthenticated
                      ? ThemeColors.successButtonGradient
                      : ThemeColors.warningButtonGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  _isAuthenticated ? Icons.lock_open : Icons.security,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '主密码验证',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    Text(
                      _isAuthenticated ? '已验证 - 可使用管理功能' : '请输入主密码以访问管理功能',
                      style: TextStyle(
                        fontSize: 14,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!_isAuthenticated) ...[
            const SizedBox(height: 20),
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
              ),
              child: TextField(
                controller: _masterPasswordController,
                obscureText: true,
                style: TextStyle(color: ThemeColors.textOnGradient),
                decoration: InputDecoration(
                  hintText: '请输入主密码',
                  hintStyle: TextStyle(
                    color: ThemeColors.textOnGradient.withOpacity(0.6),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  suffixIcon: Icon(
                    Icons.password,
                    color: ThemeColors.textOnGradient.withOpacity(0.8),
                  ),
                ),
                onSubmitted: (_) => _validateMasterPassword(),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ThemedButton(
                text: '验证密码',
                onPressed: _validateMasterPassword,
                gradient: ThemeColors.primaryButtonGradient,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDeviceInfoSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.blueGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.devices,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '当前设备信息',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '设备ID: ',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: ThemeColors.textOnGradient,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        _currentDeviceId,
                        style: TextStyle(
                          fontFamily: 'monospace',
                          color: ThemeColors.textOnGradient.withOpacity(0.9),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => _copyToClipboard(_currentDeviceId),
                      icon: Icon(
                        Icons.copy,
                        color: ThemeColors.textOnGradient.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ..._deviceInfo.entries
                    .map((entry) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            children: [
                              SizedBox(
                                width: 80,
                                child: Text(
                                  '${entry.key}:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    color: ThemeColors.textOnGradient,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  entry.value,
                                  style: TextStyle(
                                    color: ThemeColors.textOnGradient
                                        .withOpacity(0.8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ))
                    .toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLicenseTypeSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.greenGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.badge,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '许可证类型',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
              border: Border.all(color: Colors.white.withOpacity(0.2)),
            ),
            child: DropdownButtonFormField<LicenseType>(
              value: _selectedLicenseType,
              dropdownColor: ThemeColors.primary,
              style: TextStyle(color: ThemeColors.textOnGradient),
              decoration: InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              items: LicenseType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(
                    _getLicenseTypeDescription(type),
                    style: TextStyle(color: ThemeColors.textOnGradient),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedLicenseType = value;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSingleCodeSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.yellowGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.vpn_key,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '生成单个激活码',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: TextField(
              controller: _deviceIdController,
              style: TextStyle(color: ThemeColors.textOnGradient),
              decoration: InputDecoration(
                hintText: '请输入设备ID',
                hintStyle: TextStyle(
                  color: ThemeColors.textOnGradient.withOpacity(0.6),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ),
          const SizedBox(height: 16),
          ThemedButton(
            text: '生成激活码',
            onPressed: _generateSingleActivationCode,
            gradient: ThemeColors.warningButtonGradient,
          ),
          if (_generatedCode.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ThemeColors.success.withOpacity(0.2),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
                border: Border.all(color: ThemeColors.success.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle,
                      color: ThemeColors.success, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '生成的激活码:',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: ThemeColors.textOnGradient,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _generatedCode,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            color: ThemeColors.textOnGradient,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _copyToClipboard(_generatedCode),
                    icon: Icon(
                      Icons.copy,
                      color: ThemeColors.textOnGradient.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBatchCodeSection() {
    return ThemedCard(
      type: CardType.glass,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: ThemeColors.orangeGradient,
                  borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
                ),
                child: Icon(
                  Icons.group_work,
                  color: ThemeColors.textOnGradient,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  '批量生成激活码',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: ThemeColors.textOnGradient,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(ThemeColors.radiusMedium),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: TextField(
              controller: _batchDeviceIdsController,
              maxLines: 5,
              style: TextStyle(color: ThemeColors.textOnGradient),
              decoration: InputDecoration(
                hintText: '请输入设备ID列表，每行一个',
                hintStyle: TextStyle(
                  color: ThemeColors.textOnGradient.withOpacity(0.6),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ),
          const SizedBox(height: 16),
          ThemedButton(
            text: '批量生成',
            onPressed: _generateBatchActivationCodes,
            gradient: ThemeColors.dangerButtonGradient,
          ),
          if (_batchCodes.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ThemeColors.success.withOpacity(0.2),
                borderRadius: BorderRadius.circular(ThemeColors.radiusSmall),
                border: Border.all(color: ThemeColors.success.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle,
                          color: ThemeColors.success, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        '批量生成结果 (${_batchCodes.length}个):',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: ThemeColors.textOnGradient,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () {
                          final allCodes = _batchCodes.entries
                              .map((e) => '${e.key}: ${e.value}')
                              .join('\n');
                          _copyToClipboard(allCodes);
                        },
                        icon: Icon(
                          Icons.copy_all,
                          color: ThemeColors.textOnGradient.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    constraints: const BoxConstraints(maxHeight: 200),
                    child: SingleChildScrollView(
                      child: Column(
                        children: _batchCodes.entries.map((entry) {
                          return Container(
                            margin: const EdgeInsets.only(bottom: 4),
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(
                                  ThemeColors.radiusSmall),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    '${entry.key}: ${entry.value}',
                                    style: TextStyle(
                                      fontFamily: 'monospace',
                                      fontSize: 12,
                                      color: ThemeColors.textOnGradient,
                                    ),
                                  ),
                                ),
                                IconButton(
                                  onPressed: () =>
                                      _copyToClipboard(entry.value),
                                  icon: Icon(
                                    Icons.copy,
                                    size: 16,
                                    color: ThemeColors.textOnGradient
                                        .withOpacity(0.8),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAdminFeatures() {
    if (!_canAccessAdminFeatures()) {
      return ThemedCard(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(Icons.lock, size: 48, color: Colors.orange),
              const SizedBox(height: 16),
              Text(
                '需要超级管理员权限',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                '当前用户角色：${_getUserRoleName(_currentUserRole.toString().split('.').last)}',
                style: TextStyle(color: Colors.grey[600]),
              ),
              const SizedBox(height: 16),
              Text(
                '请使用超级管理员激活码激活此设备以获得管理权限',
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: [
        // 主密码验证区域
        ThemedCard(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.admin_panel_settings,
                        color: ThemeColors.primary),
                    const SizedBox(width: 8),
                    Text(
                      '超级管理员验证',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _masterPasswordController,
                  decoration: InputDecoration(
                    labelText: '主密码',
                    hintText: '输入超级管理员主密码',
                    border: OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(_isMasterPasswordValid
                          ? Icons.check_circle
                          : Icons.help),
                      onPressed: () {
                        // 显示主密码获取指南
                        _showMasterPasswordGuide();
                      },
                    ),
                  ),
                  obscureText: true,
                  onSubmitted: (_) => _validateMasterPassword(),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: ThemedButton(
                        onPressed: _validateMasterPassword,
                        text: '验证主密码',
                      ),
                    ),
                    const SizedBox(width: 8),
                    ThemedButton(
                      onPressed: _showMasterPasswordGuide,
                      text: '获取指南',
                      type: ButtonType.secondary,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),

        // 其他管理员功能保持不变...
      ],
    );
  }

  void _showMasterPasswordGuide() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('超级管理员主密码指南'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('获取主密码的方法：'),
              const SizedBox(height: 8),
              Text('1. 时间窗口激活码（推荐）'),
              Text('2. 设备特定激活码'),
              Text('3. 紧急恢复序列'),
              Text('4. 配置文件主密码'),
              const SizedBox(height: 16),
              Text('请前往安全管理页面查看当前激活码'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.push('/security-management');
            },
            child: Text('前往安全管理'),
          ),
        ],
      ),
    );
  }
}
