import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/services/local_data_manager.dart';
import 'package:loadguard/services/sync_service.dart';
import 'package:loadguard/widgets/error_handling_widgets.dart';
import 'package:share_plus/share_plus.dart';

/// 本地应用管理页面
/// 展示本地应用的数据管理、备份、导出等功能
/// 同时提供未来服务器同步的配置入口
class LocalAppManagementPage extends ConsumerStatefulWidget {
  const LocalAppManagementPage({super.key});

  @override
  ConsumerState<LocalAppManagementPage> createState() => _LocalAppManagementPageState();
}

class _LocalAppManagementPageState extends ConsumerState<LocalAppManagementPage> {
  bool _isLoading = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('本地应用管理'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: '刷新数据',
          ),
        ],
      ),
      body: ErrorBoundary(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 应用信息卡片
                    _buildAppInfoCard(),
                    const SizedBox(height: 16),
                    
                    // 数据统计卡片
                    _buildDataStatisticsCard(),
                    const SizedBox(height: 16),
                    
                    // 数据管理卡片
                    _buildDataManagementCard(),
                    const SizedBox(height: 16),
                    
                    // 同步配置卡片
                    _buildSyncConfigCard(),
                    const SizedBox(height: 16),
                    
                    // 高级功能卡片
                    _buildAdvancedFeaturesCard(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildAppInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  '应用信息',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('应用类型', '本地应用'),
            _buildInfoRow('运行模式', '离线优先'),
            _buildInfoRow('数据存储', '本地加密存储'),
            _buildInfoRow('同步状态', '可选配置'),
            
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '本地应用无需网络连接即可正常工作，所有数据安全存储在本地',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataStatisticsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.orange[600]),
                const SizedBox(width: 8),
                const Text(
                  '数据统计',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            FutureBuilder<LocalDataStatistics>(
              future: ref.read(localDataManagerProvider).getDataStatistics(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                if (snapshot.hasError) {
                  return Text('加载失败: ${snapshot.error}');
                }
                
                final stats = snapshot.data!;
                return Column(
                  children: [
                    Row(
                      children: [
                        Expanded(child: _buildStatItem('任务数量', '${stats.taskCount}', Icons.task)),
                        Expanded(child: _buildStatItem('配置项', '${stats.configItemCount}', Icons.settings)),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(child: _buildStatItem('数据大小', stats.formattedDataSize, Icons.storage)),
                        Expanded(child: _buildStatItem('数据版本', stats.dataVersion, Icons.label)),
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('最后修改', _formatDateTime(stats.lastModified)),
                    _buildInfoRow('设备ID', stats.deviceId.substring(0, 16) + '...'),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagementCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.folder_open, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  '数据管理',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 备份和导出按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createBackup,
                    icon: const Icon(Icons.backup),
                    label: const Text('创建备份'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _exportData,
                    icon: const Icon(Icons.download),
                    label: const Text('导出数据'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[600],
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 导入和清理按钮
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _importData,
                    icon: const Icon(Icons.upload),
                    label: const Text('导入数据'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _showCleanupDialog,
                    icon: const Icon(Icons.cleaning_services),
                    label: const Text('数据清理'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.orange[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncConfigCard() {
    final syncService = ref.read(syncServiceProvider);
    final syncStatus = syncService.getSyncStatus();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.sync, color: Colors.teal[600]),
                const SizedBox(width: 8),
                const Text(
                  '服务器同步 (可选)',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        '未来扩展功能',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '本地应用可完全离线工作。同步功能为未来服务器部署预留，可实现多设备数据同步和企业级管理。',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('同步状态', syncStatus.enabled ? '已启用' : '未启用'),
            _buildInfoRow('服务器地址', syncStatus.serverEndpoint ?? '未配置'),
            _buildInfoRow('最后同步', syncStatus.lastSyncTime != null 
                ? _formatDateTime(syncStatus.lastSyncTime!) 
                : '从未同步'),
            
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _showSyncConfigDialog,
                icon: const Icon(Icons.settings),
                label: const Text('配置同步服务'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedFeaturesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.advanced, color: Colors.red[600]),
                const SizedBox(width: 8),
                const Text(
                  '高级功能',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('安全设置'),
              subtitle: const Text('数据加密、访问控制等安全配置'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _openSecuritySettings,
            ),
            
            ListTile(
              leading: const Icon(Icons.bug_report),
              title: const Text('错误日志'),
              subtitle: const Text('查看应用错误日志和诊断信息'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _openErrorLogs,
            ),
            
            ListTile(
              leading: const Icon(Icons.system_update),
              title: const Text('系统信息'),
              subtitle: const Text('查看应用版本、设备信息等'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _openSystemInfo,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.grey[600], size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _refreshData() async {
    setState(() {
      _isLoading = true;
    });
    
    await Future.delayed(const Duration(milliseconds: 500));
    
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _createBackup() async {
    await context.handleAsyncError(
      ref.read(localDataManagerProvider).createBackup(),
      operation: 'createBackup',
      onRetry: _createBackup,
    ).then((backupPath) {
      if (backupPath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('备份创建成功: ${backupPath.split('/').last}')),
        );
      }
    });
  }

  Future<void> _exportData() async {
    await context.handleAsyncError(
      ref.read(localDataManagerProvider).exportData(),
      operation: 'exportData',
      onRetry: _exportData,
    ).then((exportPath) {
      if (exportPath != null) {
        _showExportSuccessDialog(exportPath);
      }
    });
  }

  void _showExportSuccessDialog(String exportPath) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出成功'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('数据已成功导出到:'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                exportPath,
                style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Share.shareXFiles([XFile(exportPath)], text: 'LoadGuard数据导出');
            },
            child: const Text('分享'),
          ),
        ],
      ),
    );
  }

  Future<void> _importData() async {
    // 这里应该实现文件选择器
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('导入功能需要文件选择器，待实现')),
    );
  }

  void _showCleanupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('数据清理'),
        content: const Text('此功能将清理临时文件和旧备份，不会影响主要数据。确定继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performCleanup();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Future<void> _performCleanup() async {
    // 这里应该实现数据清理逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('数据清理完成')),
    );
  }

  void _showSyncConfigDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('同步服务配置'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('同步服务为未来扩展功能，用于：'),
            SizedBox(height: 8),
            Text('• 多设备数据同步'),
            Text('• 企业级数据管理'),
            Text('• 云端备份和恢复'),
            Text('• 团队协作功能'),
            SizedBox(height: 16),
            Text('本地应用无需配置即可正常使用。'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }

  void _openSecuritySettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('安全设置页面待实现')),
    );
  }

  void _openErrorLogs() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('错误日志页面待实现')),
    );
  }

  void _openSystemInfo() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('系统信息页面待实现')),
    );
  }
}
