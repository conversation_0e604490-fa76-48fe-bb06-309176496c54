import 'package:flutter_test/flutter_test.dart';
import 'package:loadguard/models/config_models.dart';

void main() {
  group('ConfigModels Tests', () {
    group('WorkerConfig', () {
      test('should create WorkerConfig with required fields', () {
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        expect(worker.id, 'worker1');
        expect(worker.name, '张三');
        expect(worker.role, '操作员');
        expect(worker.warehouse, 'warehouse1');
        expect(worker.group, 'group1');
        expect(worker.isActive, true); // 默认值
        expect(worker.phone, ''); // 默认值
        expect(worker.email, ''); // 默认值
      });

      test('should create WorkerConfig with all fields', () {
        final now = DateTime.now();
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
          isActive: false,
          phone: '13800138000',
          email: '<EMAIL>',
          createdAt: now,
          updatedAt: now,
        );

        expect(worker.isActive, false);
        expect(worker.phone, '13800138000');
        expect(worker.email, '<EMAIL>');
        expect(worker.createdAt, now);
        expect(worker.updatedAt, now);
      });

      test('should support copyWith', () {
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        final updatedWorker = worker.copyWith(
          name: '李四',
          phone: '13800138000',
        );

        expect(updatedWorker.id, 'worker1'); // 未改变
        expect(updatedWorker.name, '李四'); // 已改变
        expect(updatedWorker.role, '操作员'); // 未改变
        expect(updatedWorker.phone, '13800138000'); // 已改变
        expect(updatedWorker.email, ''); // 未改变
      });

      test('should support JSON serialization', () {
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
          phone: '13800138000',
        );

        final json = worker.toJson();
        expect(json['id'], 'worker1');
        expect(json['name'], '张三');
        expect(json['phone'], '13800138000');
        expect(json['isActive'], true);

        final fromJson = WorkerConfig.fromJson(json);
        expect(fromJson.id, worker.id);
        expect(fromJson.name, worker.name);
        expect(fromJson.phone, worker.phone);
        expect(fromJson.isActive, worker.isActive);
      });

      test('should support equality comparison', () {
        final worker1 = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        final worker2 = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        final worker3 = WorkerConfig(
          id: 'worker2',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        expect(worker1, equals(worker2));
        expect(worker1, isNot(equals(worker3)));
        expect(worker1.hashCode, equals(worker2.hashCode));
      });

      test('should have meaningful toString', () {
        final worker = WorkerConfig(
          id: 'worker1',
          name: '张三',
          role: '操作员',
          warehouse: 'warehouse1',
          group: 'group1',
        );

        final str = worker.toString();
        expect(str, contains('WorkerConfig'));
        expect(str, contains('worker1'));
        expect(str, contains('张三'));
      });
    });

    group('WarehouseConfig', () {
      test('should create WarehouseConfig with required fields', () {
        final warehouse = WarehouseConfig(
          id: 'warehouse1',
          name: '主仓库',
          location: '北京市朝阳区',
        );

        expect(warehouse.id, 'warehouse1');
        expect(warehouse.name, '主仓库');
        expect(warehouse.location, '北京市朝阳区');
        expect(warehouse.isActive, true);
        expect(warehouse.description, '');
        expect(warehouse.supportedTemplates, isEmpty);
      });

      test('should support copyWith with list fields', () {
        final warehouse = WarehouseConfig(
          id: 'warehouse1',
          name: '主仓库',
          location: '北京市朝阳区',
          supportedTemplates: ['template1'],
        );

        final updated = warehouse.copyWith(
          supportedTemplates: ['template1', 'template2'],
        );

        expect(updated.supportedTemplates, ['template1', 'template2']);
        expect(warehouse.supportedTemplates, ['template1']); // 原对象未改变
      });
    });

    group('GroupConfig', () {
      test('should create GroupConfig with required fields', () {
        final group = GroupConfig(
          id: 'group1',
          name: '第一组',
          warehouseId: 'warehouse1',
        );

        expect(group.id, 'group1');
        expect(group.name, '第一组');
        expect(group.warehouseId, 'warehouse1');
        expect(group.isActive, true);
        expect(group.description, '');
        expect(group.memberIds, isEmpty);
      });

      test('should support member management', () {
        final group = GroupConfig(
          id: 'group1',
          name: '第一组',
          warehouseId: 'warehouse1',
          memberIds: ['worker1', 'worker2'],
        );

        final updated = group.copyWith(
          memberIds: ['worker1', 'worker2', 'worker3'],
        );

        expect(updated.memberIds, ['worker1', 'worker2', 'worker3']);
        expect(group.memberIds, ['worker1', 'worker2']); // 原对象未改变
      });
    });
  });
}
