import 'dart:async';
import 'dart:isolate';
import 'package:flutter/material.dart';
import 'package:loadguard/services/super_fast_recognition_service.dart';
import 'package:loadguard/services/isolate_image_processor.dart';
import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 📦 【批量识别服务】
/// 
/// 基于现有Isolate架构的安全扩展
/// 
/// 功能特性：
/// 1. 多图片并行识别
/// 2. 智能队列管理
/// 3. 进度追踪和错误处理
/// 4. 资源控制和限制
class BatchRecognitionService {
  static BatchRecognitionService? _instance;
  static BatchRecognitionService get instance {
    _instance ??= BatchRecognitionService._();
    return _instance!;
  }
  
  BatchRecognitionService._();
  
  // 并发控制配置
  static const int _maxConcurrentRecognitions = 3; // 最大并发识别数
  static const int _maxBatchSize = 10; // 单批次最大图片数
  static const Duration _batchTimeout = Duration(minutes: 5); // 批次超时
  
  final Map<String, BatchRecognitionTask> _activeBatches = {};
  int _currentConcurrentCount = 0;
  bool _isInitialized = false;
  
  /// 初始化批量识别服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    AppLogger.info('📦 初始化批量识别服务...');
    
    try {
      // 确保底层服务已初始化
      await SuperFastRecognitionService.instance.initialize();
      await IsolateImageProcessor.instance.initialize();
      
      _isInitialized = true;
      AppLogger.info('✅ 批量识别服务初始化完成');
      
    } catch (e) {
      AppLogger.error('❌ 批量识别服务初始化失败: $e');
      rethrow;
    }
  }
  
  /// 🚀 批量识别照片
  Future<BatchRecognitionResult> recognizeBatch(
    List<String> imagePaths, {
    String? batchId,
    RecognitionStrategy strategy = RecognitionStrategy.standard,
    Function(BatchProgress)? onProgress,
    String? presetProductCode,
    String? presetBatchNumber,
  }) async {
    if (!_isInitialized) await initialize();
    
    // 参数验证
    if (imagePaths.isEmpty) {
      throw ArgumentError('图片路径列表不能为空');
    }
    
    if (imagePaths.length > _maxBatchSize) {
      throw ArgumentError('批次大小超过限制：${imagePaths.length} > $_maxBatchSize');
    }
    
    final taskId = batchId ?? 'batch_${DateTime.now().millisecondsSinceEpoch}';
    
    AppLogger.info('📦 开始批量识别: $taskId, 图片数量: ${imagePaths.length}');
    
    final task = BatchRecognitionTask(
      id: taskId,
      imagePaths: imagePaths,
      strategy: strategy,
      presetProductCode: presetProductCode,
      presetBatchNumber: presetBatchNumber,
      startTime: DateTime.now(),
    );
    
    _activeBatches[taskId] = task;
    
    try {
      final result = await _processBatch(task, onProgress);
      _activeBatches.remove(taskId);
      return result;
      
    } catch (e) {
      _activeBatches.remove(taskId);
      AppLogger.error('❌ 批量识别失败: $taskId - $e');
      rethrow;
    }
  }
  
  /// 处理批量识别
  Future<BatchRecognitionResult> _processBatch(
    BatchRecognitionTask task,
    Function(BatchProgress)? onProgress,
  ) async {
    final results = <String, SuperRecognitionResult>{};
    final errors = <String, String>{};
    int completedCount = 0;
    
    // 创建进度跟踪器
    void updateProgress() {
      final progress = BatchProgress(
        batchId: task.id,
        totalImages: task.imagePaths.length,
        completedImages: completedCount,
        successCount: results.length,
        failureCount: errors.length,
        currentProgress: completedCount / task.imagePaths.length,
      );
      onProgress?.call(progress);
    }
    
    // 初始进度
    updateProgress();
    
    // 🚀 智能并发控制：根据系统负载动态调整
    final concurrency = _calculateOptimalConcurrency(task.imagePaths.length);
    
    AppLogger.info('📊 使用并发数: $concurrency');
    
    // 分批处理，控制并发
    final batches = _splitIntoBatches(task.imagePaths, concurrency);
    
    for (int batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      final batch = batches[batchIndex];
      
      // 并行处理当前批次
      final futures = batch.map((imagePath) async {
        try {
          _currentConcurrentCount++;
          
          final result = await SuperFastRecognitionService.instance.recognizeSuper(
            imagePath,
            strategy: task.strategy,
            onProgress: (progress, status) {
              // 单个图片的进度可以在这里处理
              AppLogger.debug('单图进度: $imagePath - ${(progress * 100).toInt()}%');
            },
          );
          
          results[imagePath] = result;
          
        } catch (e) {
          errors[imagePath] = e.toString();
          AppLogger.warning('单图识别失败: $imagePath - $e');
        } finally {
          _currentConcurrentCount--;
          completedCount++;
          updateProgress();
        }
      }).toList();
      
      // 等待当前批次完成
      await Future.wait(futures);
      
      AppLogger.info('批次 ${batchIndex + 1}/${batches.length} 完成');
    }
    
    final endTime = DateTime.now();
    final totalTime = endTime.difference(task.startTime);
    
    final batchResult = BatchRecognitionResult(
      batchId: task.id,
      totalImages: task.imagePaths.length,
      successResults: results,
      errors: errors,
      processingTime: totalTime.inMilliseconds,
      strategy: task.strategy,
    );
    
    AppLogger.info(
      '✅ 批量识别完成: ${task.id} - '
      '成功: ${results.length}, 失败: ${errors.length}, '
      '耗时: ${totalTime.inMilliseconds}ms'
    );
    
    return batchResult;
  }
  
  /// 计算最优并发数
  int _calculateOptimalConcurrency(int imageCount) {
    // 基于图片数量和系统负载动态调整
    if (imageCount <= 3) {
      return imageCount; // 小批次直接并行
    } else if (imageCount <= 6) {
      return 3; // 中等批次使用3个并发
    } else {
      return _maxConcurrentRecognitions; // 大批次使用最大并发
    }
  }
  
  /// 将图片列表分割成小批次
  List<List<String>> _splitIntoBatches(List<String> imagePaths, int batchSize) {
    final batches = <List<String>>[];
    
    for (int i = 0; i < imagePaths.length; i += batchSize) {
      final end = (i + batchSize < imagePaths.length) ? i + batchSize : imagePaths.length;
      batches.add(imagePaths.sublist(i, end));
    }
    
    return batches;
  }
  
  /// 取消批量识别
  Future<void> cancelBatch(String batchId) async {
    final task = _activeBatches[batchId];
    if (task != null) {
      task.isCancelled = true;
      _activeBatches.remove(batchId);
      AppLogger.info('❌ 批量识别已取消: $batchId');
    }
  }
  
  /// 获取批量识别状态
  BatchRecognitionTask? getBatchStatus(String batchId) {
    return _activeBatches[batchId];
  }
  
  /// 获取所有活跃批次
  List<BatchRecognitionTask> getActiveBatches() {
    return _activeBatches.values.toList();
  }
  
  /// 获取系统负载信息
  BatchSystemInfo getSystemInfo() {
    return BatchSystemInfo(
      activeBatches: _activeBatches.length,
      currentConcurrency: _currentConcurrentCount,
      maxConcurrency: _maxConcurrentRecognitions,
      maxBatchSize: _maxBatchSize,
    );
  }
  
  /// 销毁服务
  void dispose() {
    for (final task in _activeBatches.values) {
      task.isCancelled = true;
    }
    _activeBatches.clear();
    _currentConcurrentCount = 0;
    _isInitialized = false;
    AppLogger.info('🧹 批量识别服务已销毁');
  }
}

/// 📋 批量识别任务
class BatchRecognitionTask {
  final String id;
  final List<String> imagePaths;
  final RecognitionStrategy strategy;
  final String? presetProductCode;
  final String? presetBatchNumber;
  final DateTime startTime;
  bool isCancelled = false;
  
  BatchRecognitionTask({
    required this.id,
    required this.imagePaths,
    required this.strategy,
    this.presetProductCode,
    this.presetBatchNumber,
    required this.startTime,
  });
  
  Duration get elapsed => DateTime.now().difference(startTime);
}

/// 📊 批量识别进度
class BatchProgress {
  final String batchId;
  final int totalImages;
  final int completedImages;
  final int successCount;
  final int failureCount;
  final double currentProgress; // 0.0 - 1.0
  
  const BatchProgress({
    required this.batchId,
    required this.totalImages,
    required this.completedImages,
    required this.successCount,
    required this.failureCount,
    required this.currentProgress,
  });
  
  bool get isComplete => completedImages >= totalImages;
  double get successRate => totalImages > 0 ? successCount / totalImages : 0.0;
}

/// 📈 批量识别结果
class BatchRecognitionResult {
  final String batchId;
  final int totalImages;
  final Map<String, SuperRecognitionResult> successResults;
  final Map<String, String> errors;
  final int processingTime;
  final RecognitionStrategy strategy;
  
  const BatchRecognitionResult({
    required this.batchId,
    required this.totalImages,
    required this.successResults,
    required this.errors,
    required this.processingTime,
    required this.strategy,
  });
  
  int get successCount => successResults.length;
  int get failureCount => errors.length;
  double get successRate => totalImages > 0 ? successCount / totalImages : 0.0;
  bool get hasAnySuccess => successResults.isNotEmpty;
  bool get isCompleteSuccess => failureCount == 0;
  
  /// 获取最佳识别结果列表
  List<RecognitionResult> getBestResults() {
    return successResults.values
        .map((sr) => sr.getBestResult())
        .where((r) => r != null)
        .cast<RecognitionResult>()
        .toList();
  }
}

/// 🖥️ 系统信息
class BatchSystemInfo {
  final int activeBatches;
  final int currentConcurrency;
  final int maxConcurrency;
  final int maxBatchSize;
  
  const BatchSystemInfo({
    required this.activeBatches,
    required this.currentConcurrency,
    required this.maxConcurrency,
    required this.maxBatchSize,
  });
  
  bool get isSystemBusy => currentConcurrency >= maxConcurrency * 0.8;
  double get loadPercentage => maxConcurrency > 0 ? currentConcurrency / maxConcurrency : 0.0;
}

/// 📦 批量识别组件
class BatchRecognitionWidget extends StatefulWidget {
  final List<String> imagePaths;
  final Function(BatchRecognitionResult) onCompleted;
  final Function(String)? onError;
  final RecognitionStrategy strategy;
  
  const BatchRecognitionWidget({
    Key? key,
    required this.imagePaths,
    required this.onCompleted,
    this.onError,
    this.strategy = RecognitionStrategy.standard,
  }) : super(key: key);
  
  @override
  State<BatchRecognitionWidget> createState() => _BatchRecognitionWidgetState();
}

class _BatchRecognitionWidgetState extends State<BatchRecognitionWidget> {
  BatchProgress? _progress;
  bool _isProcessing = false;
  String? _error;
  
  @override
  void initState() {
    super.initState();
    _startBatchRecognition();
  }
  
  Future<void> _startBatchRecognition() async {
    setState(() {
      _isProcessing = true;
      _error = null;
    });
    
    try {
      final result = await BatchRecognitionService.instance.recognizeBatch(
        widget.imagePaths,
        strategy: widget.strategy,
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _progress = progress;
            });
          }
        },
      );
      
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
        widget.onCompleted(result);
      }
      
    } catch (e) {
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _error = e.toString();
        });
        widget.onError?.call(e.toString());
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return Card(
        color: Colors.red[50],
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              const Icon(Icons.error, color: Colors.red, size: 48),
              const SizedBox(height: 8),
              Text(
                '批量识别失败',
                style: TextStyle(
                  color: Colors.red[800],
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _error!,
                style: TextStyle(color: Colors.red[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Icon(Icons.batch_prediction, size: 48, color: Colors.blue),
            const SizedBox(height: 8),
            const Text(
              '批量识别中...',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (_progress != null) ...[
              LinearProgressIndicator(
                value: _progress!.currentProgress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
              const SizedBox(height: 8),
              Text(
                '${_progress!.completedImages}/${_progress!.totalImages} 张图片已完成',
                style: const TextStyle(color: Colors.grey),
              ),
              if (_progress!.completedImages > 0) ...[
                const SizedBox(height: 4),
                Text(
                  '成功: ${_progress!.successCount}, 失败: ${_progress!.failureCount}',
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ] else
              const LinearProgressIndicator(),
          ],
        ),
      ),
    );
  }
}