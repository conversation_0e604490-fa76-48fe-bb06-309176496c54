import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/exceptions/app_exceptions.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 全局错误处理器
/// 统一处理应用中的所有异常，提供用户友好的错误提示和错误恢复机制
class GlobalErrorHandler {
  static GlobalErrorHandler? _instance;
  static GlobalErrorHandler get instance => _instance ??= GlobalErrorHandler._();
  
  GlobalErrorHandler._();

  final List<ErrorRecord> _errorHistory = [];
  final StreamController<ErrorEvent> _errorStream = StreamController.broadcast();
  
  static const int _maxErrorHistory = 100;
  
  /// 错误事件流
  Stream<ErrorEvent> get errorStream => _errorStream.stream;
  
  /// 初始化全局错误处理
  void initialize() {
    // 捕获Flutter框架错误
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };
    
    // 捕获异步错误
    PlatformDispatcher.instance.onError = (error, stack) {
      _handlePlatformError(error, stack);
      return true;
    };
    
    AppLogger.info('🛡️ 全局错误处理器已初始化');
  }
  
  /// 处理应用异常
  Future<void> handleException(
    Object error, {
    StackTrace? stackTrace,
    BuildContext? context,
    String? operation,
    Map<String, dynamic>? additionalData,
    bool showUserMessage = true,
    VoidCallback? onRetry,
  }) async {
    try {
      final appException = _convertToAppException(error, stackTrace, operation, additionalData);
      final errorRecord = _createErrorRecord(appException, operation);
      
      // 记录错误
      _recordError(errorRecord);
      
      // 发送错误事件
      _errorStream.add(ErrorEvent(
        exception: appException,
        operation: operation,
        timestamp: DateTime.now(),
        context: context,
      ));
      
      // 显示用户消息
      if (showUserMessage && context != null && context.mounted) {
        await _showUserMessage(context, appException, onRetry);
      }
      
      // 执行错误恢复
      await _performErrorRecovery(appException, context);
      
    } catch (e) {
      // 错误处理器本身出错，记录但不再抛出
      AppLogger.error('❌ 错误处理器异常', error: e);
    }
  }
  
  /// 处理网络错误
  Future<void> handleNetworkError(
    Object error, {
    BuildContext? context,
    String? operation,
    VoidCallback? onRetry,
  }) async {
    NetworkException networkException;
    
    if (error is SocketException) {
      networkException = NetworkException.noConnection();
    } else if (error is TimeoutException) {
      networkException = NetworkException.connectionTimeout();
    } else if (error is HttpException) {
      final statusCode = int.tryParse(error.message.split(' ').last) ?? 500;
      networkException = NetworkException.serverError(statusCode);
    } else {
      networkException = NetworkException(
        message: error.toString(),
        originalError: error,
      );
    }
    
    await handleException(
      networkException,
      context: context,
      operation: operation,
      onRetry: onRetry,
    );
  }
  
  /// 处理存储错误
  Future<void> handleStorageError(
    Object error, {
    BuildContext? context,
    String? operation,
    String? path,
    VoidCallback? onRetry,
  }) async {
    StorageException storageException;
    
    if (error.toString().contains('No space left')) {
      storageException = StorageException.insufficientSpace();
    } else if (operation?.contains('read') == true) {
      storageException = StorageException.readFailed(path ?? 'unknown');
    } else if (operation?.contains('write') == true) {
      storageException = StorageException.writeFailed(path ?? 'unknown');
    } else {
      storageException = StorageException(
        message: error.toString(),
        originalError: error,
      );
    }
    
    await handleException(
      storageException,
      context: context,
      operation: operation,
      onRetry: onRetry,
    );
  }
  
  /// 处理识别错误
  Future<void> handleRecognitionError(
    Object error, {
    BuildContext? context,
    String? operation,
    VoidCallback? onRetry,
  }) async {
    RecognitionException recognitionException;
    
    if (error.toString().contains('timeout')) {
      recognitionException = RecognitionException.timeout();
    } else if (error.toString().contains('no text')) {
      recognitionException = RecognitionException.noTextFound();
    } else if (error.toString().contains('ML Kit')) {
      recognitionException = RecognitionException.mlkitFailed();
    } else {
      recognitionException = RecognitionException(
        message: error.toString(),
        originalError: error,
      );
    }
    
    await handleException(
      recognitionException,
      context: context,
      operation: operation,
      onRetry: onRetry,
    );
  }
  
  /// 获取错误历史
  List<ErrorRecord> getErrorHistory({
    Duration? timeWindow,
    ErrorCategory? category,
    ErrorSeverity? severity,
  }) {
    var history = List<ErrorRecord>.from(_errorHistory);
    
    if (timeWindow != null) {
      final cutoff = DateTime.now().subtract(timeWindow);
      history = history.where((record) => record.timestamp.isAfter(cutoff)).toList();
    }
    
    if (category != null) {
      history = history.where((record) => record.exception.category == category).toList();
    }
    
    if (severity != null) {
      history = history.where((record) => record.exception.severity == severity).toList();
    }
    
    return history;
  }
  
  /// 获取错误统计
  ErrorStatistics getErrorStatistics({Duration? timeWindow}) {
    final history = getErrorHistory(timeWindow: timeWindow);
    
    if (history.isEmpty) {
      return ErrorStatistics.empty();
    }
    
    final totalErrors = history.length;
    final errorsByCategory = <ErrorCategory, int>{};
    final errorsBySeverity = <ErrorSeverity, int>{};
    final errorsByCode = <String, int>{};
    
    for (final record in history) {
      final exception = record.exception;
      errorsByCategory[exception.category] = (errorsByCategory[exception.category] ?? 0) + 1;
      errorsBySeverity[exception.severity] = (errorsBySeverity[exception.severity] ?? 0) + 1;
      errorsByCode[exception.code] = (errorsByCode[exception.code] ?? 0) + 1;
    }
    
    final mostCommonCategory = errorsByCategory.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
    
    final mostCommonError = errorsByCode.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
    
    return ErrorStatistics(
      totalErrors: totalErrors,
      timeWindow: timeWindow ?? const Duration(hours: 24),
      errorsByCategory: errorsByCategory,
      errorsBySeverity: errorsBySeverity,
      errorsByCode: errorsByCode,
      mostCommonCategory: mostCommonCategory,
      mostCommonError: mostCommonError,
      generatedAt: DateTime.now(),
    );
  }
  
  /// 清理错误历史
  void clearErrorHistory() {
    _errorHistory.clear();
    AppLogger.info('🧹 错误历史已清理');
  }
  
  /// 处理Flutter框架错误
  void _handleFlutterError(FlutterErrorDetails details) {
    final exception = SystemException(
      message: 'Flutter framework error: ${details.exception}',
      code: 'FLUTTER_ERROR',
      originalError: details.exception,
      stackTrace: details.stack,
      details: {
        'library': details.library,
        'context': details.context?.toString(),
      },
    );
    
    _recordError(_createErrorRecord(exception, 'Flutter Framework'));
    
    // 在debug模式下打印详细信息
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }
  
  /// 处理平台错误
  bool _handlePlatformError(Object error, StackTrace stack) {
    final exception = SystemException(
      message: 'Platform error: $error',
      code: 'PLATFORM_ERROR',
      originalError: error,
      stackTrace: stack,
    );
    
    _recordError(_createErrorRecord(exception, 'Platform'));
    return true;
  }
  
  /// 转换为AppException
  AppException _convertToAppException(
    Object error,
    StackTrace? stackTrace,
    String? operation,
    Map<String, dynamic>? additionalData,
  ) {
    if (error is AppException) {
      return error;
    }
    
    // 根据错误类型和操作上下文推断异常类型
    if (error is SocketException || error is HttpException) {
      return NetworkException(
        message: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        details: additionalData,
      );
    }
    
    if (error is FileSystemException) {
      return StorageException(
        message: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        details: additionalData,
      );
    }
    
    if (operation?.contains('recognition') == true || operation?.contains('mlkit') == true) {
      return RecognitionException(
        message: error.toString(),
        originalError: error,
        stackTrace: stackTrace,
        details: additionalData,
      );
    }
    
    // 默认为系统异常
    return SystemException(
      message: error.toString(),
      originalError: error,
      stackTrace: stackTrace,
      details: additionalData,
    );
  }
  
  /// 创建错误记录
  ErrorRecord _createErrorRecord(AppException exception, String? operation) {
    return ErrorRecord(
      exception: exception,
      operation: operation,
      timestamp: DateTime.now(),
      deviceInfo: _getDeviceInfo(),
    );
  }
  
  /// 记录错误
  void _recordError(ErrorRecord record) {
    _errorHistory.add(record);
    
    // 保持历史记录大小
    while (_errorHistory.length > _maxErrorHistory) {
      _errorHistory.removeAt(0);
    }
    
    // 记录到日志
    AppLogger.error(
      '🚨 ${record.exception.runtimeType}: ${record.exception.message}${record.operation != null ? ' (Operation: ${record.operation})' : ''}',
      error: record.exception.originalError,
      stackTrace: record.exception.stackTrace,
    );
  }
  
  /// 显示用户消息
  Future<void> _showUserMessage(
    BuildContext context,
    AppException exception,
    VoidCallback? onRetry,
  ) async {
    if (!context.mounted) return;
    
    final actions = <Widget>[
      TextButton(
        onPressed: () => Navigator.of(context).pop(),
        child: const Text('确定'),
      ),
    ];
    
    if (exception.retryable && onRetry != null) {
      actions.insert(0, TextButton(
        onPressed: () {
          Navigator.of(context).pop();
          onRetry();
        },
        child: const Text('重试'),
      ));
    }
    
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getErrorTitle(exception)),
        content: Text(exception.displayMessage),
        actions: actions,
      ),
    );
  }
  
  /// 获取错误标题
  String _getErrorTitle(AppException exception) {
    switch (exception.severity) {
      case ErrorSeverity.critical:
        return '严重错误';
      case ErrorSeverity.high:
        return '错误';
      case ErrorSeverity.medium:
        return '警告';
      case ErrorSeverity.low:
        return '提示';
    }
  }
  
  /// 执行错误恢复
  Future<void> _performErrorRecovery(AppException exception, BuildContext? context) async {
    // 根据错误类型执行相应的恢复策略
    switch (exception.category) {
      case ErrorCategory.storage:
        // 存储错误恢复：清理临时文件
        await _cleanupTemporaryFiles();
        break;
      case ErrorCategory.network:
        // 网络错误恢复：重置网络状态
        await _resetNetworkState();
        break;
      case ErrorCategory.recognition:
        // 识别错误恢复：清理识别缓存
        await _cleanupRecognitionCache();
        break;
      default:
        // 通用恢复：记录错误即可
        break;
    }
  }
  
  /// 清理临时文件
  Future<void> _cleanupTemporaryFiles() async {
    try {
      // 实现临时文件清理逻辑
      AppLogger.debug('🧹 清理临时文件');
    } catch (e) {
      AppLogger.warning('⚠️ 清理临时文件失败: $e');
    }
  }
  
  /// 重置网络状态
  Future<void> _resetNetworkState() async {
    try {
      // 实现网络状态重置逻辑
      AppLogger.debug('🔄 重置网络状态');
    } catch (e) {
      AppLogger.warning('⚠️ 重置网络状态失败: $e');
    }
  }
  
  /// 清理识别缓存
  Future<void> _cleanupRecognitionCache() async {
    try {
      // 实现识别缓存清理逻辑
      AppLogger.debug('🧹 清理识别缓存');
    } catch (e) {
      AppLogger.warning('⚠️ 清理识别缓存失败: $e');
    }
  }
  
  /// 获取设备信息
  Map<String, dynamic> _getDeviceInfo() {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'isDebug': kDebugMode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// 释放资源
  void dispose() {
    _errorStream.close();
    _errorHistory.clear();
    AppLogger.info('🛡️ 全局错误处理器已释放');
  }
}

/// 错误记录
class ErrorRecord {
  final AppException exception;
  final String? operation;
  final DateTime timestamp;
  final Map<String, dynamic> deviceInfo;

  const ErrorRecord({
    required this.exception,
    this.operation,
    required this.timestamp,
    required this.deviceInfo,
  });

  Map<String, dynamic> toMap() {
    return {
      'exception': exception.toMap(),
      'operation': operation,
      'timestamp': timestamp.toIso8601String(),
      'deviceInfo': deviceInfo,
    };
  }
}

/// 错误事件
class ErrorEvent {
  final AppException exception;
  final String? operation;
  final DateTime timestamp;
  final BuildContext? context;

  const ErrorEvent({
    required this.exception,
    this.operation,
    required this.timestamp,
    this.context,
  });
}

/// 错误统计
class ErrorStatistics {
  final int totalErrors;
  final Duration timeWindow;
  final Map<ErrorCategory, int> errorsByCategory;
  final Map<ErrorSeverity, int> errorsBySeverity;
  final Map<String, int> errorsByCode;
  final ErrorCategory mostCommonCategory;
  final String mostCommonError;
  final DateTime generatedAt;

  const ErrorStatistics({
    required this.totalErrors,
    required this.timeWindow,
    required this.errorsByCategory,
    required this.errorsBySeverity,
    required this.errorsByCode,
    required this.mostCommonCategory,
    required this.mostCommonError,
    required this.generatedAt,
  });

  factory ErrorStatistics.empty() {
    return ErrorStatistics(
      totalErrors: 0,
      timeWindow: Duration.zero,
      errorsByCategory: {},
      errorsBySeverity: {},
      errorsByCode: {},
      mostCommonCategory: ErrorCategory.general,
      mostCommonError: '',
      generatedAt: DateTime.now(),
    );
  }

  double get errorRate => totalErrors / timeWindow.inHours;

  ErrorSeverity get overallSeverity {
    if (errorsBySeverity[ErrorSeverity.critical] != null && errorsBySeverity[ErrorSeverity.critical]! > 0) {
      return ErrorSeverity.critical;
    }
    if (errorsBySeverity[ErrorSeverity.high] != null && errorsBySeverity[ErrorSeverity.high]! > 0) {
      return ErrorSeverity.high;
    }
    if (errorsBySeverity[ErrorSeverity.medium] != null && errorsBySeverity[ErrorSeverity.medium]! > 0) {
      return ErrorSeverity.medium;
    }
    return ErrorSeverity.low;
  }
}

/// 全局错误处理器Provider
final globalErrorHandlerProvider = Provider<GlobalErrorHandler>((ref) {
  final handler = GlobalErrorHandler.instance;
  handler.initialize();
  ref.onDispose(() => handler.dispose());
  return handler;
});
