plugins {
    id "com.android.application"
    id "org.jetbrains.kotlin.android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.example.loadguard"
    compileSdk 35
    ndkVersion "26.3.11579264"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.example.loadguard"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdk 21  // 明确最低支持Android 5.0 (API 21)
        targetSdk 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        
        // 多架构支持
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.debug
            
            // 启用代码优化
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // 启用资源优化
            resValue "string", "app_name", "装运卫士"
        }
        
        debug {
            // 调试版本配置
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            debuggable true
            
            // 调试版本不启用代码混淆
            minifyEnabled false
            shrinkResources false
        }
    }
    
    // 构建缓存已在新版本中默认启用
    
    // 启用增量编译
    compileOptions {
        incremental true
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        exclude("META-INF/*.kotlin_module")
        
        // 解决重复类问题
        pickFirst '**/libc++_shared.so'
        pickFirst '**/libjsc.so'
        pickFirst '**/libflutter.so'
    }
}

flutter {
    source '../..'
}

dependencies {
    // 🚀 ML Kit Text Recognition V2 官方依赖（0.15.0版本按官方文档配置）
    implementation 'com.google.mlkit:text-recognition:16.0.1'
    
    // 🌐 离线多语言模型支持（按官方文档配置所需语言）
    implementation 'com.google.mlkit:text-recognition-chinese:16.0.1'
    implementation 'com.google.mlkit:text-recognition-devanagari:16.0.1'
    implementation 'com.google.mlkit:text-recognition-japanese:16.0.1'
    implementation 'com.google.mlkit:text-recognition-korean:16.0.1'
    
    // 🔧 修复Flutter Play Store分割安装错误的依赖
    implementation 'com.google.android.play:core:1.10.3'
    implementation 'com.google.android.play:core-ktx:1.8.1'
    
    // 协程支持 (异步处理)
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3'
}
