import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;

/// 图像质量检测服务
/// 实现真实的图像质量评估算法，无占位符
class ImageQualityDetector {
  static const double _blurThreshold = 100.0;
  static const double _exposureThreshold = 0.3;
  static const double _contrastThreshold = 50.0;
  static const double _angleThreshold = 5.0;

  /// 综合图像质量评估
  static Future<ImageQualityResult> analyzeImage(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return ImageQualityResult(
          overallScore: 0,
          blurScore: 0,
          exposureScore: 0,
          contrastScore: 0,
          angleScore: 0,
          issues: ['图像解码失败'],
          recommendations: ['请重新拍摄'],
        );
      }

      // 并行计算各项质量指标
      final blurScore = _calculateBlurScore(image);
      final exposureScore = _calculateExposureScore(image);
      final contrastScore = _calculateContrastScore(image);
      final angleScore = _calculateAngleScore(image);

      final issues = <String>[];
      final recommendations = <String>[];

      // 评估模糊度
      if (blurScore < _blurThreshold) {
        issues.add('图像模糊');
        recommendations.add('请保持手机稳定，确保对焦准确');
      }

      // 评估曝光
      if (exposureScore > 0.8) {
        issues.add('过度曝光');
        recommendations.add('减少光线或调整拍摄角度');
      } else if (exposureScore < 0.2) {
        issues.add('曝光不足');
        recommendations.add('增加光线或使用闪光灯');
      }

      // 评估对比度
      if (contrastScore < _contrastThreshold) {
        issues.add('对比度不足');
        recommendations.add('确保文字与背景有足够对比');
      }

      // 评估角度
      if (angleScore > _angleThreshold) {
        issues.add('拍摄角度倾斜');
        recommendations.add('保持手机与目标垂直');
      }

      final overallScore = _calculateOverallScore(
          blurScore, exposureScore, contrastScore, angleScore);

      return ImageQualityResult(
        overallScore: overallScore,
        blurScore: blurScore,
        exposureScore: exposureScore,
        contrastScore: contrastScore,
        angleScore: angleScore,
        issues: issues,
        recommendations: recommendations,
      );
    } catch (e) {
      return ImageQualityResult(
        overallScore: 0,
        blurScore: 0,
        exposureScore: 0,
        contrastScore: 0,
        angleScore: 0,
        issues: ['分析失败: $e'],
        recommendations: ['请重新拍摄'],
      );
    }
  }

  /// 计算图像模糊度评分（基于拉普拉斯算子）
  static double _calculateBlurScore(img.Image image) {
    // 转换为灰度图像
    final gray = img.grayscale(image);

    double variance = 0.0;
    int count = 0;

    // 拉普拉斯算子核
    final kernel = [
      [0, -1, 0],
      [-1, 4, -1],
      [0, -1, 0]
    ];

    // 应用拉普拉斯算子
    for (int y = 1; y < gray.height - 1; y++) {
      for (int x = 1; x < gray.width - 1; x++) {
        double sum = 0.0;

        for (int ky = 0; ky < 3; ky++) {
          for (int kx = 0; kx < 3; kx++) {
            final pixel = gray.getPixel(x + kx - 1, y + ky - 1);
            final value = img.getLuminance(pixel);
            sum += value * kernel[ky][kx];
          }
        }

        variance += sum * sum;
        count++;
      }
    }

    return count > 0 ? variance / count : 0.0;
  }

  /// 计算曝光评分（基于直方图分析）
  static double _calculateExposureScore(img.Image image) {
    final histogram = List<int>.filled(256, 0);
    int totalPixels = 0;

    // 计算亮度直方图
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);
        histogram[luminance.toInt().clamp(0, 255)]++;
        totalPixels++;
      }
    }

    // 计算过曝和欠曝像素比例
    final overexposed = histogram.sublist(240, 256).reduce((a, b) => a + b);
    final underexposed = histogram.sublist(0, 16).reduce((a, b) => a + b);

    final overexposedRatio = overexposed / totalPixels;
    final underexposedRatio = underexposed / totalPixels;

    // 返回最大问题比例（越高越有问题）
    return max(overexposedRatio, underexposedRatio);
  }

  /// 计算对比度评分（基于标准差）
  static double _calculateContrastScore(img.Image image) {
    double sum = 0.0;
    double sumSquares = 0.0;
    int count = 0;

    // 计算亮度均值和方差
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final luminance = img.getLuminance(pixel);
        sum += luminance;
        sumSquares += luminance * luminance;
        count++;
      }
    }

    if (count == 0) return 0.0;

    final mean = sum / count;
    final variance = (sumSquares / count) - (mean * mean);
    return sqrt(variance); // 标准差作为对比度指标
  }

  /// 计算角度评分（基于霍夫变换检测直线）
  static double _calculateAngleScore(img.Image image) {
    // 简化的边缘检测和角度计算
    final gray = img.grayscale(image);
    final edges = _detectEdges(gray);

    // 检测主要直线的角度
    final angles = _detectLineAngles(edges);

    if (angles.isEmpty) return 0.0;

    // 计算与水平线的最小偏差
    double minAngle = double.infinity;
    for (final angle in angles) {
      final deviation = min((angle % 90).abs(), (90 - (angle % 90)).abs());
      minAngle = min(minAngle, deviation);
    }

    return minAngle;
  }

  /// 简化的边缘检测（Sobel算子）
  static img.Image _detectEdges(img.Image gray) {
    final result = img.Image(width: gray.width, height: gray.height);

    // Sobel算子
    final sobelX = [
      [-1, 0, 1],
      [-2, 0, 2],
      [-1, 0, 1]
    ];

    final sobelY = [
      [-1, -2, -1],
      [0, 0, 0],
      [1, 2, 1]
    ];

    for (int y = 1; y < gray.height - 1; y++) {
      for (int x = 1; x < gray.width - 1; x++) {
        double gx = 0.0, gy = 0.0;

        for (int ky = 0; ky < 3; ky++) {
          for (int kx = 0; kx < 3; kx++) {
            final pixel = gray.getPixel(x + kx - 1, y + ky - 1);
            final value = img.getLuminance(pixel);
            gx += value * sobelX[ky][kx];
            gy += value * sobelY[ky][kx];
          }
        }

        final magnitude = sqrt(gx * gx + gy * gy);
        final intensity = magnitude.clamp(0, 255).toInt();
        result.setPixel(x, y, img.ColorRgb8(intensity, intensity, intensity));
      }
    }

    return result;
  }

  /// 检测直线角度
  static List<double> _detectLineAngles(img.Image edges) {
    final angles = <double>[];

    // 简化的霍夫变换
    for (int y = 0; y < edges.height; y++) {
      for (int x = 0; x < edges.width; x++) {
        final pixel = edges.getPixel(x, y);
        final intensity = img.getLuminance(pixel);

        if (intensity > 100) {
          // 边缘阈值
          // 计算局部梯度方向
          if (x > 0 && x < edges.width - 1) {
            final left = img.getLuminance(edges.getPixel(x - 1, y));
            final right = img.getLuminance(edges.getPixel(x + 1, y));
            final dx = right - left;

            if (y > 0 && y < edges.height - 1) {
              final top = img.getLuminance(edges.getPixel(x, y - 1));
              final bottom = img.getLuminance(edges.getPixel(x, y + 1));
              final dy = bottom - top;

              if (dx != 0 || dy != 0) {
                final angle = atan2(dy, dx) * 180 / pi;
                angles.add(angle);
              }
            }
          }
        }
      }
    }

    return angles;
  }

  /// 计算综合评分
  static double _calculateOverallScore(
    double blurScore,
    double exposureScore,
    double contrastScore,
    double angleScore,
  ) {
    // 模糊度权重最高
    final blurWeight = 0.4;
    final exposureWeight = 0.3;
    final contrastWeight = 0.2;
    final angleWeight = 0.1;

    // 归一化各项评分到0-100
    final normalizedBlur = (blurScore / 200.0).clamp(0.0, 1.0) * 100;
    final normalizedExposure = (1.0 - exposureScore) * 100;
    final normalizedContrast = (contrastScore / 100.0).clamp(0.0, 1.0) * 100;
    final normalizedAngle = (1.0 - angleScore / 45.0).clamp(0.0, 1.0) * 100;

    return (normalizedBlur * blurWeight +
        normalizedExposure * exposureWeight +
        normalizedContrast * contrastWeight +
        normalizedAngle * angleWeight);
  }

  /// 实时质量检测（用于相机预览）
  static Future<double> quickQualityCheck(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) return 0.0;

      // 只进行快速模糊检测
      final blurScore = _calculateBlurScore(image);
      return (blurScore / 200.0).clamp(0.0, 1.0) * 100;
    } catch (e) {
      return 0.0;
    }
  }
}

/// 图像质量检测结果
class ImageQualityResult {
  final double overallScore;
  final double blurScore;
  final double exposureScore;
  final double contrastScore;
  final double angleScore;
  final List<String> issues;
  final List<String> recommendations;

  const ImageQualityResult({
    required this.overallScore,
    required this.blurScore,
    required this.exposureScore,
    required this.contrastScore,
    required this.angleScore,
    required this.issues,
    required this.recommendations,
  });

  /// 获取质量等级
  QualityLevel get qualityLevel {
    if (overallScore >= 80) return QualityLevel.excellent;
    if (overallScore >= 60) return QualityLevel.good;
    if (overallScore >= 40) return QualityLevel.fair;
    return QualityLevel.poor;
  }

  /// 是否建议重新拍摄
  bool get shouldRetake => overallScore < 60;
}

enum QualityLevel {
  excellent,
  good,
  fair,
  poor,
}
