import 'dart:io';

/// 批量迁移LoggingService调用到AppLogger的脚本
void main() async {
  // 需要迁移的文件列表
  final filesToMigrate = [
    'lib/core/providers/configuration_providers.dart',
    'lib/core/providers/management_providers.dart',
    'lib/core/providers/simple_providers.dart',
    'lib/pages/app_launcher_page.dart',
    'lib/pages/enhanced_task_page.dart',
    'lib/pages/strict_activation_page.dart',
    'lib/pages/task/task_mixed_view.dart',
    'lib/services/data_consistency_checker.dart',
    'lib/services/enhanced_cache_service.dart',
    'lib/services/input_optimizer.dart',
    'lib/services/logging_service.dart',
    'lib/services/mlkit_version_adapter.dart',
    'lib/services/preset_product_service.dart',
    'lib/services/unified_license_storage.dart',
    'lib/services/system_health_checker.dart',
    'lib/utils/gesture_navigation.dart',
  ];

  int totalMigrated = 0;
  int totalFiles = 0;

  for (final filePath in filesToMigrate) {
    final file = File(filePath);

    if (!await file.exists()) {
      print('⚠️ 文件不存在: $filePath');
      continue;
    }

    String content = await file.readAsString();
    final originalContent = content;

    // 替换LoggingService调用
    content = content.replaceAll('LoggingService.info(', 'AppLogger.info(');
    content = content.replaceAll('LoggingService.warning(', 'AppLogger.warning(');
    content = content.replaceAll('LoggingService.error(', 'AppLogger.error(');
    content = content.replaceAll('LoggingService.debug(', 'AppLogger.debug(');

    // 替换导入语句
    content = content.replaceAll(
      "import 'package:loadguard/services/logging_service.dart';",
      "import 'package:loadguard/utils/app_logger.dart';"
    );
    content = content.replaceAll(
      'import "../services/logging_service.dart";',
      'import "../utils/app_logger.dart";'
    );

    if (content != originalContent) {
      await file.writeAsString(content);
      totalMigrated++;
      print('✅ 已迁移: $filePath');
    } else {
      print('ℹ️ 无需迁移: $filePath');
    }

    totalFiles++;
  }

  print('\n📊 迁移完成统计:');
  print('- 处理文件数: $totalFiles');
  print('- 成功迁移数: $totalMigrated');
  print('- 跳过文件数: ${totalFiles - totalMigrated}');
}
