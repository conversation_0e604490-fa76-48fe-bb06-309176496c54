/// 🔍 任务验证服务 V2
///
/// 专门负责任务数据的验证，包括：
/// - 任务完整性验证
/// - 照片完整性验证
/// - 业务规则验证
/// - 数据一致性验证
/// 从TaskService中提取，减少其复杂度
library task_validation_service_v2;

import 'package:loadguard/models/task_model.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 验证结果
class ValidationResult {
  final bool isValid;
  final String? errorMessage;
  final String? fieldName;
  final List<String> warnings;
  
  const ValidationResult({
    required this.isValid,
    this.errorMessage,
    this.fieldName,
    this.warnings = const [],
  });
  
  factory ValidationResult.valid([List<String> warnings = const []]) => 
      ValidationResult(isValid: true, warnings: warnings);
  
  factory ValidationResult.invalid(String message, [String? fieldName]) =>
      ValidationResult(
        isValid: false,
        errorMessage: message,
        fieldName: fieldName,
      );
}

/// 任务验证服务 V2
class TaskValidationServiceV2 {
  
  /// 验证任务完整性
  ValidationResult validateTaskCompleteness(TaskModel task) {
    try {
      AppLogger.info('🔍 开始验证任务完整性: ${task.id}', tag: 'TaskValidation');
      
      final warnings = <String>[];
      
      // 验证基本信息
      final basicResult = _validateBasicInfo(task);
      if (!basicResult.isValid) {
        return basicResult;
      }
      warnings.addAll(basicResult.warnings);
      
      // 验证批次信息
      final batchResult = _validateBatchInfo(task);
      if (!batchResult.isValid) {
        return batchResult;
      }
      warnings.addAll(batchResult.warnings);
      
      // 验证照片信息
      final photoResult = _validatePhotoInfo(task);
      if (!photoResult.isValid) {
        return photoResult;
      }
      warnings.addAll(photoResult.warnings);
      
      // 验证参与人员
      final participantResult = _validateParticipants(task);
      if (!participantResult.isValid) {
        return participantResult;
      }
      warnings.addAll(participantResult.warnings);
      
      AppLogger.info('✅ 任务完整性验证通过', tag: 'TaskValidation');
      return ValidationResult.valid(warnings);
    } catch (e) {
      AppLogger.error('❌ 任务完整性验证失败: $e', tag: 'TaskValidation');
      return ValidationResult.invalid('验证过程中发生错误: $e');
    }
  }

  /// 验证任务是否可以完成
  ValidationResult validateTaskCanComplete(TaskModel task) {
    try {
      AppLogger.info('🔍 验证任务是否可以完成: ${task.id}', tag: 'TaskValidation');
      
      // 检查必需照片是否已上传
      final requiredPhotos = task.photos.where((p) => p.isRequired).toList();
      final missingPhotos = requiredPhotos
          .where((p) => p.imagePath == null || p.imagePath!.isEmpty)
          .toList();
      
      if (missingPhotos.isNotEmpty) {
        final message = '还有${missingPhotos.length}张必需照片未上传: ${missingPhotos.map((p) => p.label).join(', ')}';
        AppLogger.warning('⚠️ $message', tag: 'TaskValidation');
        return ValidationResult.invalid(message);
      }
      
      // 检查需要识别的照片是否已识别
      final recognitionPhotos = task.photos.where((p) => p.needRecognition).toList();
      final unrecognizedPhotos = recognitionPhotos
          .where((p) => p.recognitionResult == null)
          .toList();
      
      if (unrecognizedPhotos.isNotEmpty) {
        final message = '还有${unrecognizedPhotos.length}张照片未完成识别: ${unrecognizedPhotos.map((p) => p.label).join(', ')}';
        AppLogger.warning('⚠️ $message', tag: 'TaskValidation');
        return ValidationResult.invalid(message);
      }
      
      // 检查批次是否都已完成
      final incompleteBatches = task.batches.where((b) => !b.isCompleted).toList();
      if (incompleteBatches.isNotEmpty) {
        final message = '还有${incompleteBatches.length}个批次未完成: ${incompleteBatches.map((b) => b.batchNumber).join(', ')}';
        AppLogger.warning('⚠️ $message', tag: 'TaskValidation');
        return ValidationResult.invalid(message);
      }
      
      AppLogger.info('✅ 任务可以完成', tag: 'TaskValidation');
      return ValidationResult.valid();
    } catch (e) {
      AppLogger.error('❌ 验证任务完成条件失败: $e', tag: 'TaskValidation');
      return ValidationResult.invalid('验证过程中发生错误: $e');
    }
  }

  /// 验证照片上传
  ValidationResult validatePhotoUpload(TaskModel task, String photoId, String imagePath) {
    try {
      AppLogger.info('🔍 验证照片上传: $photoId', tag: 'TaskValidation');
      
      // 检查照片是否存在
      final photo = task.photos.firstWhere(
        (p) => p.id == photoId,
        orElse: () => throw Exception('照片不存在'),
      );
      
      // 检查文件路径
      if (imagePath.isEmpty) {
        return ValidationResult.invalid('照片路径不能为空');
      }
      
      // 检查文件扩展名
      final validExtensions = ['.jpg', '.jpeg', '.png', '.bmp'];
      final extension = imagePath.toLowerCase().substring(imagePath.lastIndexOf('.'));
      if (!validExtensions.contains(extension)) {
        return ValidationResult.invalid('不支持的图片格式: $extension');
      }
      
      AppLogger.info('✅ 照片上传验证通过', tag: 'TaskValidation');
      return ValidationResult.valid();
    } catch (e) {
      AppLogger.error('❌ 照片上传验证失败: $e', tag: 'TaskValidation');
      return ValidationResult.invalid('验证照片上传失败: $e');
    }
  }

  /// 验证识别结果
  ValidationResult validateRecognitionResult(TaskModel task, String photoId, RecognitionResult result) {
    try {
      AppLogger.info('🔍 验证识别结果: $photoId', tag: 'TaskValidation');
      
      // 检查照片是否存在
      final photo = task.photos.firstWhere(
        (p) => p.id == photoId,
        orElse: () => throw Exception('照片不存在'),
      );
      
      // 检查识别结果的有效性
      if (result.recognizedText?.isEmpty ?? true) {
        return ValidationResult.invalid('识别结果不能为空');
      }
      
      // 根据照片类型验证识别结果
      final typeValidation = _validateRecognitionByPhotoType(photo, result);
      if (!typeValidation.isValid) {
        return typeValidation;
      }
      
      AppLogger.info('✅ 识别结果验证通过', tag: 'TaskValidation');
      return ValidationResult.valid();
    } catch (e) {
      AppLogger.error('❌ 识别结果验证失败: $e', tag: 'TaskValidation');
      return ValidationResult.invalid('验证识别结果失败: $e');
    }
  }

  /// 私有方法：验证基本信息
  ValidationResult _validateBasicInfo(TaskModel task) {
    final warnings = <String>[];
    
    if (task.template.isEmpty) {
      return ValidationResult.invalid('任务模板不能为空', 'template');
    }
    
    if (task.id.isEmpty) {
      return ValidationResult.invalid('任务ID不能为空', 'id');
    }
    
    if (task.createdAt.isAfter(DateTime.now())) {
      warnings.add('任务创建时间晚于当前时间');
    }
    
    return ValidationResult.valid(warnings);
  }

  /// 私有方法：验证批次信息
  ValidationResult _validateBatchInfo(TaskModel task) {
    final warnings = <String>[];
    
    if (task.batches.isEmpty) {
      return ValidationResult.invalid('任务必须包含至少一个批次', 'batches');
    }
    
    for (final batch in task.batches) {
      if (batch.productCode.isEmpty) {
        return ValidationResult.invalid('批次产品代码不能为空: ${batch.batchNumber}', 'productCode');
      }
      
      if (batch.batchNumber.isEmpty) {
        return ValidationResult.invalid('批次号不能为空', 'batchNumber');
      }
      
      if (batch.plannedQuantity <= 0) {
        return ValidationResult.invalid('批次计划数量必须大于0: ${batch.batchNumber}', 'plannedQuantity');
      }
      
      if (batch.plannedQuantity > 10000) {
        warnings.add('批次数量较大: ${batch.batchNumber} (${batch.plannedQuantity})');
      }
    }
    
    return ValidationResult.valid(warnings);
  }

  /// 私有方法：验证照片信息
  ValidationResult _validatePhotoInfo(TaskModel task) {
    final warnings = <String>[];
    
    if (task.photos.isEmpty) {
      warnings.add('任务没有配置照片要求');
      return ValidationResult.valid(warnings);
    }
    
    final requiredPhotos = task.photos.where((p) => p.isRequired).toList();
    if (requiredPhotos.isEmpty) {
      warnings.add('任务没有必需照片');
    }
    
    // 检查照片ID唯一性
    final photoIds = task.photos.map((p) => p.id).toList();
    final uniqueIds = photoIds.toSet();
    if (photoIds.length != uniqueIds.length) {
      return ValidationResult.invalid('照片ID存在重复', 'photos');
    }
    
    return ValidationResult.valid(warnings);
  }

  /// 私有方法：验证参与人员
  ValidationResult _validateParticipants(TaskModel task) {
    final warnings = <String>[];
    
    if (task.participants.isEmpty) {
      warnings.add('任务没有分配参与人员');
    }
    
    if (task.participants.length > 20) {
      warnings.add('参与人员数量较多: ${task.participants.length}人');
    }
    
    // 检查参与人员ID唯一性
    final uniqueParticipants = task.participants.toSet();
    if (task.participants.length != uniqueParticipants.length) {
      return ValidationResult.invalid('参与人员存在重复', 'participants');
    }
    
    return ValidationResult.valid(warnings);
  }

  /// 私有方法：根据照片类型验证识别结果
  ValidationResult _validateRecognitionByPhotoType(PhotoItem photo, RecognitionResult result) {
    switch (photo.label) {
      case '过磅单':
        // 过磅单应该包含数字信息
        if (!RegExp(r'\d+').hasMatch(result.recognizedText ?? '')) {
          return ValidationResult.invalid('过磅单识别结果应包含数字信息');
        }
        break;
      case '装车前':
      case '装车后':
        // 装车照片的验证逻辑
        break;
      default:
        // 其他类型照片的验证逻辑
        break;
    }

    return ValidationResult.valid();
  }

  /// 获取验证统计信息
  Map<String, dynamic> getValidationStatistics() {
    return {
      'serviceInitialized': true,
      'supportedValidations': [
        'taskCompleteness',
        'taskCanComplete',
        'photoUpload',
        'recognitionResult',
      ],
      'validationRules': {
        'maxBatchesPerTask': 50,
        'maxParticipantsPerTask': 20,
        'maxPhotosPerTask': 100,
        'supportedImageFormats': ['.jpg', '.jpeg', '.png', '.bmp'],
      },
    };
  }
}
