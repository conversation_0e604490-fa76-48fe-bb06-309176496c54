import 'dart:async' show unawaited;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:loadguard/repositories/task_repository.dart';
import 'package:loadguard/repositories/task_repository_impl.dart';
import 'package:loadguard/repositories/workload_repository.dart';
import 'package:loadguard/repositories/hive_config_data_source.dart';
import 'package:loadguard/models/workload_models.dart';
import 'package:loadguard/utils/app_logger.dart';

/// 🏗️ 现代Riverpod实现 - 使用最新的Provider API

/// 📋 TaskRepository Provider - 使用现代keepAlive语法
final taskRepositoryProvider = Provider<TaskRepository>((ref) {
  ref.keepAlive(); // 现代的keepAlive语法

  final repository = TaskRepositoryImpl();

  // 异步初始化
  ref.onDispose(() {
    repository.dispose();
  });

  // 立即初始化，但不阻塞Provider创建
  repository.initialize().catchError((e) {
    AppLogger.error('❌ TaskRepository初始化失败', error: e);
  });

  return repository;
});

/// 🏗️ WorkloadRepository Provider - 现代依赖注入
final workloadRepositoryProvider = Provider<WorkloadRepository>((ref) {
  ref.keepAlive(); // 保持活跃状态

  final taskRepository = ref.watch(taskRepositoryProvider);
  final configDataSource = HiveConfigDataSource();

  final repository = WorkloadRepositoryImpl(
    taskRepository: taskRepository,
    configDataSource: configDataSource,
  );

  // 现代的资源管理
  ref.onDispose(() {
    repository.dispose();
  });

  // 异步初始化
  repository.initialize().catchError((e) {
    AppLogger.error('❌ WorkloadRepository初始化失败', error: e);
  });

  return repository;
});

/// 📊 现代工作量统计状态管理
/// 使用最新的AsyncNotifier API和现代Riverpod模式
class WorkloadNotifier extends AsyncNotifier<WorkloadStatistics> {
  late final WorkloadRepository _repository;

  @override
  Future<WorkloadStatistics> build() async {
    // 现代的依赖获取方式
    _repository = ref.watch(workloadRepositoryProvider);

    // 监听Repository状态变化（现代响应式编程）
    // 注意：在build方法中不使用listenSelf，而是在其他地方监听状态变化

    // 加载初始数据
    return await _loadWorkloadStatistics();
  }

  /// 🔄 现代异步数据加载
  Future<WorkloadStatistics> _loadWorkloadStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final statistics = await _repository.getWorkloadStatistics(
        startDate: startDate,
        endDate: endDate,
      );

      AppLogger.info('📊 工作量统计数据加载完成');
      return statistics;
    } catch (e) {
      AppLogger.error('❌ 工作量统计数据加载失败', error: e);
      rethrow;
    }
  }

  /// 🔄 现代响应式刷新机制
  Future<void> refreshStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // 使用现代的update方法进行状态更新
    state = await AsyncValue.guard(() async {
      // 清除缓存
      await _repository.clearCachedStatistics();

      // 重新加载数据
      final statistics = await _repository.getWorkloadStatistics(
        startDate: startDate,
        endDate: endDate,
      );

      AppLogger.info('✅ 工作量统计数据刷新完成');
      return statistics;
    });
  }

  /// 🎯 现代乐观更新机制
  Future<void> optimisticRefresh({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    // 保存当前状态作为回退
    final previousState = state;

    try {
      // 立即显示加载状态
      state = const AsyncValue.loading();

      // 异步加载新数据
      final newStatistics = await _repository.getWorkloadStatistics(
        startDate: startDate,
        endDate: endDate,
      );

      // 更新状态
      state = AsyncValue.data(newStatistics);

      // 乐观更新缓存
      _repository.setCachedStatistics(newStatistics);

    } catch (e) {
      // 出错时回退到之前的状态
      state = previousState;
      AppLogger.error('❌ 乐观更新失败，已回退', error: e);
      rethrow;
    }
  }

  /// 📈 现代异步工作人员统计获取
  Future<WorkerStatistics?> getWorkerStatistics(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return await AsyncValue.guard(() async {
      return await _repository.getWorkerStatistics(
        workerId,
        startDate: startDate,
        endDate: endDate,
      );
    }).then((asyncValue) => asyncValue.valueOrNull);
  }

  /// 📊 现代工作量趋势分析
  Future<Map<String, double>> getWorkloadTrends(String workerId, {
    DateTime? startDate,
    DateTime? endDate,
    String period = 'daily',
  }) async {
    return await AsyncValue.guard(() async {
      return await _repository.getWorkloadTrends(
        workerId,
        startDate: startDate,
        endDate: endDate,
        period: period,
      );
    }).then((asyncValue) => asyncValue.valueOrNull ?? {});
  }

  /// 🏆 现代团队对比分析
  Future<Map<String, Map<String, dynamic>>> getTeamComparison({
    DateTime? startDate,
    DateTime? endDate,
    String groupBy = 'role',
  }) async {
    return await AsyncValue.guard(() async {
      return await _repository.getTeamComparison(
        startDate: startDate,
        endDate: endDate,
        groupBy: groupBy,
      );
    }).then((asyncValue) => asyncValue.valueOrNull ?? {});
  }

  /// 🔍 现代数据验证机制
  Future<Map<String, dynamic>> validateWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return await AsyncValue.guard(() async {
      return await _repository.validateWorkloadData(
        startDate: startDate,
        endDate: endDate,
      );
    }).then((asyncValue) => asyncValue.valueOrNull ?? {
      'error': 'Validation failed',
      'dataIntegrity': 'error',
      'lastValidated': DateTime.now().toIso8601String(),
    });
  }

  /// 📤 现代数据导出
  Future<Map<String, dynamic>> exportWorkloadData({
    DateTime? startDate,
    DateTime? endDate,
    List<String>? workerIds,
  }) async {
    return await AsyncValue.guard(() async {
      return await _repository.exportWorkloadData(
        startDate: startDate,
        endDate: endDate,
        workerIds: workerIds,
      );
    }).then((asyncValue) {
      if (asyncValue.hasError) {
        AppLogger.error('❌ 导出工作量数据失败', error: asyncValue.error);
        throw asyncValue.error!;
      }
      return asyncValue.value!;
    });
  }

  /// 🎯 现代乐观更新 - 响应式UI更新
  Future<void> optimisticUpdate(WorkloadStatistics newStatistics) async {
    // 立即更新UI状态
    state = AsyncValue.data(newStatistics);

    // 异步保存缓存，不阻塞UI
    unawaited(_repository.setCachedStatistics(newStatistics).catchError((e) {
      AppLogger.error('❌ 缓存工作量统计失败', error: e);
    }));
  }

  /// 🔄 现代日期范围设置
  Future<void> setDateRange(DateTime? startDate, DateTime? endDate) async {
    await optimisticRefresh(startDate: startDate, endDate: endDate);
  }

  /// 🧹 现代过滤清除
  Future<void> clearDateFilter() async {
    await optimisticRefresh();
  }
}

/// 🎯 现代WorkloadNotifier Provider - 使用最新AsyncNotifierProvider
final workloadNotifierProvider = AsyncNotifierProvider<WorkloadNotifier, WorkloadStatistics>(
  WorkloadNotifier.new,
);

/// 🎯 现代响应式Provider定义 - 使用最新的Riverpod 2.x语法

/// 📊 现代工作量概览Provider - 响应式数据流
final workloadOverviewProvider = Provider<WorkloadOverview?>((ref) {
  return ref.watch(workloadNotifierProvider).whenOrNull(
    data: (statistics) => statistics.overview,
  );
});

/// 👥 现代工作人员统计Provider - 类型安全的响应式数据
final workerStatisticsProvider = Provider<Map<String, WorkerStatistics>>((ref) {
  return ref.watch(workloadNotifierProvider).when(
    data: (statistics) => statistics.workerStats,
    loading: () => <String, WorkerStatistics>{},
    error: (_, __) => <String, WorkerStatistics>{},
  );
});

/// 🏆 现代效率排名Provider - 实时更新的排名数据
final efficiencyRankingProvider = Provider<List<EfficiencyRanking>>((ref) {
  return ref.watch(workloadNotifierProvider).when(
    data: (statistics) => statistics.efficiencyRanking,
    loading: () => <EfficiencyRanking>[],
    error: (_, __) => <EfficiencyRanking>[],
  );
});

/// 📈 现代工作人员详细统计Provider - 参数化查询
final workerDetailProvider = FutureProvider.family<WorkerStatistics?, String>((ref, workerId) async {
  final notifier = ref.read(workloadNotifierProvider.notifier);
  return await notifier.getWorkerStatistics(workerId);
});

/// 📊 现代工作量趋势Provider - 灵活的参数传递
final workloadTrendsProvider = FutureProvider.family<Map<String, double>, ({String workerId, String period})>(
  (ref, params) async {
    final notifier = ref.read(workloadNotifierProvider.notifier);
    return await notifier.getWorkloadTrends(
      params.workerId,
      period: params.period,
    );
  },
);

/// 🏆 现代团队对比Provider - 动态分组分析
final teamComparisonProvider = FutureProvider.family<Map<String, Map<String, dynamic>>, String>(
  (ref, groupBy) async {
    final notifier = ref.read(workloadNotifierProvider.notifier);
    return await notifier.getTeamComparison(groupBy: groupBy);
  },
);

/// 🔍 现代数据验证Provider - 实时数据完整性检查
final workloadValidationProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final notifier = ref.read(workloadNotifierProvider.notifier);
  return await notifier.validateWorkloadData();
});

/// 📅 现代日期范围状态Provider - 类型安全的状态管理
final dateRangeFilterProvider = StateProvider<({DateTime? startDate, DateTime? endDate})>((ref) {
  return (startDate: null, endDate: null);
});

/// 🔄 现代响应式过滤Provider - 自动响应日期变化
final filteredWorkloadProvider = FutureProvider<WorkloadStatistics>((ref) async {
  final dateRange = ref.watch(dateRangeFilterProvider);
  final notifier = ref.read(workloadNotifierProvider.notifier);

  // 响应式数据加载
  await notifier.optimisticRefresh(
    startDate: dateRange.startDate,
    endDate: dateRange.endDate,
  );

  // 返回最新状态
  return ref.read(workloadNotifierProvider).when(
    data: (statistics) => statistics,
    loading: () => throw const AsyncLoading<WorkloadStatistics>(),
    error: (error, stackTrace) => throw AsyncError<WorkloadStatistics>(error, stackTrace),
  );
});

/// 📊 现代实时监控Provider - 响应式数据流
final realTimeWorkloadProvider = StreamProvider<WorkloadStatistics>((ref) async* {
  final notifier = ref.read(workloadNotifierProvider.notifier);

  // 监听状态变化
  ref.listen(workloadNotifierProvider, (previous, next) {
    // 当状态变化时，流会自动更新
  });

  // 初始数据
  final initialState = ref.read(workloadNotifierProvider);
  if (initialState.hasValue) {
    yield initialState.value!;
  }

  // 现代定时器 - 使用Stream.periodic
  await for (final _ in Stream.periodic(const Duration(minutes: 5))) {
    try {
      await notifier.optimisticRefresh();
      final currentState = ref.read(workloadNotifierProvider);
      if (currentState.hasValue) {
        yield currentState.value!;
      }
    } catch (e) {
      AppLogger.error('❌ 实时监控刷新失败', error: e);
      // 继续监控，不中断流
    }
  }
});

/// 🎯 现代仪表板摘要Provider - 智能数据聚合
final workloadDashboardProvider = Provider<({
  int totalTasks,
  int completedTasks,
  double completionRate,
  int activeWorkers,
  int totalWorkers,
  double averageEfficiency,
  double totalTonnage,
  double completedTonnage,
  String? topPerformer,
  double? topPerformerEfficiency,
  DateTime? lastUpdated,
  bool isDataFresh,
  bool isLoading,
  bool hasError,
})>((ref) {
  final workloadAsync = ref.watch(workloadNotifierProvider);

  return workloadAsync.when(
    data: (statistics) {
      final overview = statistics.overview;
      final topPerformer = statistics.efficiencyRanking.isNotEmpty
          ? statistics.efficiencyRanking.first
          : null;

      return (
        totalTasks: overview.totalTasks,
        completedTasks: overview.completedTasks,
        completionRate: overview.completionRate,
        activeWorkers: overview.activeWorkers,
        totalWorkers: overview.totalWorkers,
        averageEfficiency: overview.averageEfficiency,
        totalTonnage: overview.totalTonnage,
        completedTonnage: overview.completedTonnage,
        topPerformer: topPerformer?.workerName,
        topPerformerEfficiency: topPerformer?.efficiency,
        lastUpdated: statistics.lastUpdated,
        isDataFresh: DateTime.now().difference(statistics.lastUpdated).inMinutes < 10,
        isLoading: false,
        hasError: false,
      );
    },
    loading: () => (
      totalTasks: 0,
      completedTasks: 0,
      completionRate: 0.0,
      activeWorkers: 0,
      totalWorkers: 0,
      averageEfficiency: 0.0,
      totalTonnage: 0.0,
      completedTonnage: 0.0,
      topPerformer: null,
      topPerformerEfficiency: null,
      lastUpdated: null,
      isDataFresh: false,
      isLoading: true,
      hasError: false,
    ),
    error: (_, __) => (
      totalTasks: 0,
      completedTasks: 0,
      completionRate: 0.0,
      activeWorkers: 0,
      totalWorkers: 0,
      averageEfficiency: 0.0,
      totalTonnage: 0.0,
      completedTonnage: 0.0,
      topPerformer: null,
      topPerformerEfficiency: null,
      lastUpdated: null,
      isDataFresh: false,
      isLoading: false,
      hasError: true,
    ),
  );
});

/// 📈 现代趋势分析Provider - 智能对比分析
final workloadTrendAnalysisProvider = FutureProvider<({
  double currentEfficiency,
  double lastWeekEfficiency,
  double efficiencyTrend,
  String efficiencyTrendDirection,
  double currentCompletionRate,
  double lastWeekCompletionRate,
  double completionRateTrend,
  String completionRateTrendDirection,
  int improvingWorkers,
  int needsAttentionWorkers,
  bool hasError,
  String? errorMessage,
})>((ref) async {
  final repository = ref.read(workloadRepositoryProvider);

  try {
    // 并行获取当前和历史数据
    final currentStatsFuture = repository.getWorkloadStatistics();
    final lastWeekStart = DateTime.now().subtract(const Duration(days: 14));
    final lastWeekEnd = DateTime.now().subtract(const Duration(days: 7));
    final lastWeekStatsFuture = repository.getWorkloadStatistics(
      startDate: lastWeekStart,
      endDate: lastWeekEnd,
    );

    final [currentStats, lastWeekStats] = await Future.wait([
      currentStatsFuture,
      lastWeekStatsFuture,
    ]);

    // 智能趋势计算
    final currentEfficiency = currentStats.overview.averageEfficiency;
    final lastWeekEfficiency = lastWeekStats.overview.averageEfficiency;
    final efficiencyTrend = currentEfficiency - lastWeekEfficiency;

    final currentCompletionRate = currentStats.overview.completionRate;
    final lastWeekCompletionRate = lastWeekStats.overview.completionRate;
    final completionRateTrend = currentCompletionRate - lastWeekCompletionRate;

    // 智能分类工作人员
    final improvingWorkers = currentStats.efficiencyRanking
        .where((w) => w.efficiency > 0.8)
        .length;
    final needsAttentionWorkers = currentStats.efficiencyRanking
        .where((w) => w.efficiency < 0.5)
        .length;

    return (
      currentEfficiency: currentEfficiency,
      lastWeekEfficiency: lastWeekEfficiency,
      efficiencyTrend: efficiencyTrend,
      efficiencyTrendDirection: efficiencyTrend > 0.05 ? 'up'
          : efficiencyTrend < -0.05 ? 'down' : 'stable',
      currentCompletionRate: currentCompletionRate,
      lastWeekCompletionRate: lastWeekCompletionRate,
      completionRateTrend: completionRateTrend,
      completionRateTrendDirection: completionRateTrend > 0.05 ? 'up'
          : completionRateTrend < -0.05 ? 'down' : 'stable',
      improvingWorkers: improvingWorkers,
      needsAttentionWorkers: needsAttentionWorkers,
      hasError: false,
      errorMessage: null,
    );
  } catch (e) {
    AppLogger.error('❌ 工作量趋势分析失败', error: e);
    return (
      currentEfficiency: 0.0,
      lastWeekEfficiency: 0.0,
      efficiencyTrend: 0.0,
      efficiencyTrendDirection: 'stable',
      currentCompletionRate: 0.0,
      lastWeekCompletionRate: 0.0,
      completionRateTrend: 0.0,
      completionRateTrendDirection: 'stable',
      improvingWorkers: 0,
      needsAttentionWorkers: 0,
      hasError: true,
      errorMessage: e.toString(),
    );
  }
});
